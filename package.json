{"name": "amap-frontend", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build:dev": "ng build --configuration development", "build:staging": "ng build --configuration staging", "build:analyze": "ng build --stats-json && npx webpack-bundle-analyzer dist/amap-frontend/stats.json", "watch": "ng build --watch --configuration development", "test": "jest", "test:watch": "jest --watch", "test:fullCoverage": "jest --coverage --maxWorkers=75%", "test:coverage": "npx jest --onlyChanged --coverage ", "sonar-scanner": "sonar-scanner", "serve:ssr:amap-frontend": "node dist/amap-frontend/server/server.mjs", "prepare": "husky || true", "lint": "ng lint", "lint:fix": "ng lint --fix", "format:check": "npx prettier src --check ", "format": "prettier --ignore-path .gitignore --write \"**/*.+(js|ts|json|md|css|html)\" --config ./.prettierrc"}, "private": true, "dependencies": {"@angular/animations": "^17.2.0", "@angular/cdk": "^17.3.10", "@angular/common": "^17.2.0", "@angular/compiler": "^17.2.0", "@angular/core": "^17.2.0", "@angular/forms": "^17.2.0", "@angular/material": "^17.3.10", "@angular/platform-browser": "^17.2.0", "@angular/platform-browser-dynamic": "^17.2.0", "@angular/platform-server": "^17.2.0", "@angular/router": "^17.2.0", "@fingerprintjs/fingerprintjs": "^4.6.1", "@ngrx/component-store": "^17.2.0", "@ngrx/store-devtools": "^17.2.0", "@segment/analytics-next": "^1.75.0", "@sentry/angular": "^9.7.0", "axios": "^1.6.8", "buffer": "^6.0.3", "chart.js": "^4.4.6", "crypto-js": "^4.2.0", "dompurify": "^3.2.6", "face-api.js": "^0.22.2", "highlight.js": "^11.11.1", "html-truncate": "^1.2.2", "joi": "^17.13.3", "jspdf-autotable": "^3.8.2", "launchdarkly-js-client-sdk": "^3.8.1", "lottie-web": "^5.12.2", "marked": "^15.0.11", "moment": "^2.30.1", "monaco-editor": "^0.52.2", "ngx-highlightjs": "^12.0.0", "ngx-image-compress": "^18.1.5", "ngx-lottie": "^11.0.2", "ngx-pagination": "^6.0.3", "ngx-quill": "^25.0.0", "papaparse": "^5.5.3", "pdfjs-dist": "^5.3.93", "primeicons": "^7.0.0", "primeng": "^17.12.0", "rxjs": "~7.8.0", "tesseract.js": "^6.0.1", "tslib": "^2.3.0", "zone.js": "^0.14.4"}, "devDependencies": {"@angular-builders/jest": "^17.0.3", "@angular-devkit/build-angular": "^17.2.2", "@angular-eslint/builder": "17.2.1", "@angular-eslint/eslint-plugin": "17.2.1", "@angular-eslint/eslint-plugin-template": "17.2.1", "@angular-eslint/schematics": "17.2.1", "@angular-eslint/template-parser": "17.2.1", "@angular/cli": "^17.3.10", "@angular/compiler-cli": "^17.2.0", "@commitlint/cli": "^19.0.3", "@commitlint/config-conventional": "^19.0.3", "@ngrx/eslint-plugin": "^17.2.0", "@types/crypto-js": "^4.2.2", "@types/express": "^4.17.17", "@types/html-truncate": "^1.2.4", "@types/jest": "^29.5.13", "@types/node": "^18.18.0", "@types/papaparse": "^5.3.14", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "6.19.0", "@typescript-eslint/parser": "6.19.0", "autoprefixer": "^10.4.18", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "husky": "^9.0.11", "identity-obj-proxy": "^3.0.0", "jest-canvas-mock": "^2.5.2", "lint-staged": "^15.2.2", "ng-lint-staged": "^12.0.4", "postcss": "^8.4.35", "prettier": "^3.2.5", "sonarqube-scanner": "^4.2.5", "tailwindcss": "^3.4.1", "typescript": "~5.3.2", "webpack-bundle-analyzer": "^4.10.2"}}