# AMAP Frontend - Comprehensive Documentation

## Table of Contents
1. [Project Overview](#project-overview)
2. [Technology Stack](#technology-stack)
3. [Architecture & Project Structure](#architecture--project-structure)
4. [Key Features](#key-features)
5. [Development Setup](#development-setup)
6. [Testing Strategy](#testing-strategy)
7. [Build & Deployment](#build--deployment)
8. [Code Quality & Standards](#code-quality--standards)
9. [Security Features](#security-features)
10. [Performance Optimizations](#performance-optimizations)
11. [API Integration](#api-integration)
12. [Troubleshooting](#troubleshooting)

---

## Project Overview

**AMAP Frontend** is a comprehensive Angular 17 application designed for assessment management and proctoring. The platform serves multiple user roles including Domain Admins, Recruiters, Report Managers, and Test Takers, providing a complete solution for creating, managing, and taking assessments.

### Key Capabilities
- **Assessment Management**: Create and manage various types of tests (MCQ, Comprehension, Coding)
- **Test Taking Platform**: Secure test-taking environment with proctoring features
- **Report Generation**: Comprehensive reporting and analytics
- **User Management**: Role-based access control and user administration
- **Real-time Monitoring**: Live proctoring and violation detection

---

## Technology Stack

### Core Framework
- **Angular 17.2.0** - Latest Angular with standalone components
- **TypeScript 5.3.2** - Type-safe development
- **RxJS 7.8.0** - Reactive programming

### UI/UX Libraries
- **Angular Material 17.3.10** - Material Design components
- **PrimeNG 17.12.0** - Rich UI component library
- **Tailwind CSS 3.4.1** - Utility-first CSS framework
- **Lottie Web 5.12.2** - Animation library

### State Management & Data
- **NgRx Component Store 17.2.0** - Local state management
- **NgRx Store DevTools 17.2.0** - Development debugging

### Testing Framework
- **Jest 29.5.13** - JavaScript testing framework
- **Angular Testing Utilities** - Component and service testing

### Development Tools
- **ESLint 8.56.0** - Code linting
- **Prettier 3.2.5** - Code formatting
- **Husky 9.0.11** - Git hooks
- **SonarQube Scanner** - Code quality analysis

### Additional Libraries
- **Monaco Editor 0.52.2** - Code editor for programming questions
- **Chart.js 4.4.6** - Data visualization
- **Quill Editor** - Rich text editing
- **Moment.js 2.30.1** - Date manipulation
- **Crypto-js 4.2.0** - Cryptographic functions
- **FingerprintJS 4.6.1** - Browser fingerprinting
- **Sentry 9.7.0** - Error monitoring

---

## Architecture & Project Structure

### Application Architecture
The application follows Angular's recommended architecture patterns:

```
src/
├── app/
│   ├── main-app/                    # Main application shell
│   ├── authentication/              # Login, registration, password reset
│   ├── dashboard-home-page/         # Role-based dashboards
│   ├── test-management/             # Test creation and management
│   ├── test-taking/                 # Test execution environment
│   ├── test-taker/                  # Test taker interface
│   ├── reportsManagement/           # Reports and analytics
│   ├── components/                  # Shared UI components
│   ├── services/                    # Business logic and API calls
│   ├── Interfaces/                  # TypeScript interfaces
│   ├── utils/                       # Utility functions
│   └── guards/                      # Route guards
├── assets/                          # Static assets
└── environments/                    # Environment configurations
```

### Key Architectural Patterns

#### 1. Standalone Components
- Modern Angular 17 standalone components
- Reduced bundle size and improved tree-shaking
- Simplified dependency injection

#### 2. Lazy Loading
- Feature modules loaded on-demand
- Improved initial load performance
- Route-based code splitting

#### 3. Reactive Programming
- RxJS observables for data flow
- Reactive forms for user input
- Event-driven architecture

#### 4. Component Store Pattern
- NgRx Component Store for local state
- Centralized state management
- Predictable state updates

---

## Key Features

### 1. Authentication & Security
- **Multi-factor Authentication**: Email verification and password policies
- **Browser Fingerprinting**: Device-based security using FingerprintJS
- **Session Management**: Automatic session timeout and renewal
- **Role-based Access Control**: Different permissions for different user types

### 2. Test Management
- **Multiple Question Types**: 
  - Multiple Choice Questions (MCQ)
  - True/False Questions
  - Essay Questions
  - Coding Questions with Monaco Editor
  - Matrix Questions
  - Fill-in-the-blank Questions
- **Comprehension Tests**: Reading passages with related questions
- **Test Configuration**: Duration, passing marks, difficulty levels
- **Question Banks**: Reusable question libraries

### 3. Test Taking Environment
- **Secure Interface**: Full-screen mode with navigation restrictions
- **Real-time Proctoring**: 
  - Screen recording
  - Camera monitoring
  - Tab switching detection
  - Copy-paste restrictions
- **Progress Tracking**: Visual progress indicators
- **Auto-save**: Automatic answer saving
- **Timer Management**: Individual question and overall test timers

### 4. Reporting & Analytics
- **Comprehensive Reports**: Test results, user performance, analytics
- **Data Visualization**: Charts and graphs using Chart.js
- **Export Capabilities**: PDF and CSV export functionality
- **Proctoring Reports**: Violation detection and monitoring data

### 5. User Management
- **Organization Management**: Multi-tenant architecture
- **User Roles**: Domain Admin, Recruiter, Report Manager, Test Taker
- **Profile Management**: User profiles and preferences
- **Bulk Operations**: Bulk user import/export

---

## Development Setup

### Prerequisites
- Node.js 18+ 
- npm or yarn package manager
- Git

### Installation Steps

1. **Clone the Repository**
   ```bash
   git clone <repository-url>
   cd amap-frontend
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   - Copy environment template files
   - Configure API endpoints and keys
   - Set up authentication providers

4. **Start Development Server**
   ```bash
   npm start
   # Application runs on http://localhost:4200
   ```

### Available Scripts

| Script | Description |
|--------|-------------|
| `npm start` | Start development server |
| `npm run build` | Production build |
| `npm run build:dev` | Development build |
| `npm run build:staging` | Staging build |
| `npm test` | Run unit tests |
| `npm run test:watch` | Run tests in watch mode |
| `npm run test:fullCoverage` | Run tests with full coverage |
| `npm run lint` | Run ESLint |
| `npm run lint:fix` | Fix ESLint issues |
| `npm run format` | Format code with Prettier |

---

## Testing Strategy

### Testing Framework Setup
- **Jest** as the primary testing framework
- **Angular Testing Utilities** for component testing
- **Coverage reporting** with lcov and text formats

### Test Configuration
```javascript
// jest.config.js
module.exports = {
  preset: 'jest-preset-angular',
  setupFilesAfterEnv: ['<rootDir>/src/setup.jest.ts'],
  coverageThreshold: {
    global: {
      branches: 59,
      functions: 73,
      lines: 80,
      statements: 80
    }
  }
};
```

### Testing Patterns

#### 1. Component Testing
- Unit tests for all components
- Mock dependencies and services
- Test user interactions and outputs

#### 2. Service Testing
- Test business logic and API calls
- Mock HTTP requests
- Verify error handling

#### 3. Integration Testing
- Test component-service interactions
- Verify data flow between components
- Test routing and navigation

#### 4. Utility Testing
- Test pure functions and utilities
- Verify edge cases and error conditions
- Test data transformations

### Coverage Requirements
- **Lines**: 80% minimum coverage
- **Functions**: 73% minimum coverage
- **Branches**: 59% minimum coverage
- **Statements**: 80% minimum coverage

### Running Tests
```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:fullCoverage

# Run tests in watch mode
npm run test:watch

# Run only changed files
npm run test:coverage
```

---

## Build & Deployment

### Build Configurations

#### Development Build
```bash
npm run build:dev
```
- Source maps enabled
- No optimization
- Development environment variables

#### Staging Build
```bash
npm run build:staging
```
- Partial optimization
- Staging environment variables
- Debug information retained

#### Production Build
```bash
npm run build
```
- Full optimization and minification
- Tree-shaking enabled
- Production environment variables
- Bundle analysis and optimization

### Deployment Targets
- **Development**: Local development environment
- **Staging**: Pre-production testing environment  
- **Production**: Live production environment

### Build Artifacts
- Output directory: `dist/amap-frontend/`
- Static assets optimized and compressed
- Service worker for caching (if enabled)
- Source maps (development only)

---

## Code Quality & Standards

### Linting Configuration
- **ESLint** with Angular-specific rules
- **Prettier** for consistent code formatting
- **TypeScript strict mode** enabled

### Git Hooks
- **Pre-commit**: Lint and format staged files
- **Commit message**: Conventional commit format
- **Pre-push**: Run tests before pushing

### Code Standards
- **TypeScript strict mode**: Type safety enforced
- **Component naming**: PascalCase for components
- **Service naming**: camelCase with Service suffix
- **File naming**: kebab-case for files
- **Import organization**: Grouped and sorted imports

### Quality Gates
- **SonarQube integration**: Code quality analysis
- **Coverage thresholds**: Minimum test coverage required
- **Build verification**: All builds must pass linting and tests

---

## Security Features

### Authentication Security
- **JWT Token Management**: Secure token storage and refresh
- **Browser Fingerprinting**: Device identification for security
- **Session Timeout**: Automatic logout after inactivity
- **Password Policies**: Strong password requirements

### Test Security
- **Proctoring Features**: 
  - Screen recording and monitoring
  - Tab switching detection
  - Copy-paste restrictions
  - Camera and microphone access
- **Content Protection**: 
  - Disable right-click and developer tools
  - Prevent text selection and copying
  - Secure question delivery

### Data Security
- **HTTPS Enforcement**: All communications encrypted
- **Input Sanitization**: XSS prevention with DOMPurify
- **CSRF Protection**: Cross-site request forgery prevention
- **Content Security Policy**: Restrict resource loading

---

## Performance Optimizations

### Bundle Optimization
- **Lazy Loading**: Feature modules loaded on demand
- **Tree Shaking**: Unused code elimination
- **Code Splitting**: Route-based chunking
- **Preloading Strategy**: Intelligent module preloading

### Runtime Performance
- **OnPush Change Detection**: Optimized change detection
- **Virtual Scrolling**: Efficient large list rendering
- **Image Optimization**: Compressed and optimized assets
- **Caching Strategy**: HTTP caching and service workers

### Monitoring
- **Sentry Integration**: Error tracking and performance monitoring
- **Analytics**: User behavior and performance metrics
- **Bundle Analysis**: Regular bundle size monitoring

---

## API Integration

### Service Architecture
- **HTTP Client**: Angular HttpClient with interceptors
- **Error Handling**: Centralized error management
- **Loading States**: Global loading indicators
- **Retry Logic**: Automatic retry for failed requests

### API Endpoints
- **Authentication Service**: `/auth-service/`
- **Test Service**: `/test-service/`
- **User Service**: `/user-service/`
- **Test Taking Service**: `/test-taking/`
- **Report Service**: `/report-service/`

### Data Models
- TypeScript interfaces for all API responses
- Validation using Joi schemas
- Type-safe API calls and responses

---

## Troubleshooting

### Common Issues

#### 1. Build Failures
- **Node version compatibility**: Ensure Node.js 18+
- **Dependency conflicts**: Clear node_modules and reinstall
- **Memory issues**: Increase Node.js heap size

#### 2. Test Failures
- **Mock setup**: Verify service mocks are properly configured
- **Async testing**: Use proper async/await patterns
- **DOM testing**: Ensure proper component fixture setup

#### 3. Runtime Errors
- **Console errors**: Check browser console for JavaScript errors
- **Network issues**: Verify API endpoints and CORS configuration
- **Authentication**: Check token validity and refresh logic

### Debug Tools
- **Angular DevTools**: Browser extension for debugging
- **Redux DevTools**: State management debugging
- **Network tab**: Monitor API calls and responses
- **Sentry Dashboard**: Production error monitoring

### Performance Issues
- **Bundle analysis**: Use webpack-bundle-analyzer
- **Memory leaks**: Check for unsubscribed observables
- **Change detection**: Profile component update cycles
- **Network optimization**: Monitor API call frequency

---

## Maintenance & Updates

### Regular Maintenance Tasks
- **Dependency updates**: Monthly security and feature updates
- **Test coverage**: Maintain minimum coverage thresholds
- **Performance monitoring**: Regular performance audits
- **Security scanning**: Automated vulnerability scanning

### Version Control
- **Semantic versioning**: Follow semver for releases
- **Branch strategy**: Feature branches with pull requests
- **Code reviews**: Mandatory peer reviews
- **Release notes**: Detailed changelog for each release

---

*This documentation is maintained by the development team and should be updated with any significant changes to the application architecture or features.*
