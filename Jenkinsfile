def appname = "dodokpo-codedeploy-app"
def deploy_group = "dodokpo-frontend-dev"
def deploy_group_staging =  "dodokpo-frontend-staging"
def deploy_group_prod = "dodokpo-frontend-production"
def s3_bucket = "dodokpo-artefacts"
def s3_filename = "dodokpo-codedeploy-src-frontend"

//Slack Notification Integration
def gitName = env.GIT_BRANCH
def jobName = env.JOB_NAME
def branchName = env.BRANCH_NAME
def main_branch = ['new-staging', 'new-build']

// Environments Declaration
environment {
  jobName = env.JOB_NAME
  branchName = env.BRANCH_NAME
}

// Successful Build
def buildSuccess = [
  [text: "AMAP Frontend Build Successful on ${branchName}",
  fallback: "AMAP Frontend Build Successful on ${branchName}",
  color: "#00FF00"
  ]
]

// Failed Build
def buildError = [
  [text: "AMAP Frontend Build Failed on ${branchName}",
  fallback: "AMAP Frontend Build Failed on ${branchName}",
  color: "#FF0000"
  ]
]

pipeline {
  agent any
  tools {nodejs "nodejs"}

    // environment {
    //   CHROME_BIN = '/usr/bin/google-chrome'
    // }

  stages {
    stage('Install Dependencies') {
      steps {
        sh 'npm install'
      }
    }

    stage('Run Tests') {
      when {
        branch 'new-build';
      }

      steps {
        withCredentials([file(credentialsId: 'dodokpo-dev', variable: 'mySecretEnvFile'),file(credentialsId: 'dodokpo-dev', variable: 'mySecretEnvFile2')]) {
          // export CHROME_BIN=/usr/bin/google-chrome
          
          sh '''
            mkdir ./src/environments/
            cp $mySecretEnvFile ./src/environments/environment.ts
            cp $mySecretEnvFile2 ./src/environments/environment.development.ts 
            npm run test:fullCoverage
          '''         
        }
      }
    }

    stage('SonarQube Analysis') {
      when {
        branch 'new-build';
      }

      steps {
        withSonarQubeEnv('SonarQube') {
          script {
            def scannerHome = tool 'SonarScanner';
            sh "${scannerHome}/bin/sonar-scanner"
          }
        }
      }
    }

    stage('Build Dev') {
        when{
          branch 'new-build';
        }

        steps {
          withCredentials([file(credentialsId: 'dodokpo-dev', variable: 'mySecretEnvFile'),file(credentialsId: 'dodokpo-dev', variable: 'mySecretEnvFile2')]) {
              // mkdir ./src/environments/
            
            sh '''
              cp $mySecretEnvFile ./src/environments/environment.ts
              cp $mySecretEnvFile2 ./src/environments/environment.development.ts 
            '''
            sh 'npm run build:dev' 
          }
        }
    }

     stage('Build Staging') {
        when{
          branch 'new-staging';
        }

        steps {
          withCredentials([file(credentialsId: 'dodokpo-staging', variable: 'mySecretEnvFile'),file(credentialsId: 'dodokpo-staging', variable: 'mySecretEnvFile2')]) {
            sh '''
              mkdir ./src/environments/
              cp $mySecretEnvFile ./src/environments/environment.ts
              cp $mySecretEnvFile2 ./src/environments/environment.staging.ts 
            '''
            sh 'npm run build:staging'
          }
        }
    }

    stage('Build Production') {
        when{
          branch 'main';
        }

        steps {
          withCredentials([file(credentialsId: 'dodokpo-production', variable: 'mySecretEnvFile'),file(credentialsId: 'dodokpo-production', variable: 'mySecretEnvFile2')]) {
            sh '''
              mkdir ./src/environments/
              cp $mySecretEnvFile ./src/environments/environment.ts
              cp $mySecretEnvFile2 ./src/environments/environment.production.ts 
            '''
            sh 'npm run build'  
          }
        }
    }

     stage('Prepare to Deploy') {
         when {
            anyOf {
              branch 'new-staging';
              branch 'new-build';
              branch 'main';
            }
         }

       steps {
         withAWS(region:'eu-west-1',credentials:'dodokpo-aws-creds') {
           script {
             def gitsha = sh(script: 'git log -n1 --format=format:"%H"', returnStdout: true)
             s3_filename = "${s3_filename}-${gitsha}"
             sh """
                 aws deploy push \
                 --application-name ${appname} \
                 --description "This is a revision for the ${appname}-${gitsha}" \
                 --ignore-hidden-files \
                 --s3-location s3://${s3_bucket}/${s3_filename}.zip \
                 --source .
               """
           }
         }
       }
     }

	 stage('Deploy to Development') {
         when {
            branch 'new-build'
         }

       steps {
         withAWS(region:'eu-west-1',credentials:'dodokpo-aws-creds') {
           script {
             sh """
                 aws deploy create-deployment \
                 --application-name ${appname} \
                 --deployment-config-name CodeDeployDefault.OneAtATime \
                 --deployment-group-name ${deploy_group} \
                 --file-exists-behavior OVERWRITE \
                 --s3-location bucket=${s3_bucket},key=${s3_filename}.zip,bundleType=zip
               """
           }
         }
	   }
	 }

    stage('Deploy To Staging') {
      when {
        branch 'new-staging'
      }

      steps {
        withAWS(region:'eu-west-1',credentials:'dodokpo-aws-creds') {
          script {
            sh """
                aws deploy create-deployment \
                --application-name ${appname} \
                --deployment-config-name CodeDeployDefault.OneAtATime \
                --deployment-group-name ${deploy_group_staging} \
                --file-exists-behavior OVERWRITE \
                --s3-location bucket=${s3_bucket},key=${s3_filename}.zip,bundleType=zip
              """
          }
        }
      }
    }
    
    stage('Deploy To Production') {
      when {
        branch 'main'
      }

      steps {
        withAWS(region:'eu-west-1',credentials:'dodokpo-aws-creds') {
          script {
            sh """
                aws deploy create-deployment \
                --application-name ${appname} \
                --deployment-config-name CodeDeployDefault.OneAtATime \
                --deployment-group-name ${deploy_group_prod} \
                --file-exists-behavior OVERWRITE \
                --s3-location bucket=${s3_bucket},key=${s3_filename}.zip,bundleType=zip
              """
          }
        }
      }
    }  

    stage('Clean WS') {
      steps {
        cleanWs()
      	}
   	}
 }

 post {
    always {
      echo 'One way or another, I have finished'
      cleanWs()
    }

    success {
      script {
        if (BRANCH_NAME in main_branch) {
            slackSend(channel:"imocha-amap", attachments: buildSuccess)
          }
      }

      // withAWS(region:'eu-west-1',credentials:'dodokpo-aws-creds') {
      //   sh 'aws ses send-email --from <EMAIL> --to <EMAIL> --subject "Deployment passed" --text "AMAP AMAP Frontend Deployment passed"'
      // 		}
    }

    unstable {
      echo 'I am unstable :/'
    }

    failure {
    script {
      if (BRANCH_NAME in main_branch) {
          slackSend(channel:"imocha-amap", attachments: buildError)
          }
    }

      // withAWS(region:'eu-west-1',credentials:'dodokpo-aws-creds') {
      //   sh 'aws ses send-email --from <EMAIL> --to <EMAIL> --subject "Deployment failed" --text "AMAP AMAP Frontend Deployment failed"'
      // 		}
    }

    changed {
      echo 'Things were different before...'
    	}
  }
}
