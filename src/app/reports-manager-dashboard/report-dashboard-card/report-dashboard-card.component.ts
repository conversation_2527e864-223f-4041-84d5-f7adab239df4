import { Component, EventEmitter, inject, Input, Output } from '@angular/core';
import { TimeAgoService } from '../../services/time-ago/time-ago.service';
import { DatePipe, DecimalPipe } from '@angular/common';
import { convertDurationPreview } from '../../utils/testManagement';
import { RouterLink, Router } from '@angular/router';
import { AnalyticsService } from '../../services/analytics.service';

@Component({
  selector: 'app-report-dashboard-card',
  standalone: true,
  imports: [DecimalPipe, DatePipe, RouterLink],
  templateUrl: './report-dashboard-card.component.html',
})
export class ReportDashboardCardComponent {
  constructor(
    private readonly analyticsService: AnalyticsService,
    public readonly timeAgoService: TimeAgoService
  ) {}

  async onAssessmentClick() {
    await this.analyticsService.track('Assessment preview', {
      assessmentId: this.id,
    });
  }
  formatDuration(duration: number) {
    return convertDurationPreview(duration, true);
  }
  @Input() isRecruiterCard = false;
  @Input() title = '';
  @Input() timeUpdated = '';
  @Input() timeStarted = '';
  @Input() timeTaken = '';
  @Input() id = '';
  @Input() duration = 0;
  @Input() averageScore = 0;
  @Input() candidates = 0;
  @Output() assessmentHover = new EventEmitter<boolean>();
  router = inject(Router);

  onAssessmentHover() {
    this.assessmentHover.emit(true);
  }
}
