<a
  class="flex justify-between p-2 cursor-pointer"
  (mouseenter)="onAssessmentHover()"
  (click)="onAssessmentClick()"
  (keyup)="onAssessmentClick()"
  tabindex="0"
>
  <div class="left flex gap-x-6 items-center">
    <div>
      <img src="../../assets/reportsManagement/healthicons.svg" alt="" />
    </div>
    <div>
      <p class="text-[16px] font-medium text-[#0C4767]">{{ title }}</p>
      <div
        class="flex items-center gap-x-2 text-[14px] font-normal text-[#404852] opacity-50"
      >
        @if (isRecruiterCard) {
          <p>{{ timeStarted | date: 'shortTime' }}</p>
        } @else {
          <p class="text-[16px] relative top-[-5px] right-[-2px] font-bold">
            .
          </p>
          <p>Average score {{ averageScore | number: '1.0-1' }}</p>
        }
        <p class="text-[16px] relative top-[-5px] right-[-2px] font-bold">.</p>
        @if (isRecruiterCard) {
          <p>{{ formatDuration(duration) }}</p>
        } @else {
          <p>{{ candidates }} candidates</p>
        }
      </div>
    </div>
  </div>
  <div
    class="right flex items-center text-[14px] font-normal text-[#404852] opacity-50"
  >
    @if (isRecruiterCard) {
      <button
        routerLink="/dashboard/test-management/assessments/preview/{{ id }}"
        class="flex gap-1 items-center"
        (click)="onAssessmentClick()"
      >
        <p class="underline text-blue-600">view assessment</p>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill=""
          class="mt-1"
        >
          <path
            d="M8.64586 12.854C8.59937 12.8076 8.56249 12.7524 8.53733 12.6917C8.51216 12.631 8.49921 12.566 8.49921 12.5003C8.49921 12.4346 8.51216 12.3695 8.53733 12.3088C8.56249 12.2481 8.59937 12.193 8.64586 12.1465L12.2927 8.50028H2.49961C2.367 8.50028 2.23982 8.4476 2.14605 8.35383C2.05229 8.26006 1.99961 8.13289 1.99961 8.00028C1.99961 7.86767 2.05229 7.74049 2.14605 7.64672C2.23982 7.55296 2.367 7.50028 2.49961 7.50028L12.2927 7.50028L8.64586 3.85403C8.55204 3.76021 8.49933 3.63296 8.49933 3.50028C8.49933 3.3676 8.55204 3.24035 8.64586 3.14653C8.73968 3.05271 8.86692 3 8.99961 3C9.13229 3 9.25954 3.05271 9.35336 3.14653L13.8534 7.64653C13.8998 7.69296 13.9367 7.74811 13.9619 7.80881C13.987 7.86951 14 7.93457 14 8.00028C14 8.06599 13.987 8.13105 13.9619 8.19175C13.9367 8.25245 13.8998 8.30759 13.8534 8.35403L9.35336 12.854C9.30692 12.9005 9.25178 12.9374 9.19108 12.9626C9.13038 12.9877 9.06531 13.0007 8.99961 13.0007C8.9339 13.0007 8.86884 12.9877 8.80814 12.9626C8.74744 12.9374 8.69229 12.9005 8.64586 12.854Z"
            fill="text-blue-600"
          />
        </svg>
      </button>
    } @else {
      <p>{{ timeAgoService.getTimeAgo(timeUpdated) }}</p>
    }
  </div>
</a>
