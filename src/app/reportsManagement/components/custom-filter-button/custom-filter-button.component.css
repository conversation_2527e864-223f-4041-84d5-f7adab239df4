.dropdown:hover {
  background-color: #f9f9f9;
  cursor: pointer;
}

.sort-dropdown {
  position: absolute;
  background-color: #ffffff;
  border-radius: 0.5rem;
  box-shadow: 7px 7px 5px 0px rgba(0, 0, 0, 0.07);
  padding: 0 0;
  z-index: 10;
  margin-top: 3.25rem;
  max-height: calc(480px - 310px);
  overflow-x: hidden;
  word-wrap: break-word;
  white-space: normal;
}

.sort-dropdown span input:hover {
  cursor: pointer;
}

.sort-dropdown div:last-child p:hover {
  border-radius: 0 0 8px 8px;
}

.sort-dropdown div:first-child p:hover {
  border-radius: 8px 8px 0 0;
}

.sort-dropdown button:first-child {
  border: none;
}

input[type='checkbox']:checked {
  appearance: none;
  border: 1px solid #e5edf2;
}

input[type='checkbox'] {
  accent-color: transparent !important;
  appearance: none;
  border: 1px solid #e5edf2;
}

.checkmark {
  position: absolute;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 50%;
  left: 0.125rem;
}

.checkmark:after {
  content: '';
  position: absolute;
  display: none;
}

.custom-checkbox input:checked ~ .checkmark:after {
  display: block;
}

.custom-checkbox .checkmark:after {
  left: 5px;
  width: 6px;
  height: 14px;
  border: solid #0c4767;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}
