import { CommonModule } from '@angular/common';
import {
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  Output,
  ViewChild,
  OnChanges,
  SimpleChanges,
} from '@angular/core';

@Component({
  selector: 'app-custom-filter-button',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './custom-filter-button.component.html',
  styleUrl: './custom-filter-button.component.css',
})
export class CustomFilterButtonComponent implements OnChanges {
  @Input() selectedFilters: string[] = [];
  @Input() filterArray: string[] = [];
  @Input() buttonUseType: string = 'Filter By';
  @Input() uniqueId?: string | number;
  @Output() selectFilterClick: EventEmitter<void> = new EventEmitter<void>();
  @Output() selectedFilterItem: EventEmitter<string> =
    new EventEmitter<string>();
  @Output() closeFilter: EventEmitter<void> = new EventEmitter();
  @Output() selectedColumnItems: EventEmitter<string[]> = new EventEmitter<
    string[]
  >();
  @Input() dropDownStatus: boolean = false;
  @Input() disabledFilter: boolean = false;
  @Input() isEnableCheckBox: boolean = true;
  constructor(private readonly elementRef: ElementRef) {}
  @ViewChild('dropdown') dropDown!: ElementRef;

  @HostListener('document:click', ['$event'])
  clickout(event: MouseEvent) {
    const targetElement = event.target as HTMLElement;

    if (
      this.dropDownStatus &&
      !this.elementRef.nativeElement.contains(targetElement) &&
      !this.dropDown?.nativeElement.contains(targetElement)
    ) {
      this.closeFilter.emit();
    }
  }
  columnSelectedList: string[] = [
    'Candidate email',
    'Taken On',
    'Candidate Score(%)',
    'Proctor Features',
    'Submitted by',
    'Status',
  ];
  filterItem: string[] = [];

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedFilters']) {
      // Sync filterItem with selectedFilters input
      this.filterItem = Array.isArray(this.selectedFilters)
        ? [...this.selectedFilters]
        : [];
    }
  }

  selectFilterOnClick(event: Event) {
    event.stopPropagation();
    if (!this.dropDownStatus) {
      this.dropDownStatus = true;
    } else {
      this.dropDownStatus = false;
    }

    this.selectFilterClick.emit();
  }

  selectedItem(item: string) {
    if (item === 'All') {
      this.selectAllItems();
    } else {
      this.buttonUseType === 'Filter By'
        ? this.filterSelectedItem(item)
        : this.columnSelectedItems(item);
    }
  }

  selectAllItems() {
    if (this.buttonUseType === 'Filter By') {
      if (this.filterItem.length === this.filterArray.length - 1) {
        this.filterItem = [];
      } else {
        this.filterItem = this.filterArray.filter((item) => item !== 'All');
      }
      this.selectedFilterItem.emit('All');
    } else {
      if (this.columnSelectedList.length === this.filterArray.length - 1) {
        this.columnSelectedList = [];
      } else {
        this.columnSelectedList = this.filterArray.filter(
          (item) => item !== 'All'
        );
      }
      this.selectedColumnItems.emit(this.columnSelectedList);
    }
  }

  filterSelectedItem(item: string) {
    const index = this.filterItem.indexOf(item);
    if (index !== -1) {
      this.filterItem.splice(index, 1);
    } else {
      this.filterItem.push(item);
    }
    this.selectedFilterItem.emit(item);
  }
  removeFilter(item: string) {
    this.filterItem = this.filterItem.filter((t) => t !== item);
  }

  columnSelectedItems(item: string) {
    const index = this.columnSelectedList.findIndex((t) => t === item);
    if (index === -1) {
      this.columnSelectedList.push(item);
    } else {
      this.columnSelectedList.splice(index, 1);
    }
    this.selectedColumnItems.emit(this.columnSelectedList);
  }

  itemChecked(item: string) {
    if (item === 'All') {
      return this.buttonUseType === 'Filter By'
        ? this.filterItem.length === this.filterArray.length - 1
        : this.columnSelectedList.length === this.filterArray.length - 1;
    }
    return this.buttonUseType === 'Filter By'
      ? this.filterItem.includes(item)
      : this.columnSelectedList.includes(item);
  }
}
