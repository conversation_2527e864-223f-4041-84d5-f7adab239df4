<div class="flex flex-col relative gap-2" #dropdown>
  <button
    class="dropdown flex items-center pl-5 justify-left bg-[#ffffff] w-fit h-11 rounded-lg gap-5"
    [style]="disabledFilter ? 'cursor: not-allowed' : 'cursor:pointer'"
    (click)="selectFilterOnClick($event)"
    (keydown.enter)="selectFilterOnClick($event)"
    aria-haspopup="listbox"
    [attr.aria-expanded]="dropDownStatus"
    [disabled]="disabledFilter"
    aria-controls="dropdown-filter-list"
  >
    @if (buttonUseType === 'Filter by') {
      <img
        src="../../../../assets/reportsManagement/Filters lines.svg"
        alt="filter icon"
        aria-hidden="true"
      />
    }
    <p>{{ buttonUseType }}</p>
    <div [class]="!dropDownStatus ? 'ml-6 pr-3' : 'rotate-180 mr-3 ml-6'">
      <img
        src="../../../../assets/reportsManagement/arrow-down.svg"
        alt=""
        aria-hidden="true"
      />
    </div>
  </button>
  <div
    class="sort-dropdown {{
      dropDownStatus ? 'block' : 'hidden'
    }} min-w-[12rem]"
    id="dropdown-filter-list"
    [attr.aria-hidden]="!dropDownStatus"
  >
    @for (list of filterArray; track $index) {
      <button
        (click)="selectedItem(list)"
        (keydown.enter)="selectedItem(list)"
        class="pl-3 border-t-[1px] border-[#CCD1D2] hover:cursor-pointer hover:bg-[#E5EDF2] w-full"
        [attr.aria-selected]="itemChecked(list)"
      >
        <span
          style="margin: 0"
          class="flex items-center justify-left h-10 gap-2 w-full"
        >
          @if (isEnableCheckBox) {
            <div class="custom-checkbox relative flex flex-row">
              <input
                type="checkbox"
                name="{{ list }}"
                value="{{ list }}"
                class="h-5 w-5"
                [checked]="itemChecked(list)"
              />
              <span class="checkmark"></span>
            </div>
          }

          {{ list }}
        </span>
      </button>
    }
  </div>
</div>
