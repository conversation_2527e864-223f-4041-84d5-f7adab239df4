<div class="outer-wrapper">
  <div class="table-container">
    <table
      #tableRef
      aria-describedby="report-custom-table"
      class="custom-table"
    >
      <thead class="bg-[#F9FAFB] h-8 w-full">
        <tr class="h-full w-full">
          <th
            *ngFor="let header of tableHeaders"
            class="text-[#474D66] text-left text-base font-bold pl-6"
          >
            {{ header.state ? header.header : '' }}
          </th>
        </tr>
      </thead>
      <tbody>
        <ng-container
          *ngFor="
            let item of tableData
              | paginate
                : {
                    itemsPerPage: size,
                    currentPage: page,
                    totalItems: tableData?.length,
                  };
            let index = index
          "
        >
          <tr
            class="{{
              tableType === 'candidates'
                ? 'h-[4.5rem] w-full hover:cursor-pointer hover:bg-[#DFE8F4]'
                : 'h-[4.5rem] w-full'
            }}"
            (click)="onRowClick(index, item)"
            (keydown.enter)="onRowClick(index, item)"
          >
            <td
              class="text-[#101828] text-left text-sm font-medium pl-6"
              *ngFor="let key of tableDataKeys"
            >
              <span class="inline-flex items-center">
                <span
                  *ngIf="item[key] === item.title"
                  class="py-[9px] px-[9px] rounded-full bg-[#DDE8EF] mr-3"
                >
                  <img
                    src="../../../../assets/reportsManagement/check-list 1.svg"
                    alt="list icon"
                  />
                </span>
                <span
                  class="{{
                    key === 'numberOfCandidates' ||
                    key === 'numberOfQuestions' ||
                    key === 'numberOfFeedbacks'
                      ? 'flex bg-[#DDE8EF] w-[7.375rem] items-center justify-center h-10 rounded-md text-base candidate-number hover:cursor-pointer'
                      : ''
                  }}"
                >
                  <span
                    [ngStyle]="
                      item[key] === item.status
                        ? {
                            'background-color': getColorCodes(
                              item.status,
                              statusColorMappings
                            ).backgroundColor,
                            color: getColorCodes(
                              item.status,
                              statusColorMappings
                            ).color,
                            padding: ' 3.5px 10px',
                            'border-radius': '8px',
                          }
                        : {}
                    "
                    [appSafeHtml]="getDisplayValue(item, key)"
                  >
                  </span>
                  <a
                    *ngIf="
                      key === 'numberOfCandidates' ||
                      key === 'numberOfQuestions' ||
                      key === 'numberOfFeedbacks'
                    "
                    class="underline"
                    (click)="
                      $event.stopPropagation();
                      onRowItemClick(item.assessmentId, index, key)
                    "
                    tabindex="0"
                    (keydown)="$event.stopPropagation()"
                    >View more</a
                  >
                </span>
              </span>
            </td>
          </tr>
        </ng-container>
      </tbody>
    </table>
  </div>
</div>
