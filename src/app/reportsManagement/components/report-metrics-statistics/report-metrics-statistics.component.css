.card-width {
  width: 11rem;
}

@media (min-width: 1540px) {
  .card-width {
    width: 13.3125rem;
  }
}

.candidate-number a {
  display: none;
}

.candidate-number:hover a {
  display: inline-block;
  text-align: center;
  font-size: inherit;
}

.candidate-number:hover span {
  display: none;
}

.card-group {
  display: flex;
  min-height: 101px;
}

.card {
  background: white;
  padding: 1rem;
  border: 1px solid black;
  border-radius: 0.25em;
  width: 100%;
}
.cut-off {
  display: -webkit-box;
  overflow: hidden;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
}

.modalView {
  position: fixed;
  z-index: 2000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

/* Close Button */
.close {
  color: #000000;
  float: right;
}

.close:hover,
.close:focus {
  color: #0c4767;
  text-decoration: none;
  cursor: pointer;
}

.answer-list {
  list-style-type: circle;
}
input[type='checkbox']:checked {
  appearance: none;
  border: 1px solid #e5edf2;
}

input[type='checkbox'] {
  accent-color: transparent !important;
  appearance: none;
  border: 1px solid #e5edf2;
}

.checkmark {
  position: absolute;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 50%;
  left: 0.125rem;
}

.checkmark:after {
  content: '';
  position: absolute;
  display: none;
}
.custom-checkbox input:checked ~ .checkmark:after {
  display: block;
}

.custom-checkbox .checkmark:after {
  left: 5px;
  width: 6px;
  height: 14px;
  border: solid #0c4767;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}
