import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReportMetricsStatisticsComponent } from './report-metrics-statistics.component';
import { ActivatedRoute } from '@angular/router';
import { ReportManagementService } from '../../../services/report-management-service/report-management.service';
import { QuestionsService } from '../../../services/test-management-service/questions.service';
import { ToastService } from '../../../services/toast-service/toast.service';
import { of, throwError, Subscription } from 'rxjs';
import {
  data,
  date,
  proctorData,
} from '../report-metrics-proctoring/report-metrics.mock';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import {
  CandidateSectionData,
  CandidateTestResult,
} from '../../../Interfaces/Types/reportServiceInterface';

jest.mock('jspdf', () => {
  return jest.fn().mockImplementation(() => {
    return {
      addImage: jest.fn(),
      save: jest.fn(),
      autoTable: jest.fn(),
      setFontSize: jest.fn(),
      text: jest.fn(),
      addPage: jest.fn(),
    };
  });
});

describe('ReportMetricsStatisticsComponent', () => {
  let component: ReportMetricsStatisticsComponent;
  let fixture: ComponentFixture<ReportMetricsStatisticsComponent>;
  let reportManagementServiceSpy: {
    getCandidateResults: jest.Mock;
    updateOrganizationId: jest.Mock;
    getProctoringProtocol: jest.Mock;
    exportCandidateMetrics: jest.Mock;
    getIndividualCandidateAssessmentResult: jest.Mock;
    getAiReportAnalysis: jest.Mock;
  };
  let questionServiceSpy: { getQuestionTypeString: jest.Mock };
  let toastServiceSpy: { onShow: jest.Mock };
  let analyticsServiceSpy: { track: jest.Mock };
  let ActivatedRouteSpy: Partial<ActivatedRoute>;

  beforeEach(async () => {
    reportManagementServiceSpy = {
      getCandidateResults: jest.fn(),
      updateOrganizationId: jest.fn(),
      getProctoringProtocol: jest.fn(),
      exportCandidateMetrics: jest.fn(),
      getIndividualCandidateAssessmentResult: jest.fn().mockReturnValue(of({})),
      getAiReportAnalysis: jest.fn().mockReturnValue(of(new Blob())),
    };
    questionServiceSpy = {
      getQuestionTypeString: jest.fn(),
    };
    toastServiceSpy = {
      onShow: jest.fn(),
    };
    analyticsServiceSpy = {
      track: jest.fn().mockResolvedValue(undefined),
    };
    ActivatedRouteSpy = {
      params: of({ id: 1 }),
    };

    reportManagementServiceSpy.getCandidateResults.mockReturnValue(of(data));
    await TestBed.configureTestingModule({
      imports: [ReportMetricsStatisticsComponent, NoopAnimationsModule],
      providers: [
        {
          provide: ActivatedRoute,
          useValue: ActivatedRouteSpy,
        },
        {
          provide: ReportManagementService,
          useValue: reportManagementServiceSpy,
        },
        {
          provide: QuestionsService,
          useValue: questionServiceSpy,
        },
        {
          provide: ToastService,
          useValue: toastServiceSpy,
        },
        {
          provide: 'AnalyticsService',
          useValue: analyticsServiceSpy,
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ReportMetricsStatisticsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize the component', () => {
    expect(component.searchTerm).toBe('');
    expect(component.candidateMetrics).toEqual([]);
    expect(component.data).toEqual([]);
    expect(component.showSmallModal).toBeFalsy();
    expect(component.size).toBe(6);
    expect(component.total).toBe(0);
    expect(component.rowid).toBe('');
    expect(component.riskLevel).toBe('');
    expect(component.isExportLaoding).toBeFalsy();
    expect(component.isLoading).toBeTruthy();
    expect(component.isExportOpen).toBeFalsy();
    expect(component.page).toBe(1);
    expect(component.totalScore).toEqual({
      min: 0,
      max: 100,
    });
  });

  it('should call #getCandidateAssessmentInfo and handle error', () => {
    const error = new ErrorEvent('error');
    reportManagementServiceSpy.getIndividualCandidateAssessmentResult.mockReturnValue(
      throwError(() => error)
    );
    component.getCandidateAssessmentInfo();
    expect(component.isLoading).toBeFalsy();
    expect(toastServiceSpy.onShow).toHaveBeenCalledWith(
      'error',
      'error accessing results',
      true,
      'error'
    );
  });

  it('should call #convertTime', () => {
    expect(component.convertTime(0)).toBe('0 secs');
    expect(component.convertTime(61)).toBe('1 min 1 sec');
    expect(component.convertTime(3660)).toBe('1hr 1 min');
  });

  it('should call #getImageDate', () => {
    expect(component.getImageDate(date)).toBe('Oct 7, 2024');
  });

  it('should call #getImageTime', () => {
    expect(component.getImageTime(date)).toMatch(/\d{1,2}:\d{2}:\d{2}/);
  });

  it('should #getProctoringProtocols', () => {
    reportManagementServiceSpy.getProctoringProtocol.mockReturnValue(
      of(proctorData)
    );
    component.getProctoringProtocols('candidateId');
    expect(reportManagementServiceSpy.getProctoringProtocol).toHaveBeenCalled();
    expect(component.proctoringProtocols).toEqual(['test']);
    expect(component.isLoading).toBeFalsy();
  });

  it('should #getQuestionsInfo', () => {
    component.candidateAssessmentSectionData = data.data.testResults;
    component.getQuestionsInfo({ assessmentId: '1', index: 0 });
    expect(component.isViewQuestions).toBeTruthy();
    expect(component.questionViewArray).toEqual(
      data.data.testResults[0].questionResults
    );
    expect(component.comprehensionPassage).toBeNull();
    expect(component.questionModalTitle).toBe('Test');
  });

  it('should #closeModal', () => {
    component.isViewQuestions = true;
    component.closeModal();
    expect(component.isViewQuestions).toBeFalsy();
  });

  it('should #openExport', () => {
    component.isExportOpen = false;
    component.openExport();
    expect(component.isExportOpen).toBeTruthy();
    component.openExport();
    expect(component.isExportOpen).toBeFalsy();
  });

  it('should handle #exportCsv with missing assessmentId', async () => {
    localStorage.removeItem('assessmentId');
    reportManagementServiceSpy.exportCandidateMetrics.mockReturnValue(of({}));
    component.exportCsv('summary');
    expect(
      reportManagementServiceSpy.exportCandidateMetrics
    ).toHaveBeenCalledWith(
      null,
      component.candidateId,
      component.candidateEmail,
      component.candidateAssessmentData,
      'summary',
      expect.any(Subscription)
    );
  });

  it('should #getQuestionType', () => {
    questionServiceSpy.getQuestionTypeString.mockReturnValue('Essay');
    expect(component.getQuestionType('Essay')).toBe('Essay');
  });

  it('should handle #getQuestionType with unknown type', () => {
    questionServiceSpy.getQuestionTypeString.mockReturnValue(undefined);
    expect(component.getQuestionType('UnknownType')).toBeUndefined();
  });

  it('should #ngOnDestroy', () => {
    component.subscription = new Subscription();
    component.ngOnDestroy();
    expect(component.subscription.closed).toBeTruthy();
  });

  describe('getFillInOptions', () => {
    it('should extract value property from valid JSON strings', () => {
      const jsonStrings = [
        '{"id":"1","fillInAnswerId":"a1","blank":"___","value":"answer1"}',
        '{"id":"2","fillInAnswerId":"a2","blank":"___","value":"answer2"}',
      ];

      const result = component.getFillInOptions(jsonStrings);

      expect(result).toEqual(['answer1', 'answer2']);
    });

    it('should return undefined for JSON strings with missing value property', () => {
      const jsonStrings = [
        '{"id":"1","fillInAnswerId":"a1","blank":"___"}',
        '{"id":"2","fillInAnswerId":"a2","blank":"___","value":"answer2"}',
      ];

      const result = component.getFillInOptions(jsonStrings);

      expect(result).toEqual([undefined, 'answer2']);
    });

    it('should handle invalid JSON gracefully', () => {
      const jsonStrings = [
        '{"id":"1","fillInAnswerId":"a1","blank":"___","value":"answer1"}',
        'invalid json',
      ];
      expect(() => component.getFillInOptions(jsonStrings)).toThrow();
    });

    it('should handle empty array', () => {
      expect(component.getFillInOptions([])).toEqual([]);
    });
  });

  describe('moveSpecificLabelToLastPosition', () => {
    it('should move the specific label to the last position', () => {
      const arr = ['a', 'b', 'c', 'd'];
      const result = component.moveSpecificLabelToLastPosition(arr, 'b');
      expect(result).toEqual(['a', 'c', 'd', 'b']);
    });

    it('should return the same array if label not found', () => {
      const arr = ['a', 'b', 'c'];
      const result = component.moveSpecificLabelToLastPosition(arr, 'z');
      expect(result).toEqual(['a', 'b', 'c']);
    });
  });

  describe('addToHeaderlist', () => {
    it('should add correct header if headerState is true', () => {
      component.tableHeaders = [];
      component.addToHeaderlist('name');
      expect(
        component.tableHeaders.some((h) => h.header === 'Section Name')
      ).toBeTruthy();
    });

    it('should not add header if headerState is false', () => {
      component.tableHeaders = [];
      component.tableHeaderCondition.push({
        condition: 'test',
        headerString: 'Test',
        headerState: false,
      });
      component.addToHeaderlist('test');
      expect(
        component.tableHeaders.some((h) => h.header === 'Test')
      ).toBeFalsy();
    });
  });

  describe('exportDetailedCsv', () => {
    beforeEach(() => {
      // Mock DOM for download
      document.body.innerHTML = '';
      component.candidateAssessmentSectionData = [
        {
          name: 'Section',
          questionsAnswered: 3,
          totalNumQuestions: 3,
          testTime: 60,
          percentage: 100,
          candidateScore: 15,
          numberOfQuestionsPassed: 2,
          numberOfQuestionsFailed: 1,
          numberOfQuestionsUnanswered: 0,
          numberOfQuestions: 3,
          questionResults: [
            {
              candidateEmail: '<EMAIL>',
              questionId: 'q1',
              questionText: '<b>What?</b>',
              multipleChoiceAnswer: {
                answer: ['A'],
                options: ['A', 'B'],
              },
              totalScore: 5,
              testTakerAnswers: ['A'],
              candidateMarks: 5,
              isAnswerCorrect: true,
            },
            {
              candidateEmail: '<EMAIL>',
              questionId: 'q2',
              questionText: 'Matrix?',
              matchMatrixAnswer: {},
              totalScore: 5,
              testTakerAnswers: [],
              candidateMarks: 0,
              isAnswerCorrect: false,
            },
            {
              candidateEmail: '<EMAIL>',
              questionId: 'q3',
              questionText: 'Essay?',
              essayAnswer: {},
              totalScore: 5,
              testTakerAnswers: [],
              candidateMarks: 0,
              isAnswerCorrect: false,
            },
          ],
        },
      ] as unknown as CandidateTestResult[]; // Cast as any if you do not have the full CandidateTestResult type, or use the correct type if available
      component.candidateEmail = '<EMAIL>';
      component.candidateAssessmentData = {
        assessmentTitle: 'Assessment',
      } as unknown as CandidateSectionData;
    });

    it('should generate and trigger download of CSV with correct content', () => {
      const createElementSpy = jest.spyOn(document, 'createElement');
      const appendChildSpy = jest.spyOn(document.body, 'appendChild');
      const removeChildSpy = jest.spyOn(document.body, 'removeChild');
      const revokeObjectURLSpy = jest.spyOn(URL, 'revokeObjectURL');
      component.exportDetailedCsv();
      expect(createElementSpy).toHaveBeenCalledWith('a');
      expect(appendChildSpy).toHaveBeenCalled();
      expect(removeChildSpy).toHaveBeenCalled();
      expect(revokeObjectURLSpy).toHaveBeenCalled();
    });

    it('should skip matrix and essay questions', () => {
      const spy = jest.spyOn(document, 'createElement');
      component.exportDetailedCsv();
      // Only one row for the non-matrix, non-essay question
      expect(spy).toHaveBeenCalledWith('a');
    });

    it('should handle empty candidateAssessmentSectionData', () => {
      component.candidateAssessmentSectionData = [];
      expect(() => component.exportDetailedCsv()).not.toThrow();
    });
  });
});
