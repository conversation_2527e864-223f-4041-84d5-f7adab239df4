import { CommonModule } from '@angular/common';
import {
  Component,
  ElementRef,
  HostListener,
  Input,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ReportManagementService } from '../../../services/report-management-service/report-management.service';
import { ActivatedRoute } from '@angular/router';
import { ToastService } from '../../../services/toast-service/toast.service';
import { convertTime } from '../../../utils/constants';
import moment from 'moment';
import { DialogModule } from 'primeng/dialog';
import {
  CandidateMetrics,
  CandidateSectionData,
  CandidateSectionDataApi,
  CandidateTestResult,
  QuestionResult,
} from '../../../Interfaces/Types/reportServiceInterface';
import { ReportCustomTableComponent } from '../report-custom-table/report-custom-table.component';
import { CustomButtonComponent } from '../../../components/custombutton/custombutton.component';
import { catchError, filter, Subscription } from 'rxjs';
import { QuestionsService } from '../../../services/test-management-service/questions.service';
import { InfoBoxComponent } from '../info-box/info-box.component';
import { RefreshLoaderComponent } from '../../../components/refresh-loader/refresh-loader.component';
import { AnalyticsService } from '../../../services/analytics.service';
import { ComprehensionPassageComponent } from './components/comprehension-passage/comprehension-passage.component';
import { QuestionViewComponent } from './components/question-view/question-view.component';
import { BasicTypeAnswerViewComponent } from './components/basic-type-answer-view/basic-type-answer-view.component';
import { EssayTypeAnswerViewComponent } from './components/essay-type-answer-view/essay-type-answer-view.component';
import { MatrixTypeAnswerViewComponent } from './components/matrix-type-answer-view/matrix-type-answer-view.component';
import { FillInTypeViewComponent } from './components/fill-in-type-view/fill-in-type-view.component';
import { StatusBadgeComponent } from './components/status-badge/status-badge.component';
import { CodingTypeAnswerViewComponent } from './components/coding-type-answer-view/coding-type-answer-view.component';

@Component({
  selector: 'app-report-metrics-statistics',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    DialogModule,
    ReportCustomTableComponent,
    CustomButtonComponent,
    InfoBoxComponent,
    RefreshLoaderComponent,
    ComprehensionPassageComponent,
    QuestionViewComponent,
    BasicTypeAnswerViewComponent,
    EssayTypeAnswerViewComponent,
    MatrixTypeAnswerViewComponent,
    FillInTypeViewComponent,
    StatusBadgeComponent,
    CodingTypeAnswerViewComponent,
  ],
  templateUrl: './report-metrics-statistics.component.html',
  styleUrl: './report-metrics-statistics.component.css',
})
export class ReportMetricsStatisticsComponent implements OnInit, OnDestroy {
  @ViewChild('exportButton') exportButton!: ElementRef;

  @HostListener('document:click', ['$event'])
  onClick(event: Event) {
    const targetElement = event.target as HTMLElement;
    if (
      this.exportButton &&
      !this.exportButton.nativeElement.contains(targetElement)
    ) {
      this.isExportOpen = false;
    }
  }

  constructor(
    private readonly reportService: ReportManagementService,
    private readonly toast: ToastService,
    private readonly route: ActivatedRoute,
    private readonly questionService: QuestionsService,
    private readonly analyticsService: AnalyticsService
  ) {}
  searchTerm: string = '';
  candidateMetrics: CandidateMetrics[] = [];
  data: CandidateMetrics[] = [];
  showSmallModal: boolean = false;
  size: number = 6;
  total: number = 0;
  rowid: string = '';
  riskLevel: string = '';
  totalScore: { min: number; max: number } = { min: 0, max: 100 };
  page: number = 1;

  //added by Me
  @Input() candidateId: string = '';
  @Input() candidateEmail: string = '';
  isLoading: boolean = true;
  isViewQuestions: boolean = false;
  isViewMore: boolean = false;
  questionModalTitle: string = '';
  isExportLaoding = false;
  isExportOpen: boolean = false;
  AIReportAnalysis: Blob | null = null;
  isErrorWithAiReview = false;
  tableHeaderCondition = [
    { condition: 'name', headerString: 'Section Name', headerState: true },
    {
      condition: 'candidateScore',
      headerString: 'Section Score',
      headerState: true,
    },
    {
      condition: 'percentage',
      headerString: 'Section Percentage',
      headerState: true,
    },
    {
      condition: 'numberOfQuestionsPassed',
      headerString: 'Questions Passed',
      headerState: true,
    },
    {
      condition: 'numberOfQuestionsFailed',
      headerString: 'Questions Failed',
      headerState: true,
    },
    {
      condition: 'numberOfQuestionsUnanswered',
      headerString: 'Questions Skipped',
      headerState: true,
    },
    {
      condition: 'numberOfQuestions',
      headerString: 'No of Questions',
      headerState: true,
    },
  ];
  tableHeaders: { header: string; state: boolean }[] = [];

  candidateAssessmentData: CandidateSectionData = {
    assessmentTitle: '',
    assessmentTime: 0,
    assessmentTimeTaken: 0,
    assessmentCandidateScore: 0,
    assessmentOverallScore: 0,
    assessmentCandidatePercentage: 0,
    assessmentWindowViolationCount: 0,
    assessmentWindowViolationDuration: 0,
    assessmentTakerShotCount: 0,
    assessmentTakerViolationShotCount: 0,
    windowShotCount: 0,
    windowViolationShotCount: 0,
    screenshotsInterval: 0,
    camerashotsInterval: 0,
    integrityScore: 0,
    status: '',
    proctor: '',
    proctorLevel: '',
    commenceDate: '',
    expireDate: '',
    assessmentEndTime: '',
    assessmentStartTime: '',
    testResults: [],
  };

  candidateAssessmentSectionData: CandidateTestResult[] = [];
  questionViewArray: QuestionResult[] = [];
  proctoringProtocols: string[] | undefined;
  dataObjectKeys: string[] = [];
  subscription: Subscription = new Subscription();
  comprehensionPassage: string | null = null;
  matrixQuestionWrongIndex: (number | null)[] = [];

  ngOnInit() {
    const paramsSub = this.route.params.subscribe((params) => {
      this.reportService.updateOrganizationId(params['id']);
    });
    this.subscription.add(paramsSub);
    localStorage.setItem('candidateEmail', this.candidateEmail);
    this.getCandidateAssessmentInfo();
    this.getAiSummaryPdf();
  }

  getCandidateAssessmentInfo() {
    this.isLoading = true;

    const candidateSub = this.reportService
      .getIndividualCandidateAssessmentResult(this.candidateId)
      .subscribe({
        next: (results) => {
          this.handleSuccessfulRespond(results);
          this.getProctoringProtocols(this.candidateId);
        },
        error: () => {
          this.isLoading = false;
          this.toast.onShow('error', 'error accessing results', true, 'error');
        },
      });
    this.subscription.add(candidateSub);
  }

  handleSuccessfulRespond(result: CandidateSectionDataApi) {
    this.candidateAssessmentData = result.data;
    this.candidateAssessmentSectionData = result.data.testResults || [];
    this.candidateAssessmentSectionData =
      this.candidateAssessmentSectionData.map((item) => ({
        ...item,
        numberOfQuestions: item.questionResults.length,
        numberOfQuestionsUnanswered:
          item.questionResults.length - item.numberOfQuestionsAnswered,
      }));
    this.reportService.cameraShotIntervalForAssessment = convertTime(
      this.candidateAssessmentData.camerashotsInterval
    );
    this.reportService.screenShotIntervalForAssessment = convertTime(
      this.candidateAssessmentData.screenshotsInterval
    );
    this.dataObjectKeys = this.getDataObjectKeys(
      this.candidateAssessmentSectionData[0],
      'numberOfQuestions'
    );
    this.processTheTableHeaderData();
  }

  processTheTableHeaderData() {
    if (
      Array.isArray(this.candidateAssessmentSectionData) &&
      this.candidateAssessmentSectionData.length > 0
    ) {
      this.moveSpecificLabelToLastPosition(
        Object.keys(this.candidateAssessmentSectionData[0]),
        'numberOfQuestions'
      ).forEach((condition) => {
        this.addToHeaderlist(condition);
      });
    }
  }

  addToHeaderlist(condition: string) {
    const matchHeader = this.tableHeaderCondition.find((header) => {
      return header.condition === condition;
    });
    if (matchHeader?.headerState) {
      this.tableHeaders.push({
        header: matchHeader.headerString,
        state: matchHeader.headerState,
      });
    }
  }

  moveSpecificLabelToLastPosition(
    arrayToModify: string[],
    specificLabel: string
  ): string[] {
    const modifiedArray = [...arrayToModify];
    const specificLabelIndex = modifiedArray.findIndex(
      (item) => item === specificLabel
    );
    if (specificLabelIndex !== -1) {
      const specificLabelValue = modifiedArray.splice(specificLabelIndex, 1)[0];
      modifiedArray.push(specificLabelValue);
    }
    return modifiedArray;
  }

  getDataObjectKeys(obj: CandidateTestResult, itemToBeLastIndex: string) {
    const allFields = Object.keys(obj);

    const filteredData = allFields.filter((field) =>
      this.tableHeaderCondition.find(
        (head) => head.condition === field && head.headerState
      )
    );
    return this.moveSpecificLabelToLastPosition(
      filteredData,
      itemToBeLastIndex
    );
  }

  convertTime(seconds: number) {
    return convertTime(seconds);
  }
  getImageDate(value: string) {
    const dateString = moment(value).format('ll');
    return dateString;
  }

  getImageTime(value: string) {
    const timeString = moment(value).format('LTS');

    return timeString;
  }

  getProctoringProtocols(candidateId: string) {
    const proctorSub = this.reportService
      .getProctoringProtocol(candidateId)
      .subscribe((results) => {
        this.proctoringProtocols =
          results.data.configuration.proctorFeatures?.map(
            (item: { name: string }) => {
              return item.name;
            }
          );

        this.isLoading = false;
      });
    this.subscription.add(proctorSub);
  }

  getQuestionsInfo(value: { assessmentId: string; index: number }) {
    this.isViewQuestions = !this.isViewQuestions;
    this.questionViewArray =
      this.candidateAssessmentSectionData[value.index].questionResults;
    this.comprehensionPassage =
      this.candidateAssessmentSectionData[value.index].passage;
    this.questionModalTitle =
      this.candidateAssessmentSectionData[value.index].name;
  }
  closeModal() {
    this.isViewQuestions = !this.isViewQuestions;
  }

  openExport() {
    this.isExportOpen = !this.isExportOpen;
  }

  exportCsv(reportType: 'summary' | 'pdf') {
    this.isExportLaoding = true;
    const assessmentId = localStorage.getItem('assessmentId') as string;
    this.subscription.add(
      this.reportService
        .exportCandidateMetrics(
          assessmentId,
          this.candidateId,
          this.candidateEmail,
          this.candidateAssessmentData,
          reportType,
          this.subscription
        )
        .pipe(
          catchError((error) => {
            this.isExportLaoding = false;
            this.isExportOpen = false;
            this.analyticsService
              .track('Report Export Failed', {
                reason: 'API error',
              })
              .then();
            this.toast.onShow('error', error, true, 'error');
            return error;
          }),
          filter((data) => !!data)
        )
        .subscribe(() => {
          this.isExportLaoding = false;
          this.isExportOpen = false;
          this.analyticsService
            .track('Report Exported', {
              reportType,
              assessmentId,
              candidateEmail: this.candidateEmail,
            })
            .then();
        })
    );
  }

  getFillInOptions(jsonStrings: string[]) {
    return jsonStrings.map((jsonString) => {
      const parsedObject: {
        id: string;
        fillInAnswerId: string;
        blank: string;
        value: string;
      } = JSON.parse(jsonString);

      return parsedObject['value'];
    });
  }

  getQuestionType(value: string) {
    return this.questionService.getQuestionTypeString(value);
  }

  private cleanText(text: string | undefined): string {
    return text
      ? text
          .replace(/<[^>]+>/g, '')
          .replace(/&nbsp;/g, ' ')
          .trim()
      : '';
  }

  private getCorrectAnswer(question: QuestionResult): string {
    if (question.multipleChoiceAnswer?.answer) {
      return this.cleanText(question.multipleChoiceAnswer.answer.join('; '));
    }
    if (question.multipleSelectAnswer?.answer) {
      return this.cleanText(question.multipleSelectAnswer.answer.join(', '));
    }
    if (question.trueOrFalseAnswer?.answer !== undefined) {
      return question.trueOrFalseAnswer.answer ? 'True' : 'False';
    }
    if (question.fillInAnswer?.answer) {
      return this.cleanText(question.fillInAnswer.answer.join(', '));
    }
    return '';
  }

  private getCandidateAnswer(question: QuestionResult): string {
    if (question.matchMatrixAnswer) {
      return 'Cannot export matrix answers';
    }
    if (question.testTakerAnswers && question.testTakerAnswers.length > 0) {
      return this.cleanText(question.testTakerAnswers.join('| '));
    }
    return '';
  }

  private getAnswerOptions(question: QuestionResult): string {
    if (question.multipleChoiceAnswer?.options) {
      return this.cleanText(question.multipleChoiceAnswer.options.join(', '));
    }
    if (question.multipleSelectAnswer?.options) {
      return this.cleanText(question.multipleSelectAnswer.options.join(', '));
    }
    if (question.trueOrFalseAnswer?.options) {
      return this.cleanText(question.trueOrFalseAnswer.options.join(', '));
    }
    if (question.fillInAnswer?.options) {
      return this.cleanText(
        question.fillInAnswer.options
          .map((option) => option['value'])
          .join(', ')
      );
    }
    return '';
  }

  exportDetailedCsv() {
    const headers = [
      'Candidate Email',
      'Assessment Name',
      'Section Name',
      'Question ID',
      'Question Detail',
      'Answer Options',
      'Correct Answer',
      'Candidate Answer',
      'Question Score',
      'Candidate Score',
      'Answer Status',
    ];

    const rows: string[] = [headers.join(',')];

    this.candidateAssessmentSectionData.forEach((section) => {
      section.questionResults.forEach((question) => {
        if (question.matchMatrixAnswer || question.essayAnswer) return;

        const correctAnswer = this.getCorrectAnswer(question);
        const candidateAnswer = this.getCandidateAnswer(question);
        const questionDetail = this.cleanText(question.questionText);
        const answerOptions = this.getAnswerOptions(question);

        const row = [
          `"${question.candidateEmail || this.candidateEmail}"`,
          `"${this.candidateAssessmentData.assessmentTitle}"`,
          `"${section.name}"`,
          `"${question.questionId}"`,
          `"${questionDetail}"`,
          `"${answerOptions}"`,
          `"${correctAnswer}"`,
          `"${candidateAnswer}"`,
          `"${question.totalScore}"`,
          `"${question.candidateMarks}"`,
          `"${question.isAnswerCorrect}"`,
        ].join(',');

        rows.push(row);
      });
    });

    const csvContent = rows.join('\r\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `detailed_report_${this.candidateEmail}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  getAiSummaryPdf() {
    const aiSub = this.reportService
      .getAiReportAnalysis(this.candidateId)
      .subscribe({
        next: (data) => {
          this.AIReportAnalysis = data;
        },
        error: () => {
          this.isErrorWithAiReview = true;
          this.toast.onShow(
            'error',
            'Failed to fetch Analysis report. Please try again later.',
            true,
            'error'
          );
        },
      });
    this.subscription.add(aiSub);
  }

  async exportAiSummaryPdf() {
    if (this.AIReportAnalysis) {
      this.toast.onShow(
        '',
        'Preparing Analysis report summary PDF...',
        true,
        'information',
        'top-right'
      );

      await new Promise((resolve) => setTimeout(resolve, 6000));

      const aiSummaryPdf = this.AIReportAnalysis;
      const blob = new Blob([aiSummaryPdf], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute(
        'download',
        `ai_summary_report_${this.candidateEmail}.pdf`
      );
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      this.toast.onShow(
        'success',
        'Analysis report summary PDF has been downloaded successfully',
        true,
        'success'
      );
    } else if (this.isErrorWithAiReview) {
      this.toast.onShow(
        'error',
        'Report generator is currently not available, Please try again later',
        true,
        'error'
      );
    } else {
      this.isExportOpen = false;
      this.toast.onShow(
        '',
        'Your analysis is currently being processed. It should be ready shortly. Please check again soon!',
        true,
        'information',
        'top-right'
      );
    }
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
}
