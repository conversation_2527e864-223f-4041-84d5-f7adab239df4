<div *ngIf="!isLoading" class="mb-3">
  <div class="flex items-center justify-between">
    <div class="flex md:flex-row flex-col md:gap-5 xl:gap-[6.625rem]">
      <div class="flex flex-col">
        <p class="text-[#7F849A] text-xs font-medium">Candidate Email</p>
        <div
          class="h-10 flex items-center text-[#0C4767] font-medium text-base"
        >
          <p>{{ candidateEmail }}</p>
        </div>
      </div>
      <div class="flex flex-col">
        <p class="text-[#7F849A] text-xs font-medium">Candidate Score</p>
        <div
          class="h-10 flex items-center text-[#0C4767] font-medium text-base"
        >
          <p>
            {{
              (candidateAssessmentData.assessmentCandidateScore % 1 !== 0
                ? candidateAssessmentData.assessmentCandidateScore.toFixed(2)
                : candidateAssessmentData.assessmentCandidateScore) +
                '/' +
                candidateAssessmentData.assessmentOverallScore
            }}
          </p>
        </div>
      </div>
      <div class="flex flex-col">
        <p class="text-[#7F849A] text-xs font-medium">
          Candidate Percentage Score
        </p>
        <div
          class="h-10 flex items-center text-[#0C4767] font-medium text-base"
        >
          <p>{{ candidateAssessmentData.assessmentCandidatePercentage }}%</p>
        </div>
      </div>
    </div>
    <div class="relative w-48" #exportButton>
      @if (isExportLaoding) {
        <app-custombutton [spinner]="true"></app-custombutton>
      } @else {
        <app-custombutton type="button" (clicked)="openExport()" class="export">
          <img src="../../../../assets/icons/export.svg" class="mr-2" alt="" />
          Export Report
        </app-custombutton>
      }
      @if (isExportOpen) {
        <form
          class="grid absolute gap-1 top-11 z-20 bg-white w-48 h-28 pl-2 mt-1 rounded"
        >
          <div
            class="relative custom-checkbox flex items-center h-full w-full content-center"
          >
            <input
              type="radio"
              class="h-5 w-5 mr-2 cursor-pointer"
              (change)="exportCsv('summary')"
            />
            <label for="">Summary CSV</label>
          </div>
          <div
            class="relative custom-checkbox flex items-center h-full w-full content-center"
          >
            <input
              type="radio"
              class="h-5 w-5 mr-2 cursor-pointer"
              (change)="exportCsv('pdf')"
            />
            <label for="">Summary PDF</label>
          </div>

          <div
            class="relative custom-checkbox flex items-center h-full w-full content-center"
          >
            <input
              type="radio"
              class="h-5 w-5 mr-2 cursor-pointer"
              (change)="exportDetailedCsv()"
            />
            <label for="">Detailed CSV</label>
          </div>
          <div
            class="relative custom-checkbox flex items-center h-full w-full content-center"
          >
            <input
              type="radio"
              class="h-5 w-5 mr-2 cursor-pointer"
              (change)="exportAiSummaryPdf()"
            />
            <label
              [ngClass]="{
                'text-[#d4d7e2]': isErrorWithAiReview,
              }"
              for=""
              >Analysis Report</label
            >
          </div>
        </form>
      }
    </div>
  </div>
  <div class="flex flex-col mt-8 gap-4">
    <p class="text-[#0C4767] font-semibold text-xl">Sections</p>
    <app-report-custom-table
      [tableData]="candidateAssessmentSectionData"
      [tableHeaders]="tableHeaders"
      [tableDataKeys]="dataObjectKeys"
      (rowItemClick)="getQuestionsInfo($event)"
      [size]="10"
      [page]="1"
    >
    </app-report-custom-table>
  </div>
  <div class="flex flex-col mt-10 gap-4">
    <p class="text-[#0C4767] font-semibold text-xl">Assessment Information</p>
    <div class="h-14 flex flex-row gap-6">
      <app-info-box
        [title]="'Assessment Start Date'"
        [content]="getImageDate(candidateAssessmentData.assessmentStartTime)"
        [condition]="true"
      ></app-info-box>
      <app-info-box
        [title]="'Assessment Start Time'"
        [content]="getImageTime(candidateAssessmentData.assessmentStartTime)"
        [condition]="true"
      ></app-info-box>
      <app-info-box
        [title]="'End Date'"
        [content]="getImageDate(candidateAssessmentData.assessmentEndTime)"
        [condition]="true"
      ></app-info-box>
      <app-info-box
        [title]="'Assessment End Time'"
        [content]="getImageTime(candidateAssessmentData.assessmentEndTime)"
        [condition]="true"
      ></app-info-box>
      <app-info-box
        [title]="'Assessment Duration'"
        [content]="convertTime(candidateAssessmentData.assessmentTimeTaken)"
        [condition]="true"
      ></app-info-box>
    </div>
  </div>

  <div class="flex flex-col mt-10 gap-4 w-full">
    <p class="text-[#0C4767] font-semibold text-xl">Proctoring Information</p>

    <div class="min-h-14 flex flex-row gap-6">
      <div class="flex flex-col">
        <p class="text-[#7F849A] text-xs font-medium">Proctoring</p>
        <div class="flex flex-col" *ngFor="let protocol of proctoringProtocols">
          <div
            class="flex h-10 bg-white card-width pl-4 items-center text-[#474D66] text-base font-normal rounded-lg mb-2"
          >
            {{ protocol }}
          </div>
        </div>
      </div>
      <app-info-box
        title="Screen Capture interval"
        [content]="
          convertTime(this.candidateAssessmentData.screenshotsInterval)
        "
        [condition]="
          proctoringProtocols?.includes('Candidate Capture') ||
          proctoringProtocols?.includes('Window Violation')
        "
        [fallbackContent]="0"
      ></app-info-box>
      <app-info-box
        title="Window Violation Duration"
        [content]="
          convertTime(candidateAssessmentData.assessmentWindowViolationDuration)
        "
        [condition]="true"
      ></app-info-box>
      <app-info-box
        title="Window Violation Count"
        [content]="candidateAssessmentData.assessmentWindowViolationCount"
        [condition]="true"
      ></app-info-box>
      <app-info-box
        title="Snapshot Interval"
        [content]="convertTime(candidateAssessmentData.camerashotsInterval)"
        [condition]="
          proctoringProtocols?.includes('Candidate Capture') ||
          proctoringProtocols?.includes('Window Violation')
        "
        [fallbackContent]="0"
      ></app-info-box>
    </div>
  </div>
</div>

<div class="flex justify-center items-center h-[60vh]" *ngIf="isLoading">
  <app-refresh-loader [loadingText]="'Loading...'"></app-refresh-loader>
</div>

<div *ngIf="isViewQuestions" class="modalView">
  <div
    class="flex flex-col bg-[#FFFFFF] max-w-[70vw] h-[78vh] rounded-lg my-[10vh] mx-auto relative px-12"
  >
    <div class="flex flex-row justify-between mb-9">
      <h1 class="text-[#0C4767] text-xl font-medium mt-8">
        {{ questionModalTitle }}
      </h1>
      <button
        class="p-0 rounded-lg text-4xl w-fit close ml-3"
        (click)="closeModal()"
      >
        &times;
      </button>
    </div>

    <div class="w-full h-[80%] bg-red overflow-y-auto mb-4 p-2">
      <div class="card-group" *ngIf="comprehensionPassage">
        <app-comprehension-passage
          [comprehensionPassage]="comprehensionPassage"
        ></app-comprehension-passage>
      </div>
      <div
        *ngFor="let question of this.questionViewArray; let i = index"
        class="flex mb-6"
      >
        <div class="flex flex-col gap-24 w-full mr-6">
          <div class="flex flex-col bg-[#EFF4F7] rounded-lg p-4">
            <app-question-view
              [questionIndex]="i"
              [question]="question"
            ></app-question-view>

            <div class="flex flex-col mt-8 pl-6 gap-2">
              <div
                *ngIf="
                  question.questionType === 'Multiple_choice' ||
                  question.questionType === 'True_or_false' ||
                  question.questionType === 'Multi_select'
                "
                class="flex flex-row gap-12 text-sm text-[#474D66] font-medium"
              >
                <app-basic-type-answer-view
                  [question]="question"
                  [questionType]="question.questionType"
                ></app-basic-type-answer-view>
              </div>
              <div *ngIf="question.questionType === 'Essay'">
                <app-essay-type-answer-view
                  [question]="question"
                ></app-essay-type-answer-view>
              </div>
              <div *ngIf="question.questionType === 'Matrix'">
                <app-matrix-type-answer-view
                  [question]="question"
                  [matrixQuestionWrongIndex]="matrixQuestionWrongIndex"
                ></app-matrix-type-answer-view>
              </div>
              <div *ngIf="question.questionType === 'Fill_in'">
                <app-fill-in-type-view
                  [question]="question"
                ></app-fill-in-type-view>
              </div>
              <div *ngIf="question.questionType === 'Code'">
                <app-coding-type-answer-view
                  [question]="question"
                ></app-coding-type-answer-view>
              </div>
              <div
                *ngIf="question.questionType !== 'Code'"
                class="mt-auto ml-auto"
              >
                <app-status-badge [question]="question"></app-status-badge>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
