<div class="flex flex-col w-[100%]">
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6 w-full">
    <!-- Candidate Code -->
    <div class="flex flex-col gap-2">
      <h2 class="text-[#0C4767] font-semibold mb-2">Candidate Code</h2>
      <div
        *ngIf="!candidateCode"
        class="flex justify-center items-center text-gray-400 italic h-96 w-full text-center"
      >
        No code submitted by candidate.
      </div>

      <div *ngIf="candidateCode" class="flex flex-col gap-2">
        <p class="text-[#0C4767]">
          Language:
          <span class="text-[#474D66]">{{
            codeLanguage(candidateLanguageId)
          }}</span>
        </p>
        <pre class="w-full h-96">
<code [highlight]="candidateCode" [language]="codeLanguage(candidateLanguageId)" class="rounded-xl w-full h-96 overflow-auto"></code>
      </pre>
        <div class="text-sm text-[#0C4767] mt-2 space-y-1">
          <div class="flex gap-4">
            <p>
              Total Test Cases:
              <span class="text-[#474D66]">{{
                question.codeExecutionSummary?.totalTests ?? 'N/A'
              }}</span>
            </p>
            <p>
              Passed:
              <span class="text-[#04952C]">{{
                question.codeExecutionSummary?.passedTests ?? 'N/A'
              }}</span>
            </p>
            <p>
              Failed:
              <span class="text-[#D25353]">{{
                question.codeExecutionSummary?.failedTests ?? 'N/A'
              }}</span>
            </p>
          </div>
          <div class="flex gap-4">
            <p>
              Memory Usage:
              <span class="text-[#474D66]">{{
                question.codeExecutionSummary?.performance?.totalMemory !==
                undefined
                  ? question.codeExecutionSummary?.performance?.totalMemory +
                    ' KB'
                  : 'N/A'
              }}</span>
            </p>
            <p>
              Runtime:
              <span class="text-[#474D66]">{{
                question.codeExecutionSummary?.performance
                  ?.averageExecutionTime !== undefined
                  ? (question.codeExecutionSummary?.performance
                      ?.averageExecutionTime | number: '1.3-3') + ' ms'
                  : 'N/A'
              }}</span>
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Reference Code -->
    <div class="flex flex-col gap-2">
      <h2 class="text-[#0C4767] font-semibold mb-2">Reference Code</h2>
      <div
        *ngIf="!referenceCode"
        class="flex justify-center items-center text-gray-400 italic h-96 w-full text-center"
      >
        No code reference code.
      </div>
      <div *ngIf="referenceCode" class="flex flex-col gap-2">
        <p class="text-[#0C4767]">
          Language:
          <span class="text-[#474D66]">{{
            codeLanguage(referenceLanguageId)
          }}</span>
        </p>
        <pre class="w-full h-fit">
<code [highlight]="referenceCode" [language]="codeLanguage(referenceLanguageId)" class="rounded-xl w-full h-96 overflow-auto"></code>
      </pre>
      </div>
    </div>
  </div>
  <!-- AI Review -->
  <div class="flex flex-col gap-2 mt-5">
    <h2 class="text-[#0C4767] font-semibold mb-2">AI Review</h2>
    <div *ngIf="candidateCode" class="flex flex-col gap-2">
      <p class="text-[#0C4767]">
        Language:
        <span class="text-[#474D66]">{{
          codeLanguage(candidateLanguageId)
        }}</span>
      </p>
      <p
        class="rounded-xl w-full bg-white p-4"
        *ngIf="aiCodeReview()"
        [appSafeHtml]="aiCodeReview()"
      ></p>
      <div
        *ngIf="!aiCodeReview()"
        class="flex justify-center items-center text-gray-400 italic h-96 w-full text-center"
      >
        No AI code review.
      </div>
    </div>
  </div>
</div>
