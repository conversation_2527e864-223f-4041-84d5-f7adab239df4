<div
  [ngClass]="{
    'flex flex-row gap-12':
      question.correctAnswers && question.correctAnswers[0].length < 9,
    'flex flex-col gap-2':
      question.correctAnswers && question.correctAnswers[0].length > 9,
  }"
>
  <div class="flex flex-col gap-1">
    <span class="text-[#0C4767] text-xs font-medium mb-4">Options</span>
    <div class="flex flex-col gap-5 max-w-[90rem]">
      <div
        *ngFor="let item of questionTypeOptions; let i = index"
        class="flex flex-row gap-3 items-center"
      >
        <div class="flex max-w-[60%]">
          <span [appSafeHtml]="item"></span>
        </div>
        <div class="flex flex-row min-w-[220px] gap-4">
          <div
            *ngIf="questionTypeAnswers && questionTypeAnswers.includes(item)"
            class="flex gap-2 items-center"
          >
            <span>
              <img
                src="../../../../assets/reportsManagement/healthicons_yes.svg"
                alt="correct"
              />
            </span>
            <span class="text-[#0ECCA6]">Correct Answer</span>
          </div>
          <div
            *ngIf="
              question.testTakerAnswers &&
              question.testTakerAnswers.includes(item)
            "
            class="flex gap-2 items-center"
          >
            <span>
              <img
                src="../../../../assets/reportsManagement/ic_outline-question-answer.svg"
                alt="answer"
              />
            </span>
            <span class="text-[#0C4767]">Candidate Answer</span>
          </div>
        </div>
      </div>
    </div>
  </div>
  @if (question.questionType === 'Multi_select') {
    <div class="flex flex-col mt-5">
      <span class="text-[#0C4767] text-xs font-medium">Strict Marking</span>
      <span>{{ question.strictMark ? 'Yes' : 'No' }}</span>
    </div>
  }
</div>
