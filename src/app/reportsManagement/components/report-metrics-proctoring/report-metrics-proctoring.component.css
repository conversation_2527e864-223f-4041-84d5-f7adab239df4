.nav-btn {
  background-color: white;
  border: 1px solid;
  cursor: none;
}

.nav-btn:hover {
  cursor: pointer;
}
.nav-btn > button {
  cursor: inherit;
  background-color: transparent;
}
.nav-btn.image-plus-nav-inactive {
  color: #b9b9b9;
  border: 2px solid #86706c;
  box-shadow: 1px 1px 5px #86706c;
}
.nav-btn.image-plus-nav-inactive:hover {
  cursor: not-allowed;
}
.nav-btn.image-plus-nav-active {
  color: #0c4767;
  border: 2px solid #0c4767;
  background-color: #faf8f8;
  box-shadow: 1px 1px 5px #0c4767;
}
.nav-btn.image-plus-nav-active:hover {
  background-color: #0c4767;
  color: #ffffff;
}
.svg-icon path {
  fill: #0c4767;
}
.svg-icon:hover path {
  fill: #ffffff; /* Change to red on hover */
}
/* Modal Container */
.modalView {
  position: fixed;
  z-index: 2000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

/* Close Button */
.close {
  color: #ffffff;
  float: right;
}

.close:hover,
.close:focus {
  color: #c1e9ff;
  text-decoration: none;
  cursor: pointer;
}

.text-shadow {
  text-shadow: 1px 2px 2px #ffffff;
}
