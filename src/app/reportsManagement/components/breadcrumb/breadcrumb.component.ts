import { Component, OnInit, OnD<PERSON>roy, inject } from '@angular/core';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { CommonModule } from '@angular/common';
import {
  BreadcrumbService,
  Breadcrumb,
} from '../../../services/breadcrumb-service/breadcrumb.service';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { filter, distinctUntilChanged } from 'rxjs/operators';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-breadcrumb',
  standalone: true,
  imports: [BreadcrumbModule, CommonModule],
  templateUrl: './breadcrumb.component.html',
})
export class BreadcrumbComponent implements OnInit, OnDestroy {
  breadcrumbService = inject(BreadcrumbService);
  router = inject(Router);
  activatedRoute = inject(ActivatedRoute);
  breadcrumbs$ = this.breadcrumbService.breadcrumbs$;

  private routerSubscription: Subscription | undefined;
  private breadcrumbsSubscription: Subscription | undefined;

  private readonly BREADCRUMB_CONFIGS = [
    {
      parentLabel: 'All Tests',
      hideOnPaths: [
        '/create-test/regular/basicInformation',
        '/edit-test/regular/basicInformation',
      ],
      queryParam: 'testId',
    },
    {
      parentLabel: 'All Assessments',
      hideOnPaths: [
        '/create-assessments/BasicInfo-And-Config',
        '/edit-assessments/BasicInfo-And-Config',
      ],
      queryParam: 'assessmentId',
    },
  ];

  ngOnInit(): void {
    this.routerSubscription = this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        distinctUntilChanged()
      )
      .subscribe(() => {
        const root = this.activatedRoute.root;
        let breadcrumbs = this.buildBreadcrumbs(root);

        // Apply breadcrumb hiding logic
        breadcrumbs = this.applyBreadcrumbHiding(breadcrumbs);

        // Remove all duplicate breadcrumbs, keeping only the first occurrence
        breadcrumbs = this.removeDuplicateBreadcrumbs(breadcrumbs);

        this.breadcrumbService.setBreadcrumbs(breadcrumbs);
      });

    this.breadcrumbsSubscription = this.breadcrumbs$.subscribe();
  }

  ngOnDestroy(): void {
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }

    if (this.breadcrumbsSubscription) {
      this.breadcrumbsSubscription.unsubscribe();
    }
  }

  private applyBreadcrumbHiding(breadcrumbs: Breadcrumb[]): Breadcrumb[] {
    if (breadcrumbs.length === 0) return breadcrumbs;

    const url = this.router.url;

    const config = this.BREADCRUMB_CONFIGS.find(
      (cfg) =>
        breadcrumbs[0].label === cfg.parentLabel &&
        cfg.hideOnPaths.some((path) => url.includes(path))
    );

    if (config && breadcrumbs.length > 2) {
      const queryParamValue =
        this.activatedRoute.snapshot.queryParams?.[config.queryParam];
      const modifiedBreadcrumbs = [
        breadcrumbs[0],
        breadcrumbs[breadcrumbs.length - 1],
      ];

      if (queryParamValue) {
        modifiedBreadcrumbs.forEach((crumb) => {
          crumb.queryParams = queryParamValue;
        });
      }

      return modifiedBreadcrumbs;
    }

    return breadcrumbs;
  }

  /**
   * Removes duplicate breadcrumbs based on label, keeping only the first occurrence
   */
  private removeDuplicateBreadcrumbs(breadcrumbs: Breadcrumb[]): Breadcrumb[] {
    const seen = new Set<string>();
    return breadcrumbs.filter((breadcrumb) => {
      if (seen.has(breadcrumb.label)) {
        return false; // Skip duplicate
      }
      seen.add(breadcrumb.label);
      return true; // Keep first occurrence
    });
  }

  buildBreadcrumbs(
    route: ActivatedRoute,
    url: string = '',
    breadcrumbs: Breadcrumb[] = []
  ): Breadcrumb[] {
    const child = route.firstChild;
    if (!child) {
      return breadcrumbs;
    }

    const routeUrl = child.snapshot.url
      .map((segment) => segment.path)
      .join('/');
    if (routeUrl) {
      url += `/${routeUrl}`;
    }

    const data = child.snapshot.data;
    const testId = child.snapshot.queryParams?.['testId'];

    if (data['breadcrumb']) {
      const breadcrumb: Breadcrumb = {
        label: data['breadcrumb'],
        path: url || '/',
        ...(testId && { queryParams: testId }),
        active: child.snapshot.url.length === 0,
      };
      breadcrumbs.push(breadcrumb);
    }

    return this.buildBreadcrumbs(child, url, breadcrumbs);
  }
}
