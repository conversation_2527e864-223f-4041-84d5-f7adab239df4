import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReportDispatchedAssessmentsComponent } from './report-dispatched-assessments.component';
import { AuthService } from '../../../services/auth.service';
import { ToastService } from '../../../services/toast-service/toast.service';
import { ReportManagementService } from '../../../services/report-management-service/report-management.service';
import { SharedService } from '../../../services/shared/shared.service';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';
import { CustomSearchInputComponent } from '../../../components/custom-search-input/custom-search-input.component';
import { ReportCustomTableComponent } from '../report-custom-table/report-custom-table.component';
import { NoResultFoundComponent } from '../../../no-result-found/no-result-found.component';
import { CustomPaginationComponent } from '../../../components/custom-pagination/custom-pagination.component';
import { OrganizationAssessment } from '../../../Interfaces/Types/reportServiceInterface';

describe('ReportDispatchedAssessmentsComponent', () => {
  let component: ReportDispatchedAssessmentsComponent;
  let fixture: ComponentFixture<ReportDispatchedAssessmentsComponent>;
  let toastService: jest.Mocked<ToastService>;
  let reportService: jest.Mocked<ReportManagementService>;
  let sharedService: jest.Mocked<SharedService>;
  let router: jest.Mocked<Router>;

  const mockAssessmentData = {
    data: [
      {
        assessmentId: '1',
        title: 'Test Assessment',
        instructions: 'Test instructions',
        averageScore: 85,
        numberOfSections: 3,
        numberOfCompleted: 10,
        numberOfFeedbacks: 5,
      },
    ],
  };

  beforeEach(async () => {
    const mockAuthService = {
      getLoginUser: jest.fn().mockReturnValue({ organizationId: 'org123' }),
    };

    const mockToastService = {
      onShow: jest.fn(),
    };

    const mockReportService = {
      getOrganizationAssessments: jest
        .fn()
        .mockReturnValue(of(mockAssessmentData)),
      updateAssessmentId: jest.fn(),
      updateOrganizationId: jest.fn(),
    };

    const mockSharedService = {
      removeMenuItems: jest.fn(),
      setBreadcrumbDetails: jest.fn(),
      setPageTitle: jest.fn(),
      addMenuItem: jest.fn(),
    };

    const mockRouter = {
      url: '/test-url',
      navigateByUrl: jest.fn(),
    };

    await TestBed.configureTestingModule({
      imports: [
        ReportDispatchedAssessmentsComponent,
        CustomSearchInputComponent,
        ReportCustomTableComponent,
        NoResultFoundComponent,
        CustomPaginationComponent,
      ],
      providers: [
        { provide: AuthService, useValue: mockAuthService },
        { provide: ToastService, useValue: mockToastService },
        { provide: ReportManagementService, useValue: mockReportService },
        { provide: SharedService, useValue: mockSharedService },
        { provide: Router, useValue: mockRouter },
      ],
    }).compileComponents();

    toastService = TestBed.inject(ToastService) as jest.Mocked<ToastService>;
    reportService = TestBed.inject(
      ReportManagementService
    ) as jest.Mocked<ReportManagementService>;
    sharedService = TestBed.inject(SharedService) as jest.Mocked<SharedService>;
    router = TestBed.inject(Router) as jest.Mocked<Router>;

    fixture = TestBed.createComponent(ReportDispatchedAssessmentsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should initialize component with correct organization ID', () => {
      component.ngOnInit();
      expect(component.orgId).toBe('org123');
      expect(sharedService.setPageTitle).toHaveBeenCalledWith(
        'Reports Management'
      );
    });

    it('should fetch organization assessments on init', () => {
      component.ngOnInit();
      expect(reportService.getOrganizationAssessments).toHaveBeenCalled();
      expect(component.organizationAssessments).toEqual(
        mockAssessmentData.data
      );
      expect(component.isLoading).toBeFalsy();
    });
  });

  describe('onPageChange', () => {
    it('should update page number and recalculate range', () => {
      component.onPageChange(2);
      expect(component.page).toBe(2);
      expect(component.range).toBeDefined();
    });
  });

  describe('onSizeChange', () => {
    it('should update items per page and recalculate range', () => {
      component.onSizeChange(50);
      expect(component.sizeOfItemsPerPage).toBe(50);
      expect(component.range).toBeDefined();
    });
  });

  describe('filterAssessment', () => {
    beforeEach(() => {
      component.initialOrganizationAssessments = [
        { ...mockAssessmentData.data[0] },
        { ...mockAssessmentData.data[0], title: 'Another Assessment' },
      ] as unknown as OrganizationAssessment[];
    });

    it('should filter assessments based on search term', () => {
      component.filterAssessment('test');
      expect(component.dataToBeDisplayed.length).toBe(1);
      expect(component.dataToBeDisplayed[0].title).toContain('Test');
    });

    it('should reset to page 1 when filtering', () => {
      component.page = 2;
      component.filterAssessment('test');
      expect(component.page).toBe(1);
    });
  });

  describe('onDetail', () => {
    it('should navigate to candidates page when numberOfCandidates is clicked', () => {
      const value = { assessmentId: '1', index: 0, type: 'numberOfCandidates' };
      component.onDetail(value);
      expect(reportService.updateAssessmentId).toHaveBeenCalledWith('1');
      expect(router.navigateByUrl).toHaveBeenCalledWith(
        'dashboard/report-management/assessment-candidates/1'
      );
    });

    it('should navigate to feedback page when numberOfFeedbacks is clicked', () => {
      const value = { assessmentId: '1', index: 0, type: 'numberOfFeedbacks' };
      component.onDetail(value);
      expect(router.navigateByUrl).toHaveBeenCalledWith(
        'dashboard/report-management/feedback/1'
      );
    });
  });

  describe('error handling', () => {
    it('should show error toast when organization assessment fetch fails', () => {
      reportService.getOrganizationAssessments.mockReturnValue(
        throwError(() => 'Test error')
      );

      component.getOrganizationAssessment();
      expect(toastService.onShow).toHaveBeenCalledWith(
        'error',
        'Test error',
        true,
        'error'
      );
      expect(component.isLoading).toBeFalsy();
    });
  });

  describe('filterAssessment', () => {
    beforeEach(() => {
      component.initialOrganizationAssessments = [
        { title: 'Assessment One' },
        { title: 'Assessment Two' },
        { title: 'Another Assessment' },
      ] as OrganizationAssessment[];
    });
    it('should filter assessments when a non-empty search term is provided', () => {
      const searchTerm = 'One';
      component.filterAssessment(searchTerm);
      expect(component.dataToBeDisplayed).toEqual([
        { title: 'Assessment One' },
      ]);
    });

    it('should handle case sensitivity in search term', () => {
      const searchTerm = 'ASSESSMENT';
      component.filterAssessment(searchTerm);
      expect(component.dataToBeDisplayed).toEqual([
        { title: 'Assessment One' },
        { title: 'Assessment Two' },
        { title: 'Another Assessment' },
      ]);
    });

    it('should return all assessments when search term is empty', () => {
      const searchTerm = '';
      component.filterAssessment(searchTerm);
      expect(component.dataToBeDisplayed).toEqual([
        { title: 'Assessment One' },
        { title: 'Assessment Two' },
        { title: 'Another Assessment' },
      ]);
    });
  });
});
