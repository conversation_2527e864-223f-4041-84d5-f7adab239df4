import { Component, EventEmitter, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { CustomSearchInputComponent } from '../../../components/custom-search-input/custom-search-input.component';
import { FormsModule } from '@angular/forms';
import { NgxPaginationModule } from 'ngx-pagination';
import { CommonModule } from '@angular/common';
import { AuthService } from '../../../services/auth.service';
import {
  OrganizationAssessment,
  OrganizationAssessmentObject,
} from '../../../Interfaces/Types/reportServiceInterface';
import { ToastService } from '../../../services/toast-service/toast.service';
import { ReportManagementService } from '../../../services/report-management-service/report-management.service';
import { SharedService } from '../../../services/shared/shared.service';
import { ReportCustomTableComponent } from '../report-custom-table/report-custom-table.component';
import { Router } from '@angular/router';
import { NoResultFoundComponent } from '../../../no-result-found/no-result-found.component';
import {
  addHeaderIfItIsToShow,
  getDataObjectKeys,
  processTableHeaders,
} from '../../../utils/report';
import { CustomPaginationComponent } from '../../../components/custom-pagination/custom-pagination.component';
import { calculateRange } from '../../../utils/questionsConstants';
import { RefreshLoaderComponent } from '../../../components/refresh-loader/refresh-loader.component';

@Component({
  selector: 'app-report-dispatched-assessments',
  standalone: true,
  imports: [
    CommonModule,
    CustomSearchInputComponent,
    FormsModule,
    NgxPaginationModule,
    ReportCustomTableComponent,
    NoResultFoundComponent,
    CustomPaginationComponent,
    RefreshLoaderComponent,
  ],
  templateUrl: './report-dispatched-assessments.component.html',
  styleUrl: './report-dispatched-assessments.component.css',
})
export class ReportDispatchedAssessmentsComponent implements OnInit, OnDestroy {
  private readonly subscription: Subscription = new Subscription();
  isLoading: boolean = true;
  searchTerm: string = '';
  selectedTests: string[] = [];
  orgId: string = '';
  minDateTime: string;
  page: number = 1;
  range = '';
  initialNumberOfItems = 1;
  currentTableContentItems = 0;
  sizeOfItemsPerPage: number = 25;
  totalItems: number = 0;
  organizationAssessments: OrganizationAssessment[] = [];
  initialOrganizationAssessments: OrganizationAssessment[] = [];
  pageChange = new EventEmitter<number>();
  pathUrl: string = '';
  tableHeadersConfiguration = [
    {
      dataKey: 'assessmentId',
      label: 'Assessment Id',
      isVisible: false,
    },
    { dataKey: 'title', label: 'Assessment Name', isVisible: true },
    {
      dataKey: 'instructions',
      label: 'Instructions',
      isVisible: false,
    },
    {
      dataKey: 'averageScore',
      label: 'Average Score',
      isVisible: true,
    },
    {
      dataKey: 'numberOfSections',
      label: 'No. of Sections',
      isVisible: true,
    },
    {
      dataKey: 'numberOfCandidates',
      label: 'No. Candidates',
      isVisible: true,
    },
    {
      dataKey: 'numberOfFeedbacks',
      label: 'No. of Feedbacks',
      isVisible: true,
    },
  ];
  dataToBeDisplayed: OrganizationAssessment[] = [];
  tableHeaders: { header: string; state: boolean }[] = [];
  dataObjectKeys: string[] = [];

  constructor(
    private readonly toast: ToastService,
    private readonly router: Router,
    private readonly reportsService: ReportManagementService,
    private readonly authService: AuthService,
    private readonly sharedService: SharedService
  ) {
    const currentDate = new Date();
    this.minDateTime = currentDate.toISOString().slice(0, 16);
  }
  ngOnInit(): void {
    this.pathUrl = this.router.url;

    localStorage.removeItem('breadcrumb');
    this.sharedService.setPageTitle('Reports Management');
    this.currentTableContentItems = this.sizeOfItemsPerPage;
    const loggedInUser = this.authService.getLoginUser();
    if (loggedInUser) {
      const organizationId = loggedInUser.organizationId;
      this.orgId = organizationId;
    }
    this.getOrganizationAssessment();
  }

  onPageChange(event: number) {
    this.pageChange.emit(event);
    this.page = event;
    this.calculateRange();
  }

  onSizeChange(event: number) {
    this.sizeOfItemsPerPage = event;
    this.calculateRange();
  }

  handleSuccess(data: OrganizationAssessmentObject) {
    const dispatchedAssessment = data;
    this.organizationAssessments = dispatchedAssessment.data;
    this.totalItems = dispatchedAssessment.data.length;
    this.initialOrganizationAssessments = this.organizationAssessments;
    this.dataToBeDisplayed = this.organizationAssessments;
    this.isLoading = false;
    this.dataObjectKeys = getDataObjectKeys(
      this.dataToBeDisplayed[0],
      'numberOfCandidates',
      this.tableHeadersConfiguration
    );

    processTableHeaders(
      this.dataToBeDisplayed,
      'numberOfCandidates',
      (header: string) =>
        addHeaderIfItIsToShow(
          header,
          this.tableHeadersConfiguration,
          this.tableHeaders
        )
    );
  }

  getOrganizationAssessment() {
    const orgAssessSub = this.reportsService
      .getOrganizationAssessments()
      .subscribe({
        next: (data: OrganizationAssessmentObject) => {
          this.handleSuccess(data);
          this.calculateRange();
        },
        error: (error) => {
          this.isLoading = false;
          this.toast.onShow('error', error, true, 'error');
        },
      });
    this.subscription.add(orgAssessSub);
  }
  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  filterAssessment(searchTitle: string) {
    this.page = 1;
    const searchTitleLowerCase = searchTitle.toLowerCase();
    this.dataToBeDisplayed = this.initialOrganizationAssessments.filter(
      (assessment) => {
        return searchTitleLowerCase === ''
          ? this.initialOrganizationAssessments
          : assessment.title.toLowerCase().includes(searchTitleLowerCase);
      }
    );

    this.totalItems = this.dataToBeDisplayed.length;
    this.calculateRange();
  }

  onDetail(value: { assessmentId: string; index: number; type: string }) {
    if (value.type === 'numberOfCandidates') {
      this.reportsService.updateAssessmentId(value.assessmentId);
      this.reportsService.updateOrganizationId(this.orgId);
      this.router.navigateByUrl(
        'dashboard/report-management/assessment-candidates/' +
          value.assessmentId
      );
    }
    if (value.type === 'numberOfFeedbacks') {
      this.router.navigateByUrl(
        `dashboard/report-management/feedback/${value.assessmentId}`
      );
    }
  }

  calculateRange() {
    this.range = calculateRange(
      this.page,
      this.sizeOfItemsPerPage,
      this.totalItems
    );
  }
}
