.card-container-control {
  height: calc(100vh - 57vh);
  overflow-y: auto;
}
@media (min-width: 1540px) {
  .card-container-control {
    height: calc(100vh - 38vh);
  }
}

.table-container {
  display: flex;
  max-height: 34.25rem;
}

.candidate-number a {
  display: none;
}

.candidate-number:hover a {
  display: inline-block;
  text-align: center;
  font-size: inherit;
}

.candidate-number:hover span {
  display: none;
}

.pagination-controller {
  display: flex;
  height: 1.9375rem;
}

.pagination-numbers {
  display: flex;
  max-width: 19.3125rem;
}

.pagination-box-size {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 2.8125rem;
  color: #545454;
}

.pagination-box-size:not(.pagination-box-size:last-child) {
  border-right: 2px solid #d9d9d9;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}
