import {
  assessmentLinks,
  candidateReportResponse,
  oneAssessment,
} from './report.mock';

describe('Mock Assessment Data', () => {
  describe('assessmentLinks', () => {
    it('should have success set to false', () => {
      expect(assessmentLinks.success).toBe(false);
    });

    it('should have one candidate', () => {
      expect(assessmentLinks.data.candidates.length).toBe(1);
    });

    it('should have valid candidate email and status', () => {
      const candidate = assessmentLinks.data.candidates[0];
      expect(candidate.email).toBe('<EMAIL>');
      expect(candidate.status).toBe('Completed');
    });

    it('should have ISO date strings for candidate time fields', () => {
      const candidate = assessmentLinks.data.candidates[0];
      expect(new Date(candidate.startTime).toISOString()).toBe(
        candidate.startTime
      );
      expect(new Date(candidate.endTime).toISOString()).toBe(candidate.endTime);
    });
  });

  describe('oneAssessment', () => {
    it('should have success set to true', () => {
      expect(oneAssessment.success).toBe(true);
    });

    it('should have title and empty testOrder', () => {
      expect(oneAssessment.data.title).toBe('assessment title');
      expect(Array.isArray(oneAssessment.data.testOrder)).toBe(true);
      expect(oneAssessment.data.testOrder.length).toBe(0);
    });

    it('should contain default boolean and empty string values', () => {
      const data = oneAssessment.data;
      expect(data.isDispatched).toBe(false);
      expect(data.proctor).toBe('');
      expect(data.organizationId).toBe('');
    });
  });

  describe('candidateReportResponse', () => {
    it('should have success set to false', () => {
      expect(candidateReportResponse.success).toBe(false);
    });

    it('should contain one candidate with default score values', () => {
      const candidate = candidateReportResponse.data.candidates[0];
      expect(candidate.assessmentScore).toBe(0);
      expect(candidate.assessmentTakerScorePercentage).toBe('');
      expect(candidate.invalid).toBe(false);
    });

    it('should have totalItems as 0', () => {
      expect(candidateReportResponse.data.totalItems).toBe(0);
    });
  });
});
