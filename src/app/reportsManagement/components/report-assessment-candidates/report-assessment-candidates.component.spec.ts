import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReportAssessmentCandidatesComponent } from './report-assessment-candidates.component';
import { HttpClient, HttpHandler } from '@angular/common/http';
import { TestTakingService } from '../../../services/test-taking-service/test-taking.service';
import { ActivatedRoute, Router } from '@angular/router';
import { of, throwError, Subject } from 'rxjs';
import {
  assessmentLinks,
  candidateReportResponse,
  oneAssessment,
} from './report.mock';
import { AssessmentService } from '../../../services/test-management-service/assessment-service/assessment.service';
import { SharedService } from '../../../services/shared/shared.service';
import { ReportManagementService } from '../../../services/report-management-service/report-management.service';
import { ToastService } from '../../../services/toast-service/toast.service';
import { Assessment } from '../../../Interfaces/Types/reportServiceInterface';
import { AuthService } from '../../../services/auth.service';
import { LoginUserInfo } from '../../../services/authServiceInterfaces';

jest.mock('jspdf', () => {
  return jest.fn().mockImplementation(() => ({
    addPage: jest.fn(),
    setFontSize: jest.fn(),
    text: jest.fn(),
    save: jest.fn(),
    autoTable: jest.fn(),
  }));
});

describe('ReportAssessmentCandidatesComponent', () => {
  let component: ReportAssessmentCandidatesComponent;
  let fixture: ComponentFixture<ReportAssessmentCandidatesComponent>;
  let testTakingServiceSpy: { getAssessmentLinkResults: jest.Mock };
  let ActivatedRouteSpy: jest.Mocked<ActivatedRoute>;
  let assessmentServiceSpy: { getOneAssessment: jest.Mock };
  let sharedServiceSpy: {
    setPageTitle: jest.Mock;
  };
  let reportManagementServiceSpy: {
    getAssessmentId: jest.Mock;
    updateAssessmentId: jest.Mock;
    exportAssessmentResults: jest.Mock;
    setTempData: jest.Mock;
    updateOrganizationId: jest.Mock;
    getAssessmentResults: jest.Mock;
  };
  let toastServiceSpy: { onShow: jest.Mock };
  let authServiceSpy: { getLoginUser: jest.Mock<LoginUserInfo> };
  let routerSpy: jest.Mocked<Router>;
  let toggleIndividualFilterSpy: jest.SpyInstance;
  let manageAllFilterSelectionSpy: jest.SpyInstance;
  let routerEventsSubject: Subject<Event>;

  beforeEach(async () => {
    testTakingServiceSpy = {
      getAssessmentLinkResults: jest.fn(),
    };
    assessmentServiceSpy = {
      getOneAssessment: jest.fn(),
    };
    sharedServiceSpy = {
      setPageTitle: jest.fn(),
    };
    reportManagementServiceSpy = {
      getAssessmentId: jest.fn(),
      updateAssessmentId: jest.fn(),
      exportAssessmentResults: jest.fn(),
      setTempData: jest.fn(),
      updateOrganizationId: jest.fn(),
      getAssessmentResults: jest.fn().mockReturnValue(of([])),
    };
    toastServiceSpy = {
      onShow: jest.fn(),
    };

    testTakingServiceSpy.getAssessmentLinkResults.mockReturnValue(
      of(assessmentLinks)
    );
    assessmentServiceSpy.getOneAssessment.mockReturnValue(of(oneAssessment));
    ActivatedRouteSpy = {
      params: of({ id: '1' }),
      queryParams: of({ filter: 'completed' }),
    } as unknown as jest.Mocked<ActivatedRoute>;
    reportManagementServiceSpy.getAssessmentId.mockReturnValue('1');
    authServiceSpy = {
      getLoginUser: jest.fn(),
    };

    routerEventsSubject = new Subject();
    routerSpy = {
      navigateByUrl: jest.fn(),
      url: '/',
      events: routerEventsSubject.asObservable(),
    } as unknown as jest.Mocked<Router>;

    await TestBed.configureTestingModule({
      imports: [ReportAssessmentCandidatesComponent],
      providers: [
        HttpClient,
        HttpHandler,
        { provide: TestTakingService, useValue: testTakingServiceSpy },
        { provide: ActivatedRoute, useValue: ActivatedRouteSpy },
        { provide: AssessmentService, useValue: assessmentServiceSpy },
        { provide: SharedService, useValue: sharedServiceSpy },
        {
          provide: ReportManagementService,
          useValue: reportManagementServiceSpy,
        },
        { provide: ToastService, useValue: toastServiceSpy },
        { provide: AuthService, useValue: authServiceSpy },
        { provide: Router, useValue: routerSpy },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ReportAssessmentCandidatesComponent);
    component = fixture.componentInstance;
    toggleIndividualFilterSpy = jest.spyOn(component, 'toggleIndividualFilter');
    manageAllFilterSelectionSpy = jest.spyOn(
      component,
      'manageAllFilterSelection'
    );

    fixture.detectChanges();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should get assessment link results', () => {
    const getAssessmentTitleSpy = jest.spyOn(component, 'getAssessmentTitle');

    const localStorageSpy = jest.spyOn(Storage.prototype, 'setItem');
    const sub = jest.spyOn(ActivatedRouteSpy.params, 'subscribe');
    component.ngOnInit();

    expect(component.pathUrl).toBe('/');
    expect(sub).toHaveBeenCalled();
    expect(component.isLoading).toBeFalsy();
    expect(component.assessmentId).toBe('1');
    expect(localStorageSpy).toHaveBeenCalledWith('assessmentId', '1');
    expect(component.assessmentTitle).toBe('assessment title');
    expect(component.pathLabel).toBe('Candidates Report - assessment title');
    expect(getAssessmentTitleSpy).toHaveBeenCalled();
    expect(sharedServiceSpy.setPageTitle).toHaveBeenCalledWith('');
    expect(reportManagementServiceSpy.updateAssessmentId).toHaveBeenCalled();
    expect(reportManagementServiceSpy.getAssessmentId).toHaveBeenCalled();
    expect(testTakingServiceSpy.getAssessmentLinkResults).toHaveBeenCalled();
    expect(component.rawAssessments).toEqual(assessmentLinks.data.candidates);
    expect(assessmentServiceSpy.getOneAssessment).toHaveBeenCalled();
  });

  it('should get assessment title #getAssessmentTitle', () => {
    component.getAssessmentTitle('1');
    expect(component.assessmentTitle).toBe('assessment title');
    expect(component.pathLabel).toBe('Candidates Report - assessment title');
    expect(sharedServiceSpy.setPageTitle).toHaveBeenCalledWith(
      'Candidates Report - assessment title'
    );
  });

  it('should get assessment title #getAssessmentTitle and throw error', () => {
    assessmentServiceSpy.getOneAssessment.mockReturnValue(
      throwError(() => 'error')
    );
    component.getAssessmentTitle('1');
    expect(toastServiceSpy.onShow).toHaveBeenCalledWith(
      'error',
      'error',
      true,
      'error'
    );
  });

  it('should toggle dropdown filter visibility to true', () => {
    component.isDropdownFilter = false;
    component.toggleDropdownFilterVisibility();
    expect(component.isDropdownFilter).toBeTruthy();
  });

  it('should toggle dropdown filter visibility to false', () => {
    component.isDropdownFilter = true;
    component.toggleDropdownFilterVisibility();
    expect(component.isDropdownFilter).toBeFalsy();
  });

  it('should toggle dropdown column visibility to true', () => {
    component.isDropdownColumn = false;
    component.toggleDropdownColumnVisibility();
    expect(component.isDropdownColumn).toBeTruthy();
  });

  it('should toggle dropdown column visibility to false', () => {
    component.isDropdownColumn = true;
    component.toggleDropdownColumnVisibility();
    expect(component.isDropdownColumn).toBeFalsy();
  });

  it('should set page to 2 on page change', () => {
    const pageChangeSpy = jest.spyOn(component.pageChange, 'emit');
    component.onPageChange(2);
    expect(component.currentPage).toBe(2);
    expect(pageChangeSpy).toHaveBeenCalledWith(2);
    expect(component.range).toBe('Showing 1 to 1 of 1');
  });

  it('should update table size per page on size change', () => {
    component.onSizeChange(10);
    expect(component.tableSizePerPage).toBe(10);
    expect(component.currentPageAssessmentNumber).toBe(1);
    expect(component.range).toBe('Showing 1 to 1 of 1');
  });

  it('should filter assessments', () => {
    const searchTerm = '<EMAIL>';
    component.currentAssessments = assessmentLinks.data.candidates;
    const updateInfo = jest.spyOn(component, 'updatePaginationInfo');
    component.filterAssessment(searchTerm);

    expect(component.currentPage).toBe(1);
    expect(component.totalAssessmentNumber).toBe(1);
    expect(component.filteredAssessments).toEqual(
      assessmentLinks.data.candidates
    );
    expect(updateInfo).toHaveBeenCalled();
  });

  it('should filter assessments and return empty array', () => {
    component.selectedFilters = new Set(['Completed']);
    component.currentAssessments = assessmentLinks.data.candidates;
    component.filterAssessment('<EMAIL>');
    expect(component.filteredAssessments).toEqual(
      assessmentLinks.data.candidates
    );
  });

  it('should handle completed assessment report navigation and return error', () => {
    component.handleCompletedAssessmentReportNavigation({
      index: 0,
      item: { status: 'Wrong status' } as Assessment,
    });
    expect(toastServiceSpy.onShow).toHaveBeenCalledWith(
      'Error Message',
      "No report on this assessment. Only Completed Assessment's have report",
      true,
      'error'
    );
  });

  it('should fetch and process organization assessment data', () => {
    const processAssessmentDataSpy = jest.spyOn(
      component,
      'processAssessmentData'
    );
    component.fetchAndProcessOrganizationAssessmentData();
    expect(component.isLoading).toBeFalsy();
    expect(component.range).toBe('Showing 1 to 1 of 1');
    expect(processAssessmentDataSpy).toHaveBeenCalledWith(assessmentLinks);
  });

  it('should fetch and process organization assessment data and throw error', () => {
    testTakingServiceSpy.getAssessmentLinkResults.mockReturnValue(
      throwError(() => new Error())
    );
    component.fetchAndProcessOrganizationAssessmentData();
    expect(toastServiceSpy.onShow).toHaveBeenCalledWith(
      'error',
      'Access Denied, You do not have access to this resource',
      true,
      'error'
    );
  });

  it('should get time', () => {
    const time = component.getTime('2023-09-21T14:35:00Z');
    expect(time).toBe('14:35');
  });

  it('should return invalid time for invalid date', () => {
    const time = component.getTime('time');
    expect(time).toBe('Invalid Time');
  });

  it('should filter items', () => {
    const toggleAllFiltersSpy = jest.spyOn(component, 'toggleAllFilters');
    const applyFiltersSpy = jest.spyOn(component, 'applyFilters');
    const updatePaginationInfoSpy = jest.spyOn(
      component,
      'updatePaginationInfo'
    );
    component.filterItems('All');
    expect(component.currentPage).toBe(1);
    expect(toggleAllFiltersSpy).toHaveBeenCalled();
    expect(toggleIndividualFilterSpy).not.toHaveBeenCalled();
    expect(manageAllFilterSelectionSpy).not.toHaveBeenCalled();
    expect(applyFiltersSpy).toHaveBeenCalled();
    expect(updatePaginationInfoSpy).toHaveBeenCalled();
  });

  it('should filter items when status is not All', () => {
    component.filterItems('Completed');
    expect(toggleIndividualFilterSpy).toHaveBeenCalledWith('Completed');
    expect(manageAllFilterSelectionSpy).toHaveBeenCalled();
  });

  it('should apply filters', () => {
    component.selectedFilters.add('Completed');
    component.applyFilters();
    expect(component.filteredAssessments).toEqual(component.currentAssessments);
  });

  it('should toggle all filters and clear set when status is All', () => {
    component.selectedFilters = new Set(['All']);
    component.toggleAllFilters();
    expect(component.selectedFilters).toEqual(new Set());
  });

  it('should toggle all filters and set All when status is not All', () => {
    component.selectedFilters = new Set(['Completed']);
    component.statusList = ['All', 'Completed', 'Not Completed'];
    component.toggleAllFilters();
    expect(component.selectedFilters).toEqual(
      new Set(['All', 'Completed', 'Not Completed'])
    );
  });

  it('should toggle individual filter and add filter to selectedFilters', () => {
    component.selectedFilters = new Set();
    component.toggleIndividualFilter('Completed');
    expect(component.selectedFilters).toEqual(new Set(['Completed']));
  });

  it('should toggle individual filter and remove filter from selectedFilters', () => {
    component.selectedFilters = new Set(['Completed']);
    component.toggleIndividualFilter('Completed');
    expect(component.selectedFilters).toEqual(new Set());
  });

  it('should manage all filter selection and clear selectedFilters', () => {
    component.selectedFilters = new Set(['All']);
    component.manageAllFilterSelection();
    expect(component.selectedFilters).toEqual(new Set());
  });

  it('should manage all filter selection and set selectedFilters', () => {
    component.statusList = ['Completed'];
    component.selectedFilters = new Set();
    component.manageAllFilterSelection();
    expect(component.selectedFilters).toEqual(new Set(['All']));
  });

  it('should update pagination info', () => {
    component.updatePaginationInfo();
    expect(component.totalAssessmentNumber).toBe(1);
    expect(component.range).toBe('Showing 1 to 1 of 1');
  });

  it('should set table column list', () => {
    component.tableColumnList(['Candidate email', 'Start time', 'End time']);
    expect(component.tableHeaders).toEqual([
      { header: 'Candidate email', state: true, sort: undefined },
      { header: 'Start time', state: true, sort: undefined },
      { header: 'End time', state: true, sort: undefined },
    ]);
  });

  it('should sort filtered assessments by email', () => {
    const p = [
      { id: '1', email: '<EMAIL>' },
      { id: '2', email: '<EMAIL>' },
    ] as Assessment[];

    component.filteredAssessments = p;
    component.sortColumn({ header: 'Candidate email', sort: true });
    expect(component.filteredAssessments).toEqual([
      { id: '2', email: '<EMAIL>' },
      { id: '1', email: '<EMAIL>' },
    ]);
  });

  it('should sort filtered assessments by email with same email', () => {
    const p = [
      { id: '1', email: '<EMAIL>' },
      { id: '2', email: '<EMAIL>' },
    ] as Assessment[];

    component.filteredAssessments = p;
    const result = component.sortColumn({
      header: 'Candidate email',
      sort: true,
    });
    expect(result).toEqual([
      { id: '1', email: '<EMAIL>' },
      { id: '2', email: '<EMAIL>' },
    ]);
  });

  it('should get column data keys', () => {
    const keys = component.columnDataKey('Candidate email');
    expect(keys).toEqual('email');
  });

  it('should export CSV', () => {
    reportManagementServiceSpy.exportAssessmentResults.mockReturnValue(
      of(candidateReportResponse)
    );
    component.exportCSV();
    expect(component.isExportLoading).toBeFalsy();
    expect(
      reportManagementServiceSpy.exportAssessmentResults
    ).toHaveBeenCalled();
    expect(toastServiceSpy.onShow).not.toHaveBeenCalled();
  });

  it('should export CSV and throw error', () => {
    reportManagementServiceSpy.exportAssessmentResults.mockReturnValue(
      throwError(() => new Error())
    );
    component.exportCSV();
    expect(component.isExportLoading).toBeFalsy();
    expect(toastServiceSpy.onShow).toHaveBeenCalled();
  });

  it('should close dropdown if clicking occurs outside the dropdown', () => {
    component.isDropdownFilter = true;
    component.isDropdownColumn = true;
    const event = new MouseEvent('click');
    document.body.dispatchEvent(event);
    component.onDocumentClick(event);

    expect(component.isDropdownFilter).toBeFalsy();
    expect(component.isDropdownColumn).toBeFalsy();
  });

  it('should set orgId if user is logged in', () => {
    authServiceSpy.getLoginUser.mockReturnValue({
      organizationId: '1',
    } as LoginUserInfo);
    component.ngOnInit();
    expect(component.orgId).toBe('1');
  });

  it('should destroy subscriptions on ngOnDestroy', () => {
    component.ngOnDestroy();
    expect(component.subscription.closed).toBeTruthy();
  });

  it('should get data object keys', () => {
    const obj: Assessment = {
      id: '',
      email: '',
      startTime: '',
      endTime: '',
      commenceDate: '',
      expireDate: '',
      createdAt: '',
      status: '',
      invalid: false,
      proctor: '',
    };
    const keys = component.getDataObjectKeys(obj);
    expect(keys).toEqual(['email', 'status']);
  });

  describe('compareDates', () => {
    it('should return a positive number if aDate is after bDate in ascending order', () => {
      const aValue = '2025-02-18T00:00:00Z';
      const bValue = '2025-02-17T00:00:00Z';
      const result = component.compareDates(aValue, bValue, true);
      expect(result).toBeGreaterThan(0);
    });

    it('should return a negative number if aDate is before bDate in ascending order', () => {
      const aValue = '2025-02-17T00:00:00Z';
      const bValue = '2025-02-18T00:00:00Z';
      const result = component.compareDates(aValue, bValue, true);
      expect(result).toBeLessThan(0);
    });

    it('should return zero if both dates are the same', () => {
      const aValue = '2025-02-18T00:00:00Z';
      const bValue = '2025-02-18T00:00:00Z';
      const result = component.compareDates(aValue, bValue, true);
      expect(result).toBe(0);
    });

    it('should return a negative number if aDate is after bDate in descending order', () => {
      const aValue = '2025-02-18T00:00:00Z';
      const bValue = '2025-02-17T00:00:00Z';
      const result = component.compareDates(aValue, bValue, false);
      expect(result).toBeLessThan(0);
    });

    it('should return a positive number if aDate is before bDate in descending order', () => {
      const aValue = '2025-02-17T00:00:00Z';
      const bValue = '2025-02-18T00:00:00Z';
      const result = component.compareDates(aValue, bValue, false);
      expect(result).toBeGreaterThan(0);
    });

    it('should return null if aValue is not a valid date', () => {
      const aValue = 'invalid-date';
      const bValue = '2025-02-18T00:00:00Z';
      const result = component.compareDates(aValue, bValue, true);
      expect(result).toBeNull();
    });

    it('should return null if bValue is not a valid date', () => {
      const aValue = '2025-02-18T00:00:00Z';
      const bValue = 'invalid-date';
      const result = component.compareDates(aValue, bValue, true);
      expect(result).toBeNull();
    });

    it('should return null if both values are invalid dates', () => {
      const aValue = 'invalid-date';
      const bValue = 'invalid-date';
      const result = component.compareDates(aValue, bValue, true);
      expect(result).toBeNull();
    });
  });
});
