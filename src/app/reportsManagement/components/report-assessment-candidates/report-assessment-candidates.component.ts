import {
  <PERSON>mpo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  HostListener,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { SharedService } from '../../../services/shared/shared.service';
import { ReportManagementService } from '../../../services/report-management-service/report-management.service';
import { AssessmentService } from '../../../services/test-management-service/assessment-service/assessment.service';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { CustomSearchInputComponent } from '../../../components/custom-search-input/custom-search-input.component';
import { FormsModule } from '@angular/forms';
import { NgxPaginationModule } from 'ngx-pagination';
import { ToastService } from '../../../services/toast-service/toast.service';
import { TestTakingService } from '../../../services/test-taking-service/test-taking.service';
import {
  Assessment,
  AssessmentDetailedResultsInterface,
  AssessmentResultInterface,
  BaseAssessment,
  candidateQuestionData,
  FillInQuestionAnswer,
  ReportAssessmentData,
  BaseQuestionAnswer,
} from '../../../Interfaces/Types/reportServiceInterface';
import { CustomFilterButtonComponent } from '../custom-filter-button/custom-filter-button.component';
import { statusColorMappings } from '../../../utils/constants';
import { AuthService } from '../../../services/auth.service';
import { CustomSortTableComponent } from '../custom-sort-table/custom-sort-table.component';
import { CustomButtonComponent } from '../../../components/custombutton/custombutton.component';
import { catchError, filter, of, Subscription } from 'rxjs';
import { NoResultFoundComponent } from '../../../no-result-found/no-result-found.component';
import { CustomPaginationComponent } from '../../../components/custom-pagination/custom-pagination.component';
import { calculateRange } from '../../../utils/questionsConstants';
import { RefreshLoaderComponent } from '../../../components/refresh-loader/refresh-loader.component';
import { AnalyticsService } from '../../../services/analytics.service';
import { BreadcrumbService } from '../../../services/breadcrumb-service/breadcrumb.service';
import { StatsCardComponent } from '../report-metrics-statistics/components/stats-card/stats-card.component';

@Component({
  selector: 'app-report-assessment-candidates',
  standalone: true,
  imports: [
    CommonModule,
    CustomSearchInputComponent,
    FormsModule,
    NgxPaginationModule,
    CustomFilterButtonComponent,
    CustomSortTableComponent,
    CustomButtonComponent,
    NoResultFoundComponent,
    CustomPaginationComponent,
    RefreshLoaderComponent,
    StatsCardComponent,
  ],
  templateUrl: './report-assessment-candidates.component.html',
  styleUrl: './report-assessment-candidates.component.css',
})
export class ReportAssessmentCandidatesComponent implements OnInit, OnDestroy {
  @ViewChild('dropdown') dropdown!: ElementRef;
  @ViewChild('exportDropdownRef') exportDropdownRef!: ElementRef;
  isDropdownDisabled = false;
  isExportOpen: boolean = false;

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    const targetElement = event.target as HTMLElement;
    if (this.dropdown && !this.dropdown.nativeElement.contains(targetElement)) {
      this.isDropdownColumn = false;
      this.isDropdownFilter = false;
      this.isExportOpen = false;
    }
  }

  dataObjectKeys: string[] = [];
  columnList: string[] = [];
  defaultColumnList: string[] = [];
  constructor(
    private readonly sharedService: SharedService,
    private readonly router: Router,
    private readonly toast: ToastService,
    private readonly authService: AuthService,
    private readonly tesTakingService: TestTakingService,
    private readonly reportManagementService: ReportManagementService,
    private readonly assessmentService: AssessmentService,
    private readonly route: ActivatedRoute,
    private readonly analyticsService: AnalyticsService,
    private readonly breadcrumbService: BreadcrumbService
  ) {}
  statusColorMappings = statusColorMappings;
  candidatesData: Assessment[] = [];
  currentAssessments: Assessment[] = [];
  filteredAssessments: Assessment[] = [];
  rawAssessments: Assessment[] = [];
  assessmentTitle: string = '';
  statusList: string[] = [
    'All',
    'Completed',
    'Viewed',
    'Incomplete',
    'In progress',
    'Expired',
    'Unattempted',
  ];
  isDropdownFilter = false;
  isDropdownColumn = false;
  isLoading: boolean = false;
  searchTerm: string = '';
  selectedTests: string[] = [];
  orgId: string = '';
  minDateTime: string = '';
  currentPage: number = 1;
  initialAssessmentNumber = 1;
  currentPageAssessmentNumber = 0;
  tableSizePerPage: number = 25;
  totalAssessmentNumber: number = 0;
  range = '';
  reportsData: ReportAssessmentData | null = null;
  pageChange = new EventEmitter<number>();
  pathUrl: string = '';
  pathLabel: string = '';
  tableHeaders: {
    header: string;
    state: boolean;
    sort: boolean | undefined;
  }[] = [];
  averageScoreIcon = '../../../../assets/icons/average-score.svg';
  totalCandidatesIcon = '../../../../assets/icons/total-candidates.svg';
  passRateIcon = '../../../../assets/icons/pass-rate.svg';
  completionRateIcon = '../../../../assets/icons/completion-rate.svg';
  tableHeaderConfig = [
    { dataKey: 'email', label: 'Candidate email', isVisible: true },
    { dataKey: 'createdAt', label: 'Dispatched On', isVisible: false },
    { dataKey: 'commenceDate', label: 'Available From', isVisible: false },
    { dataKey: 'expireDate', label: 'Expires On', isVisible: false },
    { dataKey: 'startDate', label: 'Taken On', isVisible: true },
    { dataKey: 'startTime', label: 'Start time', isVisible: false },
    { dataKey: 'endTime', label: 'End time', isVisible: false },
    { dataKey: 'duration', label: 'Duration', isVisible: false },

    { dataKey: 'assessmentScore', label: 'Assessment Score', isVisible: false },
    {
      dataKey: 'assessmentTakerScore',
      label: 'Candidates Score',
      isVisible: false,
    },
    {
      dataKey: 'assessmentTakerScorePercentage',
      label: 'Candidate Score(%)',
      isVisible: true,
    },

    { dataKey: 'submissionType', label: 'Submitted by', isVisible: true },
    { dataKey: 'status', label: 'Status', isVisible: true },
    { dataKey: 'proctorFeatures', label: 'Proctor Features', isVisible: true },
  ];
  EMPTY_DATE = '0001-01-01T00:00:00.000Z';
  DETAILED_CSV_HEADERS = [
    'Candidate Email',
    'Assessment Name',
    'Section Name',
    'Question ID',
    'Question Detail',
    'Answer Options',
    'Correct Answer',
    'Candidate Answer',
    'Question Score',
    'Candidate Score',
    'Answer Status',
  ];
  assessmentId: string = '';
  handleViewOrEditInfo: EventEmitter<number> = new EventEmitter();
  isExportLoading = false;
  subscription: Subscription = new Subscription();
  allCandidatesAssessmentDetails: AssessmentDetailedResultsInterface | null =
    null;
  ngOnInit(): void {
    this.pathUrl = this.router.url;
    this.sharedService.setPageTitle('');
    this.currentPageAssessmentNumber = this.tableSizePerPage;
    this.subscription.add(
      this.route.params.subscribe((params) => {
        this.reportManagementService.updateAssessmentId(params['id']);
        const fromAssessments: boolean = params['fromAssessments'];

        if (fromAssessments) {
          this.breadcrumbService.setBreadcrumbs([
            {
              label: 'All Assessments',
              path: '/dashboard/test-management/assessments/all-assessments',
            },
            {
              label: 'Assessment Candidates',
              path: this.router.url,
              active: true,
            },
          ]);
        }
      })
    );
    this.assessmentId = this.reportManagementService.getAssessmentId();

    localStorage.setItem('assessmentId', this.assessmentId);
    this.fetchAndProcessOrganizationAssessmentData();
    this.getAssessmentTitle(this.reportManagementService.getAssessmentId());
    const loggedInUser = this.authService.getLoginUser();
    if (loggedInUser) {
      const organizationId = loggedInUser.organizationId;
      this.orgId = organizationId;
    }

    this.columnList = this.tableHeaderConfig
      .filter((item) => item.dataKey !== 'email')
      .map((item) => {
        return item.label;
      });
  }

  getAssessmentTitle(id: string) {
    this.subscription.add(
      this.assessmentService.getOneAssessment(id).subscribe({
        next: (assessment) => {
          this.assessmentTitle = assessment.data.title;
          const pageTitle = `Candidates Report - ${this.assessmentTitle}`;
          this.sharedService.setPageTitle(pageTitle);
          this.pathLabel = pageTitle;
        },
        error: (error) => {
          this.toast.onShow('error', error, true, 'error');
        },
      })
    );
  }

  toggleDropdownFilterVisibility() {
    this.isDropdownFilter = !this.isDropdownFilter;
    this.isDropdownColumn = false;
    this.isExportOpen = false;
  }

  openExport() {
    this.isExportOpen = !this.isExportOpen;
    this.isDropdownColumn = false;
    this.isDropdownFilter = false;
  }

  toggleDropdownColumnVisibility() {
    this.isDropdownColumn = !this.isDropdownColumn;
    this.isDropdownFilter = false;
    this.isExportOpen = false;
  }

  onPageChange(event: number) {
    this.pageChange.emit(event);
    this.currentPage = event;
    this.range = calculateRange(
      this.currentPage,
      this.tableSizePerPage,
      this.totalAssessmentNumber
    );
  }

  onSizeChange(event: number) {
    this.tableSizePerPage = event;
    this.currentPageAssessmentNumber = 1;
    this.range = calculateRange(
      this.currentPage,
      this.tableSizePerPage,
      this.totalAssessmentNumber
    );
  }

  filterAssessment(searchTerm: string) {
    const searchTermLowerCase = searchTerm.toLowerCase();
    this.currentPage = 1;

    let filteredResults = this.currentAssessments;

    if (this.selectedFilters.size > 0 && !this.selectedFilters.has('All')) {
      filteredResults = filteredResults.filter((assessment) =>
        this.selectedFilters.has(assessment.status)
      );
    }
    if (searchTermLowerCase !== '') {
      filteredResults = filteredResults.filter((assessment) =>
        assessment.email.toLowerCase().includes(searchTermLowerCase)
      );
    }

    this.filteredAssessments = filteredResults;
    this.totalAssessmentNumber = filteredResults.length;

    this.updatePaginationInfo();
  }

  fetchAndProcessOrganizationAssessmentData() {
    this.isLoading = true;
    this.subscription.add(
      this.tesTakingService
        .getAssessmentLinkResults(this.assessmentId, this.searchTerm, '')
        .subscribe({
          next: (response) => {
            this.processAssessmentData(response);
            const hasReport = response.data.candidates.some(
              (item) =>
                item.status === 'Incomplete' || item.status === 'Completed'
            );

            this.isLoading = false;
            this.range = calculateRange(
              this.initialAssessmentNumber,
              this.tableSizePerPage,
              this.totalAssessmentNumber
            );

            if (hasReport) {
              this.getAllAssessmentsDetailedInfo();
            }
          },
          error: () => {
            this.isLoading = false;
            this.toast.onShow(
              'error',
              `Access Denied, You do not have access to this resource`,
              true,
              'error'
            );
          },
        })
    );
  }

  processAssessmentData(response: AssessmentResultInterface) {
    const data = response.data;
    this.reportsData = data;
    this.rawAssessments = data.candidates;
    this.rawAssessments.sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
    this.filteredAssessments = this.rawAssessments.map((candidate) => ({
      ...candidate,
      commenceDate: new Date(candidate.commenceDate).toDateString(),
      startTime:
        candidate.startTime === this.EMPTY_DATE
          ? '-'
          : this.getTime(candidate.startTime),
      endTime:
        candidate.endTime === this.EMPTY_DATE
          ? '-'
          : this.getTime(candidate.endTime),
      expireDate: new Date(candidate.expireDate).toDateString(),
      createdAt:
        new Date(candidate.createdAt).toDateString() +
        ' at ' +
        this.getTime(candidate.createdAt),
      startDate:
        candidate.startTime === this.EMPTY_DATE
          ? '-'
          : new Date(candidate.startTime).toDateString(),
    }));
    this.currentAssessments = this.filteredAssessments;
    this.candidatesData = this.filteredAssessments;

    if (this.currentAssessments.length > 0) {
      this.dataObjectKeys = this.getDataObjectKeys(this.currentAssessments[0]);
    }

    this.totalAssessmentNumber = data.totalItems;
    if (this.totalAssessmentNumber <= 7) {
      this.currentPageAssessmentNumber = this.totalAssessmentNumber;
    }
  }

  addHeaderIfItIsToShow(condition: string) {
    const matchHeader = this.tableHeaderConfig.find((header) => {
      return header.dataKey === condition && header.isVisible;
    });
    if (matchHeader) {
      this.tableHeaders.push({
        header: matchHeader.label,
        state: matchHeader.isVisible,
        sort: undefined,
      });
    }
  }

  getTime(time: string): string {
    try {
      const date = new Date(time);
      if (isNaN(date.getTime())) {
        throw new Error('Invalid time string provided.');
      }
      const hours = String(date.getUTCHours()).padStart(2, '0');
      const minutes = String(date.getUTCMinutes()).padStart(2, '0');
      return `${hours}:${minutes}`;
    } catch (error) {
      console.error(error);
      return 'Invalid Time';
    }
  }

  handleCompletedAssessmentReportNavigation(event: {
    index: number;
    item: Assessment;
  }) {
    const { status } = event.item;
    if (status !== 'Completed' && status !== 'Incomplete') {
      return this.toast.onShow(
        'Error Message',
        "No report on this assessment. Only Completed Assessment's have report",
        true,
        'error'
      );
    }

    this.reportManagementService.setTempData(event.item);
    localStorage.setItem('rowData', JSON.stringify(event.item));

    this.reportManagementService.updateOrganizationId(this.orgId);
    this.router.navigateByUrl(
      `/dashboard/report-management/assessment-candidates/${this.assessmentId}/candidate-metrics/` +
        this.orgId
    );
  }

  getDataObjectKeys(obj: Assessment) {
    const allFields = Object.keys(obj);

    const filteredValues: string[] = [];

    this.tableHeaderConfig
      .filter((item) => item.dataKey && item.isVisible)
      .forEach((item) => {
        if (allFields.includes(item.dataKey)) filteredValues.push(item.dataKey);
        this.tableHeaders.push({
          header: item.label,
          state: item.isVisible,
          sort: undefined,
        });
      });

    return filteredValues;
  }

  selectedFilters: Set<string> = new Set();

  filterItems(item: string) {
    this.currentPage = 1;

    if (item === 'All') {
      this.toggleAllFilters();
    } else {
      this.toggleIndividualFilter(item);
      this.manageAllFilterSelection();
    }

    this.applyFilters();
    this.updatePaginationInfo();
  }

  applyFilters() {
    if (this.selectedFilters.has('All') || this.selectedFilters.size === 0) {
      this.filteredAssessments = this.currentAssessments;
    } else {
      this.filteredAssessments = this.currentAssessments.filter((assessment) =>
        this.selectedFilters.has(assessment.status)
      );
    }
  }

  toggleAllFilters() {
    if (this.selectedFilters.has('All')) {
      this.selectedFilters.clear();
    } else {
      this.selectedFilters = new Set(this.statusList);
    }
  }

  toggleIndividualFilter(item: string) {
    if (this.selectedFilters.has(item)) {
      this.selectedFilters.delete(item);
    } else {
      this.selectedFilters.add(item);
    }
  }

  manageAllFilterSelection() {
    if (this.selectedFilters.size < this.statusList.length) {
      this.selectedFilters.delete('All');
    }
    if (this.selectedFilters.size === this.statusList.length - 1) {
      this.selectedFilters.add('All');
    }
  }

  updatePaginationInfo() {
    this.range = calculateRange(
      this.initialAssessmentNumber,
      this.tableSizePerPage,
      this.totalAssessmentNumber
    );
  }

  tableColumnList(item: string[]) {
    const itemSet = new Set(item);

    this.tableHeaderConfig.forEach((item) => {
      item.isVisible = itemSet.has(item.label);
    });

    this.tableHeaders = [];
    this.dataObjectKeys = [];
    const prevStats = this.reportsData?.stats;
    this.processAssessmentData({
      data: {
        candidates: this.rawAssessments,
        totalItems: this.totalAssessmentNumber,
        stats: prevStats,
        id: '',
        title: '',
      },
      success: false,
    });
  }

  sortColumn(data: { header: string; sort: boolean }) {
    const key = this.columnDataKey(data.header);

    return this.filteredAssessments.sort((a, b) =>
      this.compareValues(a[key], b[key], { sort: data.sort, key })
    );
  }

  compareValues(
    aValue: string | boolean,
    bValue: string | boolean,
    item: { sort: boolean; key: keyof BaseAssessment }
  ): number {
    if (
      item.key === ('assessmentTakerScorePercentage' as keyof BaseAssessment)
    ) {
      const numberComparison: number | null = this.compareNumbers(
        aValue,
        bValue,
        item.sort
      );
      if (numberComparison !== null) return numberComparison;
    }
    const dateComparison: number | null = this.compareDates(
      aValue,
      bValue,
      item.sort
    );
    if (dateComparison !== null) return dateComparison;

    const numberComparison: number | null = this.compareNumbers(
      aValue,
      bValue,
      item.sort
    );
    if (numberComparison !== null) return numberComparison;

    return this.compareStrings(aValue, bValue, item.sort);
  }

  compareDates(
    aValue: string | boolean,
    bValue: string | boolean,
    sort: boolean
  ): number | null {
    const aDate = new Date(aValue as string | number);
    const bDate = new Date(bValue as string | number);
    if (!isNaN(aDate.getTime()) && !isNaN(bDate.getTime())) {
      return sort
        ? aDate.getTime() - bDate.getTime()
        : bDate.getTime() - aDate.getTime();
    }
    return null;
  }

  compareNumbers(
    aValue: string | boolean,
    bValue: string | boolean,
    sort: boolean
  ): number | null {
    const aNumber = parseFloat(aValue as string);
    const bNumber = parseFloat(bValue as string);
    if (!isNaN(aNumber) && !isNaN(bNumber)) {
      return sort ? aNumber - bNumber : bNumber - aNumber;
    }
    return null;
  }

  compareStrings(
    aValue: string | boolean,
    bValue: string | boolean,
    sort: boolean
  ): number {
    const aStr = String(aValue);
    const bStr = String(bValue);
    if (aStr < bStr) {
      return sort ? -1 : 1;
    }
    if (aStr > bStr) {
      return sort ? 1 : -1;
    }
    return 0;
  }
  columnDataKey(column: string): keyof BaseAssessment {
    return this.tableHeaderConfig.find((item) => item.label === column)
      ?.dataKey as keyof BaseAssessment;
  }

  exportCSV() {
    this.isExportLoading = true;
    this.subscription.add(
      this.reportManagementService
        .exportAssessmentResults(this.assessmentId, this.selectedFilters)
        .pipe(
          catchError((error) => {
            this.isExportLoading = false;
            this.analyticsService
              .track('Assessment Results Export Failed', {
                reason: 'API error',
              })
              .then();
            this.toast.onShow('error', error, true, 'error');
            return of(null);
          }),
          filter((response) => !!response)
        )
        .subscribe(() => {
          this.isExportLoading = false;
          this.analyticsService
            .track('Assessment Results Exported', {
              assessmentId: this.assessmentId,
              filters: Array.from(this.selectedFilters),
              reportFormat: 'CSV',
            })
            .then();
        })
    );
  }

  private cleanText(text: string | undefined): string {
    return text
      ? text
          .replace(/<[^>]+>/g, '')
          .replace(/&nbsp;/g, ' ')
          .trim()
      : '';
  }

  private getAnswerOptions(question: candidateQuestionData): string {
    if (question.questionType === 'Essay') {
      return 'non';
    }
    if (question.questionType === 'Multiple_choice') {
      return this.cleanText(
        (question.correctAnswer as unknown as BaseQuestionAnswer).options.join(
          ', '
        )
      );
    }
    if (question.questionType === 'Multi_select') {
      return this.cleanText(
        (question.correctAnswer as unknown as BaseQuestionAnswer).options.join(
          ', '
        )
      );
    }
    if (question.questionType === 'True_or_false') {
      return this.cleanText(
        (question.correctAnswer as unknown as BaseQuestionAnswer).options.join(
          ', '
        )
      );
    }
    if (question.questionType === 'Fill_in') {
      return this.cleanText(
        (question.correctAnswer as unknown as FillInQuestionAnswer).options
          .map((option) => option['value'])
          .join(', ')
      );
    }

    return '';
  }
  private getCorrectAnswer(question: candidateQuestionData): string {
    if (question.questionType === 'Essay') {
      return 'non';
    }
    if (question.questionType === 'Multiple_choice') {
      return this.cleanText(
        (question.correctAnswer as unknown as BaseQuestionAnswer)?.answer.join(
          ', '
        )
      );
    }
    if (question.questionType === 'Multi_select') {
      return this.cleanText(
        (question.correctAnswer as unknown as BaseQuestionAnswer).answer.join(
          ', '
        )
      );
    }
    if (question.questionType === 'True_or_false') {
      return this.cleanText(
        (question.correctAnswer as unknown as BaseQuestionAnswer).answer.join(
          ', '
        )
      );
    }
    if (question.questionType === 'Fill_in') {
      return this.cleanText(
        (question.correctAnswer as unknown as FillInQuestionAnswer).answer.join(
          ', '
        )
      );
    }

    return '';
  }

  private getCandidateAnswer(question: candidateQuestionData): string {
    if (question.questionType === 'Matrix') {
      return 'Cannot export matrix answers';
    }
    if (question.candidateAnswer && question.candidateAnswer.length > 0) {
      return this.cleanText(question.candidateAnswer.join('| '));
    }
    return '';
  }
  exportDetailedCsv() {
    const rows: string[] = [this.DETAILED_CSV_HEADERS.join(',')];
    this.allCandidatesAssessmentDetails?.candidates.forEach((candidate) => {
      candidate.tests.forEach((section) => {
        section.questions.forEach((question) => {
          const answerOptions = this.getAnswerOptions(question);
          const correctAnswer = this.getCorrectAnswer(question);
          const candidateAnswer = this.getCandidateAnswer(question);
          const questionScore = question.maxScore;
          const candidateScore = question.scoreAwarded;
          const answerStatus = question.answerStatus || 'N/A';
          const questionDetail = this.cleanText(question.questionText);

          rows.push(
            [
              `"${candidate.candidateEmail}"`,
              `"${this.assessmentTitle}"`,
              `"${section.testTitle}"`,
              `"${question.questionId}"`,
              `"${questionDetail}"`,
              `"${answerOptions}"`,
              `"${correctAnswer}"`,
              `"${candidateAnswer}"`,
              `"${questionScore}"`,
              `"${candidateScore}"`,
              `"${answerStatus}"`,
            ].join(',')
          );
        });
      });
    });
    const csvContent = rows.join('\r\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.setAttribute(
      'download',
      `${this.assessmentTitle}-detailed-report.csv`
    );
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    this.toast.onShow(
      'success',
      'Detailed CSV report has been downloaded successfully',
      true,
      'success'
    );
    URL.revokeObjectURL(link.href);
  }
  getAllAssessmentsDetailedInfo() {
    this.subscription.add(
      this.reportManagementService
        .getAssessmentResults(this.assessmentId)
        .subscribe({
          next: (response) => {
            this.detailedAssessmentReportProcessing(response.data);
          },
          error: (error) => {
            this.toast.onShow('error', error, true, 'error');
          },
        })
    );
  }

  detailedAssessmentReportProcessing(
    results: AssessmentDetailedResultsInterface
  ) {
    this.allCandidatesAssessmentDetails = results;
  }

  scrollHorizontally(
    direction: 'left' | 'right',
    container: HTMLElement
  ): void {
    const scrollAmount = 300;

    if (direction === 'left') {
      container.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
    } else {
      container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    }
  }

  canScrollHorizontally(
    direction: 'left' | 'right',
    container: HTMLElement
  ): boolean {
    if (!container) return false;

    if (direction === 'left') {
      return container.scrollLeft > 0;
    } else {
      return (
        container.scrollLeft + container.clientWidth < container.scrollWidth
      );
    }
  }

  hasHorizontalScroll(container: HTMLElement): boolean {
    return (
      this.canScrollHorizontally('left', container) ||
      this.canScrollHorizontally('right', container)
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
