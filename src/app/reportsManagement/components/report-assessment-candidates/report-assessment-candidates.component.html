<div>
  <div class="w-full h-[1px] mb-[1.5vh] bg-[#0C4767] rounded-sm"></div>

  <div class="relative mb-6 group flex items-center">
    @if (hasHorizontalScroll(statsCarousel)) {
      <button
        class="z-10 bg-white p-2 rounded-full shadow-md disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed disabled:shadow-none disabled:opacity-50"
        (click)="scrollHorizontally('left', statsCarousel)"
        [disabled]="!canScrollHorizontally('left', statsCarousel)"
      >
        ◀
      </button>
    }

    <div
      #statsCarousel
      class="flex overflow-x-auto gap-4 no-scrollbar scroll-smooth py-2 hide-scrollbar"
      style="scroll-behavior: smooth"
    >
      <app-stats-card
        [title]="'Average Score'"
        [value]="reportsData?.stats?.averageScore || '0'"
        [isPercentage]="true"
        [secondaryText]="'Including incomplete'"
        [icon]="averageScoreIcon"
        class="snap-start transition-transform duration-300 hover:scale-[1.035] hover:shadow-lg transform origin-left"
      ></app-stats-card>

      <app-stats-card
        [title]="'Pass Rate'"
        [value]="reportsData?.stats?.passRate || '0'"
        [isPercentage]="true"
        [secondaryText]="
          'Pass Mark = ' + (reportsData?.stats?.passMark ?? '0') + '%'
        "
        [icon]="passRateIcon"
        class="snap-start transition-transform duration-300 hover:scale-[1.035] hover:shadow-lg transform origin-left"
      ></app-stats-card>

      <app-stats-card
        [title]="'Completion Rate'"
        [value]="reportsData?.stats?.completionRate || '0'"
        [isPercentage]="true"
        [secondaryText]="
          reportsData?.stats?.totalCandidatesCompleted +
          '/' +
          reportsData?.stats?.totalCandidatesInvited +
          ' candidates'
        "
        [icon]="completionRateIcon"
        class="snap-start transition-transform duration-300 hover:scale-[1.035] hover:shadow-lg transform origin-left"
      ></app-stats-card>

      <app-stats-card
        [title]="'Highest Score'"
        [value]="reportsData?.stats?.highestScore || '0'"
        [isPercentage]="true"
        [secondaryText]="'Candidates highest score'"
        [icon]="'../../../../assets/icons/highest-score.svg'"
        class="snap-start transition-transform duration-300 hover:scale-[1.035] hover:shadow-lg transform origin-left"
      ></app-stats-card>

      <app-stats-card
        [title]="'Lowest Score'"
        [value]="reportsData?.stats?.lowestScore || '0'"
        [isPercentage]="true"
        [secondaryText]="'Candidates lowest score'"
        [icon]="'../../../../assets/icons/lowest-score.svg'"
        class="snap-start transition-transform duration-300 hover:scale-102 hover:shadow-lg transform origin-left"
      ></app-stats-card>
      <app-stats-card
        [title]="'Total Candidates'"
        [value]="reportsData?.stats?.totalCandidatesInvited || '0'"
        [secondaryText]="'Candidates who received assessments'"
        [icon]="totalCandidatesIcon"
        class="snap-start transition-transform duration-300 hover:scale-102 hover:shadow-lg transform origin-left"
      ></app-stats-card>
    </div>
    @if (hasHorizontalScroll(statsCarousel)) {
      <button
        class="z-10 bg-white p-2 rounded-full shadow-md disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed disabled:shadow-none disabled:opacity-50"
        (click)="scrollHorizontally('right', statsCarousel)"
        [disabled]="!canScrollHorizontally('right', statsCarousel)"
      >
        ▶
      </button>
    }
  </div>

  <h1 class="text-[1.25rem] font-medium text-[#0C4767] mb-3">Candidates</h1>
  <div class="flex flex-wrap flex-row mb-[2rem] gap-[1.5rem] justify-between">
    <app-custom-search-input
      placeholder="Search Candidates..."
      [(ngModel)]="searchTerm"
      ngDefaultControl
      (searchTermChange)="filterAssessment($event)"
    />
    <div #dropdown class="flex items-end gap-4">
      <div class="flex flex-row gap-4 mr-0">
        <app-custom-filter-button
          (selectFilterClick)="toggleDropdownColumnVisibility()"
          [dropDownStatus]="isDropdownColumn"
          [buttonUseType]="'Select column'"
          [filterArray]="columnList"
          (selectedColumnItems)="tableColumnList($event)"
        ></app-custom-filter-button>
        <app-custom-filter-button
          (selectFilterClick)="toggleDropdownFilterVisibility()"
          [dropDownStatus]="isDropdownFilter"
          [filterArray]="statusList"
          (selectedFilterItem)="filterItems($event)"
        ></app-custom-filter-button>
      </div>
      @if (isExportLoading) {
        <app-custombutton
          type="button"
          variant="primary"
          [spinner]="true"
        ></app-custombutton>
      } @else {
        <div #exportDropdownRef class="flex relative flex-col">
          <app-custombutton
            type="button"
            variant="primary"
            (clicked)="openExport()"
          >
            <img
              class="mr-2"
              src="../../../../assets/icons/export.svg"
              alt=""
            />
            Export Report
          </app-custombutton>

          @if (isExportOpen) {
            <form
              class="grid absolute z-20 bg-white w-44 h-24 pl-2 mt-[3.25rem] rounded"
            >
              <div
                class="relative custom-checkbox flex items-center h-full w-full content-center"
              >
                <input
                  type="radio"
                  class="h-5 w-5 mr-2 cursor-pointer"
                  (change)="exportCSV()"
                />
                <label for="">Summary CSV</label>
              </div>
              <div
                class="relative custom-checkbox flex items-center h-full w-full content-center"
              >
                <input
                  type="radio"
                  class="h-5 w-5 mr-2 cursor-pointer"
                  (change)="exportDetailedCsv()"
                />
                <label for="">Detailed CSV</label>
              </div>
            </form>
          }
        </div>
      }
    </div>
  </div>
  <div class="flex justify-center items-center h-[60vh]" *ngIf="isLoading">
    <app-refresh-loader [loadingText]="'Loading...'"></app-refresh-loader>
  </div>
  <div *ngIf="!isLoading">
    <div class="flex flex-col">
      @if (filteredAssessments.length === 0 || false || false) {
        <app-no-result-found
          [message]="'No candidates found'"
          [search]="searchTerm"
        ></app-no-result-found>
      } @else {
        <app-custom-sort-table
          [tableType]="'candidates'"
          [tableData]="filteredAssessments"
          [tableHeaders]="tableHeaders"
          [tableDataKeys]="dataObjectKeys"
          (rowClick)="handleCompletedAssessmentReportNavigation($event)"
          [size]="tableSizePerPage"
          [page]="currentPage"
          (sortBy)="sortColumn($event)"
        ></app-custom-sort-table>
        <div
          *ngIf="!isLoading && currentAssessments?.length !== 0"
          class="mx-[1.5rem] mt-3"
        >
          <footer class="mt-5">
            <div class="grid md:flex justify-between items-center">
              <div class="text-[#474D66] text-[16px] font-medium">
                {{ range }}
              </div>

              <app-custom-pagination
                [size]="tableSizePerPage"
                [page]="currentPageAssessmentNumber"
                [total]="totalAssessmentNumber"
                (pagechange)="onPageChange($event)"
                (sizeSelect)="onSizeChange($event)"
              ></app-custom-pagination>
            </div>
          </footer>
        </div>
      }
    </div>
  </div>
</div>
