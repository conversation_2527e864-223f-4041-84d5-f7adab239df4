import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, from } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import * as LaunchDarkly from 'launchdarkly-js-client-sdk';
import { environment } from '../../environments/environment';

export interface FeatureFlagUser {
  key: string;
  kind: string;
  email?: string;
  name?: string;
  organizationId?: string;
  role?: string;
  custom?: Record<string, unknown>;
}

export interface FeatureFlagConfig {
  // AI Features
  enableAICodeGeneration: boolean;
  enableAITestCaseGeneration: boolean;
  enableAIQuestionGeneration: boolean;
  enableAISolutionGeneration: boolean;
  enableAIBoilerplateGeneration: boolean;
  
  // Proctoring Features
  enableFaceDetection: boolean;
  enableScreenRecording: boolean;
  enableCameraMonitoring: boolean;
  enableAudioMonitoring: boolean;
  enableScreenSwitchingDetection: boolean;
  enableIdleTimeTracking: boolean;
  enableWindowViolationTracking: boolean;
  
  // Assessment Configuration
  enableIdCapture: boolean;
  enableCandidateCapture: boolean;
  enableScreenCapture: boolean;
  enableSurveyAfterTest: boolean;
  enableHonourCode: boolean;
  
  // UI/UX Features
  enableDarkTheme: boolean;
  enableNewDashboard: boolean;
  enableAdvancedReporting: boolean;
  enableBulkOperations: boolean;
  enableRealTimeNotifications: boolean;
  enableExperimentalFeatures: boolean;
  
  // Performance Features
  enableLazyLoading: boolean;
  enableCaching: boolean;
  enableOptimizedBundling: boolean;
  
  // Security Features
  enableAdvancedFingerprinting: boolean;
  enableEnhancedEncryption: boolean;
  enableSessionMonitoring: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class FeatureFlagService {
  private client: LaunchDarkly.LDClient | null = null;
  private isInitialized = false;
  private readonly initializationSubject = new BehaviorSubject<boolean>(false);
  private readonly flagsSubject = new BehaviorSubject<Partial<FeatureFlagConfig>>({});
  
  public readonly initialized$ = this.initializationSubject.asObservable();
  public readonly flags$ = this.flagsSubject.asObservable();

  constructor() {
    this.initializeClient();
  }

  private async initializeClient(): Promise<void> {
    try {
      const clientSideId = environment.launchDarklyClientId;
      
      if (!clientSideId) {
        console.warn('LaunchDarkly client ID not found in environment');
        this.setDefaultFlags();
        return;
      }

      // Default user context
      const defaultUser: FeatureFlagUser = {
        kind: 'user',
        key: 'anonymous',
        email: '<EMAIL>',
        name: 'Anonymous User'
      };

      this.client = LaunchDarkly.initialize(clientSideId, defaultUser);
      
      await this.client.waitForInitialization();
      this.client.track('6899d13c2f867f09c56fa9e9');
      console.log('SDK successfully initialized!');
      // this.client.on('initialized', function () {
      //   // Tracking your memberId lets us know you are connected.
      // });
      
      this.isInitialized = true;
      this.initializationSubject.next(true);
      
      // Load initial flags
      this.loadAllFlags();
      
      // Listen for flag changes
      this.client.on('change', () => {
        this.loadAllFlags();
      });
      
      console.log('LaunchDarkly initialized successfully');
    } catch (error) {
      console.error('Failed to initialize LaunchDarkly:', error);
      this.setDefaultFlags();
    }
  }

  public async updateUser(user: FeatureFlagUser): Promise<void> {
    if (!this.client || !this.isInitialized) {
      console.warn('LaunchDarkly client not initialized');
      return;
    }

    try {
      await this.client.identify(user);
      this.loadAllFlags();
    } catch (error) {
      console.error('Failed to update user context:', error);
    }
  }

  private loadAllFlags(): void {
    if (!this.client || !this.isInitialized) {
      return;
    }

    const flags: Partial<FeatureFlagConfig> = {
      // AI Features
      enableAICodeGeneration: this.client.variation('enable-ai-code-generation', false),
      enableAITestCaseGeneration: this.client.variation('enable-ai-test-case-generation', false),
      enableAIQuestionGeneration: this.client.variation('enable-ai-question-generation', false),
      enableAISolutionGeneration: this.client.variation('enable-ai-solution-generation', false),
      enableAIBoilerplateGeneration: this.client.variation('enable-ai-boilerplate-generation', false),
      
      // Proctoring Features
      enableFaceDetection: this.client.variation('enable-face-detection', true),
      enableScreenRecording: this.client.variation('enable-screen-recording', true),
      enableCameraMonitoring: this.client.variation('enable-camera-monitoring', true),
      enableAudioMonitoring: this.client.variation('enable-audio-monitoring', false),
      enableScreenSwitchingDetection: this.client.variation('enable-screen-switching-detection', true),
      enableIdleTimeTracking: this.client.variation('enable-idle-time-tracking', true),
      enableWindowViolationTracking: this.client.variation('enable-window-violation-tracking', true),
      
      // Assessment Configuration
      enableIdCapture: this.client.variation('enable-id-capture', true),
      enableCandidateCapture: this.client.variation('enable-candidate-capture', true),
      enableScreenCapture: this.client.variation('enable-screen-capture', true),
      enableSurveyAfterTest: this.client.variation('enable-survey-after-test', true),
      enableHonourCode: this.client.variation('enable-honour-code', true),
      
      // UI/UX Features
      enableDarkTheme: this.client.variation('enable-dark-theme', true),
      enableNewDashboard: this.client.variation('enable-new-dashboard', false),
      enableAdvancedReporting: this.client.variation('enable-advanced-reporting', false),
      enableBulkOperations: this.client.variation('enable-bulk-operations', true),
      enableRealTimeNotifications: this.client.variation('enable-real-time-notifications', true),
      enableExperimentalFeatures: this.client.variation('enable-experimental-features', false),
      
      // Performance Features
      enableLazyLoading: this.client.variation('enable-lazy-loading', true),
      enableCaching: this.client.variation('enable-caching', true),
      enableOptimizedBundling: this.client.variation('enable-optimized-bundling', true),
      
      // Security Features
      enableAdvancedFingerprinting: this.client.variation('enable-advanced-fingerprinting', true),
      enableEnhancedEncryption: this.client.variation('enable-enhanced-encryption', false),
      enableSessionMonitoring: this.client.variation('enable-session-monitoring', true),
    };

    this.flagsSubject.next(flags);
  }

  private setDefaultFlags(): void {
    const defaultFlags: Partial<FeatureFlagConfig> = {
      // AI Features - disabled by default
      enableAICodeGeneration: false,
      enableAITestCaseGeneration: false,
      enableAIQuestionGeneration: false,
      enableAISolutionGeneration: false,
      enableAIBoilerplateGeneration: false,
      
      // Proctoring Features - enabled by default
      enableFaceDetection: true,
      enableScreenRecording: true,
      enableCameraMonitoring: true,
      enableAudioMonitoring: false,
      enableScreenSwitchingDetection: true,
      enableIdleTimeTracking: true,
      enableWindowViolationTracking: true,
      
      // Assessment Configuration - enabled by default
      enableIdCapture: true,
      enableCandidateCapture: true,
      enableScreenCapture: true,
      enableSurveyAfterTest: true,
      enableHonourCode: true,
      
      // UI/UX Features
      enableDarkTheme: true,
      enableNewDashboard: false,
      enableAdvancedReporting: false,
      enableBulkOperations: true,
      enableRealTimeNotifications: true,
      enableExperimentalFeatures: false,
      
      // Performance Features
      enableLazyLoading: true,
      enableCaching: true,
      enableOptimizedBundling: true,
      
      // Security Features
      enableAdvancedFingerprinting: true,
      enableEnhancedEncryption: false,
      enableSessionMonitoring: true,
    };

    this.flagsSubject.next(defaultFlags);
    this.initializationSubject.next(true);
  }

  public getFlag(flagName: keyof FeatureFlagConfig): Observable<boolean> {
    return this.flags$.pipe(
      map(flags => flags[flagName] ?? false)
    );
  }

  public getFlagValue(flagName: keyof FeatureFlagConfig): boolean {
    const currentFlags = this.flagsSubject.value;
    return currentFlags[flagName] ?? false;
  }

  public getAllFlags(): Observable<Partial<FeatureFlagConfig>> {
    return this.flags$;
  }

  public async close(): Promise<void> {
    if (this.client) {
      await this.client.close();
      this.client = null;
      this.isInitialized = false;
      this.initializationSubject.next(false);
    }
  }
}
