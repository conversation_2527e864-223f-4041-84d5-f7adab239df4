import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, Router } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError, take } from 'rxjs/operators';
import { FeatureFlagService, FeatureFlagConfig } from './feature-flag.service';

@Injectable({
  providedIn: 'root'
})
export class FeatureFlagGuard implements CanActivate {
  constructor(
    private featureFlagService: FeatureFlagService,
    private router: Router
  ) {}

  canActivate(route: ActivatedRouteSnapshot): Observable<boolean> {
    const flagName = route.data['featureFlag'] as keyof FeatureFlagConfig;
    const redirectTo = route.data['redirectTo'] as string;

    if (!flagName) {
      console.warn('FeatureFlagGuard: No feature flag specified in route data');
      return of(true);
    }

    return this.featureFlagService.getFlag(flagName).pipe(
      take(1),
      map((isEnabled: boolean) => {
        if (!isEnabled) {
          if (redirectTo) {
            this.router.navigate([redirectTo]);
          } else {
            this.router.navigate(['/not-found']);
          }
          return false;
        }
        return true;
      }),
      catchError((error) => {
        console.error('FeatureFlagGuard error:', error);
        return of(true); // Allow access on error to prevent blocking
      })
    );
  }
}
