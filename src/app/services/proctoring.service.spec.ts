import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import { ProctoringService } from './proctoring.service';
import { TestTakingService } from './test-taking-service/test-taking.service';
import { ActivatedRoute, Router } from '@angular/router';
import { of } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ToastService } from './toast-service/toast.service';
import { FeatureFlagService } from './feature-flag.service';
import { FaceDetectionService } from './face-detection.service';

describe('ProctoringService', () => {
  let service: ProctoringService;
  let httpClient: jest.Mocked<HttpClient>;
  let mockToastService: ToastService;
  let mockFeatureFlagService: jest.Mocked<FeatureFlagService>;

  beforeEach(() => {
    httpClient = {
      get: jest.fn(),
    } as unknown as jest.Mocked<HttpClient>;

    const featureFlagServiceSpy = {
      getFlagValue: jest.fn().mockReturnValue(true) // Enable screen recording by default
    };

    const mockRouter = {
      navigate: jest.fn()
    };

    const mockFaceDetectionService = {
      detectFacesFromImage: jest.fn()
    };

    TestBed.configureTestingModule({
      providers: [
        { provide: HttpClient, useValue: httpClient },
        { provide: FeatureFlagService, useValue: featureFlagServiceSpy },
        { provide: Router, useValue: mockRouter },
        { provide: FaceDetectionService, useValue: mockFaceDetectionService },
        ProctoringService,
        {
          provide: TestTakingService,
          useValue: {
            getTestTaking: jest.fn(),
            testTakerApiData$: {
              value: {
                assessmentTaker: {
                  organizationId: 'test-organization-id',
                  assessmentName: 'test-assessment-name',
                  email: 'test-email',
                },
              },
            },
          },
        },
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              paramMap: {
                get: () => 'test-param-value',
              },
            },
            queryParams: of({}),
          },
        },
        {
          provide: ToastService,
          useValue: {
            onShow: jest.fn()
          }
        }
      ],
    });

    service = TestBed.inject(ProctoringService);
    mockToastService = TestBed.inject(ToastService);
    mockFeatureFlagService = TestBed.inject(FeatureFlagService) as jest.Mocked<FeatureFlagService>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should initialize organizationId, assessmentName, and testTakerEmail on creation', () => {
    expect(service.organizationId).toBe('test-organization-id');
    expect(service.assessmentName).toBe('test-assessment-name');
    expect(service.testTakerEmail).toBe('test-email');
  });

  it('should handle tab detection correctly', fakeAsync(() => {
    const tabStatusSpy = jest.spyOn(service['tabStatusSource'], 'next');

    // Mock document.hidden and document.hasFocus for proper tab detection
    Object.defineProperty(document, 'hidden', {
      writable: true,
      value: false,
    });
    Object.defineProperty(document, 'hasFocus', {
      writable: true,
      value: jest.fn(() => false),
    });

    // Simulate tab switching (blur + hidden)
    window.dispatchEvent(new Event('blur'));
    Object.defineProperty(document, 'hidden', { value: true });

    // Wait for the 150ms delay in the blur handler
    tick(200);

    expect(tabStatusSpy).toHaveBeenCalledWith(false);

    // Simulate returning to tab (focus + visible)
    Object.defineProperty(document, 'hidden', { value: false });
    Object.defineProperty(document, 'hasFocus', {
      value: jest.fn(() => true),
    });
    window.dispatchEvent(new Event('focus'));

    expect(tabStatusSpy).toHaveBeenCalledWith(true);
  }));

  it('should stop all streams when stopShotsStream is called', () => {
    const mockTrack = {
      stop: jest.fn(),
    };
    service['screenStream'] = {
      getTracks: jest.fn(() => [mockTrack]),
    } as never;
    service['testTakerStream'] = {
      getTracks: jest.fn(() => [mockTrack]),
    } as never;

    service.stopShotsStream();

    expect(mockTrack.stop).toHaveBeenCalledTimes(2);
  });

  it('should clear stored time', () => {
    const clearTimeRemainingSpy = jest.spyOn(
      service['clearTimeRemaining'],
      'next'
    );
    service.isClearedStoredTime(true);
    expect(clearTimeRemainingSpy).toHaveBeenCalledWith(true);
  });
  it('should set and get count state', () => {
    service.setCountState(false);
    expect(service.getCountState()).toBe(false);
    service.setCountState(true);
    expect(service.getCountState()).toBe(true);
  });

  describe('testTakerCapture method', () => {
    global.MediaStream = jest.fn().mockImplementation(() => ({
      getTracks: jest.fn().mockReturnValue([]),
    }));
    it('should return base64 PNG test taker when t stream is active', async () => {
      const mockStream = new MediaStream();
      service.testTakerStream = mockStream;

      // Mock canvas
      const mockCanvas = {
        getContext: jest.fn().mockReturnValue({
          drawImage: jest.fn(),
        }),
        toDataURL: jest.fn().mockReturnValue('data:image/png;base64,abc123'),
      };

      const mockVideo = {
        onloadedmetadata: jest.fn(),
        srcObject: null,
        videoWidth: 1920,
        videoHeight: 1080,
        style: { position: '', left: '', top: '', width: '' },
      };
      const createElementSpy = jest
        .spyOn(document, 'createElement')
        .mockImplementationOnce(() => mockVideo as unknown as HTMLVideoElement)
        .mockImplementationOnce(
          () => mockCanvas as unknown as HTMLCanvasElement
        );

      const screenshotPromise = service.testTakerCapture();

      mockVideo.onloadedmetadata();

      await expect(screenshotPromise).resolves.toBe(
        'data:image/png;base64,abc123'
      );

      expect(mockVideo.style.position).toBe('fixed');
      expect(mockVideo.style.left).toBe('0');
      expect(mockVideo.style.top).toBe('0');
      expect(mockVideo.style.width).toBe('100%');

      createElementSpy.mockRestore();
      jest.restoreAllMocks();
    });

    it('should return null when stream is not available', () => {
      service.testTakerStream = null;

      const result = service.testTakerCapture();

      expect(result).toBeNull();
    });
  });

  describe('screen Capture method', () => {
    // Returns a Promise with base64 PNG screenshot when screen stream is active
    global.MediaStream = jest.fn().mockImplementation(() => ({
      getTracks: jest.fn().mockReturnValue([]),
    }));
    it('should return base64 PNG screenshot when screen stream is active', async () => {
      const mockStream = new MediaStream();
      service.screenStream = mockStream;

      // Mock canvas
      const mockCanvas = {
        getContext: jest.fn().mockReturnValue({
          drawImage: jest.fn(),
        }),
        toDataURL: jest.fn().mockReturnValue('data:image/png;base64,abc123'),
      };

      const mockVideo = {
        onloadedmetadata: jest.fn(),
        srcObject: null,
        videoWidth: 1920,
        videoHeight: 1080,
        style: { position: '', left: '', top: '', width: '' },
      };
      const createElementSpy = jest
        .spyOn(document, 'createElement')
        .mockImplementationOnce(() => mockVideo as unknown as HTMLVideoElement)
        .mockImplementationOnce(
          () => mockCanvas as unknown as HTMLCanvasElement
        );

      const screenshotPromise = service.takeScreenshot();

      mockVideo.onloadedmetadata();

      await expect(screenshotPromise).resolves.toBe(
        'data:image/png;base64,abc123'
      );

      expect(mockVideo.style.position).toBe('fixed');
      expect(mockVideo.style.left).toBe('0');
      expect(mockVideo.style.top).toBe('0');
      expect(mockVideo.style.width).toBe('100%');

      createElementSpy.mockRestore();
      jest.restoreAllMocks();
    });

    it('should return null when stream is not available', () => {
      service.screenStream = null;

      const result = service.takeScreenshot();

      expect(result).toBeNull();
    });
  });

  describe('openCamera method', () => {
    // Successfully opens camera and sets testTakerStream when getUserMedia is supported
    it('should set testTakerStream when camera access is granted', async () => {
      const mockStream = { id: 'test-stream' } as MediaStream;
      const mockGetUserMedia = jest.fn().mockResolvedValue(mockStream);

      Object.defineProperty(navigator, 'mediaDevices', {
        value: { getUserMedia: mockGetUserMedia },
        writable: true,
      });

      await service.openCamera();

      expect(mockGetUserMedia).toHaveBeenCalledWith({
        video: true,
        audio: false,
      });
      expect(service.testTakerStream).toBe(mockStream);
    });

    // Handles Permission denied error with correct error message
    it('should show permission denied toast when camera access is denied', async () => {
      const mockError = new Error('Permission denied');
      const mockGetUserMedia = jest.fn().mockRejectedValue(mockError);
      // const

      Object.defineProperty(navigator, 'mediaDevices', {
        value: { getUserMedia: mockGetUserMedia },
        writable: true,
      });

      jest.spyOn(mockToastService, 'onShow');

      await service.openCamera();

      expect(mockToastService.onShow).toHaveBeenCalledWith(
        'Camera access denied.',
        'Please enable permissions in your browser settings.',
        true,
        'error'
      );
    });
  });

  describe('shareScreen', () => {
    // Successfully initiates screen sharing when browser supports it and user grants permission
    it('should initialize screen stream when user grants permission', async () => {
      const mockStream = { active: true };
      const mockGetDisplayMedia = jest.fn().mockResolvedValue(mockStream);

      Object.defineProperty(global.navigator, 'mediaDevices', {
        value: { getDisplayMedia: mockGetDisplayMedia },
        writable: true,
      });

      const denialModalStateSourceSpy = jest.spyOn(
        service['denialModalStateSource'],
        'next'
      );

      await service.shareScreen();

      expect(mockGetDisplayMedia).toHaveBeenCalledWith({
        video: { displaySurface: 'monitor' },
        audio: false,
      });
      expect(service.screenStream).toBe(mockStream);
      expect(denialModalStateSourceSpy).toHaveBeenCalledWith(false);
    });

    // User denies screen sharing permission triggering error handling
    it('should handle denial when user rejects screen sharing', () => {
      const mockError = new Error('Permission denied');
      const mockGetDisplayMedia = jest.fn().mockRejectedValue(mockError);

      Object.defineProperty(global.navigator, 'mediaDevices', {
        value: { getDisplayMedia: mockGetDisplayMedia },
        writable: true,
      });

      const denialModalStateSourceSpy = jest.spyOn(
        service['denialModalStateSource'],
        'next'
      );

      service.shareScreen();

      expect(denialModalStateSourceSpy).toBeTruthy();
    });

    it('should not initialize screen sharing when feature flag is disabled', async () => {
      mockFeatureFlagService.getFlagValue.mockReturnValue(false);
      const mockGetDisplayMedia = jest.fn();
      Object.defineProperty(navigator, 'mediaDevices', {
        value: { getDisplayMedia: mockGetDisplayMedia },
        writable: true,
      });

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      await service.shareScreen();

      expect(mockGetDisplayMedia).not.toHaveBeenCalled();
      expect(consoleSpy).toHaveBeenCalledWith('Screen recording is disabled by feature flag');
      expect(mockFeatureFlagService.getFlagValue).toHaveBeenCalledWith('enableScreenRecording');

      consoleSpy.mockRestore();
    });
  });

  describe('track stream method', () => {
    it('should handle undefined value from streamGetter', fakeAsync(() => {
      const intervalMs = 100;

      // Spy to track calls to the callback function
      const callbackSpy = jest.fn((isActive: boolean) => {
        expect(isActive).toBe(undefined);
      });

      // Subscribe to the stream
      const sub = service.trackStream(
        intervalMs,
        { streamGetter: () => undefined },
        callbackSpy
      );

      tick(intervalMs * 10);

      expect(callbackSpy).toHaveBeenCalledTimes(1);

      sub.unsubscribe();
    }));

    // Subscription emits values at specified interval
    it('should emit values every specified interval milliseconds', fakeAsync(() => {
      const intervalMs = 100;

      const callbackSpy = jest.fn((isActive: boolean) => {
        expect(isActive).toBe(true);

        tick(intervalMs * 10);
      });
      const sub = service.trackStream(
        intervalMs,
        { streamGetter: () => true },
        callbackSpy
      );

      sub.unsubscribe();
    }));
  });
});
