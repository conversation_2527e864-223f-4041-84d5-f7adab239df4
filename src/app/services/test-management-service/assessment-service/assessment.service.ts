import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';
import { AuthService } from '../../auth.service';
import { environment } from '../../../../environments/environment';

import {
  AssessmentApiResponse,
  AssessmentHistoryApiResponse,
  BasicAndConfig,
  CreateAssessmentInterface,
  DispatchedAssessmentResponse,
  GenerateLinkInterface,
  GetOneAssessmentApiResponse,
  ProctorFeaturesApiResponse,
  UpdateCopyLink,
} from '../../../Interfaces/Types/assessmentInterface';
import { TestsData } from '../../../Interfaces/Types/test';

@Injectable({
  providedIn: 'root',
})
export class AssessmentService {
  allowedEmailsSubject = new BehaviorSubject<string[]>([]);
  allowedEmails$ = this.allowedEmailsSubject.asObservable();
  private readonly isAttemptedDispatchedAssessment =
    new BehaviorSubject<boolean>(false);

  private readonly assessmentUrl = `${environment.apiBaseUrl}/test-service/assessment/`;
  basicInfoAndConfig$ = new Subject<BasicAndConfig>();
  selectedTests: TestsData[] | undefined;

  constructor(
    private readonly http: HttpClient,
    private readonly authService: AuthService
  ) {}

  getOrganizationId(): string {
    const userData = this.authService.getLoginUser();
    return userData?.organizationId as string;
  }
  isAttemptedDispatchedAssessment$ =
    this.isAttemptedDispatchedAssessment.asObservable();

  setDispatchedAssessmentStatus(attempted: boolean) {
    this.isAttemptedDispatchedAssessment.next(attempted);
  }

  getOneAssessment(assessmentId: string) {
    return this.http.get<GetOneAssessmentApiResponse>(
      `${this.assessmentUrl}${this.getOrganizationId()}/${assessmentId}`
    );
  }
  getAllAssessment(
    searchTerm: string,
    limit: number,
    page: number,
    isDispatched?: boolean
  ) {
    let url = `${this.assessmentUrl}${this.getOrganizationId()}?search=${searchTerm}&limit=${limit}&page=${page}`;

    if (isDispatched !== undefined) {
      url += `&isDispatched=${isDispatched}`;
    }
    return this.http.get<AssessmentApiResponse>(url);
  }

  setBasicInfoAndConfig(basicInfoAndConfig: BasicAndConfig) {
    this.basicInfoAndConfig$.next(basicInfoAndConfig);
  }

  createAssessment(assessmentData: CreateAssessmentInterface) {
    return this.http.post<GetOneAssessmentApiResponse>(
      `${this.assessmentUrl}${this.getOrganizationId()}`,
      assessmentData
    );
  }

  editAssessment(
    assessmentData: CreateAssessmentInterface,
    assessmentId: string
  ) {
    return this.http.patch<GetOneAssessmentApiResponse>(
      `${this.assessmentUrl}${this.getOrganizationId()}/${assessmentId}`,
      assessmentData
    );
  }

  removeAssessmentInLevels(
    skillId: string,
    levelId: string,
    assessmentId: string
  ) {
    return this.http.put(
      `${this.assessmentUrl}${this.getOrganizationId()}/${skillId}/level/${levelId}/unassign-assessment`,
      {
        assessmentId: assessmentId,
      }
    );
  }

  deleteAssessment(assessmentId: string) {
    return this.http.delete(
      `${this.assessmentUrl}${this.getOrganizationId()}/${assessmentId}`
    );
  }

  dispatchAssessmentByEmail(
    assessmentId: string,
    data: CreateAssessmentInterface
  ) {
    return this.http.post<DispatchedAssessmentResponse>(
      `${this.assessmentUrl}${this.getOrganizationId()}/${assessmentId}/dispatch/batch`,
      data
    );
  }

  getAssessmentLink(assId: string, data: CreateAssessmentInterface) {
    return this.http.post<GenerateLinkInterface>(
      `${this.assessmentUrl}${this.getOrganizationId()}/${assId}/generate-link`,
      data
    );
  }

  getAllSelectedProctoring(assessmentId: string) {
    return this.http.get<ProctorFeaturesApiResponse>(
      `${this.assessmentUrl}${this.getOrganizationId()}/${assessmentId}/configuration`
    );
  }

  getAssessmentDispatchHistory(assessmentId: string) {
    return this.http.get<AssessmentHistoryApiResponse>(
      `${this.assessmentUrl}${this.getOrganizationId()}/${assessmentId}/dispatch`
    );
  }

  updateCopyLink(
    assessmentId: string,
    dispatchId: string,
    data: { expireDate: string; commenceDate: string; email: string[] }
  ) {
    return this.http.put<UpdateCopyLink>(
      `${this.assessmentUrl}${this.getOrganizationId()}/${assessmentId}/dispatch/${dispatchId}`,
      data
    );
  }
}
