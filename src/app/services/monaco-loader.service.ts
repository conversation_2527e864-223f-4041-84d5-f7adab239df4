import { Injectable } from '@angular/core';

@Injectable()
export class MonacoLoaderService {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private monacoPromise: Promise<any> | null = null;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public loadMonaco(): Promise<any> {
    this.monacoPromise ??= this.initMonaco();
    return this.monacoPromise;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private initMonaco(): Promise<any> {
    return new Promise((resolve, reject) => {
      if (window.monaco) {
        resolve(window.monaco);
        return;
      }

      this.loadMonacoScript('assets/monaco/vs/loader.js')
        .then(() => this.configureRequire())
        .then(resolve)
        .catch((err) =>
          reject(new Error('Failed to load Monaco editor: ' + err))
        );
    });
  }

  public loadMonacoScript(src: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = src;
      script.onload = () => resolve();
      script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
      document.head.appendChild(script);
    });
  }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public configureRequire(): Promise<any> {
    return new Promise((resolve, reject) => {
      const require = window.require as unknown as {
        config: (config: { paths: { vs: string } }) => void;
        (deps: string[], callback: () => void): void;
      };

      try {
        require.config({ paths: { vs: 'assets/monaco/vs' } });

        require(['vs/editor/editor.main'], () => {
          window.MonacoEnvironment = {
            getWorkerUrl: () => 'assets/monaco/vs/base/worker/workerMain.js',
          };

          if (window.monaco) {
            resolve(window.monaco);
          } else {
            reject(
              new Error('Monaco is not available on window after loading')
            );
          }
        });
      } catch (error) {
        reject(
          new Error('Failed to configure require: ' + (error as Error).message)
        );
      }
    });
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public configureMonaco(monaco: any): void {
    monaco?.editor.defineTheme('codeQuestionDark', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        { token: 'comment', foreground: '6A9955' },
        { token: 'keyword', foreground: '569CD6' },
        { token: 'string', foreground: 'CE9178' },
        { token: 'number', foreground: 'B5CEA8' },
        { token: 'operator', foreground: 'D4D4D4' },
      ],
      colors: {
        'editor.background': '#1E1E1E',
        'editor.foreground': '#D4D4D4',
        'editorCursor.foreground': '#D4D4D4',
        'editor.lineHighlightBackground': '#2D2D30',
        'editorLineNumber.foreground': '#858585',
        'editor.selectionBackground': '#264F78',
        'editor.inactiveSelectionBackground': '#3A3D41',
      },
    });
  }
}
