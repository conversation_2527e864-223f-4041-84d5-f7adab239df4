import { Injectable } from '@angular/core';
import {
  BehaviorSubject,
  Observable,
  interval,
  distinctUntilChanged,
} from 'rxjs';

export interface TimeAgoResult {
  text: string;
  isRecent: boolean; // true if less than 1 hour old
}

@Injectable({
  providedIn: 'root',
})
export class TimeAgoService {
  private readonly updateInterval$ = interval(60000); // Update every minute

  /**
   * Get time ago text for a timestamp
   * @param dateString ISO date string
   * @returns Human readable time ago text
   */
  getTimeAgo(dateString: string): string {
    if (!dateString) return '';

    const now = new Date();
    const inputDate = new Date(dateString);

    // Handle invalid dates
    if (isNaN(inputDate.getTime())) {
      return 'just now';
    }

    const diffInMilliseconds = Math.max(0, now.getTime() - inputDate.getTime());

    const { seconds, minutes, hours, days, months, years } =
      this.getTimeDifferenceInUnits(diffInMilliseconds);

    if (seconds < 5) {
      return 'just now';
    }
    if (seconds < 60) {
      return this.format(seconds, 'second');
    }
    if (minutes < 60) {
      return this.format(minutes, 'minute');
    }
    if (hours < 24) {
      return this.format(hours, 'hour');
    }
    if (days < 30) {
      return this.format(days, 'day');
    }
    if (months < 12) {
      return this.format(months, 'month');
    }
    return this.format(years, 'year');
  }

  /**
   * Get an observable that updates the time ago text automatically
   * Only updates for recent timestamps (less than 24 hours old) for performance
   * @param dateString ISO date string
   * @returns Observable of time ago text that updates automatically
   */
  getTimeAgoObservable(dateString: string): Observable<string> {
    if (!dateString) {
      return new BehaviorSubject('').asObservable();
    }

    const isRecent = this.isRecent(dateString);

    if (!isRecent) {
      // For old timestamps, just return static value
      return new BehaviorSubject(this.getTimeAgo(dateString)).asObservable();
    }

    // For recent timestamps, create updating observable
    const subject = new BehaviorSubject(this.getTimeAgo(dateString));

    const subscription = this.updateInterval$.subscribe(() => {
      const newValue = this.getTimeAgo(dateString);
      if (subject.value !== newValue) {
        subject.next(newValue);
      }

      // Stop updating if no longer recent (older than 24 hours)
      if (!this.isRecent(dateString)) {
        subscription.unsubscribe();
      }
    });

    return subject.asObservable().pipe(distinctUntilChanged());
  }

  /**
   * Get multiple time ago observables efficiently
   * Groups recent and static timestamps for better performance
   */
  getMultipleTimeAgo(timestamps: string[]): Observable<Record<string, string>> {
    const result$ = new BehaviorSubject<Record<string, string>>({});

    const updateAll = () => {
      const updated: Record<string, string> = {};
      timestamps.forEach((timestamp) => {
        updated[timestamp] = this.getTimeAgo(timestamp);
      });
      result$.next(updated);
    };

    // Initial calculation
    updateAll();

    // Only set up interval if there are recent timestamps
    const hasRecentTimestamps = timestamps.some((ts) => this.isRecent(ts));

    if (hasRecentTimestamps) {
      const subscription = this.updateInterval$.subscribe(() => {
        updateAll();

        // Stop if no more recent timestamps
        if (!timestamps.some((ts) => this.isRecent(ts))) {
          subscription.unsubscribe();
        }
      });
    }

    return result$
      .asObservable()
      .pipe(
        distinctUntilChanged((a, b) => JSON.stringify(a) === JSON.stringify(b))
      );
  }

  /**
   * Check if a timestamp is recent (less than 24 hours old)
   */
  private isRecent(dateString: string): boolean {
    const now = new Date().getTime();
    const timestamp = new Date(dateString).getTime();
    const diffHours = (now - timestamp) / (1000 * 60 * 60);
    return diffHours < 24;
  }

  private getTimeDifferenceInUnits(diffInMilliseconds: number) {
    const seconds = Math.floor(diffInMilliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    const months = Math.floor(days / 30);
    const years = Math.floor(days / 365);

    return { seconds, minutes, hours, days, months, years };
  }

  private format(value: number, unit: string): string {
    return `${value} ${unit}${value === 1 ? '' : 's'} ago`;
  }
}
