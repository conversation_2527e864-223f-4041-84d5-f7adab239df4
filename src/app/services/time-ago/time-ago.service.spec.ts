import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import { TimeAgoService } from './time-ago.service';

describe('TimeAgoService', () => {
  let service: TimeAgoService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(TimeAgoService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getTimeAgo', () => {
    it('should return "just now" for very recent timestamps', () => {
      const now = new Date().toISOString();
      expect(service.getTimeAgo(now)).toBe('just now');
    });

    it('should return empty string for empty input', () => {
      expect(service.getTimeAgo('')).toBe('');
    });

    it('should format seconds correctly', () => {
      const past = new Date(Date.now() - 30000).toISOString(); // 30 seconds ago
      expect(service.getTimeAgo(past)).toBe('30 seconds ago');
    });

    it('should format minutes correctly', () => {
      const past = new Date(Date.now() - 300000).toISOString(); // 5 minutes ago
      expect(service.getTimeAgo(past)).toBe('5 minutes ago');
    });

    it('should format hours correctly', () => {
      const past = new Date(Date.now() - 7200000).toISOString(); // 2 hours ago
      expect(service.getTimeAgo(past)).toBe('2 hours ago');
    });

    it('should format days correctly', () => {
      const past = new Date(Date.now() - 259200000).toISOString(); // 3 days ago
      expect(service.getTimeAgo(past)).toBe('3 days ago');
    });

    it('should format months correctly', () => {
      const past = new Date(Date.now() - 2592000000 * 2).toISOString(); // ~2 months ago
      expect(service.getTimeAgo(past)).toBe('2 months ago');
    });

    it('should format years correctly', () => {
      const past = new Date(Date.now() - 31536000000).toISOString(); // ~1 year ago
      expect(service.getTimeAgo(past)).toBe('1 year ago');
    });

    it('should handle singular forms correctly', () => {
      const oneSecond = new Date(Date.now() - 1000).toISOString();
      const fiveSeconds = new Date(Date.now() - 5000).toISOString();
      const oneMinute = new Date(Date.now() - 60000).toISOString();
      const oneHour = new Date(Date.now() - 3600000).toISOString();
      const oneDay = new Date(Date.now() - 86400000).toISOString();

      // 1 second ago should be "just now" due to 5-second threshold
      expect(service.getTimeAgo(oneSecond)).toBe('just now');
      expect(service.getTimeAgo(fiveSeconds)).toBe('5 seconds ago');
      expect(service.getTimeAgo(oneMinute)).toBe('1 minute ago');
      expect(service.getTimeAgo(oneHour)).toBe('1 hour ago');
      expect(service.getTimeAgo(oneDay)).toBe('1 day ago');
    });

    it('should handle future dates gracefully', () => {
      const future = new Date(Date.now() + 60000).toISOString(); // 1 minute in future
      const result = service.getTimeAgo(future);
      expect(result).toBe('just now'); // Should clamp to 0 difference
    });
  });

  describe('getTimeAgoObservable', () => {
    it('should return observable with initial value', (done) => {
      const now = new Date().toISOString();
      service.getTimeAgoObservable(now).subscribe((value) => {
        expect(value).toBe('just now');
        done();
      });
    });

    it('should return static observable for old timestamps', (done) => {
      const oldDate = new Date(Date.now() - 86400000 * 2).toISOString(); // 2 days ago
      service.getTimeAgoObservable(oldDate).subscribe((value) => {
        expect(value).toBe('2 days ago');
        done();
      });
    });

    it('should handle empty input', (done) => {
      service.getTimeAgoObservable('').subscribe((value) => {
        expect(value).toBe('');
        done();
      });
    });
  });

  describe('getMultipleTimeAgo', () => {
    it('should handle multiple timestamps', (done) => {
      const timestamps = [
        new Date(Date.now() - 30000).toISOString(), // 30 seconds ago
        new Date(Date.now() - 300000).toISOString(), // 5 minutes ago
      ];

      service.getMultipleTimeAgo(timestamps).subscribe((results) => {
        expect(Object.keys(results)).toHaveLength(2);
        expect(results[timestamps[0]]).toBe('30 seconds ago');
        expect(results[timestamps[1]]).toBe('5 minutes ago');
        done();
      });
    });

    it('should handle empty array', (done) => {
      service.getMultipleTimeAgo([]).subscribe((results) => {
        expect(results).toEqual({});
        done();
      });
    });

    it('should handle mix of recent and old timestamps', (done) => {
      const timestamps = [
        new Date(Date.now() - 30000).toISOString(), // 30 seconds ago (recent)
        new Date(Date.now() - 25 * 60 * 60 * 1000).toISOString(), // 25 hours ago (old)
      ];

      service.getMultipleTimeAgo(timestamps).subscribe((results) => {
        expect(Object.keys(results)).toHaveLength(2);
        expect(results[timestamps[0]]).toBe('30 seconds ago');
        expect(results[timestamps[1]]).toBe('1 day ago');
        done();
      });
    });

    it('should stop updating when no timestamps are recent', fakeAsync(() => {
      jest.useFakeTimers();
      const oldTimestamp = new Date(
        Date.now() - 25 * 60 * 60 * 1000
      ).toISOString(); // 25 hours ago
      const results: Record<string, string>[] = [];

      service.getMultipleTimeAgo([oldTimestamp]).subscribe((result) => {
        results.push(result);
      });

      // Should only emit once since timestamp is not recent
      tick(30000); // Advance 30 seconds
      expect(results).toHaveLength(1);

      jest.useRealTimers();
    }));

    it('should update recent timestamps and stop when they become old', fakeAsync(() => {
      jest.useFakeTimers();

      // Create a timestamp that will become old after moving time forward
      const recentTimestamp = new Date(
        Date.now() - 23 * 60 * 60 * 1000
      ).toISOString(); // 23 hours ago
      const results: Record<string, string>[] = [];

      service.getMultipleTimeAgo([recentTimestamp]).subscribe((result) => {
        results.push(result);
      });

      expect(results).toHaveLength(1); // Initial emission

      // Move time forward to make the timestamp old (beyond 24 hours)
      tick(2 * 60 * 60 * 1000); // Move forward 2 hours
      tick(30000); // Trigger update interval

      // Should still have only the initial emission since timestamp is now old
      expect(results).toHaveLength(1);

      jest.useRealTimers();
    }));
  });

  describe('edge cases', () => {
    it('should handle invalid date strings', () => {
      expect(service.getTimeAgo('invalid-date')).toBe('just now');
      expect(service.getTimeAgo('2024-13-45')).toBe('just now');
    });

    it('should handle very large time differences', () => {
      const veryOld = new Date(0).toISOString(); // Unix epoch
      const result = service.getTimeAgo(veryOld);
      expect(result).toContain('year');
    });

    it('should be consistent across multiple calls', () => {
      const timestamp = new Date(Date.now() - 300000).toISOString(); // 5 minutes ago
      const result1 = service.getTimeAgo(timestamp);
      const result2 = service.getTimeAgo(timestamp);
      expect(result1).toBe(result2);
    });

    it('should handle timestamps exactly at 24 hours boundary', () => {
      const exactly24HoursAgo = new Date(
        Date.now() - 24 * 60 * 60 * 1000
      ).toISOString();
      const result = service.getTimeAgo(exactly24HoursAgo);
      expect(result).toBe('1 day ago');
    });

    it('should handle very recent timestamps (less than 1 second)', () => {
      const veryRecent = new Date(Date.now() - 500).toISOString(); // 500ms ago
      const result = service.getTimeAgo(veryRecent);
      expect(result).toBe('just now');
    });

    it('should handle future timestamps', () => {
      const future = new Date(Date.now() + 60000).toISOString(); // 1 minute in future
      const result = service.getTimeAgo(future);
      expect(result).toBe('just now');
    });
  });
});
