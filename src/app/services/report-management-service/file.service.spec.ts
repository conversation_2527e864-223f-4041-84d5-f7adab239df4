import { TestBed } from '@angular/core/testing';
import { FileService } from './file.service';
import { TestTakingService } from '../test-taking-service/test-taking.service';
import { ToastService } from '../toast-service/toast.service';
import <PERSON> from 'papapar<PERSON>';
import jsPDF from 'jspdf';
import {
  PDFSectionConfig,
  ProctoringInformation,
} from '../../Interfaces/generalInterface';
import { Options } from '../../Interfaces/testInterface';
import { data } from '../../reportsManagement/components/report-metrics-proctoring/report-metrics.mock';
import {
  AssessmentScreenShot,
  ProctoringProtocolsResponse,
} from '../../Interfaces/Types/reportServiceInterface';
import { of, Subscription, throwError } from 'rxjs';
import { NgxImageCompressService } from 'ngx-image-compress';

fdescribe('FileService', () => {
  let fileService: FileService;
  let TestTakingServiceSpy: {
    getIdentityImages: jest.Mock;
    getScreenImages: jest.Mock;
    getTestTakerImages: jest.Mock;
  };

  let NgxImageCompressServiceSpy: { compressFile: jest.Mock };
  let toastServiceSpy: { onShow: jest.Mock };
  let doc: jsPDF;
  let originalImage: typeof Image;
  beforeEach(() => {
    doc = new jsPDF();
    originalImage = global.Image;
    TestTakingServiceSpy = {
      getIdentityImages: jest.fn(),
      getScreenImages: jest.fn(),
      getTestTakerImages: jest.fn(),
    };
    toastServiceSpy = {
      onShow: jest.fn(),
    };
    NgxImageCompressServiceSpy = {
      compressFile: jest.fn(),
    };

    TestBed.configureTestingModule({
      providers: [
        FileService,
        {
          provide: TestTakingService,
          useValue: TestTakingServiceSpy,
        },
        {
          provide: ToastService,
          useValue: toastServiceSpy,
        },
        {
          provide: NgxImageCompressService,
          useValue: NgxImageCompressServiceSpy,
        },
      ],
    });
    fileService = TestBed.inject(FileService);
  });

  afterEach(() => {
    global.Image = originalImage;
    jest.clearAllMocks();
  });

  it('should be created', () => {
    expect(fileService).toBeTruthy();
  });

  it('should #createCsvFile', () => {
    const mockData = [
      {
        name: 'test',
        age: 20,
      },
    ];
    const csvString = 'name,age\ntest,20';
    const spy = jest.spyOn(Papa, 'unparse');
    spy.mockReturnValue(csvString);
    fileService.createCsvFile(mockData, 'test.csv');
    expect(spy).toHaveBeenCalledWith(mockData, { header: true });
  });

  it('should #addHeader', () => {
    fileService.addHeader(doc, 'test');
    expect(doc.setFillColor).toHaveBeenCalledWith(0, 67, 107);
  });

  it('should #addFooter', () => {
    doc.getNumberOfPages = jest.fn().mockReturnValue(1);
    fileService.addFooter(doc);
    expect(doc.text).toHaveBeenCalledWith('Page 1 of 1', 50, 99, {
      align: 'center',
    });
  });

  it('should #drawLabelWithBackground', () => {
    fileService.drawLabelWithBackground(
      doc,
      'test',
      [10, 10, 10],
      [10, 10, 10],
      10,
      10
    );
    expect(doc.setFillColor).toHaveBeenCalledWith(255, 255, 255);
    expect(doc.roundedRect).toHaveBeenCalledWith(10, 10, 40, 10, 2, 2, 'F');
    expect(doc.setTextColor).toHaveBeenCalledWith(10, 10, 10);
    expect(doc.text).toHaveBeenCalledWith('test', 13, 17);
  });

  it('should #generateCandidateInfo', (done) => {
    const data: ProctoringInformation = {
      title: 'title',
      value: '2',
    };
    const options: Options = {
      textColor: [10, 10, 10],
      backgroundColor: [10, 10, 10],
    };

    const addTextSpy = jest.spyOn(fileService, 'addText');
    fileService.generateCandidateInfo(doc, 10, 10, options, [data]).subscribe({
      next: () => {
        expect(addTextSpy).toHaveBeenCalledTimes(2);
        done();
      },
    });
  });

  it('should #generateAssessmentInfo', () => {
    const addTextSpy = jest.spyOn(fileService, 'addText');
    const drawLabelWithBackgroundSpy = jest.spyOn(
      fileService,
      'drawLabelWithBackground'
    );
    fileService
      .generateAssessmentInfo(doc, data, 10, 10, [255, 244, 233], {})
      .subscribe();
    expect(addTextSpy).toHaveBeenCalledTimes(6);
    expect(drawLabelWithBackgroundSpy).toHaveBeenCalledTimes(5);
  });

  it('should #generateStatisticPdf', () => {
    const d: ProctoringProtocolsResponse = {
      success: false,
      data: {
        configuration: {
          conductSurvey: false,
          showClock: false,
          showResults: false,
          screenshotsInterval: '',
          camerashotsInterval: '',
          proctorFeatures: [
            {
              name: 'test',
            },
          ],
        },
      },
    };
    const addHeaderSpy = jest.spyOn(fileService, 'addHeader');
    const generateCandidateInfoSpy = jest.spyOn(
      fileService,
      'generateCandidateInfo'
    );
    fileService
      .generateStatisticPdf(data, '', data.data, of(d), new Subscription())
      .subscribe();
    expect(addHeaderSpy).toHaveBeenCalledTimes(1);
    expect(generateCandidateInfoSpy).toHaveBeenCalledTimes(1);
  });

  it('should #generateStatisticPdf and throw error', () => {
    fileService
      .generateStatisticPdf(
        data,
        '',
        data.data,
        throwError(() => new Error('error')),
        new Subscription()
      )
      .subscribe({
        error: () => {
          expect(toastServiceSpy.onShow).toHaveBeenCalledWith(
            'Error',
            'error',
            true,
            'error'
          );
        },
      });
  });

  it('should #addText', () => {
    fileService.addText(doc, 'test', 10, 10);
    expect(doc.setFillColor).toHaveBeenCalledWith(255, 255, 255);
    expect(doc.text).toHaveBeenCalledWith('test', 10, 17);
  });

  it('should #addProctoringInfo', () => {
    const info: ProctoringInformation[] = [
      {
        title: 'Window Violation',
        value: 'test',
      },
    ];
    const addTextSpy = jest.spyOn(fileService, 'addText');
    fileService.addProctoringInfo(
      info,
      doc,
      10,
      { textColor: [0, 0, 0] },
      { textColor: [0, 0, 0] }
    );

    expect(addTextSpy).toHaveBeenCalledTimes(2);
  });

  it('should #getViolationShots', (done) => {
    const screenShots: AssessmentScreenShot[] = [
      {
        id: 0,
        imageURL: '',
        isViolationShot: false,
        createdAt: '',
      },
    ];

    jest
      .spyOn(fileService, 'compressImage')
      .mockImplementation(async (imageUrl: string) => {
        return `data:image/jpeg;base64,${imageUrl}`;
      });

    fileService.getViolationShots(screenShots).subscribe((res) => {
      expect(res.length).toBe(1);
      done();
    });
  });

  it('should #addSectionToPDF', () => {
    const screenShots: AssessmentScreenShot[] = [
      {
        id: 1,
        imageURL: 'imageURL',
        createdAt: '2021-10-10',
        isViolationShot: false,
      },
    ];

    const addTextSpy = jest.spyOn(fileService, 'addText');
    const addAllImagesToPDFSpy = jest.spyOn(fileService, 'addAllImagesToPDF');

    const config: PDFSectionConfig = {
      sectionName: 'Proctoring Information',
      images: screenShots,
      imageHeight: 80,
      pageHeight: 100,
      pageWidth: 100,
      yPosition: 60,
      options: {},
    };

    fileService.addSectionToPDF(config, doc);

    expect(addTextSpy).toHaveBeenCalledTimes(1);
    expect(addAllImagesToPDFSpy).toHaveBeenCalledTimes(1);
  });

  it('should #generatePDF and throw error', () => {
    const info: ProctoringInformation[] = [
      {
        title: 'test',
        value: 'test',
      },
    ];
    TestTakingServiceSpy.getIdentityImages.mockReturnValue(
      throwError(() => 'No images')
    );
    TestTakingServiceSpy.getScreenImages.mockReturnValue(
      throwError(() => 'No images')
    );
    TestTakingServiceSpy.getTestTakerImages.mockReturnValue(
      throwError(() => 'No images')
    );

    fileService
      .generatePDF('11', info, new Subscription(), '<EMAIL>', [])
      .subscribe();

    expect(toastServiceSpy.onShow).toHaveBeenCalledWith(
      'Error',
      'No images',
      true,
      'error'
    );
  });

  it('should #generatePDF', (done) => {
    const info: ProctoringInformation[] = [
      {
        title: 'test',
        value: 'test',
      },
    ];

    const dummyIdentityData = {
      success: true,
      data: {
        identity: {
          linkId: 'abc123',
          linkHead: 'Identity Verification Document',
          createdAt: '2024-10-30T12:00:00Z',
        },
      },
    };

    const dummyAssessmentScreenShots = {
      success: true,
      data: {
        assessmentScreenShots: [
          {
            id: 1,
            imageURL: 'https://example.com/screenshot1.jpg',
            createdAt: '2024-10-30T12:00:00Z',
            isViolationShot: false,
          },
        ],
      },
    };

    const dummyAssessmentTakerCaptureShots = {
      success: true,
      data: {
        assessmentTakerCaptureShots: [
          {
            id: 1,
            imageURL: 'https://example.com/image1.jpg',
            createdAt: '2024-10-30T12:00:00Z',
            isViolationShot: false,
          },
        ],
      },
    };

    const addHeaderSpy = jest.spyOn(fileService, 'addHeader');
    const addTextSpy = jest.spyOn(fileService, 'addText');
    const addProctoringInfoSpy = jest.spyOn(fileService, 'addProctoringInfo');
    jest.spyOn(fileService, 'addSectionToPDF').mockReturnValue(of(80));

    TestTakingServiceSpy.getIdentityImages.mockReturnValue(
      of(dummyIdentityData)
    );
    TestTakingServiceSpy.getScreenImages.mockReturnValue(
      of(dummyAssessmentScreenShots)
    );
    TestTakingServiceSpy.getTestTakerImages.mockReturnValue(
      of(dummyAssessmentTakerCaptureShots)
    );

    fileService
      .generatePDF('11', info, new Subscription(), '<EMAIL>', [])
      .subscribe({
        next: () => {
          expect(addHeaderSpy).toHaveBeenCalled();
          expect(addTextSpy).toHaveBeenCalled();
          expect(addProctoringInfoSpy).toHaveBeenCalled();
          done();
        },
      });
  });

  it('should #addAllImagesToPdf and complete when allImages is 0', (done) => {
    const allImages: AssessmentScreenShot[] = [];
    const yPosition = 10;
    const pageHeight = 200;
    const pageWidth = 100;
    const imageHeight = 50;

    global.Image = class extends originalImage {
      override onload: (() => void) | null = null;
      override onerror: OnErrorEventHandler | null = null;

      constructor(width?: number, height?: number) {
        super(width, height);
        setTimeout(() => {
          if (this.onload) this.onload();
        }, 0);
      }

      override set src(_url: string) {
        setTimeout(() => {
          if (this.onload) this.onload();
        }, 0);
      }
    };
    fileService
      .addAllImagesToPDF(
        doc,
        allImages,
        imageHeight,
        pageHeight,
        pageWidth,
        yPosition
      )
      .subscribe(() => {
        expect(doc.text).not.toHaveBeenCalled();
        expect(doc.addImage).not.toHaveBeenCalled();
        done();
      });
  });

  it('should #addAllImagesToPdf', (done) => {
    const allImages: AssessmentScreenShot[] = [
      {
        imageURL: 'image url',
        imageTitle: 'title',
        id: 0,
        isViolationShot: false,
        createdAt: '',
      },
    ];
    const yPosition = 10;
    const pageHeight = 200;
    const pageWidth = 100;
    const imageHeight = 50;

    global.Image = class extends originalImage {
      override onload: (() => void) | null = null;
      override onerror: OnErrorEventHandler | null = null;

      constructor(width?: number, height?: number) {
        super(width, height);
        setTimeout(() => {
          if (this.onload) this.onload();
        }, 0);
      }

      override set src(_url: string) {
        setTimeout(() => {
          if (this.onload) this.onload();
        }, 0);
      }
    };
    fileService
      .addAllImagesToPDF(
        doc,
        allImages,
        imageHeight,
        pageHeight,
        pageWidth,
        yPosition
      )
      .subscribe(() => {
        expect(doc.text).toHaveBeenCalled();
        expect(doc.addImage).toHaveBeenCalled();
        done();
      });
  });

  it('should handle image loading errors', (done) => {
    const allImages: AssessmentScreenShot[] = [
      {
        imageURL: 'image url',
        imageTitle: 'title',
        id: 0,
        isViolationShot: false,
        createdAt: '',
      },
    ];
    const imageHeight = 100;
    const pageHeight = 200;
    const pageWidth = 150;
    const yPosition = 10;

    global.Image = class extends originalImage {
      override onload: (() => void) | null = null;
      override onerror: OnErrorEventHandler | null = null;

      constructor(width?: number, height?: number) {
        super(width, height);
        setTimeout(() => {
          if (this.onerror) {
            this.onerror('Failed to load image');
          }
        }, 0);
      }

      override set src(_url: string) {
        setTimeout(() => {
          if (this.onerror) {
            this.onerror('Failed to load image');
          }
        }, 0);
      }
    };
    fileService
      .addAllImagesToPDF(
        doc,
        allImages,
        imageHeight,
        pageHeight,
        pageWidth,
        yPosition
      )
      .subscribe(() => {
        expect(toastServiceSpy.onShow).toHaveBeenCalledWith(
          'error',
          expect.stringContaining('Failed to load image'),
          true,
          'error'
        );
        done();
      });
  });
});
