import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { FeatureFlagService } from './feature-flag.service';

type Theme = 'light' | 'dark';

@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private readonly _theme = new BehaviorSubject<Theme>(this.getInitialTheme());
  public theme$ = this._theme.asObservable();

  constructor(private readonly featureFlagService: FeatureFlagService) {
    this.applyTheme(this._theme.value);
  }

  private getInitialTheme(): Theme {
    const savedTheme = localStorage.getItem('theme') as Theme;
    if (savedTheme) {
      return savedTheme;
    }

    // Check user's system preference
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }

  public toggleTheme(): void {
    // Check if dark theme is enabled
    const isDarkThemeEnabled = this.featureFlagService.getFlagValue('enableDarkTheme');
    if (!isDarkThemeEnabled && this._theme.value === 'light') {
      console.log('Dark theme is disabled by feature flag');
      return;
    }

    const newTheme = this._theme.value === 'light' ? 'dark' : 'light';
    this._theme.next(newTheme);
    localStorage.setItem('theme', newTheme);
    this.applyTheme(newTheme);
  }

  private applyTheme(theme: Theme): void {
    document.documentElement.classList.remove('light-theme', 'dark-theme');
    document.documentElement.classList.add(`${theme}-theme`);

    // Apply theme to body for easier CSS targeting
    document.body.setAttribute('data-theme', theme);
  }

  public getMonacoTheme(): string {
    return this._theme.value === 'light' ? 'vs' : 'codeQuestionDark';
  }
}
