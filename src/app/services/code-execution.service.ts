import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { switchMap } from 'rxjs/operators';
import {
  AIToJudgeOPayloadInterface,
  APIAutoGenerateTestCasePayloadInterface,
  APIAutoGenerateTestCaseResponseInterface,
  BulkTestCasesJudgeOResponse,
  CodeExecutionResponse,
  GenerateBoilerplateResponseInterface,
  GenerateQuestionResponseInterface,
  GenerateSolutionPayloadInterface,
  GenerateSolutionResponseInterface,
} from '../Interfaces/codingTypeQuestionsInterface';
import { environment } from '../../environments/environment';
import { FeatureFlagService } from './feature-flag.service';

@Injectable({
  providedIn: 'root',
})
export class CodeExecutionService {
  private readonly testTakingUrl = `${environment.apiBaseUrl}/test-taking`;
  private readonly AI_URL = `${environment['AI_BaseUrl']}/api/v1/assessment`;

  constructor(
    private readonly http: HttpClient,
    private readonly featureFlagService: FeatureFlagService
  ) {}

  runCode(functionCode: string, input: string, languageId: number) {
    const payload = {
      code: functionCode,
      input: input,
      language_id: languageId,
    };
    return this.http.post<CodeExecutionResponse>(
      this.testTakingUrl + '/run-test-case',
      payload
    );
  }

  generateTestCases(payload: APIAutoGenerateTestCasePayloadInterface): Observable<APIAutoGenerateTestCaseResponseInterface[]> {
    return this.featureFlagService.getFlag('enableAITestCaseGeneration').pipe(
      switchMap((isEnabled: boolean) => {
        if (!isEnabled) {
          return throwError(() => new Error('AI Test Case Generation is disabled'));
        }
        return this.http.post<APIAutoGenerateTestCaseResponseInterface[]>(
          this.AI_URL + '/generate-tests',
          payload
        );
      })
    );
  }

  generateSolutionCode(payload: GenerateSolutionPayloadInterface): Observable<GenerateSolutionResponseInterface> {
    return this.featureFlagService.getFlag('enableAISolutionGeneration').pipe(
      switchMap((isEnabled: boolean) => {
        if (!isEnabled) {
          return throwError(() => new Error('AI Solution Generation is disabled'));
        }
        return this.http.post<GenerateSolutionResponseInterface>(
          this.AI_URL + '/generate-reference',
          payload
        );
      })
    );
  }

  generateBoilerplateCode(payload: GenerateSolutionPayloadInterface): Observable<GenerateBoilerplateResponseInterface> {
    return this.featureFlagService.getFlag('enableAIBoilerplateGeneration').pipe(
      switchMap((isEnabled: boolean) => {
        if (!isEnabled) {
          return throwError(() => new Error('AI Boilerplate Generation is disabled'));
        }
        return this.http.post<GenerateBoilerplateResponseInterface>(
          this.AI_URL + '/generate-boilerplate',
          payload
        );
      })
    );
  }

  generateQuestion(payload: unknown): Observable<GenerateQuestionResponseInterface> {
    return this.featureFlagService.getFlag('enableAIQuestionGeneration').pipe(
      switchMap((isEnabled: boolean) => {
        if (!isEnabled) {
          return throwError(() => new Error('AI Question Generation is disabled'));
        }
        return this.http.post<GenerateQuestionResponseInterface>(
          this.AI_URL + '/generate-question',
          payload
        );
      })
    );
  }

  runBulkTestCases(payload: AIToJudgeOPayloadInterface) {
    return this.http.post<BulkTestCasesJudgeOResponse>(
      this.testTakingUrl + '/validate-test-cases',
      payload
    );
  }
}
