import { TestBed } from '@angular/core/testing';
import { FeatureFlagService, FeatureFlagUser } from './feature-flag.service';
import { environment } from '../../environments/environment';

// Mock LaunchDarkly
const mockLDClient = {
  waitForInitialization: jest.fn().mockResolvedValue(undefined),
  variation: jest.fn(),
  identify: jest.fn().mockResolvedValue(undefined),
  on: jest.fn(),
  close: jest.fn().mockResolvedValue(undefined)
};

const mockLaunchDarkly = {
  initialize: jest.fn().mockReturnValue(mockLDClient)
};

// Mock environment
const mockEnvironment = {
  ...environment,
  launchDarklyClientId: 'test-client-id'
};

describe('FeatureFlagService', () => {
  let service: FeatureFlagService;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock LaunchDarkly module
    jest.doMock('launchdarkly-js-client-sdk', () => mockLaunchDarkly);
    
    // Mock environment
    jest.doMock('../../environments/environment', () => ({
      environment: mockEnvironment
    }));

    TestBed.configureTestingModule({
      providers: [FeatureFlagService]
    });
    
    service = TestBed.inject(FeatureFlagService);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('initialization', () => {
    it('should initialize LaunchDarkly client with correct parameters', async () => {
      // Wait for initialization
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(mockLaunchDarkly.initialize).toHaveBeenCalledWith(
        'test-client-id',
        expect.objectContaining({
          key: 'anonymous',
          email: '<EMAIL>',
          name: 'Anonymous User'
        })
      );
    });

    it('should wait for client initialization', async () => {
      await new Promise(resolve => setTimeout(resolve, 100));
      expect(mockLDClient.waitForInitialization).toHaveBeenCalled();
    });

    it('should set up change listener', async () => {
      await new Promise(resolve => setTimeout(resolve, 100));
      expect(mockLDClient.on).toHaveBeenCalledWith('change', expect.any(Function));
    });

    it('should handle initialization failure gracefully', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      mockLDClient.waitForInitialization.mockRejectedValueOnce(new Error('Init failed'));

      // Create new service instance to trigger initialization
      const newService = new FeatureFlagService();
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to initialize LaunchDarkly:',
        expect.any(Error)
      );
      
      consoleSpy.mockRestore();
    });
  });

  describe('updateUser', () => {
    it('should update user context', async () => {
      const user: FeatureFlagUser = {
        key: 'user123',
        email: '<EMAIL>',
        name: 'Test User',
        organizationId: 'org123',
        role: 'admin'
      };

      await service.updateUser(user);
      expect(mockLDClient.identify).toHaveBeenCalledWith(user);
    });

    it('should handle user update failure', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      mockLDClient.identify.mockRejectedValueOnce(new Error('Update failed'));

      const user: FeatureFlagUser = { key: 'user123' };
      await service.updateUser(user);

      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to update user context:',
        expect.any(Error)
      );
      
      consoleSpy.mockRestore();
    });
  });

  describe('getFlag', () => {
    beforeEach(() => {
      // Mock flag variations
      mockLDClient.variation.mockImplementation((flagName: string, defaultValue: boolean) => {
        const flagValues: Record<string, boolean> = {
          'enable-ai-code-generation': true,
          'enable-face-detection': false,
          'enable-dark-theme': true
        };
        return flagValues[flagName] ?? defaultValue;
      });
    });

    it('should return observable with flag value', (done) => {
      service.getFlag('enableAICodeGeneration').subscribe(value => {
        expect(value).toBe(true);
        done();
      });
    });

    it('should return false for unknown flags', (done) => {
      service.getFlag('enableUnknownFeature' as any).subscribe(value => {
        expect(value).toBe(false);
        done();
      });
    });
  });

  describe('getFlagValue', () => {
    it('should return current flag value synchronously', () => {
      // Set up mock flags
      service['flagsSubject'].next({
        enableAICodeGeneration: true,
        enableFaceDetection: false
      });

      expect(service.getFlagValue('enableAICodeGeneration')).toBe(true);
      expect(service.getFlagValue('enableFaceDetection')).toBe(false);
      expect(service.getFlagValue('enableDarkTheme')).toBe(false); // default
    });
  });

  describe('getAllFlags', () => {
    it('should return observable with all flags', (done) => {
      const mockFlags = {
        enableAICodeGeneration: true,
        enableFaceDetection: false,
        enableDarkTheme: true
      };

      service['flagsSubject'].next(mockFlags);

      service.getAllFlags().subscribe(flags => {
        expect(flags).toEqual(mockFlags);
        done();
      });
    });
  });

  describe('close', () => {
    it('should close LaunchDarkly client', async () => {
      await service.close();
      expect(mockLDClient.close).toHaveBeenCalled();
    });

    it('should reset initialization state', async () => {
      await service.close();
      
      service.initialized$.subscribe(initialized => {
        expect(initialized).toBe(false);
      });
    });
  });

  describe('fallback behavior', () => {
    it('should use default flags when LaunchDarkly is not available', async () => {
      // Mock environment without client ID
      const serviceWithoutLD = new FeatureFlagService();
      
      // Mock console.warn
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      
      await new Promise(resolve => setTimeout(resolve, 100));
      
      expect(consoleSpy).toHaveBeenCalledWith(
        'LaunchDarkly client ID not found in environment'
      );
      
      // Should still provide default values
      expect(serviceWithoutLD.getFlagValue('enableFaceDetection')).toBe(true);
      expect(serviceWithoutLD.getFlagValue('enableAICodeGeneration')).toBe(false);
      
      consoleSpy.mockRestore();
    });
  });

  describe('flag categories', () => {
    it('should have correct default values for AI features', () => {
      const aiFlags = [
        'enableAICodeGeneration',
        'enableAITestCaseGeneration',
        'enableAIQuestionGeneration',
        'enableAISolutionGeneration',
        'enableAIBoilerplateGeneration'
      ] as const;

      aiFlags.forEach(flag => {
        expect(service.getFlagValue(flag)).toBe(false);
      });
    });

    it('should have correct default values for proctoring features', () => {
      const proctoringFlags = [
        'enableFaceDetection',
        'enableScreenRecording',
        'enableCameraMonitoring',
        'enableScreenSwitchingDetection',
        'enableIdleTimeTracking',
        'enableWindowViolationTracking'
      ] as const;

      proctoringFlags.forEach(flag => {
        expect(service.getFlagValue(flag)).toBe(true);
      });

      // Audio monitoring should be disabled by default
      expect(service.getFlagValue('enableAudioMonitoring')).toBe(false);
    });

    it('should have correct default values for assessment features', () => {
      const assessmentFlags = [
        'enableIdCapture',
        'enableCandidateCapture',
        'enableScreenCapture',
        'enableSurveyAfterTest',
        'enableHonourCode'
      ] as const;

      assessmentFlags.forEach(flag => {
        expect(service.getFlagValue(flag)).toBe(true);
      });
    });

    it('should have correct default values for UI/UX features', () => {
      expect(service.getFlagValue('enableDarkTheme')).toBe(true);
      expect(service.getFlagValue('enableNewDashboard')).toBe(false);
      expect(service.getFlagValue('enableAdvancedReporting')).toBe(false);
      expect(service.getFlagValue('enableExperimentalFeatures')).toBe(false);
    });
  });
});
