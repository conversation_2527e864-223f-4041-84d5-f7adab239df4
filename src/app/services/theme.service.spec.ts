import { TestBed } from '@angular/core/testing';

import { ThemeService } from './theme.service';
import { FeatureFlagService } from './feature-flag.service';

describe('ThemeService', () => {
  let service: ThemeService;
  let mockFeatureFlagService: jest.Mocked<FeatureFlagService>;

  beforeEach(() => {
    const featureFlagServiceSpy = {
      getFlagValue: jest.fn().mockReturnValue(true) // Enable dark theme by default
    };

    TestBed.configureTestingModule({
      providers: [
        { provide: FeatureFlagService, useValue: featureFlagServiceSpy }
      ]
    });

    service = TestBed.inject(ThemeService);
    mockFeatureFlagService = TestBed.inject(FeatureFlagService) as jest.Mocked<FeatureFlagService>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('toggleTheme', () => {
    it('should toggle between light and dark themes', () => {
      // Set initial theme to light
      service['_theme'].next('light');

      // Spy on methods

      // Call toggleTheme
      service.toggleTheme();

      // Verify theme has been toggled to dark
      expect(service['_theme'].value).toBe('dark');

      // Call toggleTheme again
      service.toggleTheme();

      // Verify theme has been toggled back to light
      expect(service['_theme'].value).toBe('light');
    });

    it('should not toggle to dark theme when feature flag is disabled', () => {
      mockFeatureFlagService.getFlagValue.mockReturnValue(false);

      // Set initial theme to light
      service['_theme'].next('light');
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // Call toggleTheme
      service.toggleTheme();

      // Verify theme remains light
      expect(service['_theme'].value).toBe('light');
      expect(consoleSpy).toHaveBeenCalledWith('Dark theme is disabled by feature flag');
      expect(mockFeatureFlagService.getFlagValue).toHaveBeenCalledWith('enableDarkTheme');

      consoleSpy.mockRestore();
    });

    it('should allow toggling from dark to light even when dark theme is disabled', () => {
      mockFeatureFlagService.getFlagValue.mockReturnValue(false);

      // Set initial theme to dark
      service['_theme'].next('dark');

      // Call toggleTheme
      service.toggleTheme();

      // Verify theme changes to light
      expect(service['_theme'].value).toBe('light');
    });
  });

  describe('getMonacoTheme', () => {
    it('should return vs for light theme', () => {
      // Set the current theme to light
      service['_theme'].next('light');

      const monacoTheme = service.getMonacoTheme();
      expect(monacoTheme).toBe('vs');
    });

    it('should return codeQuestionDark for dark theme', () => {
      // Set the current theme to dark
      service['_theme'].next('dark');

      const monacoTheme = service.getMonacoTheme();
      expect(monacoTheme).toBe('codeQuestionDark');
    });
  });
});
