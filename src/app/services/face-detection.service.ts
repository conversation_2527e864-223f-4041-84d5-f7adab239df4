import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import * as faceapi from 'face-api.js';
import { FeatureFlagService } from './feature-flag.service';

export interface FaceDetectionResult {
  faceDetected: boolean;
  faceCount: number;
  confidence?: number;
  boundingBox?: { x: number; y: number; width: number; height: number };
  landmarks?: Array<{ x: number; y: number }>;
  faceQuality?: 'good' | 'poor'; // Add this
  isRealFace?: boolean; // Add this
  error?: string;
}

@Injectable({ providedIn: 'root' })
export class FaceDetectionService {
  private modelsLoaded = false;
  private readonly loadingSubject = new BehaviorSubject<boolean>(false);
  public readonly loading$ = this.loadingSubject.asObservable();

  constructor(private readonly featureFlagService: FeatureFlagService) {}

  async initializeModels(): Promise<boolean> {
    if (this.modelsLoaded) return true;

    this.loadingSubject.next(true);
    try {
      const MODEL_URL = '/assets/models';

      await faceapi.nets.tinyFaceDetector.loadFromUri(MODEL_URL);
      await faceapi.nets.faceLandmark68TinyNet.loadFromUri(MODEL_URL);

      this.modelsLoaded = true;
      return true;
    } catch (error) {
      console.error('Model load error:', error);
      return false;
    } finally {
      this.loadingSubject.next(false);
    }
  }

  async detectFacesFromImage(
    imageElement: HTMLImageElement | HTMLVideoElement | HTMLCanvasElement
  ): Promise<FaceDetectionResult> {
    // Check if face detection is enabled
    const isFaceDetectionEnabled = this.featureFlagService.getFlagValue('enableFaceDetection');
    if (!isFaceDetectionEnabled) {
      return {
        faceDetected: true,
        faceCount: 1,
        confidence: 1,
        isRealFace: true,
        faceQuality: 'good',
        error: 'Face detection disabled by feature flag',
      };
    }

    if (!this.modelsLoaded) {
      await this.initializeModels();
    }

    try {
      const detectionOptions = new faceapi.TinyFaceDetectorOptions({
        inputSize: 416,
        scoreThreshold: 0.6,
      });

      const detections = await faceapi.detectAllFaces(
        imageElement,
        detectionOptions
      );

      if (!detections || detections.length === 0) {
        return {
          faceDetected: false,
          faceCount: 0,
          confidence: 0,
          isRealFace: false,
          faceQuality: 'poor',
          error: 'No face detected',
        };
      }

      if (detections.length > 1) {
        return {
          faceDetected: true,
          faceCount: detections.length,
          confidence: 0,
          isRealFace: false,
          faceQuality: 'poor',
          error: 'Multiple faces detected',
        };
      }

      const detection = detections[0];
      const confidence = detection.score ?? 0;

      const faceArea = detection.box.width * detection.box.height;
      const faceQuality = faceArea > 10000 ? 'good' : 'poor';

      return {
        faceDetected: true,
        faceCount: 1,
        confidence,
        isRealFace: true,
        faceQuality,
        boundingBox: {
          x: detection.box.x,
          y: detection.box.y,
          width: detection.box.width,
          height: detection.box.height,
        },
      };
    } catch (error) {
      return {
        faceDetected: false,
        faceCount: 0,
        confidence: 0,
        isRealFace: false,
        faceQuality: 'poor',
        error: 'Detection failed: ' + error,
      };
    }
  }
}
