import { TestBed } from '@angular/core/testing';
import { FaceDetectionService } from './face-detection.service';
import { FeatureFlagService } from './feature-flag.service';
import * as faceapi from 'face-api.js';

// Mock face-api.js functions
jest.mock('face-api.js', () => ({
  nets: {
    tinyFaceDetector: {
      loadFromUri: jest.fn(),
    },
    faceLandmark68TinyNet: {
      loadFromUri: jest.fn(),
    },
  },
  TinyFaceDetectorOptions: jest.fn().mockImplementation(() => ({})),
  detectAllFaces: jest.fn(),
}));

describe('FaceDetectionService', () => {
  let service: FaceDetectionService;
  let mockFeatureFlagService: jest.Mocked<FeatureFlagService>;

  beforeEach(() => {
    const featureFlagServiceSpy = {
      getFlagValue: jest.fn().mockReturnValue(true) // Enable face detection by default
    };

    TestBed.configureTestingModule({
      providers: [
        { provide: FeatureFlagService, useValue: featureFlagServiceSpy }
      ]
    });

    service = TestBed.inject(FaceDetectionService);
    mockFeatureFlagService = TestBed.inject(FeatureFlagService) as jest.Mocked<FeatureFlagService>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('initializeModels', () => {
    it('should load models successfully', async () => {
      (
        faceapi.nets.tinyFaceDetector.loadFromUri as jest.Mock
      ).mockResolvedValue(true);
      (
        faceapi.nets.faceLandmark68TinyNet.loadFromUri as jest.Mock
      ).mockResolvedValue(true);

      const result = await service.initializeModels();

      expect(result).toBe(true);
      expect(faceapi.nets.tinyFaceDetector.loadFromUri).toHaveBeenCalled();
      expect(faceapi.nets.faceLandmark68TinyNet.loadFromUri).toHaveBeenCalled();
    });

    it('should handle model load failure', async () => {
      (
        faceapi.nets.tinyFaceDetector.loadFromUri as jest.Mock
      ).mockRejectedValue('load error');

      const result = await service.initializeModels();

      expect(result).toBe(false);
    });
  });

  describe('detectFacesFromImage', () => {
    const dummyElement = {} as HTMLImageElement;

    beforeEach(async () => {
      // Pretend models are already loaded
      service['modelsLoaded'] = true;
    });

    it('should return no face detected', async () => {
      (faceapi.detectAllFaces as jest.Mock).mockResolvedValue([]);

      const result = await service.detectFacesFromImage(dummyElement);

      expect(result.faceDetected).toBe(false);
      expect(result.faceCount).toBe(0);
      expect(result.error).toContain('No face detected');
    });

    it('should return error if multiple faces are detected', async () => {
      const mockDetections = [
        { score: 0.9, box: { x: 10, y: 10, width: 100, height: 100 } },
        { score: 0.85, box: { x: 120, y: 10, width: 90, height: 90 } },
      ];
      (faceapi.detectAllFaces as jest.Mock).mockResolvedValue(mockDetections);

      const result = await service.detectFacesFromImage(dummyElement);

      expect(result.faceDetected).toBe(true);
      expect(result.faceCount).toBe(2);
      expect(result.isRealFace).toBe(false);
      expect(result.error).toContain('Multiple faces detected');
    });

    it('should return one good face detected', async () => {
      const detection = {
        score: 0.95,
        box: { x: 50, y: 50, width: 120, height: 120 },
      };
      (faceapi.detectAllFaces as jest.Mock).mockResolvedValue([detection]);

      const result = await service.detectFacesFromImage(dummyElement);

      expect(result.faceDetected).toBe(true);
      expect(result.faceCount).toBe(1);
      expect(result.confidence).toBeCloseTo(0.95);
      expect(result.faceQuality).toBe('good');
      expect(result.boundingBox).toEqual({
        x: 50,
        y: 50,
        width: 120,
        height: 120,
      });
    });

    it('should handle detection error', async () => {
      (faceapi.detectAllFaces as jest.Mock).mockRejectedValue(
        'Detection crash'
      );

      const result = await service.detectFacesFromImage(dummyElement);

      expect(result.faceDetected).toBe(false);
      expect(result.error).toContain('Detection failed');
    });

    it('should return success when face detection is disabled by feature flag', async () => {
      mockFeatureFlagService.getFlagValue.mockReturnValue(false);
      const dummyElement = document.createElement('img');

      const result = await service.detectFacesFromImage(dummyElement);

      expect(result.faceDetected).toBe(true);
      expect(result.faceCount).toBe(1);
      expect(result.confidence).toBe(1);
      expect(result.isRealFace).toBe(true);
      expect(result.faceQuality).toBe('good');
      expect(result.error).toBe('Face detection disabled by feature flag');
      expect(mockFeatureFlagService.getFlagValue).toHaveBeenCalledWith('enableFaceDetection');
    });
  });
});
