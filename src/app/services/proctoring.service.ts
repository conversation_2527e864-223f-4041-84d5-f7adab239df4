import { Injectable } from '@angular/core';
import { TestTakingService } from './test-taking-service/test-taking.service';
import { FaceDetectionService } from './face-detection.service';
import * as CryptoJS from 'crypto-js';

import { Buffer } from 'buffer';
import {
  catchError,
  distinctUntilChanged,
  EMPTY,
  exhaustMap,
  interval,
  map,
  of,
  Subject,
  Subscription,
} from 'rxjs';
import { ToastService } from './toast-service/toast.service';
import { ViolationScreenShot } from '../Interfaces/Types/testTakerInterface';
import axios from 'axios';
import { HttpClient } from '@angular/common/http';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from '../../environments/environment';
import {
  CameraErrorMessages,
  getCameraError,
} from '../utils/testTakerConstants';
import { isInternetAvailable } from '../utils/constants';
import { FeatureFlagService } from './feature-flag.service';

@Injectable({
  providedIn: 'root',
})
export class ProctoringService {
  organizationId: string | undefined;
  assessmentName: string | undefined;
  testTakerEmail: string | undefined;
  testTakerId: string | undefined;
  private readonly tabStatusSource = new Subject<boolean>();
  tabStatus$ = this.tabStatusSource.asObservable();
  public screenStream: MediaStream | null = null;
  public testTakerStream: MediaStream | null = null;
  private readonly denialModalStateSource = new Subject<boolean>();
  public denialModalState$ = this.denialModalStateSource.asObservable();
  myCountState: boolean = true;
  private readonly clearTimeRemaining = new Subject<boolean>();
  public clearTimeRemaining$ = this.clearTimeRemaining.asObservable();
  violateSnapsScreen: ViolationScreenShot[] = [];
  violateSnapsTaker: ViolationScreenShot[] = [];
  private isSharingInProgress = false;
  private readonly baseUrl = `${environment.apiBaseUrl}`;

  keyType = {
    Id: 'id-shot',
    headshot: 'head-shot',
    screen: 'screen-monitoring',
    candidate: 'candidate-monitoring',
  };

  constructor(
    private readonly testTakerService: TestTakingService,
    private readonly toastService: ToastService,
    private readonly _http: HttpClient,
    private readonly route: ActivatedRoute,
    private readonly faceDetectionService: FaceDetectionService,
    private readonly router: Router,
    private readonly featureFlagService: FeatureFlagService
  ) {
    this.organizationId =
      this.testTakerService.testTakerApiData$.value?.assessmentTaker.organizationId;
    this.assessmentName =
      this.testTakerService.testTakerApiData$.value?.assessmentTaker.assessmentName;
    this.testTakerEmail =
      this.testTakerService.testTakerApiData$.value?.assessmentTaker.email;
    this.route.queryParams.subscribe((params) => {
      this.testTakerId = params['testTakerId'];
    });
    this.initializeTabDetection();
  }

  public async takerShotsUpload(base64Image: string, imageName: string) {
    const imageType = this.keyType[imageName as keyof typeof this.keyType];

    const buffer = Buffer.from(
      base64Image.replace(/^data:image\/\w+;base64,/, ''),
      'base64'
    );
    const imageExtension = base64Image.split(';')[0].split('/')[1];
    const mimeType = `image/${imageExtension}`;

    return this.systemImageHandling(mimeType, imageType, buffer);
  }

  /**
   * Enhanced version of takerShotsUpload with face detection
   */
  public async takerShotsUploadWithFaceDetection(
    base64Image: string,
    imageName: string
  ) {
    try {
      return this.takerShotsUpload(base64Image, imageName);
    } catch (error) {
      console.error('Face detection error:', error);
      this.toastService.onShow(
        'Face Detection Error',
        'Failed to verify face in image. Proceeding with upload.',
        true,
        'warning'
      );

      // Fallback to normal upload if face detection fails
      return this.takerShotsUpload(base64Image, imageName);
    }
  }

  public async uploadFile(file: File) {
    const mimeType = file.type;

    return this.systemImageHandling(mimeType, this.keyType['Id'], file);
  }

  async systemImageHandling(
    mimeType: string,
    imageType: string,
    base64Image: File | string | Buffer
  ) {
    try {
      const isOnline = await isInternetAvailable();

      if (!isOnline) {
        return EMPTY;
      }

      const contentMD5 = await this.computeMD5Base64(
        base64Image as Blob | File
      );

      const getPresignedResponse = this.getPresignedUrl(
        mimeType,
        imageType,
        contentMD5
      );

      return getPresignedResponse.pipe(
        exhaustMap((response) => {
          const bucketImagelocation = response.data.location;
          return this.uploadImageToS3(
            { presignedUrl: response.data.url, headers: response.data.headers },
            base64Image as Blob | File
          ).then(() => bucketImagelocation);
        }),
        map((bucketImagelocation) => bucketImagelocation),
        catchError((err) => {
          console.error('Upload failed:', err);
          return of('UPLOAD_FAILED');
        })
      );
    } catch (error) {
      console.error('Failed to handle system image:', error);
      return of('SYSTEM_ERROR');
    }
  }

  getPresignedUrl(mimeType: string, imageType: string, contentMD5: string) {
    mimeType = mimeType.split('/').includes('image')
      ? mimeType
      : `image/${mimeType}`;

    return this._http.post<{
      success: boolean;
      data: {
        url: string;
        location: string;
        headers: {
          'Content-Type': string;
          'Content-MD5': string;
          'x-amz-object-lock-mode': string;
          'x-amz-object-lock-retain-until-date': string;
        };
      };
    }>(
      `${this.baseUrl}/test-taking/assessment/${this.testTakerId}/presigned-url`,
      {
        keyType: imageType,
        mimeType: mimeType,
        contentMD5: contentMD5,
      }
    );
  }

  async computeMD5Base64(file: Blob | File | Buffer | string): Promise<string> {
    try {
      let wordArray;
      if (file instanceof Blob || file instanceof File) {
        const arrayBuffer = await file.arrayBuffer();
        wordArray = CryptoJS.lib.WordArray.create(arrayBuffer);
      } else if (file instanceof Buffer) {
        wordArray = CryptoJS.lib.WordArray.create(file);
      } else if (typeof file === 'string') {
        wordArray = CryptoJS.enc.Utf8.parse(file);
      } else {
        throw new Error('Unsupported input type for MD5 hash computation.');
      }

      const hash = CryptoJS.MD5(wordArray);
      return CryptoJS.enc.Base64.stringify(hash);
    } catch (error) {
      throw new Error(`Failed to compute MD5 hash: ${error}`);
    }
  }

  async uploadImageToS3(
    data: {
      presignedUrl: string;
      headers: {
        'Content-Type': string;
        'Content-MD5': string;
        'x-amz-object-lock-mode': string;
        'x-amz-object-lock-retain-until-date': string;
      };
    },
    imageFile: File | string | Blob
  ) {
    await axios.put(data.presignedUrl, imageFile, {
      headers: data.headers,
    });
  }

  private initializeTabDetection(): void {
    let blurTimeout: NodeJS.Timeout | null = null;
    let isRealViolation = false;

    window.addEventListener('focus', () => {
      if (blurTimeout) {
        clearTimeout(blurTimeout);
        blurTimeout = null;
      }
      if (isRealViolation) {
        this.tabStatusSource.next(true);
        isRealViolation = false;
      }
    });

    window.addEventListener('blur', () => {
      blurTimeout = setTimeout(() => {
        const hasActiveModals =
          document.querySelectorAll(
            '.modal, .dropdown-menu, .overlay, [role="dialog"], .toast, .notification'
          ).length > 0;
        const documentHasFocus = document.hasFocus && document.hasFocus();

        if (hasActiveModals || documentHasFocus) {
          blurTimeout = null;
          return;
        }

        if (document.hidden || !document.hasFocus()) {
          isRealViolation = true;
          this.tabStatusSource.next(false);
        }
        blurTimeout = null;
      }, 150);
    });

    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        if (blurTimeout) {
          clearTimeout(blurTimeout);
          blurTimeout = null;
        }
        isRealViolation = true;
        this.tabStatusSource.next(false);
      } else {
        if (isRealViolation) {
          this.tabStatusSource.next(true);
          isRealViolation = false;
        }
      }
    });
  }

  public async shareScreen(): Promise<void> {
    // Check if screen recording is enabled
    const isScreenRecordingEnabled = this.featureFlagService.getFlagValue('enableScreenRecording');
    if (!isScreenRecordingEnabled) {
      console.log('Screen recording is disabled by feature flag');
      return;
    }

    if (this.isSharingInProgress) {
      console.warn('shareScreen() was already called.');
      return;
    }

    this.isSharingInProgress = true;

    try {
      // Stop any old stream
      this.stopShotsStream();

      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: { displaySurface: 'monitor' },
        audio: false,
      });

      this.screenStream = stream;
      this.denialModalStateSource.next(false);

      // Add handler to reset when screen sharing ends
      stream.getVideoTracks()[0].onended = () => {
        console.warn('[ProctoringService] screen sharing ended');
        this.isSharingInProgress = false;
        this.toastService.onShow(
          'Screen Sharing Ended',
          'You stopped screen sharing. Please re-share or the test will be terminated.',
          true,
          'warning'
        );
        this.router.navigate(['/test-terminated']);
      };
    } catch (error) {
      console.error('[ProctoringService] Screen sharing failed:', error);
      this.denialModalStateSource.next(true);
      this.toastService.onShow(
        'Screen Sharing Denied',
        'Screen sharing was denied. Please allow screen sharing to continue.',
        true,
        'error'
      );
    } finally {
      // Allow retry if it failed
      if (!this.screenStream) {
        this.isSharingInProgress = false;
      }
    }
  }

  public openCamera(): Promise<void> {
    return new Promise((resolve) => {
      if (navigator.mediaDevices.getUserMedia) {
        navigator.mediaDevices
          .getUserMedia({
            video: true,
            audio: false,
          })
          .then((stream: MediaStream) => {
            this.testTakerStream = stream;
            resolve();
          })
          .catch((err) => {
            const errorDetails = getCameraError(err.message);
            this.toastService.onShow(
              errorDetails.header,
              errorDetails.notice,
              true,
              'error'
            );
            resolve();
          });
      } else {
        const errorDetails = CameraErrorMessages.notSupported;
        this.toastService.onShow(
          errorDetails.header,
          errorDetails.notice,
          true,
          'error'
        );
      }
    });
  }

  public takeScreenshot() {
    if (!this.screenStream) {
      return null;
    }
    const videoElement = document.createElement('video');
    videoElement.srcObject = this.screenStream;
    videoElement.autoplay = true;
    videoElement.style.position = 'fixed';
    videoElement.style.left = '0';
    videoElement.style.top = '0';
    videoElement.style.width = '100%';
    videoElement.style.height = '100%';
    videoElement.style.zIndex = '999999';
    videoElement.style.pointerEvents = 'none';

    return new Promise<string>((resolve) => {
      videoElement.onloadedmetadata = () => {
        const canvas = document.createElement('canvas');
        canvas.width = videoElement.videoWidth;
        canvas.height = videoElement.videoHeight;
        const ctx = canvas.getContext('2d');
        ctx?.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
        const screenshotDataUrl = canvas.toDataURL(`image/png`);
        resolve(screenshotDataUrl);
      };
    });
  }

  testTakerCapture() {
    if (!this.testTakerStream) {
      return null;
    }
    const takerElement = document.createElement('video');
    takerElement.srcObject = this.testTakerStream;
    takerElement.autoplay = true;
    takerElement.style.position = 'fixed';
    takerElement.style.left = '0';
    takerElement.style.top = '0';
    takerElement.style.width = '100%';
    takerElement.style.height = '100%';
    takerElement.style.zIndex = '999999';
    takerElement.style.pointerEvents = 'none';

    return new Promise<string>((resolve) => {
      takerElement.onloadedmetadata = () => {
        const canvas = document.createElement('canvas');
        canvas.width = takerElement.videoWidth;
        canvas.height = takerElement.videoHeight;
        const ctx = canvas.getContext('2d');
        ctx?.drawImage(takerElement, 0, 0);
        const screenshotDataUrl = canvas.toDataURL(`image/png`);
        resolve(screenshotDataUrl);
      };
    });
  }
  isClearedStoredTime(state: boolean) {
    this.clearTimeRemaining.next(state);
  }

  setCountState(state: boolean) {
    this.myCountState = state;
  }
  getCountState() {
    return this.myCountState;
  }
  stopShotsStream() {
    this.screenStream?.getTracks().forEach((track) => track.stop());
    this.testTakerStream?.getTracks().forEach((track) => track.stop());
  }

  trackStream(
    intervalMilliseconds: number,
    stateAsserters: {
      streamGetter: () => boolean | undefined;
      captureGetter?: () => boolean | undefined;
    },
    callback: (isActive: boolean) => void
  ): Subscription {
    return interval(intervalMilliseconds)
      .pipe(
        map(() => ({
          isActive: stateAsserters.streamGetter(),
          isImageCaptured: stateAsserters.captureGetter
            ? stateAsserters.captureGetter()
            : undefined,
        })),
        distinctUntilChanged(
          (prev, curr) =>
            prev.isActive === curr.isActive &&
            prev.isImageCaptured === curr.isImageCaptured
        )
      )
      .subscribe(({ isActive }) => callback(isActive as boolean));
  }
}
