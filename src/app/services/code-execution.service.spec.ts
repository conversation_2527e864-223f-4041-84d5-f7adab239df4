import { TestBed } from '@angular/core/testing';
import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';

import { CodeExecutionService } from './code-execution.service';
import {
  AIToJudgeOPayloadInterface,
  APIAutoGenerateTestCasePayloadInterface,
  APIAutoGenerateTestCaseResponseInterface,
  BulkTestCasesJudgeOResponse,
  CodeExecutionResponse,
  GenerateBoilerplateResponseInterface,
  GenerateQuestionResponseInterface,
  GenerateSolutionPayloadInterface,
  GenerateSolutionResponseInterface,
} from '../Interfaces/codingTypeQuestionsInterface';
import { environment } from '../../environments/environment';

describe('CodeExecutionService', () => {
  let service: CodeExecutionService;
  let httpMock: HttpTestingController;

  const testTakingUrl = `${environment.apiBaseUrl}/test-taking`;
  const AI_URL = `${environment['AI_BaseUrl']}/api/v1/assessment`;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
    });
    service = TestBed.inject(CodeExecutionService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('runCode', () => {
    it('should execute code with correct payload', () => {
      const mockResponse: CodeExecutionResponse = {
        success: true,
        data: { output: 'Hello World' },
      };

      const functionCode = 'console.log("Hello World");';
      const input = '';
      const languageId = 63; // JavaScript

      service.runCode(functionCode, input, languageId).subscribe((response) => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${testTakingUrl}/run-test-case`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual({
        code: functionCode,
        input: input,
        language_id: languageId,
      });

      req.flush(mockResponse);
    });

    it('should handle different language IDs', () => {
      const mockResponse: CodeExecutionResponse = {
        success: true,
        data: { output: 'Python output' },
      };

      const functionCode = 'print("Hello Python")';
      const input = 'test input';
      const languageId = 71; // Python

      service.runCode(functionCode, input, languageId).subscribe((response) => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${testTakingUrl}/run-test-case`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual({
        code: functionCode,
        input: input,
        language_id: languageId,
      });

      req.flush(mockResponse);
    });
  });

  describe('generateTestCases', () => {
    it('should generate test cases with correct payload', () => {
      const mockResponse: APIAutoGenerateTestCaseResponseInterface[] = [
        {
          input: ['test input 1'],
          expected_output: 'expected output 1',
          description: 'Test case 1',
          hidden: false,
        },
        {
          input: ['test input 2'],
          expected_output: 'expected output 2',
          description: 'Test case 2',
          hidden: false,
        },
      ];

      const payload: APIAutoGenerateTestCasePayloadInterface = {
        problem_definition: 'Write a function to add two numbers',
        reference_solution: 'function add(a, b) { return a + b; }',
        language: 'javascript',
      };

      service.generateTestCases(payload).subscribe((response) => {
        expect(response).toEqual(mockResponse);
        expect(response.length).toBe(2);
      });

      const req = httpMock.expectOne(`${AI_URL}/generate-tests`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(payload);

      req.flush(mockResponse);
    });
  });

  describe('generateSolutionCode', () => {
    it('should generate solution code with correct payload', () => {
      const mockResponse: GenerateSolutionResponseInterface = {
        solution: 'function add(a, b) { return a + b; }',
      };

      const payload: GenerateSolutionPayloadInterface = {
        problem_definition: 'Write a function to add two numbers',
        examples: [
          { input: '1, 2', expectedOutput: '3' },
          { input: '5, 7', expectedOutput: '12' },
        ],
        language: 'javascript',
      };

      service.generateSolutionCode(payload).subscribe((response) => {
        expect(response).toEqual(mockResponse);
        expect(response.solution).toContain('function add');
      });

      const req = httpMock.expectOne(`${AI_URL}/generate-reference`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(payload);

      req.flush(mockResponse);
    });
  });

  describe('generateBoilerplateCode', () => {
    it('should generate boilerplate code with correct payload', () => {
      const mockResponse: GenerateBoilerplateResponseInterface = {
        boilerplate: 'function add(a, b) {\n  // Your code here\n}',
      };

      const payload: GenerateSolutionPayloadInterface = {
        problem_definition: 'Write a function to add two numbers',
        language: 'javascript',
      };

      service.generateBoilerplateCode(payload).subscribe((response) => {
        expect(response).toEqual(mockResponse);
        expect(response.boilerplate).toContain('function add');
      });

      const req = httpMock.expectOne(`${AI_URL}/generate-boilerplate`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(payload);

      req.flush(mockResponse);
    });
  });

  describe('generateQuestion', () => {
    it('should generate question with correct payload', () => {
      const mockResponse: GenerateQuestionResponseInterface = {
        question_definition: 'Write a function that adds two numbers',
        reference_solution: 'function add(a, b) { return a + b; }',
        boilerplate: 'function add(a, b) {}',
        test_cases: [],
      };

      const payload = {
        topic: 'basic arithmetic',
        difficulty: 'easy',
        language: 'javascript',
      };

      service.generateQuestion(payload).subscribe((response) => {
        expect(response).toEqual(mockResponse);
        expect(response.question_definition).toBeTruthy();
      });

      const req = httpMock.expectOne(`${AI_URL}/generate-question`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(payload);

      req.flush(mockResponse);
    });

    it('should handle unknown payload type', () => {
      const mockResponse: GenerateQuestionResponseInterface = {
        question_definition: '',
        reference_solution: '',
        boilerplate: '',
        test_cases: [],
      };

      const payload = { customField: 'custom value' };

      service.generateQuestion(payload).subscribe((response) => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${AI_URL}/generate-question`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(payload);

      req.flush(mockResponse);
    });
  });

  describe('runBulkTestCases', () => {
    it('should run bulk test cases with correct payload', () => {
      const mockResponse: BulkTestCasesJudgeOResponse = {
        success: true,
        data: {
          validatedTestCases: [
            {
              input: '1, 2',
              expected_output: '3',
              description: 'Test case 1',
              hidden: false,
            },
            {
              input: '5, 7',
              expected_output: '12',
              description: 'Test case 2',
              hidden: false,
            },
          ],
        },
      };

      const payload: AIToJudgeOPayloadInterface = {
        code: 'function add(a, b) { return a + b; }',
        language_id: 63,
        testCases: [
          {
            input: '1, 2',
            expected_output: '3',
            description: 'Test case 1',
            hidden: false,
          },
          {
            input: '5, 7',
            expected_output: '12',
            description: 'Test case 2',
            hidden: false,
          },
        ],
      };

      service.runBulkTestCases(payload).subscribe((response) => {
        expect(response).toEqual(mockResponse);
        expect(response.success).toBe(true);
        expect(response.data.validatedTestCases.length).toBe(2);
      });

      const req = httpMock.expectOne(`${testTakingUrl}/validate-test-cases`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(payload);

      req.flush(mockResponse);
    });
  });
});
