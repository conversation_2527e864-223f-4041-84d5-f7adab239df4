import { TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { ActivatedRouteSnapshot } from '@angular/router';
import { of, throwError } from 'rxjs';
import { FeatureFlagGuard } from './feature-flag.guard';
import { FeatureFlagService } from './feature-flag.service';

describe('FeatureFlagGuard', () => {
  let guard: FeatureFlagGuard;
  let mockFeatureFlagService: jest.Mocked<FeatureFlagService>;
  let mockRouter: jest.Mocked<Router>;

  beforeEach(() => {
    const featureFlagServiceSpy = {
      getFlag: jest.fn()
    };

    const routerSpy = {
      navigate: jest.fn()
    };

    TestBed.configureTestingModule({
      providers: [
        FeatureFlagGuard,
        { provide: FeatureFlagService, useValue: featureFlagServiceSpy },
        { provide: Router, useValue: routerSpy }
      ]
    });

    guard = TestBed.inject(FeatureFlagGuard);
    mockFeatureFlagService = TestBed.inject(FeatureFlagService) as jest.Mocked<FeatureFlagService>;
    mockRouter = TestBed.inject(Router) as jest.Mocked<Router>;
  });

  it('should be created', () => {
    expect(guard).toBeTruthy();
  });

  describe('canActivate', () => {
    let route: ActivatedRouteSnapshot;

    beforeEach(() => {
      route = new ActivatedRouteSnapshot();
    });

    it('should allow access when feature flag is enabled', (done) => {
      route.data = { featureFlag: 'enableAICodeGeneration' };
      mockFeatureFlagService.getFlag.mockReturnValue(of(true));

      guard.canActivate(route).subscribe(result => {
        expect(result).toBe(true);
        expect(mockRouter.navigate).not.toHaveBeenCalled();
        done();
      });
    });

    it('should deny access when feature flag is disabled', (done) => {
      route.data = { featureFlag: 'enableAICodeGeneration' };
      mockFeatureFlagService.getFlag.mockReturnValue(of(false));

      guard.canActivate(route).subscribe(result => {
        expect(result).toBe(false);
        expect(mockRouter.navigate).toHaveBeenCalledWith(['/not-found']);
        done();
      });
    });

    it('should redirect to custom route when specified', (done) => {
      route.data = { 
        featureFlag: 'enableAICodeGeneration',
        redirectTo: '/custom-redirect'
      };
      mockFeatureFlagService.getFlag.mockReturnValue(of(false));

      guard.canActivate(route).subscribe(result => {
        expect(result).toBe(false);
        expect(mockRouter.navigate).toHaveBeenCalledWith(['/custom-redirect']);
        done();
      });
    });

    it('should allow access when no feature flag is specified', (done) => {
      route.data = {}; // No featureFlag specified
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      guard.canActivate(route).subscribe(result => {
        expect(result).toBe(true);
        expect(consoleSpy).toHaveBeenCalledWith(
          'FeatureFlagGuard: No feature flag specified in route data'
        );
        expect(mockRouter.navigate).not.toHaveBeenCalled();
        
        consoleSpy.mockRestore();
        done();
      });
    });

    it('should allow access on service error', (done) => {
      route.data = { featureFlag: 'enableAICodeGeneration' };
      mockFeatureFlagService.getFlag.mockReturnValue(
        throwError(() => new Error('Service error'))
      );
      
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      guard.canActivate(route).subscribe(result => {
        expect(result).toBe(true);
        expect(consoleSpy).toHaveBeenCalledWith(
          'FeatureFlagGuard error:',
          expect.any(Error)
        );
        expect(mockRouter.navigate).not.toHaveBeenCalled();
        
        consoleSpy.mockRestore();
        done();
      });
    });

    it('should handle different feature flag types', (done) => {
      const testCases = [
        'enableFaceDetection',
        'enableDarkTheme',
        'enableAdvancedReporting'
      ] as const;

      let completedTests = 0;
      
      testCases.forEach(flagName => {
        route.data = { featureFlag: flagName };
        mockFeatureFlagService.getFlag.mockReturnValue(of(true));

        guard.canActivate(route).subscribe(result => {
          expect(result).toBe(true);
          expect(mockFeatureFlagService.getFlag).toHaveBeenCalledWith(flagName);
          
          completedTests++;
          if (completedTests === testCases.length) {
            done();
          }
        });
      });
    });

    it('should take only the first emission from the flag service', (done) => {
      route.data = { featureFlag: 'enableAICodeGeneration' };
      
      // Create a subject that emits multiple values
      const flagSubject = new BehaviorSubject(true);
      mockFeatureFlagService.getFlag.mockReturnValue(flagSubject.asObservable());

      let emissionCount = 0;
      
      guard.canActivate(route).subscribe(result => {
        emissionCount++;
        expect(result).toBe(true);
        
        // Emit another value - this should not trigger another subscription
        flagSubject.next(false);
        
        // Wait a bit and check that we only got one emission
        setTimeout(() => {
          expect(emissionCount).toBe(1);
          done();
        }, 50);
      });
    });
  });

  describe('integration scenarios', () => {
    let route: ActivatedRouteSnapshot;

    beforeEach(() => {
      route = new ActivatedRouteSnapshot();
    });

    it('should work with AI feature flags', (done) => {
      route.data = { featureFlag: 'enableAIQuestionGeneration' };
      mockFeatureFlagService.getFlag.mockReturnValue(of(false));

      guard.canActivate(route).subscribe(result => {
        expect(result).toBe(false);
        expect(mockFeatureFlagService.getFlag).toHaveBeenCalledWith('enableAIQuestionGeneration');
        done();
      });
    });

    it('should work with proctoring feature flags', (done) => {
      route.data = { featureFlag: 'enableFaceDetection' };
      mockFeatureFlagService.getFlag.mockReturnValue(of(true));

      guard.canActivate(route).subscribe(result => {
        expect(result).toBe(true);
        expect(mockFeatureFlagService.getFlag).toHaveBeenCalledWith('enableFaceDetection');
        done();
      });
    });

    it('should work with UI feature flags', (done) => {
      route.data = { 
        featureFlag: 'enableNewDashboard',
        redirectTo: '/old-dashboard'
      };
      mockFeatureFlagService.getFlag.mockReturnValue(of(false));

      guard.canActivate(route).subscribe(result => {
        expect(result).toBe(false);
        expect(mockRouter.navigate).toHaveBeenCalledWith(['/old-dashboard']);
        done();
      });
    });
  });
});

// Import BehaviorSubject for the test
import { BehaviorSubject } from 'rxjs';
