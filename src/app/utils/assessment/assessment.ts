import { QuestionsData } from '../../Interfaces/questionInterface';
import {
  AssessmentWithTest,
  BasicAndConfig,
  BasicInfoWithTest,
  GetOneAssessmentApiResponse,
  ProctorFeatures,
} from '../../Interfaces/Types/assessmentInterface';
import { TestsData } from '../../Interfaces/Types/test';
import { LoginUserInfo } from '../../services/authServiceInterfaces';
import { Router } from '@angular/router';
import { generateUUID } from '../constants';

// Note: This configuration will be dynamically filtered based on feature flags
// Use getFilteredConfigurations() function instead of directly accessing this array
export const configurations = [
  {
    name: 'An Honour Code',
    value: 'Honour Code',
    selected: true,
    locked: true,
    featureFlag: 'enableHonourCode',
  },
  {
    name: 'ID Capture',
    value: 'ID Capture',
    selected: false,
    locked: false,
    featureFlag: 'enableIdCapture',
  },
  {
    name: 'Candidate Capture',
    value: 'Candidate Capture',
    selected: false,
    locked: false,
    featureFlag: 'enableCandidateCapture',
  },
  {
    name: 'Screen Capture',
    value: 'Screen Capture',
    selected: false,
    locked: false,
    featureFlag: 'enableScreenCapture',
  },
  {
    name: 'Track Idle Time',
    value: 'Idle Time Tracking',
    selected: false,
    locked: false,
    featureFlag: 'enableIdleTimeTracking',
  },
  {
    name: 'Track Window Violation',
    value: 'Window Violation',
    selected: false,
    locked: false,
    featureFlag: 'enableWindowViolationTracking',
  },
  {
    name: 'Enable Survey After Test',
    value: 'Enable Survey After Test',
    selected: false,
    locked: false,
    featureFlag: 'enableSurveyAfterTest',
  },
];

// Function to get filtered configurations based on feature flags
export function getFilteredConfigurations(featureFlagService: any) {
  return configurations.filter(config => {
    if (!config.featureFlag) return true;
    return featureFlagService.getFlagValue(config.featureFlag);
  });
}

export function mapToBackendFormat(uiFormat: string): string {
  switch (uiFormat) {
    case 'An Honour Code':
      return 'Honour Code';
    case 'Track Idle Time':
      return 'Idle Time Tracking';
    case 'Track Window Violation':
      return 'Window Violation';
    default:
      return uiFormat;
  }
}
export function getTimeDifferenceInUnits(diffInMilliseconds: number) {
  const seconds = Math.floor(diffInMilliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  const months = Math.floor(days / 30);
  const years = Math.floor(days / 365);

  return { seconds, minutes, hours, days, months, years };
}

function formatTimeAgo(unit: number, timeUnit: string): string {
  return `${unit} ${timeUnit}${unit === 1 ? '' : 's'} ago`;
}

export function timeAgo(dateString: string): string {
  const now = new Date();
  const inputDate = new Date(dateString);
  const diffInMilliseconds = Math.max(0, now.getTime() - inputDate.getTime());

  const { seconds, minutes, hours, days, months, years } =
    getTimeDifferenceInUnits(diffInMilliseconds);

  if (seconds < 5) {
    return 'just now';
  }
  if (seconds < 60) {
    return formatTimeAgo(seconds, 'second');
  }
  if (minutes < 60) {
    return formatTimeAgo(minutes, 'minute');
  }
  if (hours < 24) {
    return formatTimeAgo(hours, 'hour');
  }
  if (days < 30) {
    return formatTimeAgo(days, 'day');
  }
  if (months < 12) {
    return formatTimeAgo(months, 'month');
  }
  return formatTimeAgo(years, 'year');
}

export function processConfigurations(
  configurations: string[],
  settings: { showClock: boolean; conductSurvey: boolean }
) {
  const processedConfigurations = configurations.filter((config) => {
    if (config === 'Display Clock') {
      settings.showClock = true;
      return false;
    }
    if (config === 'Enable Survey After Test') {
      settings.conductSurvey = true;
      return false;
    }
    return true;
  });

  return { ...settings, proctorFeatures: processedConfigurations };
}

export function checkMinDate() {
  const today = new Date();
  const yyyy = today.getFullYear();
  const mm = String(today.getMonth() + 1).padStart(2, '0');
  const dd = String(today.getDate()).padStart(2, '0');
  return `${yyyy}-${mm}-${dd}`;
}

export const proctoringDetails = [
  'Require candidates to agree to an honour code before starting',
  "Capture candidate's ID for verification",
  'Take periodic photos of the candidate during the test',
  "Take periodic screenshots of the candidate's screen",
  'Monitor when candidate is inactive or away from the test',
  'Detect when candidate switches away from the test window',
  'Show a feedback survey to candidates after test completion',
];

export const mockQuestionsData: QuestionsData = {
  correctAnswers: ['option1', 'option3'],
  selectedAnswerIndex: 1,
  answerOptions: ['option1', 'option2', 'option3'],
  id: 'question1',
  questionText: 'What are the capital cities of Ghana and Nigeria?',
  questionType: 'multipleSelect',
  score: 2,
  system: false,
  timeLimit: '10',
  difficultyLevel: { name: 'Medium', value: '2' },
  multipleSelectAnswer: {
    id: 'multipleSelect1',
    questionId: 'question1',
    options: ['option1', 'option3'],
    answer: ['option1'],
  },
  fillInAnswer: {
    options: [
      {
        id: 'aa294d31-0103-4d2d-96a8-c5e049b8815a',
        fillInAnswerId: 'ac6679c8-7fd0-40df-a194-90b032023a78',
        blank: '702717a7-44ce-49f9-82c2-5b104614f6c4',
        value: 'purporses',
      },
    ],
    answer: ['purporses', 'testing'],
  },
  essayAnswer: {
    rubrics:
      'The essay should demonstrate a clear understanding of the topic and provide relevant examples.',
  },
  strictMark: { name: 'Yes', value: 'true' },
  isActive: true,
  category: {
    id: 'category1',
    name: 'Geography',
  },
  domain: {
    id: 'domain1',
    name: 'Geography',
  },
};
export const mockTestData: TestsData = {
  id: 'test-1',
  title: 'Test Title',
  questions: [],
  categoryId: 'category-1',
  createdAt: new Date().toISOString(),
  description: 'Test Description',
  difficultyLevel: 'Easy',
  domainId: 'domain-1',
  duration: 30,
  instructions: 'Test Instructions',
  isActivated: true,
  isActive: true,
  organizationId: 'org-1',
  passage: 'Test Passage',
  passMark: 50,
  system: false,
  testManagerId: 'manager-1',
  type: 'MCQ',
  updatedAt: new Date().toISOString(),
  domain: {
    id: 'domain-1',
    name: 'Domain Name',
    description: 'Domain Description',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    system: false,
    organizationId: 'org-1',
  },
};
export const dummyProctorFeatures: ProctorFeatures[] = [
  {
    id: 'feature-1',
    name: 'Live Proctoring',
    description:
      'A live proctor will monitor the student during the assessment.',
    createdAt: '2024-10-17T07:36:22Z',
    updatedAt: '2024-10-17T07:36:22Z',
  },
  {
    id: 'feature-2',
    name: 'Screen Recording',
    description: "The student's screen will be recorded during the assessment.",
    createdAt: '2024-10-17T07:36:22Z',
    updatedAt: '2024-10-17T07:36:22Z',
  },
  {
    id: 'feature-3',
    name: 'Audio Monitoring',
    description: "The student's audio will be monitored during the assessment.",
    createdAt: '2024-10-17T07:36:22Z',
    updatedAt: '2024-10-17T07:36:22Z',
  },
];
export const dummyAssessmentWithTest: AssessmentWithTest = {
  id: 'assessment-123',
  title: 'Mathematics Assessment',
  instructions:
    'Please complete the following questions to the best of your ability.',
  passMark: 70,
  duration: 60,
  showClock: true,
  conductSurvey: true,
  isDispatched: false,
  system: true,
  attempted: false,
  createdAt: '2024-10-17T07:36:22Z',
  updatedAt: '2024-10-17T07:36:22Z',
  camerashotsInterval: '10',
  screenshotsInterval: '30',
  proctor: 'John Doe',
  organizationId: 'org-456',
  testOrder: ['test-1', 'test-2', 'test-3'],
  tests: [mockTestData],
  proctorFeatures: dummyProctorFeatures,
};
export const mockApiResponse: GetOneAssessmentApiResponse = {
  success: true,
  data: dummyAssessmentWithTest,
};
export const mockLoginUserInfo: LoginUserInfo = {
  activated: true,
  Id: '123456789',
  first_name: 'John',
  last_name: 'Doe',
  email: '<EMAIL>',
  password: generateUUID(),
  role: 'admin',
  password_reset_token: null,
  phone: '+1234567890',
  createdAt: '2023-12-31T23:59:59.000Z',
  updatedAt: '2024-01-01T00:00:00.000Z',
  organizationId: 'abc123',
  permissions: ['user'],
  system: true,
  image_link: 'https://example.com/profile.jpg',
  id: '123456789',
};

export function isValidTestData(data: BasicInfoWithTest): boolean {
  if (
    !data ||
    typeof data !== 'object' ||
    !data.basicInfo ||
    !data.tests ||
    !Array.isArray(data.tests)
  ) {
    return false;
  }

  return isValidBasicInfo(data.basicInfo) && isValidTests(data.tests);
}

export function isValidBasicInfo(basicInfo: BasicAndConfig): boolean {
  if (
    typeof basicInfo.title !== 'string' ||
    basicInfo.title.trim() === '' ||
    typeof basicInfo.passMark !== 'number' ||
    typeof basicInfo.instructions !== 'string' ||
    basicInfo.instructions.trim() === '' ||
    !Array.isArray(basicInfo.configurations)
  ) {
    return false;
  }

  for (const config of basicInfo.configurations) {
    if (
      typeof config.name !== 'string' ||
      typeof config.value !== 'string' ||
      typeof config.selected !== 'boolean' ||
      typeof config.locked !== 'boolean'
    ) {
      return false;
    }
  }

  return true;
}

function isValidTests(tests: TestsData[]): boolean {
  if (tests.length === 0) {
    return false;
  }

  for (const test of tests) {
    if (
      typeof test.id !== 'string' ||
      typeof test.title !== 'string' ||
      typeof test.passMark !== 'number' ||
      typeof test.duration !== 'number' ||
      typeof test.difficultyLevel !== 'string' ||
      !Array.isArray(test.questions) ||
      test.questions.length === 0 ||
      !test.domain ||
      typeof test.domain.name !== 'string'
    ) {
      return false;
    }

    for (const question of test.questions) {
      if (typeof question.id !== 'string') {
        return false;
      }
    }
  }

  return true;
}

export function navigateWithAssessmentId(
  router: Router,
  isEditMode: boolean,
  assessmentId: string | undefined,
  routePath: string // example: 'preview-assessments'
) {
  const baseUrl = 'dashboard/test-management/assessments';
  const fullPath = isEditMode
    ? `edit-assessments/${routePath}`
    : `create-assessments/${routePath}`;

  const queryParams = isEditMode && assessmentId ? { assessmentId } : {};

  router.navigate([`${baseUrl}/${fullPath}`], { queryParams });
}
