import { inject } from '@angular/core';
import { Observable } from 'rxjs';
import { TimeAgoService } from '../services/time-ago/time-ago.service';

/**
 * Utility function to get time ago text
 * Use this in component methods or computed properties
 */
export function getTimeAgo(dateString: string): string {
  const timeAgoService = inject(TimeAgoService);
  return timeAgoService.getTimeAgo(dateString);
}

/**
 * Utility function to get live updating time ago observable
 * Use this when you need real-time updates
 */
export function getTimeAgoLive(dateString: string): Observable<string> {
  const timeAgoService = inject(TimeAgoService);
  return timeAgoService.getTimeAgoObservable(dateString);
}

/**
 * Simple time ago calculation without service injection
 * Use this for one-off calculations or in pure functions
 */
export function calculateTimeAgo(dateString: string): string {
  if (!dateString) return '';

  const now = new Date();
  const inputDate = new Date(dateString);
  const diffInMilliseconds = Math.max(0, now.getTime() - inputDate.getTime());

  const seconds = Math.floor(diffInMilliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  const months = Math.floor(days / 30);
  const years = Math.floor(days / 365);

  const format = (value: number, unit: string): string => {
    return `${value} ${unit}${value === 1 ? '' : 's'} ago`;
  };

  if (seconds < 5) {
    return 'just now';
  }
  if (seconds < 60) {
    return format(seconds, 'second');
  }
  if (minutes < 60) {
    return format(minutes, 'minute');
  }
  if (hours < 24) {
    return format(hours, 'hour');
  }
  if (days < 30) {
    return format(days, 'day');
  }
  if (months < 12) {
    return format(months, 'month');
  }
  return format(years, 'year');
}
