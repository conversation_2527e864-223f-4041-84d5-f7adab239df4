import {
  JavaScriptProcessor,
  PythonProcessor,
  JavaProcessor,
  getLanguageProcessor,
} from './codingTypeUtils';

describe('Language Processors', () => {
  describe('JavaScriptProcessor', () => {
    let processor: JavaScriptProcessor;

    beforeEach(() => {
      processor = new JavaScriptProcessor();
    });

    describe('processTestCaseInput', () => {
      // Function handling tests
      it('should return functions as-is without processing', () => {
        const testCases = [
          'function add(a, b) { return a + b; }',
          '(x) => x * 2',
          'x => x + 1',
          '(a, b) => a + b',
          'console.log(JSON.stringify(mapArray([],("num") => "num" * 2)))',
          'arr.map(x => x.toUpperCase())',
          'const fn = function() { return 42; }',
          'setTimeout(() => console.log("hello"), 1000)',
        ];

        testCases.forEach((testCase) => {
          expect(processor.processTestCaseInput(testCase)).toBe(testCase);
        });
      });

      it('should extract values from variable declarations', () => {
        expect(processor.processTestCaseInput('const a="john"')).toBe('"john"');
        expect(processor.processTestCaseInput('let x=5, y="test"')).toBe(
          '5, "test"'
        );
        expect(processor.processTestCaseInput('var name="Alice", age=25')).toBe(
          '"Alice", 25'
        );
      });

      it('should not extract from arrow function assignments', () => {
        const input = 'const fn = x => x * 2';
        expect(processor.processTestCaseInput(input)).toBe('x => x * 2');
      });

      it('should handle arrays', () => {
        expect(processor.processTestCaseInput('[apple, banana, 42]')).toBe(
          '"[apple, banana, 42]"'
        );
        expect(processor.processTestCaseInput('[1, 2, true, null]')).toBe(
          '"[1, 2, true, null]"'
        );
      });

      it('should preserve keywords', () => {
        expect(
          processor.processTestCaseInput('true false null undefined')
        ).toBe('"true false null undefined"');
      });

      it('should handle mixed complex cases', () => {
        expect(processor.processTestCaseInput('name: john, active: true')).toBe(
          '"name: john, active: true"'
        );
      });

      it('should handle already quoted strings', () => {
        expect(processor.processTestCaseInput('"already quoted"')).toBe(
          '"already quoted"'
        );
        expect(processor.processTestCaseInput("'single quoted'")).toBe(
          `"'single quoted'"`
        );
      });
    });
  });

  describe('PythonProcessor', () => {
    let processor: PythonProcessor;

    beforeEach(() => {
      processor = new PythonProcessor();
    });

    describe('processTestCaseInput', () => {
      it('should return Python functions as-is', () => {
        const testCases = [
          'def add(a, b): return a + b',
          'lambda x: x * 2',
          'lambda a, b: a + b',
          'def process_data(data): return data.upper()',
        ];

        testCases.forEach((testCase) => {
          expect(processor.processTestCaseInput(testCase)).toBe(testCase);
        });
      });

      it('should extract values from Python assignments', () => {
        expect(processor.processTestCaseInput('name="john"')).toBe("'john'");
        expect(processor.processTestCaseInput('x=5, y="test"')).toBe(
          "5, 'test'"
        );
      });

      it('should handle Python booleans and None', () => {
        expect(processor.processTestCaseInput(true)).toBe('True');
        expect(processor.processTestCaseInput(false)).toBe('False');
        expect(processor.processTestCaseInput(null)).toBe('None');
      });

      it('should use single quotes for Python strings', () => {
        expect(processor.processTestCaseInput('name: john')).toBe(
          "'name': 'john'"
        );
      });

      it('should preserve Python keywords', () => {
        expect(processor.processTestCaseInput('True False None')).toBe(
          'True False None'
        );
      });
    });
  });

  describe('JavaProcessor', () => {
    let processor: JavaProcessor;

    beforeEach(() => {
      processor = new JavaProcessor();
    });

    describe('processTestCaseInput', () => {
      it('should return Java methods as-is', () => {
        const testCases = [
          'public int add(int a, int b) { return a + b; }',
          'private String getName() { return name; }',
          'protected void process() { }',
        ];

        testCases.forEach((testCase) => {
          expect(processor.processTestCaseInput(testCase)).toBe(testCase);
        });
      });

      it('should return Java lambda expressions as-is', () => {
        const testCases = [
          'x -> x * 2',
          '(a, b) -> a + b',
          'list.stream().map(x -> x.toUpperCase())',
        ];

        testCases.forEach((testCase) => {
          expect(processor.processTestCaseInput(testCase)).toBe(testCase);
        });
      });

      it('should extract values from Java declarations', () => {
        expect(processor.processTestCaseInput('int a=5')).toBe('5');
        expect(processor.processTestCaseInput('String name="john"')).toBe(
          '"john"'
        );
        expect(processor.processTestCaseInput('boolean active=true')).toBe(
          'true'
        );
      });

      it('should handle Java collections as-is', () => {
        const input = 'Arrays.asList(1, 2, 3)';
        expect(processor.processTestCaseInput(input)).toBe(input);
      });

      it('should handle Java object creation as-is', () => {
        const input = 'new ArrayList<String>()';
        expect(processor.processTestCaseInput(input)).toBe(input);
      });

      it('should handle Java booleans and null', () => {
        expect(processor.processTestCaseInput(true)).toBe('true');
        expect(processor.processTestCaseInput(false)).toBe('false');
        expect(processor.processTestCaseInput(null)).toBe('null');
      });
    });
  });

  describe('getLanguageProcessor', () => {
    it('should return correct processor for each language', () => {
      expect(getLanguageProcessor('JavaScript')).toBeInstanceOf(
        JavaScriptProcessor
      );
      expect(getLanguageProcessor('TypeScript')).toBeInstanceOf(
        JavaScriptProcessor
      );
      expect(getLanguageProcessor('Python')).toBeInstanceOf(PythonProcessor);
      expect(getLanguageProcessor('Java')).toBeInstanceOf(JavaProcessor);
    });

    it('should default to JavaScript processor for unknown languages', () => {
      expect(getLanguageProcessor('UnknownLanguage')).toBeInstanceOf(
        JavaScriptProcessor
      );
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty strings', () => {
      const jsProcessor = new JavaScriptProcessor();
      expect(jsProcessor.processTestCaseInput('')).toBe('""'); // two double quotes as string
    });

    it('should handle whitespace-only strings', () => {
      const jsProcessor = new JavaScriptProcessor();
      expect(jsProcessor.processTestCaseInput('   ')).toBe('""');
    });

    it('should handle complex nested structures', () => {
      const jsProcessor = new JavaScriptProcessor();
      const input = '{users: [{name: john, age: 25}, {name: jane, age: 30}]}';
      const expected =
        '"{users: [{name: john, age: 25}, {name: jane, age: 30}]}"'; // wrapped in quotes
      expect(jsProcessor.processTestCaseInput(input)).toBe(expected);
    });

    it('should handle functions with complex parameters', () => {
      const jsProcessor = new JavaScriptProcessor();
      const input = 'users.filter(user => user.age > 18 && user.active)';
      expect(jsProcessor.processTestCaseInput(input)).toBe(input);
    });
  });
});
