import { marked } from 'marked';
import {
  EditorOptions,
  TestCase,
} from '../Interfaces/codingTypeQuestionsInterface';
import { ToastService } from '../services/toast-service/toast.service';
import DOMPurify from 'dompurify';

export const SUPPORTED_LANGUAGES = [
  { id: 'javascript', name: 'JavaScript', extension: 'js' },
  { id: 'typescript', name: 'TypeScript', extension: 'ts' },
  { id: 'python', name: 'Python', extension: 'py' },
  { id: 'java', name: 'Java', extension: 'java' },
  { id: 'csharp', name: 'C#', extension: 'cs' },
  { id: 'cpp', name: 'C++', extension: 'cpp' },
  { id: 'go', name: 'Go', extension: 'go' },
  { id: 'ruby', name: 'Ruby', extension: 'rb' },
];

export const languagesForMonacoAndJudge = {
  JavaScript: {
    languageName: 'JavaScript',
    judge0Id: 63,
    monacoValue: 'javascript',
  },
  TypeScript: {
    languageName: 'TypeScript',
    judge0Id: 74,
    monacoValue: 'typescript',
  },
  'Python 3': { languageName: 'Python 3', judge0Id: 71, monacoValue: 'python' },
  Java: { languageName: 'Java', judge0Id: 91, monacoValue: 'java' },
  'C#': { languageName: 'C#', judge0Id: 51, monacoValue: 'csharp' },
  'C++': { languageName: 'C++', judge0Id: 54, monacoValue: 'cpp' },
  Go: { languageName: 'Go', judge0Id: 22, monacoValue: 'go' },
  Ruby: { languageName: 'Ruby', judge0Id: 72, monacoValue: 'ruby' },
};

// Enhanced language configuration for coding questions
export const CODING_LANGUAGE_CONFIG = [
  {
    languageName: 'JavaScript',
    judge0Id: 63,
    monacoValue: 'javascript',
    fileExtension: 'js',
    supportsArrowFunctions: true,
    supportsObjectLiterals: true,
    commentSyntax: { single: '//', multi: { start: '/*', end: '*/' } },
  },
  {
    languageName: 'TypeScript',
    judge0Id: 74,
    monacoValue: 'typescript',
    fileExtension: 'ts',
    supportsArrowFunctions: true,
    supportsObjectLiterals: true,
    commentSyntax: { single: '//', multi: { start: '/*', end: '*/' } },
  },
  // {
  //   languageName: 'Python',
  //   judge0Id: 71,
  //   monacoValue: 'python',
  //   fileExtension: 'py',
  //   supportsArrowFunctions: false,
  //   supportsObjectLiterals: false,
  //   commentSyntax: { single: '#', multi: { start: '"""', end: '"""' } },
  // },
  // {
  //   languageName: 'Java',
  //   judge0Id: 91,
  //   monacoValue: 'java',
  //   fileExtension: 'java',
  //   supportsArrowFunctions: false,
  //   supportsObjectLiterals: false,
  //   commentSyntax: { single: '//', multi: { start: '/*', end: '*/' } },
  // },
];

export const DEFAULT_EDITOR_OPTIONS: EditorOptions = {
  minimap: { enabled: true },
  lineNumbers: 'on',
  scrollBeyondLastLine: false,
  automaticLayout: true,
  fontSize: 14,
  tabSize: 2,
  theme: 'vs',
};

// testCaseUtils.ts

export function getMissingOutputIndexes(testCases: TestCase[]): number[] {
  return testCases
    .map((tc, index) => {
      let outputStr: string;
      if (tc.output === undefined || tc.output === null) {
        outputStr = '';
      } else if (typeof tc.output === 'object') {
        outputStr = JSON.stringify(tc.output).trim();
        // Removed redundant condition: typeof tc.output === 'object' && tc.output !== null
      } else if (tc.output !== undefined && tc.output !== null) {
        let result: string;
        if (typeof tc.output === 'object' && tc.output !== null) {
          result = JSON.stringify(tc.output).trim();
        } else if (tc.output !== undefined && tc.output !== null) {
          result = JSON.stringify(tc.output).trim();
        } else {
          result = '';
        }
        outputStr = result;
      } else {
        outputStr = '';
      }
      return !outputStr ? index + 1 : null;
    })
    .filter((index): index is number => index !== null);
}

function isValidTestCase(tc: TestCase): boolean {
  return (
    typeof tc.input === 'string' &&
    tc.input.trim() !== '' &&
    typeof tc.output &&
    typeof tc.visibility === 'string' &&
    typeof tc.weight === 'number'
  );
}

export function isValidTestCases(
  testCases: TestCase[],
  toastService?: ToastService
): boolean {
  if (!testCases || testCases.length === 0) {
    toastService?.onShow(
      'No test cases',
      'Please add at least one test case.',
      true,
      'error'
    );
    return false;
  }

  const hasPublicTestCase = testCases.some((tc) => tc.visibility === 'public');
  if (!hasPublicTestCase) {
    toastService?.onShow(
      'No Public Test Case',
      'Please ensure at least one test case is marked as public.',
      true,
      'error'
    );
    return false;
  }

  const indexes: number[] = [];
  testCases.forEach((tc, i) => {
    if (!isValidTestCase(tc)) {
      indexes.push(i + 1);
    }
  });

  if (indexes.length > 0) {
    toastService?.onShow(
      'Invalid test case',
      `Test case${indexes.length > 1 ? 's' : ''} at position${indexes.length > 1 ? 's' : ''} ${indexes.join(', ')} ${indexes.length > 1 ? 'are' : 'is'} invalid.`,

      true,
      'error'
    );
    return false;
  }

  return true;
}

export async function parsedHtml(markdownInput: string): Promise<string> {
  return await marked.parse(markdownInput, {
    breaks: true,
  });
}

export interface ValidationResponse {
  isValid: boolean;
  message: string;
}

export const MAX_TEST_CASES = 15;

/**
 * Validate test cases.
 *
 * This function takes an object with properties `num_hidden_cases` and `num_test_cases`
 * and returns a `ValidationResponse` object that indicates whether the input is valid
 * and a message that describes the reason for the validation failure.
 *
 * The function will return false if the number of hidden test cases is greater than or
 * equal to the number of test cases, or if the total number of test cases (visible +
 * hidden) exceeds the maximum allowed (15).
 */
export const validateTestCases = (payload: {
  num_hidden_cases?: number;
  num_test_cases?: number;
}): ValidationResponse => {
  const publicTestCases =
    (payload.num_test_cases ?? 0) - (payload.num_hidden_cases ?? 0);
  const hiddenTestCases = payload.num_hidden_cases || 0;

  // At least 1 public test case is required
  if (publicTestCases < 1) {
    return {
      isValid: false,
      message: 'At least one public test case is required',
    };
  }

  // Total test cases (public + hidden) cannot exceed MAX_TEST_CASES
  const totalTestCases = publicTestCases + hiddenTestCases;
  if (totalTestCases > MAX_TEST_CASES) {
    return {
      isValid: false,
      message: `Total number of test cases (public + hidden) cannot exceed ${MAX_TEST_CASES}`,
    };
  }

  // Hidden test cases cannot be greater than or equal to total test cases
  // This ensures there's always at least 1 public test case
  if (hiddenTestCases >= totalTestCases) {
    return {
      isValid: false,
      message:
        'Number of hidden test cases must be less than total test cases to ensure at least one public test case',
    };
  }

  return { isValid: true, message: '' };
};

/**
 * Validate required fields for a coding question.
 *
 * This function takes an object with properties `reference_solution` and `language`
 * and returns a `ValidationResponse` object that indicates whether the input is valid
 * and a message that describes the reason for the validation failure.
 *
 * The function will return false if either the `reference_solution` or `language`
 * properties are not present in the input object.
 */
export const validateRequiredFields = (payload: {
  reference_solution?: string;
  language?: string;
}): ValidationResponse => {
  if (!payload.reference_solution) {
    return {
      isValid: false,
      message: 'Please enter a solution code',
    };
  }

  if (!payload.language) {
    return {
      isValid: false,
      message: 'Please Select a language',
    };
  }

  return { isValid: true, message: '' };
};

/**
 * Validate problem definition for a coding question.
 *
 * This function takes an object with property `problem_definition`
 * and returns a `ValidationResponse` object that indicates whether the input is valid
 * and a message that describes the reason for the validation failure.
 *
 * The function will return false if the problem definition is empty or too short.
 */
export const validateProblemDefinition = (payload: {
  problem_definition?: string;
}): ValidationResponse => {
  if (
    !payload.problem_definition ||
    payload.problem_definition.trim().length === 0
  ) {
    return {
      isValid: false,
      message: 'Please enter question details',
    };
  }

  if (payload.problem_definition.trim().length < 10) {
    return {
      isValid: false,
      message: 'Problem definition must be at least 10 characters long',
    };
  }

  return { isValid: true, message: '' };
};

export const validateNumberOfTestCase = (payload: {
  num_test_cases?: number;
}): ValidationResponse => {
  if (typeof payload.num_test_cases !== 'number') {
    return {
      isValid: false,
      message: 'Number of test cases must be provided',
    };
  }

  if (payload.num_test_cases < 1) {
    return {
      isValid: false,
      message: 'There must be at least one test case',
    };
  }

  if (payload.num_test_cases > MAX_TEST_CASES) {
    return {
      isValid: false,
      message: `Number of test cases cannot exceed ${MAX_TEST_CASES}`,
    };
  }

  return {
    isValid: true,
    message: '',
  };
};

/**
 * Validates the test cases for a coding question.
 *
 * This function checks if the provided payload contains valid test cases
 * and required fields necessary for a coding question. It validates the
 * number of test cases, number of hidden test cases, reference solution,
 * and language. The function returns a `ValidationResponse` object that
 * indicates whether the input is valid and a message that describes the
 * reason for any validation failure.
 *
 * @param payload - An object that may contain the following optional
 * properties:
 *   - `num_hidden_cases`: The number of hidden test cases.
 *   - `num_test_cases`: The total number of test cases.
 *   - `reference_solution`: The reference solution code.
 *   - `language`: The programming language for the solution.
 *
 * @returns A `ValidationResponse` indicating if the validation passed,
 * and if not, the reason for the failure.
 */

// Language-specific test case processing utilities
export interface LanguageProcessor {
  processTestCaseInput(input: unknown): string;
  formatOutput(output: unknown): string;
  isValidSyntax(code: string): boolean;
  getKeywords(): Set<string>;
}

export class JavaScriptProcessor implements LanguageProcessor {
  processTestCaseInput(input: unknown): string {
    if (typeof input === 'function') {
      return input.toString();
    }

    if (typeof input === 'string') {
      const str = input.trim();

      if (this.isFunctionOrArrow(str)) {
        return this.handleFunctionOrArrow(str);
      }

      if (this.isObjectLiteral(str)) {
        return this.convertObjectLiteral(str);
      }

      if (this.isVariableDeclaration(str) || this.isBareAssignment(str)) {
        const extractedValues = this.extractValuesFromAssignments(str);
        return this.stringifyUnquotedWords(extractedValues);
      }

      if (!str.startsWith('"') && !str.endsWith('"')) {
        return `"${str}"`;
      }

      return str;
    }

    return JSON.stringify(input);
  }

  private isObjectLiteral(str: string): boolean {
    return /^\{[^}]+\}$/.test(str.trim());
  }
  private convertObjectLiteral(str: string): string {
    try {
      const entries = str
        .replace(/^{|}$/g, '') // remove outer braces
        .split(',')
        .map((entry) => entry.trim())
        .filter(Boolean)
        .map((entry) => {
          const [rawKey, rawValue] = entry.split(':').map((x) => x.trim());

          const key = this.ensureProperJsonValue(rawKey, true); // true = isKey
          const value = this.ensureProperJsonValue(rawValue, false);

          return `${key}:${value}`;
        });

      return `{${entries.join(',')}}`;
    } catch {
      return `"${str}"`; // fallback to quoted string
    }
  }
  private ensureProperJsonValue(value: string, isKey: boolean): string {
    const isQuoted = /^["'].*["']$/.test(value);
    const keywords = this.getKeywords();

    if (isQuoted || keywords.has(value.toLowerCase())) {
      return value;
    }

    if (!isKey) {
      if (!isNaN(Number(value))) return value;
      if (value === 'true' || value === 'false' || value === 'null')
        return value;
    }

    return `"${value}"`;
  }

  private isFunctionOrArrow(str: string): boolean {
    return (
      /^function\s*\w*\s*\(/.test(str) ||
      /=>\s*/.test(str) ||
      /^(const|let|var)\s+\w+\s*=\s*.*=>\s*/.test(str) ||
      str.includes('function') ||
      str.includes('=>')
    );
  }

  private handleFunctionOrArrow(str: string): string {
    const isArrowAssignment = /^(const|let|var)\s+\w+\s*=\s*.*=>\s*/.test(str);
    if (isArrowAssignment) {
      const equalIndex = str.indexOf('=');
      if (equalIndex !== -1) {
        return str
          .slice(equalIndex + 1)
          .trim()
          .replace(/;$/, '');
      }
    }
    return str.replace(/;$/, '');
  }

  private isBareAssignment(str: string): boolean {
    return /^\s*\w+\s*=/.test(str);
  }

  private isVariableDeclaration(str: string): boolean {
    return /^\s*(const|let|var)\s+/.test(str) && !str.includes('=>');
  }

  private extractValuesFromAssignments(input: string): string {
    const lines = input
      .split('\n')
      .map((line) => line.trim())
      .filter(Boolean);

    const allValues: string[] = [];

    for (const line of lines) {
      // Remove leading variable declaration keywords (const, let, var)
      const cleaned = line.replace(/^\s*(const|let|var)?\s*/, '');

      let current = '';
      const parts: string[] = [];
      const depth = { curly: 0, square: 0, paren: 0 };

      for (const char of cleaned) {
        if (
          char === ',' &&
          depth.curly === 0 &&
          depth.square === 0 &&
          depth.paren === 0
        ) {
          parts.push(current.trim());
          current = '';
        } else {
          current += char;
          if (char === '{') depth.curly++;
          else if (char === '}') depth.curly--;
          else if (char === '[') depth.square++;
          else if (char === ']') depth.square--;
          else if (char === '(') depth.paren++;
          else if (char === ')') depth.paren--;
        }
      }

      if (current.trim()) {
        parts.push(current.trim());
      }

      // Extract the RHS values after '=' for each part
      const values = parts
        .map((part) => {
          const index = part.indexOf('=');
          if (index !== -1) {
            // Take everything after the first '=' sign
            return part.slice(index + 1).trim();
          } else {
            // If no '=', just take the entire part (could be a value itself)
            return part.trim();
          }
        })
        .filter((val) => val.length > 0);

      allValues.push(...values);
    }

    return allValues.join(', ');
  }

  private stringifyUnquotedWords(input: string): string {
    const trimmedInput = input.trim();

    // Alternative implementation: count quotes up to index and check if odd
    function isInsideQuotes(str: string, index: number): boolean {
      let insideSingle = false;
      let insideDouble = false;
      for (let i = 0; i < index; i++) {
        if (str[i] === "'" && !insideDouble) insideSingle = !insideSingle;
        if (str[i] === '"' && !insideSingle) insideDouble = !insideDouble;
      }
      return insideSingle || insideDouble;
    }

    const keywords = this.getKeywords();

    return trimmedInput.replace(/([a-zA-Z_]\w*)/g, (_, word, offset, str) => {
      const lower = word.toLowerCase();

      if (keywords.has(lower)) {
        return word;
      }

      if (isInsideQuotes(str, offset)) {
        return word;
      }

      return `"${word}"`;
    });
  }

  formatOutput(output: unknown): string {
    if (typeof output === 'string') {
      // Handle empty string case
      if (output === '') {
        return '""';
      }

      // Check if the string is already JSON-formatted (starts and ends with quotes)
      if (output.startsWith('"') && output.endsWith('"')) {
        try {
          // Try to parse it as JSON to see if it's a valid JSON string
          const parsed = JSON.parse(output);
          if (typeof parsed === 'string') {
            // It's a JSON-encoded string, return the parsed value
            return parsed;
          }
        } catch {
          // If parsing fails, treat it as a regular string
        }
      }

      return output;
    }
    return JSON.stringify(output);
  }

  isValidSyntax(code: string): boolean {
    try {
      new Function(code);
      return true;
    } catch {
      return false;
    }
  }

  getKeywords(): Set<string> {
    return new Set([
      'true',
      'false',
      'null',
      'undefined',
      'function',
      'return',
      'if',
      'else',
      'for',
      'while',
      'const',
      'let',
      'var',
      'class',
      'extends',
      'import',
      'export',
      'default',
      'async',
      'await',
    ]);
  }
}

export class PythonProcessor implements LanguageProcessor {
  processTestCaseInput(input: unknown): string {
    if (typeof input === 'string') {
      return this.handleStringInput(input.trim());
    }
    return this.handleNonStringInput(input);
  }

  private handleNonStringInput(input: unknown): string {
    if (typeof input === 'boolean') {
      return input ? 'True' : 'False';
    }
    if (input === null) {
      return 'None';
    }
    return JSON.stringify(input);
  }

  private handleStringInput(str: string): string {
    if (this.isPythonFunctionOrLambda(str)) {
      return str;
    }

    if (this.isPythonVariableAssignment(str)) {
      return this.processPythonVariableAssignment(str);
    }

    return this.stringifyUnquotedWords(str);
  }

  private processPythonVariableAssignment(str: string): string {
    const extractedValues = this.extractValuesFromAssignments(str);
    return this.stringifyUnquotedWords(extractedValues);
  }

  private isPythonFunctionOrLambda(str: string): boolean {
    return (
      str.startsWith('def ') || str.includes('lambda ') || str.includes('def ')
    );
  }

  private isPythonVariableAssignment(str: string): boolean {
    return /^\s*\w+\s*=/.test(str) && !this.isPythonFunctionOrLambda(str);
  }

  private extractValuesFromAssignments(input: string): string {
    // For Python assignments like: a="john", b=25, c=True
    const parts: string[] = [];
    let current = '';
    const depth = { curly: 0, square: 0, paren: 0 };

    for (const char of input) {
      if (
        char === ',' &&
        depth.curly === 0 &&
        depth.square === 0 &&
        depth.paren === 0
      ) {
        parts.push(current.trim());
        current = '';
      } else {
        current += char;

        if (char === '{') depth.curly++;
        else if (char === '}') depth.curly--;
        else if (char === '[') depth.square++;
        else if (char === ']') depth.square--;
        else if (char === '(') depth.paren++;
        else if (char === ')') depth.paren--;
      }
    }

    if (current.trim()) {
      parts.push(current.trim());
    }

    const values = parts
      .map((part) => {
        const split = part.split('=');
        const value =
          split.length > 1 ? split.slice(1).join('=').trim() : part.trim();
        return value || null;
      })
      .filter((val): val is string => val !== null);

    return values.join(', ');
  }

  private stringifyUnquotedWords(input: string): string {
    const trimmedInput = input.trim();

    // Check if this is a space-separated list of keywords (like "True False None")
    const words = trimmedInput.split(/\s+/);
    if (
      words.length > 1 &&
      words.every((word) => this.getKeywords().has(word))
    ) {
      return trimmedInput; // Return as-is for keyword lists
    }

    // Handle single quoted string - convert double quotes to single quotes for Python
    if (
      trimmedInput.startsWith('"') &&
      trimmedInput.endsWith('"') &&
      trimmedInput.length > 1
    ) {
      const content = trimmedInput.slice(1, -1);
      return `'${content}'`;
    }

    // Handle comma-separated values (like from assignments)
    if (trimmedInput.includes(',')) {
      const parts = trimmedInput.split(',').map((part) => {
        const trimmed = part.trim();

        // If it's already quoted with double quotes, convert to single quotes for Python
        if (
          trimmed.startsWith('"') &&
          trimmed.endsWith('"') &&
          trimmed.length > 1
        ) {
          const content = trimmed.slice(1, -1);
          return `'${content}'`;
        }

        // If it's already quoted with single quotes, keep as-is
        if (trimmed.startsWith("'") && trimmed.endsWith("'")) {
          return trimmed;
        }

        // If it's a number or keyword, keep as-is
        if (/^\d+(\.\d+)?$/.test(trimmed) || this.getKeywords().has(trimmed)) {
          return trimmed;
        }

        // Quote unquoted words with single quotes
        return `'${trimmed}'`;
      });

      return parts.join(', ');
    }

    // Alternative implementation: count quotes and check if inside quotes
    function isInsideQuotes(str: string, index: number): boolean {
      let singleQuotes = 0;
      let doubleQuotes = 0;
      for (let i = 0; i < index; i++) {
        if (str[i] === "'") singleQuotes++;
        if (str[i] === '"') doubleQuotes++;
      }
      // Odd number of quotes means inside quotes
      return singleQuotes % 2 === 1 || doubleQuotes % 2 === 1;
    }

    const keywords = this.getKeywords();

    return trimmedInput.replace(/([a-zA-Z_]\w*)/g, (_, word, offset, str) => {
      const lower = word.toLowerCase();

      if (keywords.has(lower)) {
        return word;
      }

      if (isInsideQuotes(str, offset)) {
        return word;
      }

      // Use single quotes for Python strings
      return `'${word}'`;
    });
  }

  formatOutput(output: unknown): string {
    if (typeof output === 'string') {
      if (output === '') {
        return '""';
      }
      const parsedString = this.tryParseJsonString(output);
      if (parsedString !== null) {
        return parsedString;
      }
      return output;
    }
    if (typeof output === 'boolean') {
      return output ? 'True' : 'False';
    }
    if (output === null) {
      return 'None';
    }
    return JSON.stringify(output);
  }

  private tryParseJsonString(str: string): string | null {
    if (str.startsWith('"') && str.endsWith('"')) {
      try {
        const parsed = JSON.parse(str);
        if (typeof parsed === 'string') {
          return parsed;
        }
      } catch {
        // If parsing fails, treat it as a regular string
      }
    }
    return null;
  }

  isValidSyntax(code: string): boolean {
    // Basic Python syntax validation (simplified)
    const lines = code.split('\n');

    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        // Check for basic Python syntax patterns
        if (trimmed.includes('def ') && !trimmed.endsWith(':')) {
          return false;
        }
      }
    }
    return true;
  }

  getKeywords(): Set<string> {
    return new Set([
      'True',
      'False',
      'None',
      'def',
      'return',
      'if',
      'else',
      'elif',
      'for',
      'while',
      'class',
      'import',
      'from',
      'as',
      'try',
      'except',
      'finally',
      'with',
      'lambda',
      'and',
      'or',
      'not',
      'in',
      'is',
    ]);
  }
}

export class JavaProcessor implements LanguageProcessor {
  processTestCaseInput(input: unknown): string {
    if (typeof input === 'string') {
      return this.handleStringInput(input.trim());
    }

    if (typeof input === 'boolean') {
      return input.toString();
    }

    if (input === null) {
      return 'null';
    }

    // For non-string values, use JSON.stringify
    return JSON.stringify(input);
  }

  private handleStringInput(str: string): string {
    // Handle Java method definitions and lambda expressions - these should be returned as-is
    const containsMethod =
      str.includes('public ') ||
      str.includes('private ') ||
      str.includes('protected ');
    const containsLambda = str.includes('->'); // Java lambda expressions
    const containsFunction = containsMethod || containsLambda;

    if (containsFunction) {
      return str;
    }

    // Handle Java arrays and collections - these should be returned as-is
    if (str.startsWith('new ') || str.includes('Arrays.asList')) {
      return str;
    }

    // Handle variable declarations - extract values only
    if (
      (/^\s*(int|String|boolean|double|float|long|char|byte|short)\s+\w+\s*=/.test(
        str
      ) ||
        /^\s*\w+\s*=/.test(str)) &&
      !containsFunction
    ) {
      const extractedValues = this.extractValuesFromDeclarations(str);
      return this.stringifyUnquotedWords(extractedValues);
    }

    // For everything else (objects, arrays, primitives, identifiers), use stringifyUnquotedWords
    // This handles all Java syntax forms comprehensively
    return this.stringifyUnquotedWords(str);
  }

  private extractValuesFromDeclarations(input: string): string {
    // For Java declarations like: int a=5, String b="john", boolean c=true
    // Remove type declarations
    const cleaned = input.replace(
      /^\s*(int|String|boolean|double|float|long|char|byte|short)\s+/,
      ''
    );

    const parts: string[] = [];
    let current = '';
    const depth = { curly: 0, square: 0, paren: 0 };

    for (const char of cleaned) {
      if (
        char === ',' &&
        depth.curly === 0 &&
        depth.square === 0 &&
        depth.paren === 0
      ) {
        parts.push(current.trim());
        current = '';
      } else {
        current += char;

        if (char === '{') depth.curly++;
        else if (char === '}') depth.curly--;
        else if (char === '[') depth.square++;
        else if (char === ']') depth.square--;
        else if (char === '(') depth.paren++;
        else if (char === ')') depth.paren--;
      }
    }

    if (current.trim()) {
      parts.push(current.trim());
    }

    const values = parts
      .map((part) => {
        const split = part.split('=');
        const value =
          split.length > 1 ? split.slice(1).join('=').trim() : part.trim();
        return value || null;
      })
      .filter((val): val is string => val !== null);

    return values.join(', ');
  }

  private stringifyUnquotedWords(input: string): string {
    const trimmedInput = input.trim();

    // For Java, let's only quote words that are not keywords and not already inside quotes,
    // but also skip quoting numeric literals.
    function isInsideQuotes(str: string, index: number): boolean {
      let inSingle = false;
      let inDouble = false;

      for (let i = 0; i < index; i++) {
        const char = str[i];
        if (char === '"' && !inSingle) inDouble = !inDouble;
        else if (char === "'" && !inDouble) inSingle = !inSingle;
      }

      return inSingle || inDouble;
    }

    const keywords = this.getKeywords();

    return trimmedInput.replace(/([a-zA-Z_]\w*)/g, (_, word, offset, str) => {
      const lower = word.toLowerCase();

      // Do not quote keywords or words inside quotes
      if (keywords.has(lower) || isInsideQuotes(str, offset)) {
        return word;
      }

      // Do not quote numeric literals
      if (/^\d+$/.test(word)) {
        return word;
      }

      // For Java, use single quotes for single characters, double quotes otherwise
      if (word.length === 1) {
        return `'${word}'`;
      }
      return `"${word}"`;
    });
  }

  formatOutput(output: unknown): string {
    if (typeof output === 'string') {
      // Handle empty string case
      if (output === '') {
        return '""';
      }

      // Check if the string is already JSON-formatted (starts and ends with quotes)
      if (output.startsWith('"') && output.endsWith('"')) {
        try {
          // Try to parse it as JSON to see if it's a valid JSON string
          const parsed = JSON.parse(output);
          if (typeof parsed === 'string') {
            // It's a JSON-encoded string, return the parsed value
            return parsed;
          }
        } catch {
          // If parsing fails, treat it as a regular string
        }
      }

      return output;
    }
    if (output === null) {
      return 'null';
    }
    return JSON.stringify(output);
  }

  isValidSyntax(code: string): boolean {
    // Basic Java syntax validation (simplified)
    // Check for basic Java class structure
    if (code.includes('class ') && !code.includes('{')) {
      return false;
    }

    return true;
  }

  getKeywords(): Set<string> {
    return new Set([
      'true',
      'false',
      'null',
      'public',
      'private',
      'protected',
      'static',
      'final',
      'class',
      'interface',
      'extends',
      'implements',
      'import',
      'package',
      'return',
      'if',
      'else',
      'for',
      'while',
      'do',
      'switch',
      'case',
      'default',
      'try',
      'catch',
      'finally',
      'throw',
      'throws',
    ]);
  }
}

// Factory function to get the appropriate processor
export function getLanguageProcessor(languageName: string): LanguageProcessor {
  switch (languageName.toLowerCase()) {
    case 'javascript':
    case 'typescript':
      return new JavaScriptProcessor();
    case 'python':
    case 'python 3':
      return new PythonProcessor();
    case 'java':
      return new JavaProcessor();
    default:
      return new JavaScriptProcessor(); // Default fallback
  }
}

export const validateCodingQuestionTestCases = (payload: {
  num_hidden_cases?: number;
  num_test_cases?: number;
  reference_solution?: string;
  language?: string;
  problem_definition?: string;
}): ValidationResponse => {
  const testCasesValidation = validateTestCases(payload);
  if (!testCasesValidation.isValid) return testCasesValidation;

  const requiredFieldsValidation = validateRequiredFields(payload);
  if (!requiredFieldsValidation.isValid) return requiredFieldsValidation;

  const problemDefinitionValidation = validateProblemDefinition(payload);
  if (!problemDefinitionValidation.isValid) return problemDefinitionValidation;
  const numberOfTestCaseValidation = validateNumberOfTestCase(payload);
  if (!numberOfTestCaseValidation.isValid) return numberOfTestCaseValidation;
  return { isValid: true, message: '' };
};

// utils/sanitizer.ts

const allowedTags = [
  'b',
  'i',
  'u',
  'strong',
  'em',
  'p',
  'br',
  'ul',
  'li',
  'span',
  'div',
  'a',
  'img',
  'table',
  'thead',
  'tbody',
  'tr',
  'td',
  'th',
  'h1',
  'h2',
  'h3',
  'h4',
  'h5',
  'h6',
];

const allowedAttributes = [
  'style',
  'class',
  'href',
  'src',
  'alt',
  'title',
  'target',
];

export function sanitizeHtml(html: string): string {
  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: allowedTags,
    ALLOWED_ATTR: allowedAttributes,
  });
}
