import { HttpErrorResponse } from '@angular/common/http';
import { AbstractControl, ValidationErrors } from '@angular/forms';
import { throwError } from 'rxjs';
import { removeHTMLTags } from './regexConstants';
import * as <PERSON><PERSON> from 'joi';
import { environment } from '../../environments/environment';

export const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/;

export function convertTime(seconds: number | string) {
  const newSeconds = Number(seconds);
  const minutes = Math.floor(newSeconds / 60);
  const hours = Math.floor(minutes / 60);
  const remainingSeconds = newSeconds % 60;
  const remainingMinutes = minutes % 60;
  if (newSeconds < 61) {
    return seconds + ' secs';
  } else if (minutes === 1 && remainingSeconds === 1) {
    return minutes + ' min ' + remainingSeconds + ' sec';
  } else if (minutes >= 61) {
    return hours + 'hr ' + remainingMinutes + ' min';
  } else {
    return minutes + ' mins ' + remainingSeconds + ' secs';
  }
}

export const assessmentStatusColorMappings = [
  { condition: 'Passed', backgroundColor: '#C2FCDD', textColor: '#04952C' },
  { condition: 'Failed', backgroundColor: '#FFBFBF', textColor: '#D25353' },
  { condition: 'Pending', backgroundColor: '#FFFAC8', textColor: '#999303' },
  { condition: 'CORRECT', backgroundColor: '#C2FCDD', textColor: '#04952C' },
  { condition: 'WRONG', backgroundColor: '#FFBFBF', textColor: '#D25353' },
  { condition: 'SKIPPED', backgroundColor: '#93C5FD36', textColor: '#6AAFFD' },
  { condition: 'No Status', backgroundColor: '#FFEFD2', textColor: '#996A13' },
];

export const statusColorMappings = [
  {
    condition: 'Completed',
    backgroundColor: '#BAE1E3',
    textColor: '#667085',
  },
  {
    condition: 'Incomplete',
    backgroundColor: '#FFEBEB',
    textColor: '#A73636',
  },
  {
    condition: 'Unattempted',
    backgroundColor: '#EFF1F6',
    textColor: '#6B6B6B',
  },
  { condition: 'Expired', backgroundColor: '#FFEFD2', textColor: '#996A13' },
  { condition: 'Viewed', backgroundColor: '#E7E4F9', textColor: '#6E62B6' },
  {
    condition: 'InProgress',
    backgroundColor: '#FFF8D4',
    textColor: '#B38228',
  },
  {
    condition: 'Active',
    backgroundColor: '#BAE1E3',
    textColor: '#237E84',
  },
  {
    condition: 'Inactive',
    backgroundColor: '#EFF1F6',
    textColor: '#6B6B6B',
  },
];

export function errorHandler(error: HttpErrorResponse) {
  return throwError(() => error);
}

export function convertSecondsToTime(seconds: number) {
  if (seconds === 0) {
    return ' 0 secs';
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    const min = minutes === 0 ? '' : minutes + ' min(s)';
    const hr = hours === 0 ? '' : hours + ' hr(s)';
    const sec = remainingSeconds === 0 ? '' : remainingSeconds + ' sec(s)';
    return `${hr} ${min} ${sec}`;
  }
}

export function isObjectEqual(object1: string, object2: string): boolean {
  if (typeof object1 !== 'string' || typeof object2 !== 'string') {
    return false;
  }

  let obj1, obj2;

  try {
    obj1 = JSON.parse(object1);
  } catch {
    return false;
  }

  try {
    obj2 = JSON.parse(object2);
  } catch {
    return false;
  }

  if (obj1 === obj2) return true;

  if (
    typeof obj1 !== 'object' ||
    typeof obj2 !== 'object' ||
    obj1 === null ||
    obj2 === null
  ) {
    return false;
  }

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) return false;

  for (const key of keys1) {
    if (!keys2.includes(key)) return false;
    if (!isObjectEqual(JSON.stringify(obj1[key]), JSON.stringify(obj2[key])))
      return false;
  }

  return true;
}

export function getErrorMessageForInput(
  errors: ValidationErrors,
  label: string = 'Input'
): string {
  for (const error of Object.keys(errors)) {
    switch (error) {
      case 'required':
        return `${label} is required`;
      case 'whitespace':
        return `${label} should not be empty`;
      case 'number':
        return `${label} should be a number`;
      case 'max':
        return `${errors['max']['actual']} is greater than maximum value: ${errors['max']['max']}`;
      case 'min':
        return `${errors['min']['actual']} is less than minimum value: ${errors['min']['min']}`;
      default:
        return '';
    }
  }
  return '';
}

export function noWhitespaceTextareaValidator(
  control: AbstractControl
): ValidationErrors | null {
  return removeHTMLTags(control.value)?.trim() ? null : { whitespace: true };
}

export function noWhitespaceValidator(
  control: AbstractControl
): ValidationErrors | null {
  return control.value?.trim() ? null : { whitespace: true };
}

export const fakePassword = 'password123';

export function generateUUID(): string {
  const buf = new Uint8Array(16);
  crypto.getRandomValues(buf);

  buf[6] = (buf[6] & 0x0f) | 0x40;
  buf[8] = (buf[8] & 0x3f) | 0x80;

  return Array.from(buf)
    .map((b, i) =>
      [4, 6, 8, 10].includes(i)
        ? '-' + b.toString(16).padStart(2, '0')
        : b.toString(16).padStart(2, '0')
    )
    .join('');
}

export function extractPathAndParams(url: string): {
  path: string;
  queryParams: { [k: string]: string };
} {
  const urlObj = new URL(url, window.location.origin);

  const path = urlObj.pathname;

  const queryParams: Record<string, string> = {};
  urlObj.searchParams.forEach((value, key) => {
    queryParams[key] = value;
  });

  return { path, queryParams };
}

export function hasText(value: string): boolean {
  return typeof value === 'string' && value.trim().length > 0;
}

export function getRandomIndex(n: number): number {
  const array = new Uint32Array(1);
  crypto.getRandomValues(array);
  return array[0] % n;
}

export const emailSchema = Joi.string().email({ tlds: { allow: false } });
export const domainSchema = Joi.string().pattern(
  /^(?!-)([a-zA-Z0-9-]{1,63})(?<!-)\.([a-zA-Z]{2,63})(\.[a-zA-Z]{2,63})?$/
);
export function secondsToMilliseconds(seconds: number): number {
  return seconds * 1000;
}

export function escapeHtml(text: string): string {
  const map: Record<string, string> = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#039;',
  };

  return text.replace(/[&<>"']/g, (match) => map[match]);
}

export async function isInternetAvailable(): Promise<boolean> {
  const url = environment['healthCheckUrl']; // Make sure this URL supports CORS

  // Ensure required APIs are available
  if (
    typeof window === 'undefined' ||
    typeof navigator === 'undefined' ||
    typeof fetch === 'undefined'
  ) {
    return false;
  }

  // Check browser offline status first
  if (!navigator.onLine) {
    return false;
  }

  const controller = new AbortController();
  const timeout = setTimeout(() => controller.abort(), 3000); // Set 3-second timeout

  try {
    const response = await fetch(url as string, {
      method: 'GET',
      cache: 'no-cache',
      mode: 'cors',
      credentials: 'same-origin',
      signal: controller.signal,
    });

    clearTimeout(timeout);

    return response.ok || response.type === 'opaque';
  } catch (err) {
    clearTimeout(timeout);

    if (err instanceof DOMException && err.name === 'AbortError') {
      return false;
    }

    if (err instanceof TypeError) {
      const message = err.message.toLowerCase();

      // Check known offline patterns
      const offlinePatterns = [
        'failed to fetch',
        'networkerror',
        'network request failed',
        'internet disconnected',
        'the user aborted a request',
        'request aborted',
        'err_connection_reset',
      ];

      if (offlinePatterns.some((pattern) => message.includes(pattern))) {
        return false;
      }

      return navigator.onLine;
    }

    // Unknown error – fall back to browser's status
    return navigator.onLine;
  }
}

export function formatDateTime(isoString: string): string {
  const date = new Date(isoString);
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  };
  return date.toLocaleString('en-US', options);
}
