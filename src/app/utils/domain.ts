import { ColumnDefinition } from '../Interfaces/generalInterface';

export const domainTableColumns: ColumnDefinition<string>[] = [
  {
    title: 'Domain Name',
    key: 'name',
    width: '250px',
    cursor: 'default',
    sortable: true,
  },

  {
    title: 'Description',
    key: 'description',
    width: '800px',
    cursor: 'pointer',
    truncateLength: 250,
    showTooltip: true,
    sortable: true,
  },

  {
    title: '',
    key: 'actions',
    width: '50px',
    cursor: 'pointer',
  },
];
