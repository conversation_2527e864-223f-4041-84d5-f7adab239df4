import { Component, OnInit } from '@angular/core';
import { SharedService } from '../services/shared/shared.service';
import { DashbordCardComponent } from '../main-app/components/dashboard-card/dashbord-card.component';
import { cardsInfo } from '../utils/dashboard';
import { DashboardSmallCardComponent } from '../main-app/components/dashboard-small-card/dashboard-small-card.component';
import { RolesService } from '../services/usermanagement-service/roles.service';
import { ModifiedRolesData } from '../Interfaces/Types/rolesInterface';
import { CustomButtonComponent } from '../components/custombutton/custombutton.component';
import { Router } from '@angular/router';
import { ReportCustomTableComponent } from '../reportsManagement/components/report-custom-table/report-custom-table.component';
import { ReportManagementService } from '../services/report-management-service/report-management.service';
import {
  OrganizationAssessment,
  OrganizationAssessmentObject,
} from '../Interfaces/Types/reportServiceInterface';
import { ToastService } from '../services/toast-service/toast.service';
import {
  addHeaderIfItIsToShow,
  getDataObjectKeys,
  processTableHeaders,
} from '../utils/report';
import { NoResultFoundComponent } from '../no-result-found/no-result-found.component';
import { RefreshLoaderComponent } from '../components/refresh-loader/refresh-loader.component';

@Component({
  selector: 'app-domain-admin-dashboard',
  standalone: true,
  imports: [
    DashbordCardComponent,
    DashboardSmallCardComponent,
    CustomButtonComponent,
    ReportCustomTableComponent,
    NoResultFoundComponent,
    RefreshLoaderComponent,
  ],
  templateUrl: './domain-admin-dashboard.component.html',
})
export class DomainAdminDashboardComponent implements OnInit {
  items = cardsInfo;
  roles: ModifiedRolesData[] = [];
  recentlyDispatchedAssessments: OrganizationAssessment[] | null = null;
  page = 1;
  size = 10;
  isLoading = true;
  dataObjectKeys: string[] = [];
  sizeOfItemsPerPageDispatchAssessment = 4;
  tableHeaders: { header: string; state: boolean }[] = [];
  tableHeadersConfiguration = [
    {
      dataKey: 'assessmentId',
      label: 'Assessment Id',
      isVisible: false,
    },
    { dataKey: 'title', label: 'Assessment Name', isVisible: true },
    {
      dataKey: 'instructions',
      label: 'Instructions',
      isVisible: false,
    },
    {
      dataKey: 'averageScore',
      label: 'Average Score',
      isVisible: true,
    },
    {
      dataKey: 'numberOfSections',
      label: 'No. of Sections',
      isVisible: true,
    },
    {
      dataKey: 'numberOfCandidates',
      label: 'No. Completed',
      isVisible: true,
    },
  ];
  constructor(
    private readonly sharedService: SharedService,
    private readonly roleService: RolesService,
    private readonly router: Router,
    private readonly reportsService: ReportManagementService,
    private readonly toast: ToastService
  ) {}
  ngOnInit(): void {
    this.sharedService.setPageTitle('Dashboard');
    this.roleService.isRolesLoaded$.subscribe((isLoaded) => {
      if (isLoaded) this.isLoading = false;
    });
    this.roleService.rolesData$.subscribe((data) => {
      if (!data.data.roles.length && this.isLoading) {
        this.roleService.getCompleteRolesData(this.page, this.size);
      }
      this.roles = data.data.roles.slice(0, 6);
    });

    this.getDispatchedAssessments();
  }

  addRole() {
    this.router.navigate(['dashboard/user-management/roles'], {
      queryParams: { button: 'Create-Role' },
    });
  }

  viewFullReport() {
    this.router.navigate(
      ['dashboard/report-management/dispatched-assessments'],
      {
        queryParams: { button: 'View-Report' },
      }
    );
  }

  getDispatchedAssessments() {
    this.reportsService.getOrganizationAssessments().subscribe({
      next: (data: OrganizationAssessmentObject) => {
        this.recentlyDispatchedAssessments = data.data;
        this.dataObjectKeys = getDataObjectKeys(
          this.recentlyDispatchedAssessments[0],
          'numberOfCandidates',
          this.tableHeadersConfiguration
        );
        processTableHeaders(
          this.recentlyDispatchedAssessments,
          'numberOfCandidates',
          (header: string) =>
            addHeaderIfItIsToShow(
              header,
              this.tableHeadersConfiguration,
              this.tableHeaders
            )
        );
        this.isLoading = false;
      },
      error: (error) => {
        this.isLoading = false;
        this.toast.onShow('error', error, true, 'error');
      },
    });
  }

  onDetail(event: { assessmentId: string; index: number }) {
    this.router.navigateByUrl(
      'dashboard/report-management/assessment-candidates/' + event.assessmentId
    );
  }
}
