.feature-flag-admin {
  max-width: 1200px;
  margin: 0 auto;
}

.feature-flags-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1rem;
}

.feature-flag-card {
  transition: all 0.2s ease-in-out;
}

.feature-flag-card:hover {
  transform: translateY(-2px);
}

.category-filter select {
  min-width: 150px;
}

.actions {
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .feature-flags-grid {
    grid-template-columns: 1fr;
  }
  
  .controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .actions {
    margin-left: 0;
    margin-top: 1rem;
    justify-content: stretch;
  }
  
  .actions app-custombutton {
    flex: 1;
  }
}

code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
}

.toggle-switch-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}
