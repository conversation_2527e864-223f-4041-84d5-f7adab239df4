import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { BehaviorSubject } from 'rxjs';
import { FeatureFlagAdminComponent } from './feature-flag-admin.component';
import { FeatureFlagService, FeatureFlagConfig } from '../../services/feature-flag.service';
import { CustomButtonComponent } from '../custombutton/custombutton.component';
import { ToggleSwitchComponent } from '../toggle-switch/toggle-switch.component';

// Mock FeatureFlagService
class MockFeatureFlagService {
  private flagsSubject = new BehaviorSubject<Partial<FeatureFlagConfig>>({
    enableAICodeGeneration: false,
    enableFaceDetection: true,
    enableDarkTheme: true,
    enableAdvancedReporting: false
  });

  flags$ = this.flagsSubject.asObservable();

  setFlags(flags: Partial<FeatureFlagConfig>) {
    this.flagsSubject.next(flags);
  }
}

// Mock CustomButtonComponent
const MockCustomButtonComponent = {
  selector: 'app-custombutton',
  template: '<button (click)="clicked.emit()"><ng-content></ng-content></button>',
  inputs: ['variant', 'disabled'],
  outputs: ['clicked']
};

// Mock ToggleSwitchComponent
const MockToggleSwitchComponent = {
  selector: 'app-toggle-switch',
  template: '<div (click)="onClick()">Toggle</div>',
  inputs: ['toggleInfo'],
  methods: {
    onClick: function() {
      // Mock toggle behavior
    }
  }
};

describe('FeatureFlagAdminComponent', () => {
  let component: FeatureFlagAdminComponent;
  let fixture: ComponentFixture<FeatureFlagAdminComponent>;
  let mockFeatureFlagService: MockFeatureFlagService;

  beforeEach(async () => {
    mockFeatureFlagService = new MockFeatureFlagService();

    await TestBed.configureTestingModule({
      imports: [
        FeatureFlagAdminComponent,
        FormsModule
      ],
      providers: [
        { provide: FeatureFlagService, useValue: mockFeatureFlagService }
      ]
    })
    .overrideComponent(FeatureFlagAdminComponent, {
      remove: { imports: [CustomButtonComponent, ToggleSwitchComponent] },
      add: { 
        imports: [],
        schemas: []
      }
    })
    .compileComponents();

    // Add mock components to declarations
    TestBed.overrideComponent(FeatureFlagAdminComponent, {
      set: {
        template: `
          <div class="feature-flag-admin">
            <h2>Feature Flag Management</h2>
            <select [(ngModel)]="selectedCategory">
              <option *ngFor="let category of categories" [value]="category">
                {{ category }}
              </option>
            </select>
            <div *ngFor="let flag of filteredFlags" class="feature-flag-card">
              <h3>{{ flag.name }}</h3>
              <p>{{ flag.description }}</p>
              <span>{{ flag.category }}</span>
              <button (click)="onToggleFlag(flag)">Toggle</button>
            </div>
            <button (click)="resetToDefaults()">Reset</button>
            <button (click)="exportConfiguration()">Export</button>
          </div>
        `
      }
    });

    fixture = TestBed.createComponent(FeatureFlagAdminComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with correct categories', () => {
    fixture.detectChanges();
    
    expect(component.categories).toContain('All');
    expect(component.categories).toContain('AI Features');
    expect(component.categories).toContain('Proctoring');
    expect(component.categories).toContain('Assessment');
    expect(component.categories).toContain('UI/UX');
  });

  it('should load flag states from service on init', () => {
    const testFlags = {
      enableAICodeGeneration: true,
      enableFaceDetection: false,
      enableDarkTheme: true
    };
    
    mockFeatureFlagService.setFlags(testFlags);
    fixture.detectChanges();

    const aiFlag = component.featureFlags.find(f => f.key === 'enableAICodeGeneration');
    const faceFlag = component.featureFlags.find(f => f.key === 'enableFaceDetection');
    const themeFlag = component.featureFlags.find(f => f.key === 'enableDarkTheme');

    expect(aiFlag?.enabled).toBe(true);
    expect(faceFlag?.enabled).toBe(false);
    expect(themeFlag?.enabled).toBe(true);
  });

  it('should filter flags by category', () => {
    fixture.detectChanges();
    
    // Test "All" category
    component.selectedCategory = 'All';
    expect(component.filteredFlags.length).toBe(component.featureFlags.length);

    // Test specific category
    component.selectedCategory = 'AI Features';
    const aiFlags = component.filteredFlags;
    expect(aiFlags.every(flag => flag.category === 'AI Features')).toBe(true);
    expect(aiFlags.length).toBeGreaterThan(0);

    // Test another category
    component.selectedCategory = 'Proctoring';
    const proctoringFlags = component.filteredFlags;
    expect(proctoringFlags.every(flag => flag.category === 'Proctoring')).toBe(true);
  });

  it('should toggle flag state', () => {
    fixture.detectChanges();
    
    const flag = component.featureFlags.find(f => f.key === 'enableAICodeGeneration');
    const initialState = flag!.enabled;
    
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
    
    component.onToggleFlag(flag!);
    
    expect(flag!.enabled).toBe(!initialState);
    expect(consoleSpy).toHaveBeenCalledWith(
      `Feature flag enableAICodeGeneration ${!initialState ? 'enabled' : 'disabled'}`
    );
    
    consoleSpy.mockRestore();
  });

  it('should reset flags to defaults', () => {
    fixture.detectChanges();
    
    // Change some flags from defaults
    const aiFlag = component.featureFlags.find(f => f.key === 'enableAICodeGeneration');
    const faceFlag = component.featureFlags.find(f => f.key === 'enableFaceDetection');
    
    aiFlag!.enabled = true; // Should be false by default
    faceFlag!.enabled = false; // Should be true by default
    
    component.resetToDefaults();
    
    expect(aiFlag!.enabled).toBe(false);
    expect(faceFlag!.enabled).toBe(true);
  });

  it('should export configuration', () => {
    fixture.detectChanges();
    
    // Mock URL and document methods
    const mockBlob = new Blob(['test'], { type: 'application/json' });
    const mockUrl = 'blob:test-url';
    const mockAnchor = {
      href: '',
      download: '',
      click: jest.fn()
    };
    
    jest.spyOn(window, 'Blob').mockReturnValue(mockBlob);
    jest.spyOn(URL, 'createObjectURL').mockReturnValue(mockUrl);
    jest.spyOn(URL, 'revokeObjectURL').mockImplementation();
    jest.spyOn(document, 'createElement').mockReturnValue(mockAnchor as any);
    
    component.exportConfiguration();
    
    expect(mockAnchor.href).toBe(mockUrl);
    expect(mockAnchor.download).toBe('feature-flags-config.json');
    expect(mockAnchor.click).toHaveBeenCalled();
    expect(URL.revokeObjectURL).toHaveBeenCalledWith(mockUrl);
  });

  it('should track flags by key', () => {
    const flag = component.featureFlags[0];
    const result = component.trackByKey(0, flag);
    expect(result).toBe(flag.key);
  });

  it('should handle flag updates from service', () => {
    fixture.detectChanges();
    
    const newFlags = {
      enableAICodeGeneration: true,
      enableFaceDetection: false,
      enableDarkTheme: false,
      enableAdvancedReporting: true
    };
    
    mockFeatureFlagService.setFlags(newFlags);
    
    // Verify flags are updated
    const aiFlag = component.featureFlags.find(f => f.key === 'enableAICodeGeneration');
    const reportingFlag = component.featureFlags.find(f => f.key === 'enableAdvancedReporting');
    
    expect(aiFlag?.enabled).toBe(true);
    expect(reportingFlag?.enabled).toBe(true);
  });

  it('should handle missing flags gracefully', () => {
    fixture.detectChanges();
    
    const partialFlags = {
      enableAICodeGeneration: true
      // Missing other flags
    };
    
    mockFeatureFlagService.setFlags(partialFlags);
    
    // Should not throw errors
    expect(() => fixture.detectChanges()).not.toThrow();
    
    // Flags not in the update should retain their default values
    const faceFlag = component.featureFlags.find(f => f.key === 'enableFaceDetection');
    expect(faceFlag?.enabled).toBe(false); // Default value
  });

  it('should categorize flags correctly', () => {
    fixture.detectChanges();
    
    const aiFlags = component.featureFlags.filter(f => f.category === 'AI Features');
    const proctoringFlags = component.featureFlags.filter(f => f.category === 'Proctoring');
    const assessmentFlags = component.featureFlags.filter(f => f.category === 'Assessment');
    const uiFlags = component.featureFlags.filter(f => f.category === 'UI/UX');
    
    expect(aiFlags.length).toBeGreaterThan(0);
    expect(proctoringFlags.length).toBeGreaterThan(0);
    expect(assessmentFlags.length).toBeGreaterThan(0);
    expect(uiFlags.length).toBeGreaterThan(0);
    
    // Verify specific flags are in correct categories
    expect(aiFlags.some(f => f.key === 'enableAICodeGeneration')).toBe(true);
    expect(proctoringFlags.some(f => f.key === 'enableFaceDetection')).toBe(true);
    expect(assessmentFlags.some(f => f.key === 'enableIdCapture')).toBe(true);
    expect(uiFlags.some(f => f.key === 'enableDarkTheme')).toBe(true);
  });
});
