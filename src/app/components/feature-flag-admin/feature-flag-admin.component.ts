import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { FeatureFlagService, FeatureFlagConfig } from '../../services/feature-flag.service';
import { CustomButtonComponent } from '../custombutton/custombutton.component';
import { ToggleSwitchComponent } from '../toggle-switch/toggle-switch.component';

interface FeatureFlagItem {
  key: keyof FeatureFlagConfig;
  name: string;
  description: string;
  category: string;
  enabled: boolean;
}

@Component({
  selector: 'app-feature-flag-admin',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    CustomButtonComponent,
    ToggleSwitchComponent
  ],
  templateUrl: './feature-flag-admin.component.html',
  styleUrls: ['./feature-flag-admin.component.css']
})
export class FeatureFlagAdminComponent implements OnInit, OnD<PERSON>roy {
  private destroy$ = new Subject<void>();

  featureFlags: FeatureFlagItem[] = [
    // AI Features
    {
      key: 'enableAICodeGeneration',
      name: 'AI Code Generation',
      description: 'Enable AI-powered code generation for coding questions',
      category: 'AI Features',
      enabled: false
    },
    {
      key: 'enableAITestCaseGeneration',
      name: 'AI Test Case Generation',
      description: 'Enable AI-powered test case generation',
      category: 'AI Features',
      enabled: false
    },
    {
      key: 'enableAIQuestionGeneration',
      name: 'AI Question Generation',
      description: 'Enable AI-powered question generation',
      category: 'AI Features',
      enabled: false
    },
    {
      key: 'enableAISolutionGeneration',
      name: 'AI Solution Generation',
      description: 'Enable AI-powered solution generation',
      category: 'AI Features',
      enabled: false
    },
    {
      key: 'enableAIBoilerplateGeneration',
      name: 'AI Boilerplate Generation',
      description: 'Enable AI-powered boilerplate code generation',
      category: 'AI Features',
      enabled: false
    },

    // Proctoring Features
    {
      key: 'enableFaceDetection',
      name: 'Face Detection',
      description: 'Enable face detection during assessments',
      category: 'Proctoring',
      enabled: true
    },
    {
      key: 'enableScreenRecording',
      name: 'Screen Recording',
      description: 'Enable screen recording during assessments',
      category: 'Proctoring',
      enabled: true
    },
    {
      key: 'enableCameraMonitoring',
      name: 'Camera Monitoring',
      description: 'Enable camera monitoring during assessments',
      category: 'Proctoring',
      enabled: true
    },
    {
      key: 'enableAudioMonitoring',
      name: 'Audio Monitoring',
      description: 'Enable audio monitoring during assessments',
      category: 'Proctoring',
      enabled: false
    },
    {
      key: 'enableScreenSwitchingDetection',
      name: 'Screen Switching Detection',
      description: 'Enable detection of screen switching violations',
      category: 'Proctoring',
      enabled: true
    },

    // Assessment Configuration
    {
      key: 'enableIdCapture',
      name: 'ID Capture',
      description: 'Enable ID capture during assessment setup',
      category: 'Assessment',
      enabled: true
    },
    {
      key: 'enableCandidateCapture',
      name: 'Candidate Capture',
      description: 'Enable candidate photo capture',
      category: 'Assessment',
      enabled: true
    },
    {
      key: 'enableSurveyAfterTest',
      name: 'Post-Test Survey',
      description: 'Enable survey after test completion',
      category: 'Assessment',
      enabled: true
    },

    // UI/UX Features
    {
      key: 'enableDarkTheme',
      name: 'Dark Theme',
      description: 'Enable dark theme option for users',
      category: 'UI/UX',
      enabled: true
    },
    {
      key: 'enableNewDashboard',
      name: 'New Dashboard',
      description: 'Enable the new dashboard design',
      category: 'UI/UX',
      enabled: false
    },
    {
      key: 'enableAdvancedReporting',
      name: 'Advanced Reporting',
      description: 'Enable advanced reporting features',
      category: 'UI/UX',
      enabled: false
    },
    {
      key: 'enableExperimentalFeatures',
      name: 'Experimental Features',
      description: 'Enable experimental and beta features',
      category: 'UI/UX',
      enabled: false
    }
  ];

  categories: string[] = [];
  selectedCategory = 'All';

  constructor(private featureFlagService: FeatureFlagService) {}

  ngOnInit(): void {
    this.categories = ['All', ...new Set(this.featureFlags.map(flag => flag.category))];

    // Load current flag states
    this.featureFlagService.flags$
      .pipe(takeUntil(this.destroy$))
      .subscribe(flags => {
        this.featureFlags.forEach(flag => {
          flag.enabled = flags[flag.key] ?? false;
        });
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  get filteredFlags(): FeatureFlagItem[] {
    if (this.selectedCategory === 'All') {
      return this.featureFlags;
    }
    return this.featureFlags.filter(flag => flag.category === this.selectedCategory);
  }

  onToggleFlag(flag: FeatureFlagItem): void {
    flag.enabled = !flag.enabled;
    // Note: In a real implementation, you would send this to LaunchDarkly
    // or your feature flag management system
    console.log(`Feature flag ${flag.key} ${flag.enabled ? 'enabled' : 'disabled'}`);
  }

  exportConfiguration(): void {
    const config = this.featureFlags.reduce((acc, flag) => {
      acc[flag.key] = flag.enabled;
      return acc;
    }, {} as Record<string, boolean>);

    const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'feature-flags-config.json';
    a.click();
    URL.revokeObjectURL(url);
  }

  resetToDefaults(): void {
    // Reset to default values
    this.featureFlags.forEach(flag => {
      switch (flag.category) {
        case 'AI Features':
          flag.enabled = false;
          break;
        case 'Proctoring':
          flag.enabled = flag.key !== 'enableAudioMonitoring';
          break;
        case 'Assessment':
          flag.enabled = true;
          break;
        case 'UI/UX':
          flag.enabled = flag.key === 'enableDarkTheme';
          break;
        default:
          flag.enabled = false;
      }
    });
  }

  trackByKey(index: number, flag: FeatureFlagItem): string {
    return flag.key;
  }
}
