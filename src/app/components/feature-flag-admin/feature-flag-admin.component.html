<div class="feature-flag-admin p-6 bg-white rounded-lg shadow-lg">
  <div class="header mb-6">
    <h2 class="text-2xl font-bold text-gray-800 mb-2">Feature Flag Management</h2>
    <p class="text-gray-600">Configure feature flags for the application</p>
  </div>

  <div class="controls mb-6 flex flex-wrap gap-4 items-center">
    <div class="category-filter">
      <label class="block text-sm font-medium text-gray-700 mb-1">Category:</label>
      <select 
        [(ngModel)]="selectedCategory" 
        class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        <option *ngFor="let category of categories" [value]="category">
          {{ category }}
        </option>
      </select>
    </div>

    <div class="actions flex gap-2 ml-auto">
      <app-custombutton 
        [variant]="'secondary'" 
        (clicked)="resetToDefaults()"
      >
        Reset to Defaults
      </app-custombutton>
      
      <app-custombutton 
        [variant]="'primary'" 
        (clicked)="exportConfiguration()"
      >
        Export Config
      </app-custombutton>
    </div>
  </div>

  <div class="feature-flags-grid">
    <div 
      *ngFor="let flag of filteredFlags; trackBy: trackByKey" 
      class="feature-flag-card bg-gray-50 p-4 rounded-lg border border-gray-200 hover:shadow-md transition-shadow"
    >
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <div class="flex items-center gap-2 mb-2">
            <h3 class="font-semibold text-gray-800">{{ flag.name }}</h3>
            <span 
              class="px-2 py-1 text-xs rounded-full"
              [ngClass]="{
                'bg-blue-100 text-blue-800': flag.category === 'AI Features',
                'bg-green-100 text-green-800': flag.category === 'Proctoring',
                'bg-purple-100 text-purple-800': flag.category === 'Assessment',
                'bg-orange-100 text-orange-800': flag.category === 'UI/UX'
              }"
            >
              {{ flag.category }}
            </span>
          </div>
          <p class="text-sm text-gray-600 mb-3">{{ flag.description }}</p>
          <div class="text-xs text-gray-500">
            Key: <code class="bg-gray-200 px-1 rounded">{{ flag.key }}</code>
          </div>
        </div>
        
        <div class="ml-4">
          <app-toggle-switch
            [toggleInfo]="{
              label: '',
              toggled: flag.enabled,
              tooltipMessage: flag.enabled ? 'Disable feature' : 'Enable feature'
            }"
            (click)="onToggleFlag(flag)"
          ></app-toggle-switch>
        </div>
      </div>
    </div>
  </div>

  <div *ngIf="filteredFlags.length === 0" class="text-center py-8 text-gray-500">
    No feature flags found for the selected category.
  </div>

  <div class="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
    <h3 class="font-semibold text-blue-800 mb-2">
      <i class="fas fa-info-circle mr-2"></i>
      Important Notes
    </h3>
    <ul class="text-sm text-blue-700 space-y-1">
      <li>• Changes to feature flags may require application restart in some cases</li>
      <li>• AI features require proper API configuration to function</li>
      <li>• Proctoring features require camera and microphone permissions</li>
      <li>• Some features may have dependencies on other enabled features</li>
    </ul>
  </div>
</div>
