<button
  class="min-w-[10rem] flex justify-center items-center"
  [ngClass]="{
    primary: variant === 'primary',
    secondary: variant === 'secondary',
    tertiary: variant === 'tertiary',
    testSave: variant === 'testSave',
    primaryWithGreenBg: variant === 'primaryWithGreenBg',
  }"
  [type]="type"
  [disabled]="disabled"
  (click)="onClick($event)"
  id="{id}"
>
  <ng-container *ngIf="spinner">
    <i class="pi pi-spin pi-spinner mr-2" style="font-size: 1rem"></i>
  </ng-container>
  <div class="capitalize flex">
    <ng-content></ng-content>
  </div>
</button>
