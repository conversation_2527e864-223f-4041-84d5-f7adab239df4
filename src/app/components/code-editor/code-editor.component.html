<div class="code-editor w-full relative h-full" [id]="editorId">
  <div
    class="absolute inset-0 flex flex-col items-center justify-center z-10 bg-white dark:bg-gray-900"
    *ngIf="loading"
  >
    <div
      class="w-8 h-8 border-4 border-gray-300 border-t-blue-500 rounded-full animate-spin"
    ></div>
    <span class="mt-2 text-sm text-gray-600 dark:text-gray-300"
      >Loading editor...</span
    >
  </div>

  <!-- Monaco Container -->
  <div
    #editorContainer
    class="w-full h-full"
    [class.hidden]="loading"
    [id]="editorId + '-container'"
  ></div>
</div>
@if (error) {
  <p>please refresh the page</p>
}
