<div class="w-[97.5%] mx-auto my-4">
  <button
    type="button"
    class="bg-[#f8fafd] hover:bg-[#e6f3ff] w-full p-6 border-l-4 border-[#0C4766] flex justify-between items-center cursor-pointer transition-all duration-300"
    [class.rounded-b-lg]="!isExpanded"
    [class.rounded-t-lg]="true"
    (click)="toggleExpanded()"
    [attr.aria-pressed]="isExpanded"
  >
    <p class="text-[#082230] text-lg font-medium">{{ title }}</p>
    <span
      class="transition-transform duration-300 ease-in-out"
      [class.rotate-180]="isExpanded"
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        class="text-[#0C4766]"
      >
        <polyline points="6,9 12,15 18,9"></polyline>
      </svg>
    </span>
  </button>

  <div
    class="w-full bg-[#f8fafd] border-l-4 border-[#0C4766] overflow-hidden transition-all duration-500 ease-in-out"
    [class]="isExpanded ? 'max-h-[500px] opacity-100' : 'max-h-0 opacity-0'"
  >
    <div class="p-6 pt-2 space-y-4 rounded-b-lg shadow-sm">
      @for (instruction of instructions; track $index) {
        <div class="flex items-start group">
          <span
            class="inline-flex items-center justify-center w-6 h-6 bg-[#0C4766] text-white rounded-full text-sm font-medium mr-3 mt-0.5 flex-shrink-0 group-hover:scale-110 transition-transform"
          >
            {{ $index + 1 }}
          </span>
          <p
            class="text-[#082230] text-base leading-relaxed group-hover:text-[#0C4766] transition-colors"
          >
            {{ instruction }}
          </p>
        </div>
      }
    </div>
  </div>
</div>
