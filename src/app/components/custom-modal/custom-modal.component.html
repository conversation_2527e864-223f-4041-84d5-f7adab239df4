<div *ngIf="visibleModal" id="myModal" class="modal">
  <div
    [style.background-color]="backgroundColor"
    class="modal-content rounded-lg overflow-auto max-w-[90%] max-h-[90%]"
    [style.minWidth]="width"
    [style.minHeight]="height"
  >
    <div
      class="modal-header sticky top-0 flex justify-between items-center py-[24px] px-[31px]"
      [style.background-color]="headerColor"
    >
      <div
        class="w-full flex flex-col gap-1"
        [style]="{ 'text-align': textAlign }"
      >
        <span class="flex items-center gap-2">
          <ng-content select="[headerImage]"></ng-content>
          <span>
            <h1
              class="font-semibold text-[0.8rem] lg:text-[20px]"
              [style.color]="textColor"
            >
              {{ headerTitle }}
            </h1>
            <ng-content select="[description]"></ng-content>
          </span>
        </span>
      </div>
      <span
        class="ml-2 close text-[1.5em] cursor-pointer"
        [style.color]="textColor"
        aria-hidden="true"
        (click)="hideDialog()"
        (keydown)="hideDialogKeyBoard($event)"
        >&times;</span
      >
    </div>
    <div [class]="center ? 'grid place-items-center' : ''">
      <ng-content></ng-content>
    </div>
  </div>
</div>
