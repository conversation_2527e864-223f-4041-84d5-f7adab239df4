<div *ngIf="visibleModal" id="myModal" class="modal">
  <div
    [style.background-color]="backgroundColor"
    class="modal-content rounded-lg max-w-[720px] max-h-[820px] pb-4"
    [style.width]="width"
    [style.height]="height"
    [ngClass]="caution ? 'border border-red-500' : ''"
  >
    <div
      class="flex justify-end px-[16px]"
      [ngClass]="!closeButton ? 'invisible' : ''"
    >
      <span
        class="close text-[2em] cursor-pointer text-[#71717A] font-light"
        aria-hidden="true"
        (click)="hideDialog()"
        (keydown)="hideDialogKeyBoard($event)"
        >&times;</span
      >
    </div>

    <div
      class="modal-header flex justify-between items-center pb-[8px] px-[31px]"
      [style.background-color]="headerColor"
    >
      <div class="w-full" [style]="{ 'text-align': textAlign }">
        <div class="flex w-full justify-center gap-x-2">
          @if (caution) {
            <svg
              width="28"
              height="25"
              viewBox="0 0 28 25"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M27.6004 20.5113L16.6691 1.52758C16.396 1.06249 16.006 0.676856 15.5379 0.408908C15.0698 0.14096 14.5397 0 14.0004 0C13.461 0 12.931 0.14096 12.4629 0.408908C11.9947 0.676856 11.6048 1.06249 11.3316 1.52758L0.400364 20.5113C0.137534 20.9612 -0.000976562 21.4728 -0.000976562 21.9938C-0.000976562 22.5148 0.137534 23.0265 0.400364 23.4763C0.670027 23.9442 1.05933 24.332 1.52832 24.5997C1.9973 24.8675 2.52909 25.0057 3.06911 25.0001H24.9316C25.4712 25.0052 26.0025 24.8669 26.471 24.5991C26.9395 24.3314 27.3284 23.9439 27.5979 23.4763C27.8611 23.0267 28 22.5152 28.0005 21.9942C28.0009 21.4732 27.8628 20.9614 27.6004 20.5113ZM25.8666 22.4751C25.7713 22.6376 25.6345 22.7719 25.4702 22.8642C25.3059 22.9564 25.12 23.0033 24.9316 23.0001H3.06911C2.88071 23.0033 2.69483 22.9564 2.53053 22.8642C2.36622 22.7719 2.22941 22.6376 2.13411 22.4751C2.04779 22.3289 2.00226 22.1623 2.00226 21.9926C2.00226 21.8228 2.04779 21.6562 2.13411 21.5101L13.0654 2.52633C13.1626 2.36455 13.3 2.23068 13.4643 2.13774C13.6286 2.04479 13.8141 1.99595 14.0029 1.99595C14.1916 1.99595 14.3771 2.04479 14.5414 2.13774C14.7057 2.23068 14.8431 2.36455 14.9404 2.52633L25.8716 21.5101C25.9572 21.6567 26.0018 21.8235 26.001 21.9933C26.0001 22.163 25.9537 22.3294 25.8666 22.4751ZM13.0004 15.0001V10.0001C13.0004 9.73487 13.1057 9.48051 13.2933 9.29298C13.4808 9.10544 13.7351 9.00008 14.0004 9.00008C14.2656 9.00008 14.5199 9.10544 14.7075 9.29298C14.895 9.48051 15.0004 9.73487 15.0004 10.0001V15.0001C15.0004 15.2653 14.895 15.5197 14.7075 15.7072C14.5199 15.8947 14.2656 16.0001 14.0004 16.0001C13.7351 16.0001 13.4808 15.8947 13.2933 15.7072C13.1057 15.5197 13.0004 15.2653 13.0004 15.0001ZM15.5004 19.5001C15.5004 19.7968 15.4124 20.0868 15.2476 20.3334C15.0827 20.5801 14.8485 20.7724 14.5744 20.8859C14.3003 20.9994 13.9987 21.0291 13.7077 20.9713C13.4168 20.9134 13.1495 20.7705 12.9397 20.5607C12.7299 20.351 12.5871 20.0837 12.5292 19.7927C12.4713 19.5017 12.501 19.2001 12.6145 18.9261C12.7281 18.652 12.9203 18.4177 13.167 18.2529C13.4137 18.0881 13.7037 18.0001 14.0004 18.0001C14.3982 18.0001 14.7797 18.1581 15.061 18.4394C15.3423 18.7207 15.5004 19.1023 15.5004 19.5001Z"
                fill="#ED2020"
              />
            </svg>
          }
          @if (showHeader) {
            <h1 class="font-semibold text-[20px] text-[#0C4767]">
              {{ headerTitle }}
            </h1>
          }
        </div>
      </div>
    </div>
    @if (showBody) {
      <div class="grid place-items-center">
        <p
          class="text-center text-[18px] mx-[54px] text-[#474D66]"
          [appSafeHtml]="bodyText"
        ></p>
        @if (showButtons) {
          <div class="flex gap-x-3 justify-center mt-[24px]">
            @if (showLeftButton) {
              <app-custombutton
                [variant]="'secondary'"
                (clicked)="button1Click()"
                (keydown)="handleKeyButton1($event)"
              >
                {{ left }}
              </app-custombutton>
            }

            <app-custombutton
              [variant]="'primary'"
              (clicked)="button2Click()"
              (keydown)="handleKeyButton2($event)"
              [spinner]="loading"
            >
              {{ right }}
            </app-custombutton>
          </div>
        }

        @if (loaderBelow) {
          <div class="flex flex-col">
            <app-dodokpo-loader></app-dodokpo-loader>
            <p>Submitting...</p>
          </div>
        }
      </div>
    }
    <ng-content></ng-content>
  </div>
</div>
