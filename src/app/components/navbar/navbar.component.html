@if (isLoading) {
  <p>loading</p>
} @else {
  <nav
    class="w-full bg-[#EFEFEF] flex flex-col-reverse lg:flex-row lg:justify-between"
  >
    <div class="lg:mt-11 flex flex-col">
      <p class="flex flex-row text-xs text-[#474D66] items-center">
        <app-breadcrumb></app-breadcrumb>
      </p>
      @defer {
        <h1
          class="font-medium text-H700 md:text-[32px] mb-[2rem] leading-10 text-[#0C4767]"
          [title]="pageTitle"
        >
          {{ pageTitle }}
        </h1>
      }
    </div>
    <div class="flex justify-end items-center gap-x-5 pr-4 relative">
      <button
        class="relative cursor-pointer min-w-[33px] h-[33px] bg-[#7A9AAC66] rounded-full flex justify-center items-center"
        (click)="toggleNotification($event)"
      >
        @if (soundEnabled) {
          <img src="../../../assets/dashboardImages/bell.svg" alt="bell" />
        } @else {
          <img
            src="../../../assets/notificationImages/notification-badge-no-sound.svg"
            alt="bell"
          />
        }
        @if (notificationCount > 0) {
          <p
            class="absolute top-[-10px] right-[-10px] text-[#000] text-xs font-bold w-[20px] h-[20px] rounded-full flex justify-center items-center"
          >
            {{ notificationCount > 10 ? '10+' : notificationCount }}
          </p>
        }
      </button>

      <div class="vertical-line h-[55px] w-[1px] bg-[#8f95b2]"></div>
      <div class="usernameWithGreeting">
        Hello, <br />
        {{ profileName }}
      </div>
      <button
        class="flex items-center gap-x-2 mt-4 lg:mt-0"
        (click)="toggleDropdown($event)"
      >
        <div
          class="profileImage bg-slate-400 min-w-[40px] max-w-[40px] h-[40px] rounded-full overflow-hidden"
        >
          <img class="h-full" src="{{ image }}" alt="" />
        </div>
        <button class="dropdown">
          <img src="../../../assets/dashboardImages/dropdown.svg" alt="" />
        </button>
      </button>

      @if (dropdownOpen) {
        <div class="absolute top-[1.3rem] right-0 z-[60]">
          <app-action-items-pop-up
            (closeModal)="closeProfileTray()"
            [actionButtonItems]="actionButtonItems"
          ></app-action-items-pop-up>
        </div>
      }
    </div>
  </nav>

  @if (notificationsOpen) {
    <div
      class="absolute top-[50px] lg:top-[80px] z-[100] lg:mr-3"
      [ngStyle]="{ right: notifications.length > 0 ? '40px' : '0px' }"
    >
      <app-notifications-drop-downdown
        [notificationsOpen]="notificationsOpen"
        (closeNotifications)="closeNotificationModal()"
      ></app-notifications-drop-downdown>
    </div>
  }
}
