<div class="hamburger-container lg:hidden fixed top-4 left-4 z-[100]">
  <button
    (click)="toggleSidebar()"
    class="p-2 bg-white rounded-lg shadow-md"
    title="Toggle Sidebar"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="#0C4767"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
    >
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </button>
</div>

@if (isSidebarOpen) {
  <div
    class="fixed inset-0 bg-black bg-opacity-50 z-[90] lg:hidden"
    (click)="closeSidebar()"
    (keydown)="closeSidebar()"
    tabindex="0"
  ></div>
}

<aside
  [ngClass]="{
    'translate-x-0': isSidebarOpen,
    '-translate-x-full': !isSidebarOpen && isTabletOrMobile,
  }"
  class="fixed lg:relative z-[100] lg:z-auto mobile-size h-screen max-h-screen overflow-y-auto w-[194px] bg-white rounded-tl-lg rounded-br-[8px] rounded-tr-[8px] flex flex-col items-center transform transition-transform duration-300 ease-in-out lg:translate-x-0"
>
  <nav>
    <div class="logo mt-5 flex justify-center mb-16">
      <div>
        <img
          class="w-[78px] h-[69px]"
          src="../../../assets/icons/dodopkoIcon.svg"
          alt=""
        />
      </div>
    </div>
    <ul class="flex flex-col gap-y-5">
      @for (content of sideBarContent; track content) {
        <li
          class="mobile-active flex w-[140px] h-[78px] justify-center rounded-lg flex-col items-center text-center cursor-pointer"
          routerLinkActive="bg-[#DDE8EF]"
          routerLink="{{ content.path }}"
          [attr.data-prefetch]="true"
          tabindex="0"
          (click)="closeSidebarOnMobile()"
          (keydown)="closeSidebarOnMobile()"
        >
          <div class="mobile-icon-sizes">
            <img
              class="w-full"
              src="../../../assets/dashboardImages/{{ content.image }}"
              [ngClass]="{ 'active-icon': activePath === content.path }"
              alt=""
            />
          </div>
          <span class="mobile text-[12px] font-medium text-[#0C4767]">{{
            content.title
          }}</span>
        </li>
      }
    </ul>
  </nav>
</aside>
