import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CustomGraphComponent } from './custom-graph.component';
import { ElementRef } from '@angular/core';
import { ChartModule } from 'primeng/chart';

describe('CustomGraphComponent', () => {
  let component: CustomGraphComponent;
  let fixture: ComponentFixture<CustomGraphComponent>;
  let mockElementRef: {
    nativeElement: {
      offsetWidth: number;
      offsetHeight: number;
    };
  };

  class MockResizeObserver {
    observe() {}
    unobserve() {}
    disconnect() {}
  }
  global.ResizeObserver = MockResizeObserver;

  beforeEach(async () => {
    mockElementRef = {
      nativeElement: {
        offsetWidth: 800,
        offsetHeight: 600,
      },
    };

    await TestBed.configureTestingModule({
      imports: [ChartModule, CustomGraphComponent],
      providers: [{ provide: ElementRef, useValue: mockElementRef }],
    }).compileComponents();

    fixture = TestBed.createComponent(CustomGraphComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should set basic options with correct configuration', () => {
      const getComputedStyleMock = jest.fn().mockReturnValue({
        getPropertyValue: jest.fn(),
      });

      window.getComputedStyle = getComputedStyleMock;

      fixture.componentRef.setInput('data', [
        { x: '2023-01-01', y: 10 },
        { x: '2023-01-02', y: 20 },
      ]);
    });

    it('should handle empty data array', () => {
      fixture.componentRef.setInput('data', []);
      fixture.detectChanges();

      component.setData();

      expect(component.basicData).toEqual({
        datasets: [],
        labels: ['Sun', 'Mon', 'Tues', 'Wed', 'Thurs', 'Fri', 'Sat'],
      });
    });
  });

  describe('onResize', () => {
    it('should call setData when window is resized', () => {
      const setDataSpy = jest.spyOn(component, 'setData');

      window.dispatchEvent(new Event('resize'));

      expect(setDataSpy).toHaveBeenCalled();

      setDataSpy.mockRestore();
    });
  });
});

describe('CustomGraphComponent', () => {
  let component: CustomGraphComponent;
  let fixture: ComponentFixture<CustomGraphComponent>;
  let mockElementRef: {
    nativeElement: {
      offsetWidth: number;
      offsetHeight: number;
    };
  };

  class MockResizeObserver {
    observe() {}
    unobserve() {}
    disconnect() {}
  }
  global.ResizeObserver = MockResizeObserver;

  beforeEach(async () => {
    mockElementRef = {
      nativeElement: {
        offsetWidth: 800,
        offsetHeight: 600,
      },
    };

    await TestBed.configureTestingModule({
      imports: [ChartModule, CustomGraphComponent],
      providers: [{ provide: ElementRef, useValue: mockElementRef }],
    }).compileComponents();

    fixture = TestBed.createComponent(CustomGraphComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should handle empty data array', () => {
      fixture.componentRef.setInput('data', []);
      fixture.detectChanges();

      component.setData();

      expect(component.basicData).toEqual({
        datasets: [],
        labels: ['Sun', 'Mon', 'Tues', 'Wed', 'Thurs', 'Fri', 'Sat'],
      });
    });
  });

  describe('setData', () => {
    it('should set basicData with correct labels and data', () => {
      const mockPlotData = [
        {
          title: '',
          plots: [
            { x: '2023-01-01', y: 10 },
            { x: '2023-01-02', y: 20 },
          ],
        },
      ];
      fixture.componentRef.setInput('data', mockPlotData);
      fixture.detectChanges();

      component.setData();

      expect(component.basicData).toEqual({
        labels: ['2023-01-01', '2023-01-02'],
        datasets: [
          {
            label: '',
            data: [10, 20],
            borderWidth: 1,
          },
        ],
      });
    });

    it('should handle data with missing plots', () => {
      const mockPlotData = [
        {
          title: 'Assessments',
          plots: [
            { x: '2023-01-01', y: 10 },
            { x: '2023-01-02', y: null },
          ],
        },
      ];
      fixture.componentRef.setInput('data', mockPlotData);
      fixture.detectChanges();

      component.setData();

      expect(component.basicData).toEqual({
        labels: ['2023-01-01', '2023-01-02'],
        datasets: [
          {
            label: 'Assessments',
            data: [10, null],
            borderWidth: 1,
          },
        ],
      });
    });
  });

  describe('onResize', () => {
    it('should call setData when window is resized', () => {
      const setDataSpy = jest.spyOn(component, 'setData');

      window.dispatchEvent(new Event('resize'));

      expect(setDataSpy).toHaveBeenCalled();

      setDataSpy.mockRestore();
    });
  });

  describe('constructor effect', () => {
    it('should call setData when data changes', () => {
      const setDataSpy = jest.spyOn(component, 'setData');

      fixture.componentRef.setInput('data', [
        { plots: [{ x: '2023-01-03', y: 30 }] },
      ]);
      fixture.detectChanges();

      expect(setDataSpy).toHaveBeenCalled();
    });
  });
});
