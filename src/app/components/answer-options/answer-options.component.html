<section class="p-4">
  <h3 class="text-[#262626] text-lg font-medium">
    @if (questionType === 'true-or-false') {
      Select the Correct Answer
    } @else {
      <span class="flex gap-2 items-center"
        ><span>Answer Options</span>
        <app-required-svg />
      </span>
    }
  </h3>

  <div class="flex text-[#082230CC] justify-between items-center mb-4">
    <p>
      @if (questionType === 'multi-select') {
        Add Options and select the correct answer(s)
      } @else if (questionType === 'multi-choice') {
        Add options and select the correct answer. You need at least 2 options
        with exactly one answer
      } @else if (questionType === 'true-or-false') {
        Choose whether the above statement is true or False
      }
    </p>
  </div>

  @if (questionType === 'multi-choice') {
    @if (optionsArray && optionsArray.length > 0) {
      <div class="bg-[#08223008] rounded-lg mb-4">
        <div class="options-container">
          <app-multi-choice-options
            [optionsArray]="optionsArray"
            [form]="form"
            [isEdit]="isEdit"
            [selectedAnswerIndex]="selectedAnswerIndex"
            (emitDeleteOption)="deleteOption.emit($event)"
            (radioChange)="radioChange.emit($event)"
          ></app-multi-choice-options>
        </div>
      </div>
    }
  } @else if (questionType === 'multi-select') {
    @if (optionsArray && optionsArray.length > 0) {
      <div class="bg-[#08223008] rounded-lg mb-4">
        <div class="options-container">
          <app-multi-select-options
            [optionsArray]="optionsArray"
            [selectedAnswerIndex]="selectedAnswerIndex"
            [form]="form"
            [isEdit]="isEdit"
            (emitDeleteOption)="deleteOption.emit($event)"
            (isCheckedEmitter)="isChecked.emit($event)"
          ></app-multi-select-options>
        </div>
      </div>
    }
  } @else if (questionType === 'true-or-false') {
    <div class="bg-[#08223008] rounded-lg mb-4">
      <div class="options-container">
        <app-true-or-false-options
          [form]="form"
          [optionsArray]="optionsArray"
          [isEdit]="isEdit"
          [selectedAnswerIndex]="selectedAnswerIndex"
          (radioChange)="onRadioChange($event)"
        ></app-true-or-false-options>
      </div>
    </div>
  }

  @if (questionType === 'multi-choice' || questionType === 'multi-select') {
    <div class="button-container mb-6">
      <app-custombutton
        class="add-option-btn"
        [variant]="'secondary'"
        (keydown)="addOptionKeydown.emit($event)"
        (click)="addOption.emit()"
        >Add Option</app-custombutton
      >
    </div>
  }
</section>
