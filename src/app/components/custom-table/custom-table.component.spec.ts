import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { CustomTableComponent } from './custom-table.component';
import { CustomPaginationComponent } from '../custom-pagination/custom-pagination.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ColumnDefinition } from '../../Interfaces/generalInterface';

fdescribe('CustomTableComponent', () => {
  let component: CustomTableComponent<string>;
  let fixture: ComponentFixture<CustomTableComponent<string>>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        FormsModule,
        NgxPaginationModule,
        CustomPaginationComponent,
        CustomTableComponent,
        HttpClientTestingModule,
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(CustomTableComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should calculate the range correctly on changes', () => {
    jest.spyOn(component, 'ngOnChanges').mockImplementation();
    component.currentPage = 1;
    component.pageSize = 10;
    component.totalNumberOfItems = 100;

    component.ngOnChanges();

    expect(component.ngOnChanges).toHaveBeenCalled();
    expect(component.range).toBe('');
  });

  it('should emit page change event', () => {
    jest.spyOn(component.pageChange, 'emit').mockImplementation();

    component.onPageChange(2);
    expect(component.currentPage).toBe(2);
    expect(component.pageChange.emit).toHaveBeenCalledWith(2);
  });

  it('should start editing on cell click', () => {
    component.startEditing(1, 1);
    expect(component.editingCell).toEqual({ rowIndex: 1, colIndex: 1 });
  });

  it('should return true if the cell is being edited', () => {
    component.editingCell = { rowIndex: 1, colIndex: 1 };
    component.editingEnabled = true;
    expect(component.isEditing(1, 1)).toBeTruthy();
  });

  it('should return false if the cell is not being edited', () => {
    component.editingCell = { rowIndex: 1, colIndex: 1 };
    component.editingEnabled = true;
    expect(component.isEditing(1, 2)).toBeFalsy();
  });

  it('should render table headers', () => {
    component.columns = [
      { title: 'Column 1', key: 'col1' },
      { title: 'Column 2', key: 'col2' },
    ];
    fixture.detectChanges();
    const headers = fixture.debugElement.queryAll(By.css('th'));
    expect(headers.length).toBe(2);
    expect(headers[0].nativeElement.textContent.trim()).toBe('Column 1');
    expect(headers[1].nativeElement.textContent.trim()).toBe('Column 2');
  });

  it('should render table rows', () => {
    component.data = [
      { col1: 'Data 1-1', col2: 'Data 1-2' },
      { col1: 'Data 2-1', col2: 'Data 2-2' },
    ];
    component.columns = [
      { title: 'Column 1', key: 'col1' },
      { title: 'Column 2', key: 'col2' },
    ];
    component.pageSize = 10;
    component.currentPage = 1;
    component.totalNumberOfItems = 2;
    fixture.detectChanges();
    const rows = fixture.debugElement.queryAll(By.css('tbody tr'));
    expect(rows.length).toBe(3);
  });

  it('should sort column', () => {
    component.sortColumn = 'name';
    component.sortDirection = 'asc';
    component.onSortColumn('name');
    expect(component.sortDirection).toBe('desc');
    component.onSortColumn('name');
    expect(component.sortDirection).toBe('asc');
  });

  it('should emit size change event', () => {
    jest.spyOn(component.sizeChange, 'emit').mockImplementation();

    component.onPageSizeChange(20);
    expect(component.sizeChange.emit).toHaveBeenCalledWith(20);
  });

  it('should #isSystem return true for system role', () => {
    expect(component.isSystem('Test Manager', {}, 'role')).toBe(true);
    expect(component.isSystem('Test role', { system: 'true' }, 'name')).toBe(
      true
    );
  });

  it('should #onRowClick emit row click event', () => {
    const row = { id: '1', name: 'Test' }; // Convert id to string
    jest.spyOn(component.rowClickedOnTable, 'emit').mockImplementation();
    component.onRowClick(row, 0);
    expect(component.rowClickedOnTable.emit).toHaveBeenCalledWith({
      ...row,
      index: 0,
    });
  });

  it('should not add index if row has no empty strings', () => {
    component.data = [
      { name: 'John', role: 'Admin' },
      { name: '', role: 'User' },
      { name: 'Alice', role: 'Manager' },
    ];
    const row = JSON.stringify(component.data[0]); // Convert object to string

    component.validateRow(row);
    expect(component.errorRows.has(0)).toBe(false);
  });

  it('should #closeModal close the modal', () => {
    component.closeModal();
    expect(component.selectedCardIndex).toBe(null);
  });

  it('should #isStringEmpty return true for empty string', () => {
    expect(component.isString('')).toBe(true);
    expect(component.isString('test')).toBe(true);
    expect(component.isString(123)).toBe(false);
  });

  it('should #getTextColor return correct color', () => {
    const value = 'test';
    const column = {
      getTextColor: (value: string) => {
        return value === 'test' ? 'red' : 'blue';
      },
    } as ColumnDefinition<string>;
    const result = component.getTextColor(column, value);
    expect(result).toBe('red');
  });

  it('should #getTextColor return default color', () => {
    const value = 'test';
    const column = {} as ColumnDefinition<string>;
    const result = component.getTextColor(column, value);
    expect(result).toBe('inherit');
  });

  it('should #getTextColor return correct color', () => {
    const value = 'test';
    const column = {
      getBackgroundColor: (value: string) => {
        return value === 'test' ? 'red' : 'blue';
      },
    } as ColumnDefinition<string>;
    const result = component.getBackgroundColor(column, value);
    expect(result).toBe('red');
  });

  it('should #getDynamicStyles return correct styles', () => {
    const item = { error: 'error' };
    const result = component.getDynamicStyles(item);
    expect(result).toEqual({
      'background-color': '#ffdddd',
      cursor: 'pointer',
    });
  });

  it('should #getDynamicStyles return correct styles', () => {
    jest.spyOn(component.actionButtonClickedFromTable, 'emit');
    const item = { error: 'error' };
    component.clickedActionButton(item, 0);
    expect(component.selectedCardIndex).toEqual(0);
    expect(component.actionButtonClickedFromTable.emit).toHaveBeenCalledWith(
      item
    );
  });
});
