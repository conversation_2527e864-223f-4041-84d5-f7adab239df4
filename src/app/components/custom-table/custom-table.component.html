<div class="table-wrapper">
  <div class="table-container">
    <div
      class="table-wrapper"
      [ngClass]="data.length >= 2 ? 'overflowContainer' : ''"
    >
      <table aria-describedby="custom-table">
        <thead class="sticky-header">
          <tr>
            @if (showRowNumbers) {
              <th class="text-left">#</th>
            }
            @for (column of columns; track column; let index = $index) {
              <th
                (click)="column.sortable ? onSortColumn(column.key) : null"
                (keydown.enter)="
                  column.sortable ? onSortColumn(column.key) : null
                "
                [ngStyle]="{
                  cursor: column.sortable ? 'pointer' : 'default',
                  textAlign: column.textAlign,
                  minWidth: getColumnMinWidth(column.title),
                }"
              >
                <span
                  class="flex gap-x-1 justify-{{ column.textAlign || 'start' }}"
                >
                  {{ column.title }}
                  @if (column.sortable) {
                    <img
                      [src]="
                        sortIcons[column.key] ||
                        '../../../assets/icons/sortIcon.svg'
                      "
                      alt="Sort Icon"
                    />
                  }
                </span>
              </th>
            }
          </tr>
        </thead>
        <tbody>
          @for (
            row of data
              | paginate
                : {
                    itemsPerPage: pageSize,
                    currentPage: currentPage,
                    totalItems: totalNumberOfItems,
                  };
            track row;
            let rowIndex = $index
          ) {
            <tr
              [id]="'row-' + rowIndex"
              (click)="isRowClickable(row) ? onRowClick(row, rowIndex) : null"
              (keydown.enter)="
                isRowClickable(row) ? onRowClick(row, rowIndex) : null
              "
              [ngClass]="{
                'clickable-row': isRowClickable(row),
                'error-row': hasErrors(row),
              }"
              [ngStyle]="validate ? getDynamicStyles(row) : {}"
            >
              @if (showRowNumbers) {
                <td>{{ rowIndex + 1 }}</td>
              }
              @for (column of columns; track $index) {
                @if (
                  column.key === 'actions' &&
                  (!column.showActionButton ||
                    column?.showActionButton(row, loggedInUserId))
                ) {
                  <td class="flex justify-center relative">
                    <app-action-button
                      (actionButtonClicked)="clickedActionButton(row, rowIndex)"
                    ></app-action-button>
                    @if (selectedCardIndex === rowIndex) {
                      <div
                        class="absolute right-0 lg:right-[3.0vw] min-w-[10rem] z-20"
                        [ngStyle]="{
                          top:
                            rowIndex === data.length - 1 ||
                            rowIndex === data.length - 2
                              ? '-7rem'
                              : '-2.0rem',
                        }"
                      >
                        <app-action-items-pop-up
                          [actionButtonItems]="actionButtonItems"
                          (closeModal)="closeModal()"
                        ></app-action-items-pop-up>
                      </div>
                    }
                  </td>
                } @else {
                  <td [attr.data-label]="column.title">
                    <div
                      [ngStyle]="{
                        display: column.getImageUrl ? 'flex' : 'inherit',
                        alignItems: column.getImageUrl ? 'center' : 'inherit',
                        justifyContent: column.getImageUrl
                          ? 'center'
                          : 'inherit',
                      }"
                    >
                      <span
                        [ngStyle]="{
                          textAlign: column.textAlign,
                          display: column.getImageUrl ? 'flex' : 'inherit',
                          'background-color': column.getBackgroundColor
                            ? column.getBackgroundColor(row[column.key])
                            : 'inherit',
                          color: column.getTextColor
                            ? column.getTextColor(row[column.key])
                            : 'inherit',
                          width: column.width,
                          alignItems: column.getImageUrl ? 'center' : 'inherit',
                          justifyContent: column.getImageUrl
                            ? 'center'
                            : 'inherit',
                          borderRadius: column.getBackgroundColor
                            ? '8px'
                            : 'inherit',
                          paddingLeft:
                            column.getBackgroundColor &&
                            column.getBackgroundColor(row[column.key])
                              ? column.paddingLeft
                              : '0px',
                          paddingRight: column.paddingRight ?? '',
                        }"
                      >
                        @if (column.getImageUrl) {
                          <span
                            ><img
                              src="{{ column.getImageUrl(row[column.key]) }}"
                              alt=""
                          /></span>
                          {{ row[column.key] }}
                        } @else {
                          @if (longTruncate) {
                            <span
                              [title]="row[column.key]"
                              [ngClass]="
                                isSystem(row[column.key], row, column.key)
                                  ? 'flex whitespace-nowrap'
                                  : ''
                              "
                            >
                              @if (isSystem(row[column.key], row, column.key)) {
                                <span>
                                  {{ row[column.key] | truncateLongText: 150 }}
                                  <span
                                    class="bg-[#DDE8EF] py-1 px-3 rounded-full ml-2 text-[11px]"
                                    >System</span
                                  >
                                </span>
                              } @else {
                                {{ row[column.key] | truncateLongText: 150 }}
                              }
                            </span>
                          }
                          @if (!longTruncate) {
                            <span [title]="row[column.key]">
                              {{ row[column.key] | truncateLongText: 20 }}</span
                            >
                          }
                        }
                      </span>
                    </div>
                  </td>
                }
              }
            </tr>
          }
          <!-- Extra row at the bottom of the table -->
          <tr class="extra-bottom-row">
            @if (showRowNumbers) {
              <td></td>
            }
            @for (column of columns; track $index) {
              <td>
                @if (column.key === 'actions') {
                  <!-- Empty cell for actions column -->
                } @else {
                  <!-- Default content for extra row -->
                  <span class="text-gray-400 italic">-</span>
                }
              </td>
            }
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

@if (showPagination) {
  <div
    class="footer md:flex w-[100%] max-w-[100%] mb-2 justify-between items-center mt-4"
  >
    <div class="text-[#474D66] text-[16px] font-medium" *ngIf="showRange">
      {{ range }}
    </div>
    <app-custom-pagination
      [total]="totalNumberOfItems"
      [size]="pageSize"
      (pagechange)="onPageChange($event)"
      (sizeSelect)="onPageSizeChange($event)"
    ></app-custom-pagination>
  </div>
}
