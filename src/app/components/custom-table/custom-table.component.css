.table-container {
  width: 100%;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  background-color: #fff;
  margin-bottom: 1.5rem;
}

/* Responsive design for tablet devices */
@media (max-width: 1024px) and (min-width: 769px) {
  .table-container {
    overflow-x: auto;
  }

  table {
    min-width: 800px; /* Ensure table doesn't get too compressed */
  }

  th,
  td {
    padding: 0.75rem;
    font-size: 0.875rem;
  }

  th {
    font-size: 0.875rem;
  }
}

/* Responsive design for mobile devices */
@media (max-width: 768px) {
  .table-container {
    overflow-x: auto;
    box-shadow: none;
    background-color: transparent;
    margin-bottom: 0;
  }

  .table-wrapper {
    display: block;
  }

  table {
    display: block;
    border: none;
  }

  .sticky-header {
    display: none; /* Hide table header on mobile */
  }

  tbody {
    display: block;
  }

  tbody tr {
    display: block;
    background-color: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 1rem;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.2s ease;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  tbody tr:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    background-color: #fff;
  }

  tbody tr.extra-bottom-row {
    display: none; /* Hide extra row on mobile */
  }

  td {
    display: flex;
    padding: 0.75rem 0;
    border: none;
    position: relative;
    align-items: flex-start;
    gap: 1rem;
    font-size: 0.875rem;
    min-height: 2.5rem;
  }

  td:before {
    content: attr(data-label);
    flex: 0 0 35%;
    font-weight: 600;
    color: #4b5563;
    font-size: 0.875rem;
    line-height: 1.4;
    word-wrap: break-word;
  }

  td > div {
    flex: 1;
    min-width: 0; /* Allow content to shrink */
  }

  td > div span {
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  /* Special handling for action buttons on mobile */
  td:has(app-action-button) {
    display: flex;
    justify-content: flex-end;
    margin-top: 1rem;
    border-top: 1px solid #e5e7eb;
    padding-top: 1rem;
    padding-left: 0;
    padding-right: 0;
  }

  td:has(app-action-button):before {
    display: none;
  }

  /* For row numbers on mobile */
  td:first-child {
    display: none; /* Hide row numbers on mobile for cleaner look */
  }

  /* Responsive pagination */
  .footer {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .footer > div {
    text-align: center;
  }
}

.table-wrapper {
  width: 100%;
  min-width: 100%;
}

.table-wrapper.overflowContainer {
  max-height: 60vh;
  overflow-y: auto;
  @media (max-width: 640px) {
    height: 100%;
  }
}

table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.sticky-header th {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #f9fafb;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

th {
  padding: 1rem;
  font-weight: 600;
  font-size: 1rem;
  letter-spacing: 0.05em;
  color: #4b5563;
  border-bottom: 1px solid #e5e7eb;
  white-space: nowrap;
  transition: background-color 0.2s;
}

th:hover {
  background-color: #f3f4f6;
}

tbody tr {
  border-radius: 8px;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.15s ease;
}

tbody tr:last-child {
  border-bottom: none;
}

tbody tr:hover {
  background-color: #f9fafb;
}

td {
  padding: 0.875rem 1rem;
  color: #1f2937;
  font-size: 0.875rem;
}

.clickable-row {
  cursor: pointer;
}

.clickable-row:focus {
  outline: 2px solid #3b82f6;
  outline-offset: -2px;
}

.error-row {
  background-color: #fef2f2;
}

.error-row:hover {
  background-color: #fee2e2;
}

app-action-button {
  display: flex;
  justify-content: center;
}

.system-tag {
  background-color: #dde8ef;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  margin-left: 0.5rem;
  font-size: 0.6875rem;
  font-weight: 500;
  color: #1f2937;
}

.footer {
  padding: 0 1rem;
}
