<div class="dropdown cursor-pointer">
  <app-custombutton
    class="dropdown-button"
    (keydown)="handleKeydownToggle($event)"
    (click)="toggleDropdown()"
    [disabled]="isDisabled"
  >
    <span class="capitalize">{{ selectedLabel }}</span>
  </app-custombutton>
  <div class="dropdown-content" [class.show]="isDropdownOpen">
    <ul>
      @for (item of options; track $index) {
        <li
          [attr.aria-hidden]="true"
          (keydown)="selectOption(item.value)"
          (click)="selectOption(item.value)"
        >
          {{ item.label }}
        </li>
      }
    </ul>
  </div>
</div>
