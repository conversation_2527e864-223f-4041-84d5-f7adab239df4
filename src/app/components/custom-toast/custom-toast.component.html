<div [class]="'z-[10000] fixed ' + getPositionClasses()">
  <div [class]="class">
    <p>{{ message }}</p>
  </div>
  <div
    class="absolute rounded-xl p-5 z-[1000] shadow-lg toast-container"
    [ngClass]="{
      'success-toast': toaster?.type === 'success',
      'error-toast': toaster?.type === 'error',
      'warning-toast': toaster?.type === 'warning',
      'information-toast': toaster?.type === 'information',
    }"
    [style.right]="toaster?.position?.includes('right') ? '20px' : 'auto'"
    [style.left]="toaster?.position?.includes('left') ? '20px' : 'auto'"
    [style.top]="toaster?.position?.includes('top') ? '32px' : 'auto'"
    [style.bottom]="toaster?.position?.includes('bottom') ? '32px' : 'auto'"
    [style.min-width]="getMinWidth(toaster?.msg || '')"
    *ngIf="Show"
  >
    <div class="flex gap-4">
      <div class="flex h-fit w-fit">
        <span
          class="text-white rounded-md py-[9.5px] px-[8.5px] flex"
          [ngClass]="{
            'success-icon': toaster?.type === 'success',
            'error-icon': toaster?.type === 'error',
            'warning-icon': toaster?.type === 'warning',
            'information-icon': toaster?.type === 'information',
          }"
        >
          <!-- Success Icon -->
          <div *ngIf="toaster?.type === 'success'">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="10"
              height="8"
              viewBox="0 0 10 8"
              fill="none"
            >
              <path
                d="M1.5 4.5L3.5 6.5L8.5 1.5"
                stroke="white"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>

          <!-- Error Icon -->
          <div *ngIf="toaster?.type === 'error'">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="12"
              height="12"
              viewBox="0 0 12 12"
              fill="none"
            >
              <path
                d="M9 3L3 9M3 3L9 9"
                stroke="white"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>

          <!-- Warning Icon -->
          <div *ngIf="toaster?.type === 'warning'">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="12"
              height="12"
              viewBox="0 0 12 12"
              fill="none"
            >
              <path
                d="M6 4V6M6 8H6.01M11 6C11 8.76142 8.76142 11 6 11C3.23858 11 1 8.76142 1 6C1 3.23858 3.23858 1 6 1C8.76142 1 11 3.23858 11 6Z"
                stroke="white"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>

          <!-- Information Icon -->
          <div *ngIf="toaster?.type === 'information'">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="12"
              height="12"
              viewBox="0 0 12 12"
              fill="none"
            >
              <path
                d="M6 8V6M6 4H6.01M11 6C11 8.76142 8.76142 11 6 11C3.23858 11 1 8.76142 1 6C1 3.23858 3.23858 1 6 1C8.76142 1 11 3.23858 11 6Z"
                stroke="white"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
        </span>
      </div>

      <div class="flex flex-col text-left toast-content">
        <span
          class="title toast-title"
          [ngClass]="{
            'success-title': toaster?.type === 'success',
            'error-title': toaster?.type === 'error',
            'warning-title': toaster?.type === 'warning',
            'information-title': toaster?.type === 'information',
          }"
          *ngIf="
            toaster?.title && shouldShowTitle(toaster?.title, toaster?.msg)
          "
          [title]="toaster?.title"
          >{{ toaster?.title }}</span
        >
        <div class="msg toast-message" [title]="toaster?.msg">
          <span [innerHTML]="toaster?.msg"></span>
        </div>
      </div>
      <div>
        <button class="cursor-pointer text-[#979FA9]" (click)="onClose()">
          <svg
            width="18"
            height="18"
            viewBox="0 0 18 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M14 4.00004L4 14M3.99996 4L13.9999 14"
              stroke="#979FA9"
              stroke-width="1.5"
              stroke-linecap="round"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</div>
