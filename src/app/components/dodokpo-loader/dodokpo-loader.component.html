<div>
  <div
    [@stateChange]="currentState"
    (@stateChange.done)="onAnimationDone($event)"
  ></div>
  <svg width="94" height="103" viewBox="0 0 179 207">
    <g clip-path="url(#clip1_1920_6039)" *ngIf="currentState !== 'state4'">
      <path
        d="M44.7119 43.9875L111.78 82.8L89.4239 95.7375L22.356 56.925L44.7119 43.9875Z"
        fill="#0C4767"
      />
      <path
        d="M156.492 56.925L89.4239 95.7375L67.0679 82.8L134.136 43.9875L156.492 56.925Z"
        fill="#0C4767"
      />
      <rect
        width="32.4706"
        height="32.4706"
        transform="matrix(0.865517 -0.500879 0.865517 0.500879 61.373 36.6131)"
        fill="#0C4767"
      />
    </g>
    <g clip-path="url(#clip1_1920_6049)" *ngIf="currentState === 'state4'">
      <path
        d="M123.766 63.3243L73.0028 34.1109L89.9237 24.373L140.687 53.5865L123.766 63.3243Z"
        fill="#1E709C"
      />
      <path
        d="M39.1609 53.5865L89.9237 24.373L106.845 34.1109L56.0818 63.3243L39.1609 53.5865Z"
        fill="#1E709C"
      />
      <rect
        width="25.9687"
        height="25.9687"
        transform="matrix(-0.866723 0.49879 -0.866723 -0.49879 112.248 69.8779)"
        fill="#1E709C"
      />
    </g>
    <g clip-path="url(#clip0_1920_6039)" *ngIf="currentState === 'state1'">
      <path
        d="M60.6191 172.069L60.6191 94.4437L82.9751 107.381L82.9751 185.006L60.6191 172.069Z"
        fill="#0C4767"
      />
      <path
        d="M15.9072 68.5687L82.9751 107.381L82.9751 133.256L15.9072 94.4437L15.9072 68.5687Z"
        fill="#0C4767"
      />
      <rect
        width="32.287"
        height="32.3437"
        transform="matrix(0.865517 0.500879 -9.75121e-08 1 15.9072 113.85)"
        fill="#0C4767"
      />
    </g>
    <g *ngIf="currentState === 'state2'">
      <path
        d="M64.0126 148.935L64.0126 109.982L75.231 116.474L75.231 155.427L64.0126 148.935Z"
        fill="#1E709C"
      />
      <path
        d="M41.5759 96.9977L75.231 116.474L75.231 129.458L41.5759 109.982L41.5759 96.9977Z"
        fill="#1E709C"
      />
      <rect
        width="51.7261"
        height="51.817"
        transform="matrix(0.865517 0.500879 -9.75121e-08 1 8.16284 103.47)"
        fill="#1E709C"
      />
    </g>
    <g clip-path="url(#clip2_1920_6069)" *ngIf="currentState === 'state3'">
      <path
        d="M119.529 150.262L119.529 111.309L108.248 117.801L108.248 156.754L119.529 150.262Z"
        fill="#1E709C"
      />
      <path
        d="M142.091 98.325L108.248 117.801L108.248 130.786L142.091 111.309L142.091 98.325Z"
        fill="#1E709C"
      />
      <rect
        width="51.9427"
        height="51.817"
        transform="matrix(-0.866723 0.49879 9.80569e-08 1 175.69 104.797)"
        fill="#1E709C"
      />
    </g>
    <g clip-path="url(#clip2_1920_6039)" *ngIf="currentState === 'state1'">
      <path
        d="M118.639 172.069L118.639 94.4437L96.2832 107.381L96.2832 185.006L118.639 172.069Z"
        fill="#0C4767"
      />
      <path
        d="M163.351 68.5687L96.2832 107.381L96.2832 133.256L163.351 94.4437L163.351 68.5687Z"
        fill="#0C4767"
      />
      <rect
        width="32.287"
        height="32.3437"
        transform="matrix(-0.865517 0.500879 9.75121e-08 1 163.351 113.85)"
        fill="#0C4767"
      />
    </g>
    <g clip-path="url(#clip1_1920_6059)" *ngIf="currentState === 'state2'">
      <path
        d="M140.995 81.5063L140.995 159.131L163.351 146.194L163.351 68.5688L140.995 81.5063Z"
        fill="#0C4767"
      />
      <path
        d="M96.2832 185.006L163.351 146.194L163.351 120.319L96.2832 159.131L96.2832 185.006Z"
        fill="#0C4767"
      />
      <rect
        width="32.287"
        height="32.3437"
        transform="matrix(0.865517 -0.500879 -9.75121e-08 -1 96.2832 139.725)"
        fill="#0C4767"
      />
    </g>
    <g
      clip-path="url(#clip0_1920_6069)"
      *ngIf="currentState === 'state3' || currentState === 'state4'"
    >
      <path
        d="M38.4403 81.6132L38.4403 159.11L15.9966 146.194L15.9966 68.6971L38.4403 81.6132Z"
        fill="#0C4767"
      />
      <path
        d="M83.3277 184.942L15.9966 146.194L15.9966 120.362L83.3277 159.11L83.3277 184.942Z"
        fill="#0C4767"
      />
      <rect
        width="32.475"
        height="32.3964"
        transform="matrix(-0.866723 -0.49879 9.80569e-08 -1 83.439 139.778)"
        fill="#0C4767"
      />
    </g>
    <g clip-path="url(#clip2_1920_6049)" *ngIf="currentState === 'state4'">
      <path
        d="M141.783 81.5063L141.783 159.131L164.264 146.194L164.264 68.5688L141.783 81.5063Z"
        fill="#0C4767"
      />
      <path
        d="M96.8211 185.006L164.264 146.194L164.264 120.319L96.8211 159.131L96.8211 185.006Z"
        fill="#0C4767"
      />
      <rect
        width="32.4222"
        height="32.3437"
        transform="matrix(0.866723 -0.49879 -9.80569e-08 -1 96.8208 139.725)"
        fill="#0C4767"
      />
    </g>
    <defs>
      <clipPath id="clip0_1920_6059">
        <rect
          width="103.318"
          height="103.318"
          fill="white"
          transform="matrix(0.865517 -0.500879 0.865517 0.500879 0 51.75)"
        />
      </clipPath>
      <clipPath id="clip1_1920_6059">
        <rect
          width="103.318"
          height="103.5"
          fill="white"
          transform="matrix(0.865517 -0.500879 2.1979e-08 1 89.5762 103.5)"
        />
      </clipPath>
    </defs>
  </svg>
</div>
