@defer {
  <div
    class="shadow-custom bg-[#FFF] rounded-lg h-fit absolute z-20 top-16 right-8 grid"
    [ngStyle]="{ minWidth: isNotification ? '140px' : '160px' }"
  >
    @for (action of actionButtonItems; track $index) {
      <button
        class="hover:bg-[#E5EDF2] first:hover:rounded-t-lg last:hover:rounded-b-lg w-full flex items-center gap-x-2 h-[32px]"
        (click)="action.onClick()"
      >
        <img
          class="ml-[16px]"
          src="{{ action.icon }}"
          alt="{{ action.name }}"
        />
        <span
          class="text-[#474D66] font-medium mr-[16px]"
          [ngStyle]="{ fontSize: isNotification ? '12px' : '16px' }"
          >{{ action.name }}</span
        >
      </button>
    }
  </div>
}
