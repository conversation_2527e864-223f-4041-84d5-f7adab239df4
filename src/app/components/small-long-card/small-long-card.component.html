<div
  class="card-wrapper"
  [ngClass]="{
    'card-primary': variant === 'primary',
    'card-secondary': variant === 'secondary',
  }"
>
  <div class="card-header">
    <h3 class="card-title">{{ title }}</h3>
    <div class="card-actions">
      <p class="score-badge mt-2" [ngClass]="{ hidden: !showScore }">
        Score: {{ score }}
      </p>
      @if (showEditDeleteButtons) {
        <button
          class="action-btn edit-btn"
          (keydown)="handleKeyEdit($event)"
          (click)="editCard()"
        >
          <img src="../../../assets/icons/new-edit.svg" alt="edit-icon" />
        </button>
        <button
          class="action-btn delete-btn"
          (keydown)="handleKeyDelete($event)"
          (click)="deleteCard()"
        >
          <img src="../../../assets/icons/delete-icon.svg" alt="delete-icon" />
        </button>
      }
    </div>
  </div>

  <div class="card-content">
    <p
      class="card-description"
      [appSafeHtml]="description | truncateLongText: 120"
    ></p>
  </div>
</div>
