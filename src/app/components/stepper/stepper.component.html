<!-- Default Design -->
<div *ngIf="design === 'default'" class="flex items-center ml-6 mb-6">
  @for (step of steps; track step; let i = $index) {
    <div class="relative flex items-center">
      <div class="grid place-items-center">
        <button
          class="cursor-pointer w-10 h-10 rounded-full flex items-center justify-center border-2"
          (click)="navigateToStep(i)"
          (keydown.enter)="navigateToStep(i)"
          [ngClass]="{
            'w-8 h-8 !rounded-full flex items-center justify-center border-2 border-[#0C4767] bg-[#0C4767] text-white':
              activeStepIndex === i,
            'w-8 h-8 rounded-full flex items-center justify-center border-2 border-[#0C4767] text-[#0C4767]':
              activeStepIndex !== i,
          }"
        >
          {{ i + 1 }}
        </button>
        <span
          class="absolute top-10 text-xs min-w-[150px] text-center"
          [ngClass]="{
            'text-[#0C4767] font-medium': activeStepIndex === i,
            'text-[#B5B5C3]': activeStepIndex !== i,
          }"
        >
          {{ step }}
        </span>
      </div>
    </div>

    @if (i < steps.length - 1) {
      <div
        class="w-[166px] border-t-2"
        [ngClass]="{
          'border-[#0C4767]': activeStepIndex > i,
          'border-gray-400': activeStepIndex <= i,
        }"
      ></div>
    }
  }
</div>

<!-- Modern Design -->
<div *ngIf="design === 'modern'" class="tabs-container">
  <div class="tabs-header">
    @for (step of steps; track step; let i = $index) {
      <button
        class="tab-button"
        [class.active]="isStepActive(i)"
        [class.disabled]="isStepDisabled(i)"
        [disabled]="isStepDisabled(i)"
        (click)="navigateToStep(i)"
        (keydown.enter)="navigateToStep(i)"
      >
        {{ step }}
      </button>
    }
  </div>
</div>

<!-- Custom Design -->
<div
  *ngIf="design === 'custom'"
  [class]="containerClass || 'flex items-center ml-6 mb-6'"
>
  @for (step of steps; track step; let i = $index) {
    <div [class]="stepWrapperClass || 'relative flex items-center'">
      <div [class]="stepContainerClass || 'grid place-items-center'">
        <button
          class="cursor-pointer"
          (click)="navigateToStep(i)"
          (keydown.enter)="navigateToStep(i)"
          [class]="getCustomStepClasses(i)"
        >
          @if (showCheckmarks && isStepCompleted(i)) {
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path
                fill-rule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clip-rule="evenodd"
              />
            </svg>
          } @else {
            {{ i + 1 }}
          }
        </button>
        <span [class]="getCustomLabelClasses(i)">
          {{ step }}
        </span>
      </div>
    </div>

    @if (i < steps.length - 1) {
      <div [class]="getCustomConnectionClasses(i)"></div>
    }
  }
</div>
