<main class="flex lg:gap-x-3 flex-wrap">
  <div>
    @if (total > 10) {
      <div class="flex items-center gap-x-2">
        <select
          id="itemsPerPage"
          class="border border-[#0C4767] rounded-md p-1 bg-inherit"
          [(ngModel)]="size"
          (ngModelChange)="onSizeSelect($event)"
        >
          @for (option of [10, 25, 50]; track option) {
            <option [value]="option">{{ option }}</option>
          }
        </select>
        <label for="itemsPerPage" class="mr-2 text-[#0C4767]"
          >Items per page</label
        >
      </div>
    }
  </div>
  @if (size <= total) {
    <pagination-template
      #p="paginationApi"
      (pageChange)="onPageChange($event)"
      class="flex flex-row justify-between items-center rounded pagination-controller"
    >
      <div
        class="flex flex-row pagination-numbers border-2 border-[#D9D9D9] rounded-md h-full bg-[#F5F5F5]"
      >
        <button
          class="pagination-box-size hover:cursor-pointer text-[#545454]"
          (click)="p.previous()"
          [disabled]="p.isFirstPage()"
        >
          <
        </button>

        <button
          *ngFor="let page of p.pages"
          [class.current]="p.getCurrent() === page.value"
          (click)="p.setCurrent(page.value)"
          class="pagination-box-size hover:bg-[#d3d3d3] hover:cursor-pointer"
        >
          <button
            (click)="p.setCurrent(page.value)"
            *ngIf="p.getCurrent() !== page.value"
          >
            <span class="text-[#545454]">{{ page.label }}</span>
          </button>
          <div
            *ngIf="p.getCurrent() === page.value"
            class="pagination-box-size hover:bg-[#d3d3d3] hover:cursor-pointer bg-[#d3d3d3] h-full"
          >
            <span class="text-[#545454]">{{ page.label }}</span>
          </div>
        </button>

        <button
          class="pagination-box-size hover:cursor-pointer text-[#545454]"
          (click)="p.next()"
          [disabled]="p.isLastPage()"
        >
          >
        </button>
      </div>
    </pagination-template>
  }
</main>
