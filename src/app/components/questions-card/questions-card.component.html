<div
  class="w-full bg-[#f5f5f5] rounded-xl shadow-sm border border-gray-300 relative p-3"
  [ngStyle]="cardStyle()"
  [attr.tabindex]="isClickable() ? 0 : null"
  (click)="clickedCardButton($event)"
  (keydown)="handleKeydown($event)"
>
  <div class="p-3 sm:p-2">
    <div class="flex items-start justify-between gap-1 mb-2">
      <div
        class="relative flex-1 min-w-0"
        (mouseenter)="onMouseEnterTitle()"
        (mouseleave)="onMouseLeaveTitle()"
      >
        <h3
          #titleElement
          class="font-medium text-[#082230E5] line-clamp-2 overflow-hidden whitespace-normal [word-break:break-word] text-xs sm:text-sm max-width-95"
          [appSafeHtml]="title()"
          [attr.aria-label]="title() || 'Default Title'"
        >
          {{ title() }}
        </h3>

        @if (isTooltipVisible() && showToolTip()) {
          <div
            #tooltipElement
            class="card-tooltip absolute bg-gray-800 text-white rounded p-2 text-sm"
            [style.width.px]="tooltipWidth()"
            [class.top-tooltip]="position() === 'top'"
            [class.bottom-tooltip]="position() === 'bottom'"
            (mouseenter)="onMouseEnterTooltip()"
            (mouseleave)="onMouseLeaveTooltip()"
          >
            <div class="tooltip-arrow"></div>
            <div
              class="tooltip-content text-xs whitespace-normal break-words"
              [appSafeHtml]="title()"
            ></div>
          </div>
        }
      </div>

      <div class="flex-shrink-0">
        @if (customRight()) {
          <ng-content select=".rightSide"></ng-content>
        }
        @if (showRightSide()) {
          <button
            class="text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-1 focus:ring-blue-500 rounded-full p-1"
            (click)="clickedActionButton($event)"
            aria-label="More options"
          >
            <svg
              class="w-3 h-3 sm:w-4 sm:h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
              ></path>
            </svg>
          </button>
        }
      </div>
    </div>

    <div
      class="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 text-[#082230CC] text-xs sm:text-xs mb-1"
    >
      <ng-content select=".leftSideBody1"></ng-content>
      <ng-content select=".leftSideBody2"></ng-content>
    </div>
  </div>

  <div class="px-2 sm:px-3 py-1 sm:py-2 flex justify-between items-center">
    <div class="flex flex-wrap items-center gap-1">
      @if (getType(middleContent())) {
        <div
          class="bg-white text-[#085AC4] text-xs px-4 py-1 rounded-lg whitespace-nowrap"
        >
          {{ getType(middleContent()) }}
        </div>
      }
      @if (system()) {
        <div
          class="bg-white text-gray-800 text-xs px-4 py-1 rounded-lg whitespace-nowrap"
        >
          System
        </div>
      }
      @if (isActive()) {
        <div
          class="text-[#04952C] bg-[#e7fef1] text-xs px-4 py-1 rounded-lg whitespace-nowrap transparent"
        >
          Active
        </div>
      }
    </div>
  </div>
</div>
