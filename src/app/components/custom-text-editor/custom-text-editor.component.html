@defer {
  <main>
    <div class="overflow-hidden">
      <quill-editor
        [dir]="'ltr'"
        class="w-full quill-full-width"
        [(ngModel)]="formControl.value"
        [ngModelOptions]="{ standalone: true }"
        [styles]="editorContainerStyles"
        (ngModelChange)="onEditorChange($event)"
        [placeholder]="placeholder || 'type here'"
        [modules]="editorModules"
        [ngClass]="{ 'read-only-mode': isViewerMode }"
        [disabled]="formControl.disabled"
        (onEditorCreated)="onEditorInitialized($event)"
      ></quill-editor>
    </div>
    @if (insertBlankFillIn) {
      <div class="w-full flex justify-end mt-2">
        <app-custombutton (clicked)="insertBlank()"
          >Insert Blank</app-custombutton
        >
      </div>
    }
  </main>
} @loading {
  <div class="flex flex-col items-center justify-center w-full py-8">
    <div
      class="w-8 h-8 border-4 border-gray-300 border-t-blue-500 rounded-full animate-spin"
    ></div>
    <span class="mt-2 text-sm text-gray-600 dark:text-gray-300"
      >Loading ...</span
    >
  </div>
} @error {
  <div class="w-full text-center py-4">
    <p>please refresh the page</p>
  </div>
}
