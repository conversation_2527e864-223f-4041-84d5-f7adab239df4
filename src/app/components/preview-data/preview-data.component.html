@if (cardIndex !== null) {
  <main
    class="bg-[#fff] h-full flex justify-between items-center box-border relative"
  >
    <div class="relative left-3">
      @if (questions.length > 1) {
        <button (click)="previousQuestion()" class="min-w-3">
          @if (cardIndex > 0) {
            <img
              src="{{ previous }}"
              alt="previous button"
              style="font-size: 2rem"
            />
          } @else {
            <img
              src="{{ inactivePrevious }}"
              alt="inactive previous button"
              style="font-size: 2rem"
            />
          }
        </button>
      }
    </div>
    <section class="max-h-[calc(100vh-200px)] overflow-y-auto w-full">
      <div
        class="px-6 3xl:w-[1117px] 3xl:pl-[31px] 3xl:pr-[2rem] max-w-full mx-auto"
      >
        <div class="question-description">
          <div class="flex justify-between question-title">
            @if (showQuestionHeader) {
              <h3 class="text-[#0C4767] text-[16px] font-semibold mb-[16px]">
                Question
              </h3>
            }
            @if (showSaveButton && !saveButton) {
              <app-custombutton (clicked)="onSave()" class="mb-2">
                Save question
              </app-custombutton>
            } @else if (showSaveButton && saveButton === true) {
              <app-custombutton [spinner]="true"> </app-custombutton>
            }
            @if (questions.length > 0) {
              <p class="question-number">
                Question {{ cardIndex + 1 }} of {{ questions.length }}
              </p>
            }
          </div>
          <div
            class="rendered-content break-words overflow-hidden max-w-full"
            [appSafeHtml]="getQuestion().questionText"
          ></div>
        </div>

        <div>
          @if (question.matchMatrixAnswer) {
            <app-matrix-table
              [options]="question.matchMatrixAnswer.options"
              [subquestions]="subQuestion"
              [disable]="true"
            ></app-matrix-table>
          } @else if (!question.essayAnswer && !question.matchMatrixAnswer) {
            <h4
              class="text-[#0C4767] text-[14px] font-semibold mt-[24px] mb-[16px]"
            >
              Options
            </h4>
          }
          @if (!question.essayAnswer && !question.matchMatrixAnswer) {
            <ul class="space-y-2 mb-8">
              @for (option of questionAnswer?.options; track option) {
                @if (!isFillInOption(option)) {
                  @if (questionAnswer?.answer?.includes(option)) {
                    <li class="flex items-center mb-2 space-x-3">
                      <input
                        type="radio"
                        class="form-radio text-green-500 h-4 w-4"
                        checked
                        disabled
                      />
                      <span
                        class="option break-words max-w-full"
                        [appSafeHtml]="option"
                      ></span>
                      <span
                        class="text-green-500 flex items-center space-x-2 lg:min-w-4"
                      >
                        <img src="{{ checkMark }}" alt="" class="w-4 h-4" />
                        <span>Correct answer</span>
                      </span>
                    </li>
                  } @else {
                    <li class="flex items-center space-x-2">
                      <input
                        type="radio"
                        class="form-radio text-gray-400 h-4 w-4"
                        disabled
                      />
                      <span
                        class="option break-words max-w-full"
                        [appSafeHtml]="option"
                      ></span>
                    </li>
                  }
                } @else if (isFillInOption(option)) {
                  @if (questionAnswer?.answer?.includes(option.value)) {
                    <li class="flex items-center mb-2 space-x-3">
                      <input
                        type="radio"
                        class="form-radio text-green-500 h-4 w-4"
                        checked
                        disabled
                      />
                      <span
                        class="option break-words max-w-full"
                        [appSafeHtml]="option.value"
                      ></span>
                      <span
                        class="text-green-500 flex items-center space-x-2 lg:min-w-4"
                      >
                        <img src="{{ checkMark }}" alt="" class="w-4 h-4" />
                        <span>Correct answer</span>
                      </span>
                    </li>
                  } @else {
                    <li class="flex items-center space-x-2">
                      <input
                        type="radio"
                        class="form-radio text-gray-400 h-4 w-4"
                        disabled
                      />
                      <span
                        class="option break-words max-w-full"
                        [appSafeHtml]="option.value"
                      ></span>
                    </li>
                  }
                }
              }
            </ul>
          }
        </div>

        <div class="question-constraints">
          <h3
            class="text-[#0C4767] text-[16px] font-semibold mb-[16px] mt-[24px]"
          >
            Constraints
          </h3>
          <div class="flex flex-wrap w-full">
            <div class="domain mb-[20px] w-[50%]">
              <h5 class="text-[#0C4767] text-[12px]">Domain</h5>
              <p>
                {{ questionDomain ?? question.domain.name }}
              </p>
            </div>
            <div class="category mb-[20px]">
              <h5 class="text-[#0C4767] text-[12px]">Category</h5>
              <p>{{ question.category.name }}</p>
            </div>
          </div>
          <div class="flex flex-wrap w-full">
            <div class="difficulty-level mb-[20px] w-[50%]">
              <h5 class="text-[#0C4767] text-[12px]">Difficulty Level</h5>
              <p>{{ question.difficultyLevel }}</p>
            </div>
            <div class="score mb-[25px]">
              <h5 class="text-[#0C4767] text-[12px]">Question score</h5>
              <p>{{ question.score }}</p>
            </div>
          </div>

          @if (question.essayAnswer && question.essayAnswer.rubrics !== '') {
            <div class="score mb-[25px] flex flex-wrap w-full">
              <div class="score mb-[25px] w-[50%]">
                <h5 class="text-[#0C4767] text-[12px]">Total Rubrics score</h5>
                <p>{{ score }}</p>
              </div>
              <div>
                <h5 class="text-[#0C4767] text-[12px]">Scoring Method</h5>
                <p>Rubric-based AI Scoring</p>
              </div>
            </div>
          } @else if (
            question.questionType === 'Essay' &&
            question.essayAnswer &&
            question.essayAnswer.rubrics === ''
          ) {
            <div>
              <h5 class="text-[#0C4767] text-[12px]">Scoring Method</h5>
              <p>AI-based Scoring</p>
            </div>
          }

          @if (question.questionType === 'Multi_select') {
            <div>
              <h5 class="text-[#0C4767] text-[12px]">Strict marking</h5>
              <p>{{ question.strictMark ? 'Yes' : 'No' }}</p>
            </div>
          }
        </div>
        <div class="question-options">
          <h4
            class="text-[#0C4767] text-[14px] font-semibold mt-[24px] mb-[16px]"
          >
            {{
              question.essayAnswer && question.essayAnswer.rubrics !== ''
                ? 'Rubrics'
                : ''
            }}
          </h4>
          @if (question.essayAnswer) {
            @for (
              item of questionService.convertRubricsStringToArray(
                question.essayAnswer.rubrics
              );
              track $index
            ) {
              <div class="h-fit mb-2">
                <app-small-long-card
                  [title]="item.rubricTitle"
                  [description]="item.description"
                  [score]="item.score"
                  [showEditDeleteButtons]="false"
                >
                </app-small-long-card>
              </div>
            }
          }
        </div>
      </div>
    </section>
    <div class="relative w-16 flex justify-center">
      @if (questions.length > 1) {
        <button (click)="nextQuestion()" class="min-w-3">
          @if (cardIndex < questions.length - 1) {
            <img src="{{ next }}" alt="next button" style="font-size: 2rem" />
          } @else {
            <img
              src="{{ inactiveNext }}"
              alt="inactive next button"
              style="font-size: 2rem"
            />
          }
        </button>
      }
    </div>
  </main>
}
