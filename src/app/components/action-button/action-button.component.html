<button
  *ngIf="arrangement === 'vertical'"
  class="action-button border border-neutral-N300 rounded-full relative cursor-pointer"
  (click)="actionButtonOnClick(); $event.stopPropagation()"
>
  <svg
    width="17"
    height="18"
    viewBox="0 0 17 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="17" height="18" rx="8.5" [attr.fill]="backgroundColor" />
    <circle
      *ngFor="let circle of circles; let i = index"
      [attr.cx]="circle.cx"
      [attr.cy]="circle.cy"
      r="0.5"
      [attr.fill]="innerCirclesColor"
    />
  </svg>
</button>

<button
  *ngIf="arrangement === 'horizontal'"
  class="action-button rounded-full relative cursor-pointer p-1"
  (click)="actionButtonOnClick(); $event.stopPropagation()"
>
  <svg
    [attr.fill]="innerCirclesColor"
    width="20"
    height="20"
    viewBox="0 0 32 32"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M6.66667 13.333C5.2 13.333 4 14.533 4 15.9997C4 17.4663 5.2 18.6663 6.66667 18.6663C8.13333 18.6663 9.33333 17.4663 9.33333 15.9997C9.33333 14.533 8.13333 13.333 6.66667 13.333ZM25.3333 13.333C23.8667 13.333 22.6667 14.533 22.6667 15.9997C22.6667 17.4663 23.8667 18.6663 25.3333 18.6663C26.8 18.6663 28 17.4663 28 15.9997C28 14.533 26.8 13.333 25.3333 13.333ZM16 13.333C14.5333 13.333 13.3333 14.533 13.3333 15.9997C13.3333 17.4663 14.5333 18.6663 16 18.6663C17.4667 18.6663 18.6667 17.4663 18.6667 15.9997C18.6667 14.533 17.4667 13.333 16 13.333Z"
      fill="#0C4767"
    />
  </svg>
</button>
