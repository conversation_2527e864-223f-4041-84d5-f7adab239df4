<div [formGroup]="form">
  @if (showOptions) {
    <div
      formArrayName="answerOptions"
      class="w-full grid gap-4 grid-cols-1 md:grid-cols-2"
    >
      @for (option of optionsArray.controls; track option; let i = $index) {
        <div class="flex items-center gap-3">
          <div class="w-full">
            <app-custom-input-editor-edition
              formControlName="{{ i }}"
              placeholder="Enter option here"
            ></app-custom-input-editor-edition>
          </div>
          <button
            title="Remove option"
            (click)="removeOption(i)"
            class="group flex items-center justify-center w-10 h-10 rounded-lg border border-gray-200 bg-red-50 hover:bg-red-50 hover:border-red-200 active:bg-red-100 active:border-red-300 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50"
          >
            <img
              src="../../../../../../../../assets/icons/delete-icon.svg"
              alt="Delete"
              class="w-5 h-5 opacity-60 group-hover:opacity-80 group-active:opacity-90 transition-opacity duration-200 ease-in-out filter group-hover:brightness-0 group-hover:saturate-100 group-hover:hue-rotate-[315deg] group-active:brightness-0 group-active:saturate-100 group-active:hue-rotate-[315deg]"
            />
          </button>
        </div>
      }
    </div>
  } @else {
    <div
      formArrayName="subquestions"
      class="w-full grid gap-4 grid-cols-1 md:grid-cols-2"
    >
      @for (row of subquestionsArray.controls; track row; let i = $index) {
        <div class="flex items-center gap-3" [formGroupName]="i">
          <div class="w-full">
            <app-custom-input-editor-edition
              formControlName="subquestion"
              placeholder="Enter option here"
            ></app-custom-input-editor-edition>
          </div>
          <button
            (click)="removeRow(i)"
            title="Remove row"
            class="group flex items-center justify-center w-10 h-10 rounded-lg border border-gray-200 bg-red-50 hover:bg-red-50 hover:border-red-200 active:bg-red-100 active:border-red-300 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50"
          >
            <img
              src="../../../../../../../../assets/icons/delete-icon.svg"
              alt="Delete"
              class="w-5 h-5 opacity-60 group-hover:opacity-80 group-active:opacity-90 transition-opacity duration-200 ease-in-out filter group-hover:brightness-0 group-hover:saturate-100 group-hover:hue-rotate-[315deg] group-active:brightness-0 group-active:saturate-100 group-active:hue-rotate-[315deg]"
            />
          </button>
        </div>
      }
    </div>
  }
</div>
