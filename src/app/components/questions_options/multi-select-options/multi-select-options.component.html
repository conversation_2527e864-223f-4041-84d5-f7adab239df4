<section [formGroup]="form" class="multi-choice-container">
  <div formArrayName="answerOptions" class="options-list">
    @for (option of optionsArray.controls; track index; let index = $index) {
      <div class="flex gap-x-3 mb-2 option-item" [formGroupName]="index">
        <input
          class="checkMark hover:cursor-pointer"
          type="checkbox"
          [checked]="
            isFormGroup
              ? optionsArray.at(index).value?.check
              : this.isEdit
                ? isChecked(index)
                : selectedAnswerIndex === index
          "
          formControlName="check"
          [id]="'option-checkbox-' + index"
          title="Select option"
        />
        <label [for]="'option-checkbox-' + index" class="sr-only">
          Select option
        </label>
        <div class="w-full input-container">
          @if (isFormGroup) {
            <app-custom-input-editor-edition
              id="{{ this.isEdit ? 'edit' + index : index }}"
              placeholder="Enter option text here"
              [formControl]="
                getControlInFormArray(index, optionsArray, 'inputValue')
              "
              class="option-input"
            ></app-custom-input-editor-edition>
          } @else {
            <app-custom-input-editor-edition
              placeholder="Enter option text here"
              id="{{ this.isEdit ? 'edit' + index : index }}"
              formControlName="inputValue"
              class="option-input"
            ></app-custom-input-editor-edition>
          }
        </div>

        <button
          type="button"
          class="delete-button"
          (click)="deleteOption(index)"
          (keydown.enter)="deleteOption(index)"
          (keydown.space)="deleteOption(index)"
          aria-label="Delete option"
        >
          <svg
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="red"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <polyline points="3,6 5,6 21,6"></polyline>
            <path
              d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"
            ></path>
            <line x1="10" y1="11" x2="10" y2="17"></line>
            <line x1="14" y1="11" x2="14" y2="17"></line>
          </svg>
        </button>
      </div>
    }
  </div>
</section>
