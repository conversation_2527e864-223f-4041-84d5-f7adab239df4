<div [formGroup]="form" class="multi-choice-container">
  <section formArrayName="answerOptions" class="options-list">
    @for (option of optionsArray.controls; track option; let i = $index) {
      <div class="flex gap-x-3 mb-2 option-item">
        <div class="w-full gap-2 radio-container">
          <label
            for="radio-{{ this.isEdit ? 'edit' + i : i }}"
            class="radio-label"
          >
            <input
              id="radio-{{ this.isEdit ? 'edit' + i : i }}"
              type="radio"
              [value]="i"
              class="custom-radio"
              [checked]="
                isFormGroup
                  ? optionsArray.at(i).value?.check
                  : selectedAnswerIndex === i
              "
              (change)="onRadioChange(i)"
            />
            <span class="radio-checkmark"></span>
          </label>
          <div class="w-full input-container">
            @if (isFormGroup) {
              <app-custom-input
                [isDisabled]="true"
                id="{{ i }}"
                [formControl]="
                  getControlInFormArray(i, optionsArray, 'inputValue')
                "
                class="option-input"
              />
            } @else {
              <app-custom-input
                [isDisabled]="true"
                id="{{ this.isEdit ? 'edit' + i : i }}"
                formControlName="{{ i }}"
                class="option-input"
              ></app-custom-input>
            }
          </div>
        </div>
      </div>
    }
  </section>
</div>
