<div [formGroup]="form" class="multi-choice-container">
  <div formArrayName="answerOptions" class="options-list">
    @for (option of optionsArray.controls; track option; let i = $index) {
      <div class="option-item">
        <div class="radio-container">
          <input
            id="radio-{{ this.isEdit ? 'edit' + i : i }}"
            type="radio"
            [value]="i"
            class="custom-radio"
            [checked]="
              isFormGroup
                ? optionsArray.at(i).value?.check
                : selectedAnswerIndex === i
            "
            (change)="onRadioChange(i)"
            title="Select this option"
          />
          <span class="radio-checkmark"></span>
        </div>

        <div class="input-container">
          @if (isFormGroup) {
            <app-custom-input-editor-edition
              id="{{ this.isEdit ? 'edit' + i : i }}"
              [placeholder]="'Enter option text here'"
              [formControl]="
                getControlInFormArray(i, optionsArray, 'inputValue')
              "
              class="option-input"
            ></app-custom-input-editor-edition>
          } @else {
            <app-custom-input-editor-edition
              id="{{ this.isEdit ? 'edit' + i : i }}"
              [placeholder]="'Enter option text here'"
              formControlName="{{ i }}"
              class="option-input"
            ></app-custom-input-editor-edition>
          }
        </div>

        <button
          type="button"
          class="delete-button"
          (click)="deleteOption(i)"
          (keydown.enter)="deleteOption(i)"
          (keydown.space)="deleteOption(i)"
          aria-label="Delete option"
        >
          <svg
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="red"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <polyline points="3,6 5,6 21,6"></polyline>
            <path
              d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"
            ></path>
            <line x1="10" y1="11" x2="10" y2="17"></line>
            <line x1="14" y1="11" x2="14" y2="17"></line>
          </svg>
        </button>
      </div>
    }
  </div>
</div>
