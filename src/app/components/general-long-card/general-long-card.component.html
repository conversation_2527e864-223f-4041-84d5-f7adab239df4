<main
  class="main-card-container relative rounded-xl px-4 sm:px-6 py-2 h-auto sm:h-36 mb-2 flex flex-col min-w-0 w-full sm:min-w-[20rem] md:min-w-[40rem] border border-gray-300 bg-white"
  [ngStyle]="{
    'background-color': backgroundColor,
    cursor: isClickable ? 'pointer' : 'default',
  }"
  (click)="clickedCardButton()"
  (keydown)="handleKeydown($event)"
  aria-disabled="true"
>
  <div
    class="flex flex-col sm:flex-row justify-between items-start gap-3 sm:gap-0 mb-3"
  >
    <div class="flex items-center gap-2 flex-1 w-full sm:w-auto">
      @if (showIcon) {
        <img
          [src]="IconSrc"
          alt=""
          class="bg-[#dde8ef] rounded-md w-6 h-6 flex-shrink-0"
        />
      }
      <div class="block group relative pt-0 sm:pt-4 w-full sm:w-auto">
        <h3
          [ngClass]="
            grayOut
              ? 'title-color-important font-medium text-base sm:text-lg'
              : 'text-[#082230E5] font-medium text-base sm:text-lg'
          "
          [attr.aria-label]="title"
          class="text-ellipsis overflow-hidden whitespace-nowrap break-words line-clamp-2"
        >
          <span [appSafeHtml]="title"></span>
          <span class="sr-only">{{ title }}</span>
        </h3>

        @if (showToolTip) {
          <div
            class="absolute z-20 text-white bg-[#0C4767] rounded-lg text-[10px] px-1.5 py-0.5 hidden group-hover:block max-w-[70%] overflow-hidden break-words"
            [ngClass]="{
              'top-full transform -translate-y-2': position === 'bottom',
              'bottom-full transform translate-y-2': position === 'top',
              block: showToolTip,
            }"
            [appSafeHtml]="title"
          ></div>
        }
      </div>
    </div>

    <div
      class="flex items-center gap-2 sm:gap-4 w-full sm:w-auto justify-between sm:justify-start"
    >
      @if (showMiddleContent) {
        <span
          [ngClass]="
            grayOut
              ? 'text-[#b8bdc0] text-center text-[0.75rem] sm:text-[0.875rem] rounded-xl bg-[#e2edf4] px-2 sm:px-3 py-1'
              : getType(middleContent) === 'Active'
                ? 'text-center text-[0.75rem] sm:text-[0.875rem] rounded-xl px-2 sm:px-3 py-1 font-medium bg-green-100 text-green-800'
                : getType(middleContent) === 'Inactive'
                  ? 'text-center text-[0.75rem] sm:text-[0.875rem] rounded-xl px-2 sm:px-3 py-1 font-medium bg-red-100 text-red-800'
                  : 'text-center text-[0.75rem] sm:text-[0.875rem] rounded-xl px-2 sm:px-3 py-1 font-medium'
          "
          [ngStyle]="{
            'background-color':
              !grayOut && getType(middleContent) === 'Active'
                ? ''
                : !grayOut && getType(middleContent) === 'Inactive'
                  ? ''
                  : centerItemBackgroundColor,
            color:
              !grayOut &&
              (getType(middleContent) === 'Active' ||
                getType(middleContent) === 'Inactive')
                ? ''
                : centerItemTextColor,
          }"
        >
          {{ getType(middleContent) }}
        </span>
      }

      @if (system) {
        <span
          class="bg-[#0822300A] text-[0.75rem] sm:text-[0.875rem] py-1 px-2 sm:px-3 rounded-full whitespace-nowrap font-medium"
        >
          System
        </span>
      }

      @if (customRight) {
        <div class="right">
          <ng-content select=".rightSide"></ng-content>
        </div>
      }

      @if (showRightSide) {
        <ng-content select=".leftSideBody3"></ng-content>
        <app-action-button
          (actionButtonClicked)="clickedActionButton($event)"
        ></app-action-button>
      }
    </div>
  </div>

  <div
    class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 sm:gap-0"
  >
    <div class="flex flex-col gap-2 w-full sm:w-auto">
      <div
        class="flex items-center gap-2 sm:gap-3 text-[#474D66] text-[12px] flex-wrap"
      >
        @if (typeOfTest) {
          <span
            class="text-[0.75rem] sm:text-[0.875rem] text-[#082230CC] flex items-center gap-x-2"
          >
            <img
              src="../../../assets/icons/akar-icon.svg"
              alt="File"
              class="w-3 h-3 sm:w-4 sm:h-4"
            />
            {{ typeOfTest | titlecase }}
          </span>
        }
        <ng-content select=".leftSideBody1"></ng-content>
      </div>

      <div class="text-[12px] text-gray-600">
        <ng-content select=".leftSideBody2"></ng-content>
      </div>
    </div>
  </div>
</main>
