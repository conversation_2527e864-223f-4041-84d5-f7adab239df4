import { TestBed, ComponentFixture } from '@angular/core/testing';
import { CustomAssessementsCardComponent } from './custom-assessements-card.component';

describe('CustomAssessementsCardComponent Integration Tests', () => {
  let component: CustomAssessementsCardComponent;
  let fixture: ComponentFixture<CustomAssessementsCardComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CustomAssessementsCardComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(CustomAssessementsCardComponent);
    component = fixture.componentInstance;
  });

  describe('Template Integration', () => {
    it('should display time created and updated values from getters', () => {
      component.displayTimeCreated = '2 hours ago';
      component.displayTimeUpdated = '1 hour ago';

      fixture.detectChanges();

      expect(component.currentTimeCreated).toBe('2 hours ago');
      expect(component.currentTimeUpdated).toBe('1 hour ago');
    });

    it('should emit card clicked event when card is clicked', () => {
      const emitSpy = jest.spyOn(component.cardClicked, 'emit');

      component.onCardClicked(void 0);

      expect(emitSpy).toHaveBeenCalledWith(void 0);
    });

    it('should emit action button clicked event', () => {
      const emitSpy = jest.spyOn(component.actionButtonClicked, 'emit');
      const mockEvent = { test: 'event' } as unknown as void;

      component.clickedActionButton(mockEvent);

      expect(emitSpy).toHaveBeenCalledWith(mockEvent);
    });

    it('should show footer when showFooter is true', () => {
      component.showFooter = true;

      expect(component.showFooter).toBe(true);
    });

    it('should hide footer when showFooter is false', () => {
      component.showFooter = false;

      expect(component.showFooter).toBe(false);
    });
  });

  describe('Input Property Tests', () => {
    it('should accept and display assessments title', () => {
      const testTitle = 'Test Assessment Title';
      component.AssessmentsTitle = testTitle;

      expect(component.AssessmentsTitle).toBe(testTitle);
    });

    it('should accept dispatched state', () => {
      const dispatchedState = 'dispatched';
      component.dispatchedState = dispatchedState;

      expect(component.dispatchedState).toBe(dispatchedState);
    });

    it('should accept number of tests', () => {
      const numberOfTests = 5;
      component.numberOfTests = numberOfTests;

      expect(component.numberOfTests).toBe(numberOfTests);
    });

    it('should accept instruction mini details', () => {
      const details = 'Mini instruction details';
      component.instructionMiniDetails = details;

      expect(component.instructionMiniDetails).toBe(details);
    });
  });

  describe('Duration Edge Cases', () => {
    it('should handle very large duration values', () => {
      component.duration = 86400; // 24 hours
      expect(component.formattedDuration).toBe('24 hrs');
    });

    it('should handle fractional seconds correctly', () => {
      component.duration = 90.5; // 1.5 minutes (should round down)
      expect(component.formattedDuration).toBe('1 min');
    });

    it('should handle exactly 1 hour', () => {
      component.duration = 3600;
      expect(component.formattedDuration).toBe('1 hr');
    });

    it('should handle exactly 1 minute', () => {
      component.duration = 60;
      expect(component.formattedDuration).toBe('1 min');
    });
  });
});
