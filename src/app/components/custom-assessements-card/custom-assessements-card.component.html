@defer {
  <div
    class="h-auto rounded-lg border border-[#0822301A] border-t-4 border-t-[#0C4766CC] flex flex-col overflow-hidden pt-3 bg-[#f5f5f5] w-full min-w-0 max-w-full md:h-[260px] sm:min-w-[320px]"
    (click)="onCardClicked()"
    (keydown.enter)="onCardClicked()"
    aria-disabled="true"
  >
    <section
      class="header pl-3 mb-3 cursor-pointer flex items-center justify-between sm:pl-4 sm:mb-[14px]"
    >
      <div
        class="w-10 h-10 bg-[#0C476608] rounded grid place-items-center sm:w-[44px] sm:h-[44px]"
      >
        <img
          src="../../../assets/icons/assessmentCardIcon.svg"
          alt="Assessment Icon"
          class="w-5 h-5 sm:w-6 sm:h-6"
        />
      </div>
      <section class="body pr-3 cursor-pointer sm:pr-4">
        <div
          class="flex items-center gap-x-1 text-xs text-[#082230CC] sm:gap-x-2 sm:text-[12px]"
        >
          <span>
            <img
              src="../../../assets/icons/black-calendar.svg"
              alt="Calendar"
              class="w-3 h-3 sm:w-4 sm:h-4"
            />
          </span>
          {{ currentTimeCreated }}
        </div>
      </section>
    </section>

    <section
      class="title w-full px-3 text-base font-medium mb-2 text-[#082230CC] cursor-pointer sm:px-4 sm:text-[1.125rem] sm:mb-[12px]"
    >
      <div class="line-clamp-2 leading-tight sm:leading-5">
        {{ AssessmentsTitle }}
      </div>
    </section>

    <section class="body flex-grow px-3 cursor-pointer mb-3 sm:px-4 sm:mb-0">
      <div
        [appSafeHtml]="instructionMiniDetails"
        class="description text-xs text-[#474D66] leading-4 w-full max-w-full sm:text-[0.875rem] sm:max-w-[330px]"
        style="
          display: -webkit-box;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          word-wrap: break-word;
        "
      ></div>
    </section>

    <footer
      class="footer h-auto bg-[#dee4e7] flex flex-col px-3 py-2 justify-center relative sm:h-[70px] sm:px-4 sm:py-0"
      (click)="$event.stopPropagation()"
      (keydown)="$event.stopPropagation()"
      aria-disabled="true"
    >
      <section
        class="flex flex-row justify-between items-center w-full gap-2 sm:gap-0"
      >
        <div
          class="left-side flex flex-wrap items-center sm:gap-2 gap-x-2 xl:text-sm text-[#474D66] flex-1 sm:gap-x-4 sm:text-[12px] sm:py-0"
        >
          <div class="flex items-center gap-x-1">
            @if (dispatchedState === 'Dispatched') {
              <div class="text-[#04952C]"><app-dispatch-svg /></div>
            } @else {
              <img
                src="../../../assets/icons/not.svg"
                alt="Check Mark"
                class="w-4 h-4"
              />
            }
            <span>{{ dispatchedState }}</span>
          </div>

          <div class="flex items-center gap-x-1">
            <img
              src="../../../assets/icons/akar-icons_clipboard.svg"
              alt="Clipboard"
              class="w-4 h-4"
            />
            <span
              >{{ numberOfTests }} test{{
                numberOfTests !== 1 ? 's' : ''
              }}</span
            >
          </div>

          <div class="flex items-center gap-x-1">
            <img
              src="../../../assets/icons/clock.svg"
              alt="Duration"
              class="w-4 h-4"
            />
            <span>{{ formattedDuration }}</span>
          </div>
        </div>

        <div class="right-side flex items-center">
          <app-action-button
            (actionButtonClicked)="clickedActionButton($event)"
          ></app-action-button>
        </div>
      </section>
    </footer>
  </div>
} @error {
  <h1 class="text-sm sm:text-base">No internet connection</h1>
}
