import {
  Component,
  EventEmitter,
  Input,
  Output,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { ActionButtonComponent } from '../action-button/action-button.component';
import { SafeHtmlDirective } from '../../directives/safe-html.directive';
import { DispatchSvgComponent } from '../dispatch-svg/dispatch-svg.component';

@Component({
  selector: 'app-custom-assessements-card',
  standalone: true,
  imports: [ActionButtonComponent, SafeHtmlDirective, DispatchSvgComponent],
  templateUrl: './custom-assessements-card.component.html',
})
export class CustomAssessementsCardComponent implements OnChanges {
  @Input() AssessmentsTitle = '';
  @Input() dispatchedState = '';
  @Input() numberOfTests = 0;
  @Input() Instructions = 'Instructions';
  @Input() instructionMiniDetails = '';
  @Input() duration = 0;
  @Input() timeCreated!: string;
  @Input() timeUpdated!: string;
  @Output() actionButtonClicked = new EventEmitter<unknown>();
  @Output() cardClicked = new EventEmitter<unknown>();
  @Input() showFooter = true;

  // Internal properties to hold processed time values
  displayTimeCreated = '';
  displayTimeUpdated = '';

  ngOnChanges(changes: SimpleChanges): void {
    // Update display time values when timeCreated or timeUpdated inputs change
    if (changes['timeCreated']) {
      this.displayTimeCreated = this.processTimeValue(
        changes['timeCreated'].currentValue || this.timeCreated
      );
    }

    if (changes['timeUpdated']) {
      this.displayTimeUpdated = this.processTimeValue(
        changes['timeUpdated'].currentValue || this.timeUpdated
      );
    }

    // Initialize display values on first change detection if not already set
    if (!this.displayTimeCreated && this.timeCreated) {
      this.displayTimeCreated = this.processTimeValue(this.timeCreated);
    }

    if (!this.displayTimeUpdated && this.timeUpdated) {
      this.displayTimeUpdated = this.processTimeValue(this.timeUpdated);
    }
  }

  /**
   * Process time value - you can add any additional formatting or processing here
   * @param timeValue - The time value to process
   * @returns Processed time string
   */
  private processTimeValue(timeValue: string): string {
    if (!timeValue) return '';

    // For now, just return the value as is
    // You can add additional processing logic here such as:
    // - Additional formatting
    // - Validation
    // - Fallback values
    // - Custom time calculations
    return timeValue;
  }

  /**
   * Get the current display time created value
   * @returns The processed time created string
   */
  get currentTimeCreated(): string {
    return this.displayTimeCreated || this.timeCreated || '';
  }

  /**
   * Get the current display time updated value
   * @returns The processed time updated string
   */
  get currentTimeUpdated(): string {
    return this.displayTimeUpdated || this.timeUpdated || '';
  }

  clickedActionButton(event: void) {
    this.actionButtonClicked.emit(event);
  }

  onCardClicked(event: void) {
    this.cardClicked.emit(event);
  }

  get formattedDuration(): string {
    if (this.duration <= 0) return '0 mins';

    const hours = Math.floor(this.duration / 3600);
    const minutes = Math.floor((this.duration % 3600) / 60);

    if (hours === 0) {
      return `${minutes} min${minutes !== 1 ? 's' : ''}`;
    } else if (minutes === 0) {
      return `${hours} hr${hours !== 1 ? 's' : ''}`;
    } else {
      return `${hours} hr${hours !== 1 ? 's' : ''} ${minutes} min${minutes !== 1 ? 's' : ''}`;
    }
  }
}
