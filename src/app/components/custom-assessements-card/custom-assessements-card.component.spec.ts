import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CustomAssessementsCardComponent } from './custom-assessements-card.component';
import { SimpleChanges } from '@angular/core';

describe('CustomAssessementsCardComponent', () => {
  let component: CustomAssessementsCardComponent;
  let fixture: ComponentFixture<CustomAssessementsCardComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CustomAssessementsCardComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(CustomAssessementsCardComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnChanges', () => {
    it('should update displayTimeCreated when timeCreated changes', () => {
      const mockChanges: SimpleChanges = {
        timeCreated: {
          currentValue: '2023-01-01T10:00:00Z',
          previousValue: null,
          firstChange: true,
          isFirstChange: () => true,
        },
      };

      component.ngOnChanges(mockChanges);

      expect(component.displayTimeCreated).toBe('2023-01-01T10:00:00Z');
    });

    it('should update displayTimeUpdated when timeUpdated changes', () => {
      const mockChanges: SimpleChanges = {
        timeUpdated: {
          currentValue: '2023-01-01T11:00:00Z',
          previousValue: '2023-01-01T10:00:00Z',
          firstChange: false,
          isFirstChange: () => false,
        },
      };

      component.ngOnChanges(mockChanges);

      expect(component.displayTimeUpdated).toBe('2023-01-01T11:00:00Z');
    });

    it('should initialize display values when inputs are available but display values are empty', () => {
      component.timeCreated = '2023-01-01T10:00:00Z';
      component.timeUpdated = '2023-01-01T11:00:00Z';
      component.displayTimeCreated = '';
      component.displayTimeUpdated = '';

      component.ngOnChanges({});

      expect(component.displayTimeCreated).toBe('2023-01-01T10:00:00Z');
      expect(component.displayTimeUpdated).toBe('2023-01-01T11:00:00Z');
    });

    it('should handle null/undefined time values gracefully', () => {
      const mockChanges: SimpleChanges = {
        timeCreated: {
          currentValue: null,
          previousValue: null,
          firstChange: true,
          isFirstChange: () => true,
        },
      };

      expect(() => component.ngOnChanges(mockChanges)).not.toThrow();
      expect(component.displayTimeCreated).toBe('');
    });

    it('should not overwrite existing display values with empty inputs', () => {
      component.displayTimeCreated = 'existing time';
      component.displayTimeUpdated = 'existing updated time';
      component.timeCreated = '';
      component.timeUpdated = '';

      component.ngOnChanges({});

      expect(component.displayTimeCreated).toBe('existing time');
      expect(component.displayTimeUpdated).toBe('existing updated time');
    });
  });

  describe('currentTimeCreated getter', () => {
    it('should return displayTimeCreated when available', () => {
      component.displayTimeCreated = 'display time';
      component.timeCreated = 'original time';

      expect(component.currentTimeCreated).toBe('display time');
    });

    it('should return timeCreated when displayTimeCreated is empty', () => {
      component.displayTimeCreated = '';
      component.timeCreated = 'original time';

      expect(component.currentTimeCreated).toBe('original time');
    });

    it('should return empty string when both are empty', () => {
      component.displayTimeCreated = '';
      component.timeCreated = '';

      expect(component.currentTimeCreated).toBe('');
    });
  });

  describe('currentTimeUpdated getter', () => {
    it('should return displayTimeUpdated when available', () => {
      component.displayTimeUpdated = 'display time updated';
      component.timeUpdated = 'original time updated';

      expect(component.currentTimeUpdated).toBe('display time updated');
    });

    it('should return timeUpdated when displayTimeUpdated is empty', () => {
      component.displayTimeUpdated = '';
      component.timeUpdated = 'original time updated';

      expect(component.currentTimeUpdated).toBe('original time updated');
    });

    it('should return empty string when both are empty', () => {
      component.displayTimeUpdated = '';
      component.timeUpdated = '';

      expect(component.currentTimeUpdated).toBe('');
    });
  });

  describe('Branch Coverage Tests', () => {
    it('should handle empty or falsy time inputs in ngOnChanges', () => {
      // Test with empty string
      component.ngOnChanges({
        timeCreated: {
          currentValue: '',
          previousValue: null,
          firstChange: true,
          isFirstChange: () => true,
        },
      });
      expect(component.displayTimeCreated).toBe('');

      // Test with null
      component.ngOnChanges({
        timeUpdated: {
          currentValue: null,
          previousValue: null,
          firstChange: true,
          isFirstChange: () => true,
        },
      });
      expect(component.displayTimeUpdated).toBe('');
    });

    it('should handle missing changes object properties', () => {
      // Should not throw error when changes object doesn't have expected properties
      expect(() => {
        component.ngOnChanges({
          someOtherProperty: {
            currentValue: 'test',
            previousValue: null,
            firstChange: true,
            isFirstChange: () => true,
          },
        });
      }).not.toThrow();
    });

    it('should handle initialization when displayTime values exist but inputs are empty', () => {
      component.displayTimeCreated = 'existing display';
      component.displayTimeUpdated = 'existing updated';
      component.timeCreated = '';
      component.timeUpdated = '';

      component.ngOnChanges({});

      // Should not overwrite existing display values with empty inputs
      expect(component.displayTimeCreated).toBe('existing display');
      expect(component.displayTimeUpdated).toBe('existing updated');
    });

    it('should handle initialization when both display and input values exist', () => {
      component.displayTimeCreated = 'existing display';
      component.displayTimeUpdated = 'existing updated';
      component.timeCreated = 'input time';
      component.timeUpdated = 'input updated';

      component.ngOnChanges({});

      // Should keep existing display values
      expect(component.displayTimeCreated).toBe('existing display');
      expect(component.displayTimeUpdated).toBe('existing updated');
    });

    it('should handle getter fallbacks correctly', () => {
      // Test currentTimeCreated fallback chain
      component.displayTimeCreated = '';
      component.timeCreated = '';
      expect(component.currentTimeCreated).toBe('');

      component.displayTimeCreated = '';
      component.timeCreated = 'fallback time';
      expect(component.currentTimeCreated).toBe('fallback time');

      component.displayTimeCreated = 'display time';
      component.timeCreated = 'fallback time';
      expect(component.currentTimeCreated).toBe('display time');

      // Test currentTimeUpdated fallback chain
      component.displayTimeUpdated = '';
      component.timeUpdated = '';
      expect(component.currentTimeUpdated).toBe('');

      component.displayTimeUpdated = '';
      component.timeUpdated = 'fallback updated';
      expect(component.currentTimeUpdated).toBe('fallback updated');

      component.displayTimeUpdated = 'display updated';
      component.timeUpdated = 'fallback updated';
      expect(component.currentTimeUpdated).toBe('display updated');
    });
  });

  describe('clickedActionButton', () => {
    it('should emit event through actionButtonClicked when method is called', () => {
      const emitSpy = jest.spyOn(component.actionButtonClicked, 'emit');

      component.clickedActionButton(void 0);

      expect(emitSpy).toHaveBeenCalledWith(void 0);
    });

    it('should emit event when called with non-void parameter', () => {
      const emitSpy = jest.spyOn(component.actionButtonClicked, 'emit');
      const event = {};

      component.clickedActionButton(event as unknown as void);

      expect(emitSpy).toHaveBeenCalledWith(event);
    });
  });

  describe('onCardClicked', () => {
    it('should emit event through cardClicked when method is called', () => {
      const emitSpy = jest.spyOn(component.cardClicked, 'emit');

      component.onCardClicked(void 0);

      expect(emitSpy).toHaveBeenCalledWith(void 0);
    });
  });

  describe('formattedDuration', () => {
    it('should return "0 mins" when duration is 0 or less', () => {
      component.duration = 0;
      expect(component.formattedDuration).toBe('0 mins');

      component.duration = -10;
      expect(component.formattedDuration).toBe('0 mins');
    });

    it('should return minutes only when duration is less than an hour', () => {
      component.duration = 60;
      expect(component.formattedDuration).toBe('1 min');

      component.duration = 120;
      expect(component.formattedDuration).toBe('2 mins');

      component.duration = 1800;
      expect(component.formattedDuration).toBe('30 mins');
    });

    it('should return hours only when minutes are 0', () => {
      component.duration = 3600;
      expect(component.formattedDuration).toBe('1 hr');

      component.duration = 7200;
      expect(component.formattedDuration).toBe('2 hrs');
    });

    it('should return hours and minutes when both are present', () => {
      component.duration = 3660;
      expect(component.formattedDuration).toBe('1 hr 1 min');

      component.duration = 7320;
      expect(component.formattedDuration).toBe('2 hrs 2 mins');

      component.duration = 3720;
      expect(component.formattedDuration).toBe('1 hr 2 mins');
    });
  });
});
