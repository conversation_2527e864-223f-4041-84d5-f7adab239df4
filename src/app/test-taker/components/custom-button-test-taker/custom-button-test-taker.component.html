<button
  [ngClass]="{
    primary: variant === 'primary',
    secondary: variant === 'secondary',
    tertiary: variant === 'tertiary',
    smallPrimary: variant === 'smallPrimary',
  }"
  [disabled]="disabled"
  id="custom-button"
  [type]="type"
  [style.fontSize.px]="fontSize"
  (click)="onClick($event)"
  (keydown)="onKeyDown($event)"
>
  @if (spinner) {
    <i class="pi pi-spin pi-spinner mr-2" style="font-size: 1rem"></i>
  }
  <ng-content></ng-content>
</button>
