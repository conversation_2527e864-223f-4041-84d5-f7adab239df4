@defer {
  <div class="w-full md:w-[90%] lg:w-[80%] mx-auto mt-[2rem]">
    <div class="mb-10 sm:px-4">
      <app-test-taking-page-title title="Verification" />
    </div>
    <div class="flex items-center justify-center mt-[5.6875rem]">
      <div class="grid justify-center w-full sm:w-[40rem]">
        <h2 class="text-[#0C4767] text-2xl sm:text-lg font-medium">
          Kindly enter your email to start your assessment
        </h2>
        <div>
          <div class="flex flex-col px-3 pt-5">
            <form [formGroup]="enterEmailForm" (ngSubmit)="onSubmit()">
              <label
                for="email"
                class="text-[#0C4767] text-[0.625rem] font-[400] mb-3"
                >Email</label
              >
              <input
                class="w-full rounded-lg border border-[#0c4767] h-[40px] px-3"
                id="email"
                type="text"
                [placeholder]="'Enter email'"
                formControlName="email"
              />

              <div class="py-5 lg:pt-10">
                <app-custom-button-test-taker
                  [type]="'submit'"
                  [disabled]="enterEmailForm.invalid || isLoading"
                  [spinner]="isLoading"
                >
                  {{ isLoading ? 'Submitting' : 'Submit' }}
                </app-custom-button-test-taker>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
} @loading {
  <div class="h-[50vh] grid place-content-center">
    <app-refresh-loader></app-refresh-loader>
  </div>
} @error {
  <p>please refresh the page</p>
}
