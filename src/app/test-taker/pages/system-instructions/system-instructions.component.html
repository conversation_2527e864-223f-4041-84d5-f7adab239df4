@defer {
  <main>
    <div id="mobileMessage" class="mobile-message mt-3">
      <p>Please use a laptop or desktop computer for the best experience.</p>
      <p class="text-center">Thank you.</p>
    </div>
    <div class="displayOnLargeScreen">
      <div
        *ngIf="isLoading"
        class="w-full h-[60vh] flex justify-center items-center"
      >
        <span>loading</span>
      </div>

      @if (!isLoading) {
        @switch (isVerified) {
          @case (true) {
            <section
              class="w-full md:w-[90%] mx-auto mt-[2rem] min-h-80 max-h-96"
            >
              <div class="mb-10">
                <app-test-taking-page-title title="System Instructions" />
              </div>
              <p
                class="flex gap-x-4 items-center text-base md:text-lg mb-2 text-[#d9534f] font-bold"
              >
                ⚠️ **Warning:** Once you begin the assessment, you will **NOT**
                be able to change or switch your browser. Please ensure you are
                using the desired browser before starting.
              </p>

              @for (item of proctorFeatures; track item; let index = $index) {
                <p
                  class="flex gap-x-4 items-center text-base md:text-lg mb-2 text-[#474d66]"
                >
                  <span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                    >
                      <path
                        d="M22 11.0801V12.0001C21.9988 14.1565 21.3005 16.2548 20.0093 17.9819C18.7182 19.7091 16.9033 20.9726 14.8354 21.584C12.7674 22.1954 10.5573 22.122 8.53447 21.3747C6.51168 20.6274 4.78465 19.2462 3.61096 17.4372C2.43727 15.6281 1.87979 13.4882 2.02168 11.3364C2.16356 9.18467 2.99721 7.13643 4.39828 5.49718C5.79935 3.85793 7.69279 2.71549 9.79619 2.24025C11.8996 1.76502 14.1003 1.98245 16.07 2.86011"
                        stroke="#0C4767"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M9 11L12 14L22 4"
                        stroke="#0C4767"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      /></svg
                  ></span>
                  {{ systemInstructions[item] }}
                </p>
              }
            </section>

            <section class="md:w-[90%] flex gap-x-2 md:gap-x-5 justify-end">
              <app-custom-button-test-taker
                (clicked)="handleClick()"
                (keyBoardEvent)="handleClick()"
                [spinner]="isButtonLoading"
              >
                {{ isButtonLoading ? '' : 'Proceed' }}
              </app-custom-button-test-taker>
            </section>
          }
          @case (false) {
            <app-enter-email
              [isVerified]="isVerified"
              (emailVerified)="onEmailVerified($event)"
            ></app-enter-email>
          }
        }
      }
    </div>
  </main>
} @loading {
  <div class="h-[50vh] grid place-content-center">
    <app-refresh-loader></app-refresh-loader>
  </div>
} @error {
  <p>please refresh the page</p>
}
