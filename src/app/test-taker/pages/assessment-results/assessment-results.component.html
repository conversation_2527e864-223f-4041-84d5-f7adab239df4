@if (!isLoading && !errorFound && assessmentResult) {
  <div class="w-full max-h-[52rem] overflow-y-auto mt-4">
    <main class="max-w-[1008px] mx-auto border rounded-lg overflow-y-hidden">
      <div>
        <header
          class="flex items-center justify-center bg-[#1E709C] h-[64px] text-white text-[24px] font-bold"
        >
          {{ assessmentResult.assessmentName }}
        </header>
        <div class="content p-8">
          <section class="grid grid-cols-1 gap-4 md:flex justify-between">
            <div class="left-side flex items-center gap-4">
              <div class="icon medal-bg">
                <img src="assets/generalImages/medal.svg" alt="medal" />
              </div>
              <div>
                <h1 class="text-[24px] font-semibold">
                  Test Result{{
                    assessmentResult.testResults.length > 1 ? 's' : ''
                  }}
                </h1>
                <p class="text-[ #082230E5]">
                  Completed on {{ formatDateTime(assessmentResult.endTime) }}
                </p>
              </div>
            </div>
            <div class="right-side grid place-items-center">
              <h1 class="text-[#1E709C] text-[32px] font-semibold">
                {{ assessmentResult.overallAssessmentPercentage }}%
              </h1>
              <p class="text-[#082230CC] text-[16px]">
                Your Score:
                {{
                  assessmentResult.overallAssessmentPassScore +
                    '/' +
                    assessmentResult.overallAssessmentScore
                }}
              </p>
            </div>
          </section>
          <section
            class="cards flex flex-wrap sm:justify-between sm:gap-0 sm:gap-y-4 gap-4 my-6"
          >
            @for (cardInfo of cardsData; track $index) {
              <div
                class="card w-[224px] h-[156px] border rounded-xl flex flex-col justify-center items-center"
              >
                <div
                  class="icon"
                  [ngClass]="
                    iconBackgroundMap[cardInfo.img.split('/').pop() || '']
                  "
                >
                  <img src="{{ cardInfo.img }}" alt="icon" />
                </div>
                <h1 class="text-[16px] font-normal">{{ cardInfo.title }}</h1>
                <p class="text-[#082230E5] text-[20px] font-semibold">
                  {{ cardInfo.value }}
                </p>
              </div>
            }
          </section>
          <section class="assessment-result">
            <h1 class="text-[24px] font-semibold mb-6">Assessment Breakdown</h1>
            <table
              class="min-w-full border border-[#0C47660] rounded-md overflow-hidden"
            >
              <thead
                class="bg-gray-100 h-[42px] text-[18px] text-[#082230E5] font-medium"
              >
                <tr>
                  <th
                    class="px-4 py-2 text-left text-sm font-medium border-b border-[#0C47660]"
                  >
                    Test
                  </th>
                  <th
                    class="px-4 py-2 text-center text-sm font-medium border-b border-[#0C47660]"
                  >
                    Time Spent
                  </th>
                  <th
                    class="px-4 py-2 text-center text-sm font-medium border-b border-[#0C47660]"
                  >
                    Mark Scored
                  </th>
                  <th
                    class="px-4 py-2 text-center text-sm font-medium border-b border-[#0C47660]"
                  >
                    Percentage Score %
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white">
                @for (section of combinedData; track $index) {
                  <tr class="border-b border-[#0822301A]">
                    <td class="px-4 py-2 text-sm text-gray-800 text-left">
                      <span class="text-[16px] font-medium mb-1 block">{{
                        section.title
                      }}</span>
                      <div class="text-[#082230CC] text-[14px] font-normal">
                        {{
                          section.numberOfQuestionsAnswered +
                            '/' +
                            section.numberOfQuestions +
                            ' questions answered'
                        }}
                      </div>
                    </td>
                    <td class="px-4 py-2 text-sm text-gray-800 text-center">
                      {{ this.convertTime(section.duration) }}
                    </td>
                    <td class="px-4 py-2 text-sm text-gray-800 text-center">
                      {{ section.totalPassedScore + '/' + section.totalScore }}
                    </td>
                    <td
                      class="px-4 py-2 text-sm text-center"
                      [ngClass]="
                        section.passStatus === 'PASS'
                          ? 'text-[#04952C]'
                          : 'text-[#C73A3A]'
                      "
                    >
                      {{ section.testPercentage }}
                    </td>
                  </tr>
                }
              </tbody>
            </table>
          </section>
        </div>
      </div>
    </main>
    <div class="w-full flex justify-center my-[1rem]">
      @if (showResult) {
        <button
          class="bg-[#0c4767] flex justify-center h-[45px] px-4 rounded-lg items-center cursor-pointe font-semibold text-base text-[#ffffff]"
          (click)="feedbackPage()"
        >
          Share your thoughts
        </button>
      }
    </div>
  </div>
} @else if (isLoading && !errorFound) {
  <div class="h-[50vh] grid place-content-center">
    <app-refresh-loader></app-refresh-loader>
  </div>
} @else {
  <div class="h-[60vh] grid place-content-center">
    <app-not-found-page [showLogo]="false"></app-not-found-page>
  </div>
}
