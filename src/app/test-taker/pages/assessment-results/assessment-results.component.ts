import { CommonModule } from '@angular/common';
import { Component, NgZone, OnInit, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { TestTakingService } from '../../../services/test-taking-service/test-taking.service';
import { ToastService } from '../../../services/toast-service/toast.service';
import {
  convertSecondsToTime,
  convertTime,
  formatDateTime,
} from '../../../utils/constants';
import {
  AssessmentTakerResult,
  Test,
  TestResult,
} from '../../../Interfaces/Types/testTakerInterface';
import { MatTooltipModule } from '@angular/material/tooltip';
import { QuestionResult } from '../../../Interfaces/Types/reportServiceInterface';
import { RefreshLoaderComponent } from '../../../components/refresh-loader/refresh-loader.component';
import { NotFoundPageComponent } from '../../../not-found-page/not-found-page.component';
@Component({
  selector: 'app-assessment-results',
  standalone: true,
  imports: [
    CommonModule,
    MatTooltipModule,
    RefreshLoaderComponent,
    NotFoundPageComponent,
  ],
  templateUrl: './assessment-results.component.html',
  styles: [
    `
      .icon {
        margin-right: 10px;
        height: 56px;
        width: 56px;
        display: grid;
        place-items: center;
        border-radius: 7px;
      }
      .medal-bg {
        background: #0c476608;
      }
    `,
  ],
})
export class AssessmentResultsComponent implements OnInit, OnDestroy {
  private readonly subscription: Subscription = new Subscription();
  combinedData: TestResult[] = [];
  errorFound = false;
  isLoading = true;
  organizationId: string = '';
  testTakerId: string = '';
  assessmentResult: AssessmentTakerResult | undefined;
  accumulatedTime: number = 0;
  showResult!: string;
  cardsData: {
    img: string;
    title: string;
    value: string;
  }[] = [];
  iconBackgroundMap: Record<string, string> = {
    'note.svg': 'bg-[#085AC408]', // example: red
    'clock.svg': 'bg-[#085AC408]', // example: blue
    'correctIconGreen.svg': 'bg-[#04952C0A]', // example: green
    'crossWithCircle.svg': 'bg-[#C735320A]', // example: orange
  };

  protected readonly convertSecondsToTime = convertSecondsToTime;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly testTakingService: TestTakingService,
    private readonly toast: ToastService,
    private readonly ngZone: NgZone,
    private readonly router: Router
  ) {}
  ngOnInit(): void {
    const resultState = JSON.parse(
      localStorage.getItem('surveyState') as string
    );
    this.showResult = resultState;

    const queryParamsSub = this.route.queryParams.subscribe((params) => {
      this.organizationId = params['organizationId'];
      this.testTakerId = params['testTakerId'];
    });
    this.subscription.add(queryParamsSub);

    const resultSub = this.testTakingService
      .getCandidatesResult(this.testTakerId)
      .subscribe({
        next: (value) => {
          this.assessmentResult = value.data.assessmentTakerResult;

          this.getResultsToLoad();
          this.isLoading = false;
        },
        error: (error) => {
          this.toast.showError(
            '',
            `Failed to load assessment results: ${error.message}`,
            true
          );
          this.isLoading = false;
          this.errorFound = true;
        },
      });
    this.subscription.add(resultSub);
    this.accumulatedTime = this.testTakingService.getAccumulatedTimeTaken();
  }
  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  getResultsToLoad() {
    if (this.assessmentResult) {
      this.combinedData = [...this.assessmentResult.testResults];
      this.cardsData = [
        {
          img: 'assets/generalImages/note.svg',
          title: 'Number of Questions',
          value: this.getTotalQuestions().toString(),
        },
        {
          img: 'assets/generalImages/clock.svg',
          title: 'Total Duration',
          value: this.convertTime(this.assessmentResult.assessmentDuration),
        },
        {
          img: 'assets/generalImages/correctIconGreen.svg',
          title: 'Correct Answers',
          value: this.getTotalQuestionsPassed().toString(),
        },
        {
          img: 'assets/generalImages/crossWithCircle.svg',
          title: 'Wrong Answers',
          value: this.getTotalQuestionsFailed().toString(),
        },
      ];
    }
  }

  getTotalQuestionsPassed() {
    if (!Array.isArray(this.combinedData)) return 0;

    return this.combinedData.reduce((sum, test) => {
      return sum + (test.numberOfQuestionsPassed || 0);
    }, 0);
  }

  getTotalQuestions(): number {
    if (!Array.isArray(this.combinedData)) return 0;

    return this.combinedData.reduce((sum, test) => {
      return sum + (test.numberOfQuestions || 0);
    }, 0);
  }

  getTotalQuestionsFailed() {
    if (!Array.isArray(this.combinedData)) return 0;

    return this.combinedData.reduce((sum, test) => {
      return sum + (test.numberOfQuestionsFailed || 0);
    }, 0);
  }

  convertTime(seconds: number) {
    return convertTime(seconds);
  }

  totalDuration(tests: Test[]) {
    let totalDuration = 0;
    tests.forEach((test: Test) => {
      totalDuration += test.duration;
    });
    return convertSecondsToTime(totalDuration);
  }

  secondsToDigitalClock(time: number): string {
    const seconds = Math.floor(time);
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    const formattedHours = String(hours).padStart(2, '0');
    const formattedMinutes = String(minutes).padStart(2, '0');
    const formattedSeconds = String(remainingSeconds).padStart(2, '0');

    return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
  }

  questionsAnswerOnTest(test: {
    testResult: { questionResults: QuestionResult[] };
  }) {
    let questionsAnswered = 0;

    test.testResult.questionResults.forEach((result) => {
      const answer = result.testTakerAnswers;

      if (answer && answer.length > 0) {
        questionsAnswered += 1;
      }
    });

    return questionsAnswered;
  }

  feedbackPage() {
    this.ngZone.run(() => {
      this.router.navigate(['test-taker/feedback'], {
        queryParams: {
          organizationId: this.organizationId,
          testTakerId: this.testTakerId,
        },
      });
    });
  }
  formatDateTime(value: string) {
    return formatDateTime(value);
  }
}
