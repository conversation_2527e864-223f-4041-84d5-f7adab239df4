<div class="flex flex-col justify-center gap-6">
  <div class="flex flex-col items-center">
    @if (submitted) {
      <img
        src="../../../../../../assets/icons/green tick.png"
        alt="test status"
      />
      <p class="text-xl font-normal text-[#0C4767]">Submitted</p>
    } @else {
      <img
        src="../../../../../../assets/icons/grey tick.png"
        alt="test status"
      />
      <p class="text-xl font-normal text-[#0C4767]">Not Submitted</p>
    }
  </div>
  <div
    class="flex flex-col rounded-lg pt-6 pl-6 border border-[#0c4767] bg-[#EFF3F8] min-w-72 min-h-36"
  >
    <p class="mb-3 text-base font-medium">{{ title }}</p>
    <p class="mb-5 text-xs text-[#687182]">
      {{ questionsAnswered }}/{{ totalQuestions }} questions answered
    </p>
    @if (submitted) {
      <span class="bg-[#E7E8F2] text-xs w-fit py-1 px-2 rounded-xl"
        >Time spent: {{ convertSecondsToTime(timeSpent) }}
      </span>
    } @else {
      <span class="bg-[#E7E8F2] text-xs w-fit py-1 px-2 rounded-xl"
        >Estimated time: {{ convertSecondsToTime(testDuration) }}</span
      >
    }
  </div>
</div>
