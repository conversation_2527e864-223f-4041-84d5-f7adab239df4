<app-custom-toast />
<div class="px-6 py-2">
  <div class="flex flex-col h-[80vh] justify-between items-center">
    <div
      class="flex flex-col items-center justify-center text-center text-blue-B700"
    >
      <h1 class="font-bold text-blue-B700 text-[32px]">
        Welcome back to your "{{ assessmentName }}" Assessment
      </h1>
      <p class="text-lg text-[#0c4767] font-normal">
        We noticed your assessment was interrupted. Please click on
        <b>'Continue Test'</b> to proceed
      </p>
      <div class="flex flex-row text-[#474D66] text-base gap-8">
        <p>Tests: {{ assessmentSections.length }}</p>
        <p>Duration: {{ assessmentDuration }}</p>
        <p>Duration Left: {{ assessmentRemainingDuration }}</p>
      </div>
    </div>

    <div
      class="max-w-4xl mx-5 gap-6 grid grid-flow-col auto-cols-[20rem] overscroll-x-contain snap-x snap-proximity overflow-x-auto pb-6"
    >
      @for (item of assessmentSections; track $index) {
        <app-test-step-status-card
          [title]="item.title"
          [submitted]="item.submitted"
          [testDuration]="item.duration"
          [endTime]="item.endTime"
          [startTime]="item.startTime"
          [questionsAnswered]="getStoredTestAnswers(item)"
          [totalQuestions]="item.numberOfQuestions"
        ></app-test-step-status-card>
      }
    </div>
    <div>
      <button
        class="bg-[#0c4767] text-white w-[157px] rounded-lg h-11"
        (click)="onResume()"
      >
        Continue Test
      </button>
    </div>
  </div>
</div>
