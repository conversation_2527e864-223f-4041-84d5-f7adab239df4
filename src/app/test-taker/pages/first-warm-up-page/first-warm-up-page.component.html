<div
  class="absolute z-10 hidden rounded-xl border-[5px] border-[#0c4767] bg-[#f6f9ff] top-4 left-1/2 transform -translate-x-1/2 sm:left-4 sm:transform-none"
  #shareBanner
>
  <div
    class="flex flex-col text-center p-4 w-80 sm:w-96 lg:w-[420px] min-h-[200px] sm:min-h-[220px]"
  >
    <div class="flex flex-col justify-center h-full space-y-4">
      <span class="text-2xl sm:text-3xl font-semibold text-[#0c4767]">
        Screen Selection
      </span>
      <span
        class="text-lg sm:text-xl lg:text-2xl font-normal text-[#474d66] leading-relaxed"
      >
        Under "Entire Screen", select the screen on which the assessment is
        being taken.
      </span>
    </div>
  </div>
</div>
@switch (isLoading) {
  @case (true) {
    <div class="flex items-center justify-center min-h-screen">
      <p class="text-lg text-blue-B700">Loading...</p>
    </div>
  }
  @case (false) {
    <div
      class="h-[calc(100vh-5rem)] flex flex-col px-3 py-1 sm:px-4 sm:py-2 md:px-6 md:py-3 lg:px-8 lg:py-4 overflow-hidden"
    >
      <!-- Header Section -->
      <div class="flex-shrink-0 text-center mb-1 sm:mb-2 md:mb-3">
        <h1
          class="font-bold text-blue-B700 text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl capitalize px-1 sm:px-2 leading-tight mb-0.5 sm:mb-1"
        >
          Get Ready To Start Your "<span class="break-words">{{
            assessment
          }}</span
          >" Assessment
        </h1>
        <h2
          class="text-blue-B700 text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl mb-0.5"
        >
          You are about to start the first of {{ numberOfTest }} Section(s)
        </h2>
        <h3
          class="text-blue-B700 text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl font-medium"
        >
          Your First Section Is "{{ test }}"
        </h3>
      </div>

      <!-- Main Content Section -->
      <div
        class="flex-1 flex flex-col sm:flex-row items-center justify-center gap-1 sm:gap-2 md:gap-4 lg:gap-6 min-h-0 max-h-full overflow-hidden"
      >
        <!-- Image Container -->
        <div
          class="flex-shrink-0 w-full max-w-[180px] sm:max-w-[250px] md:max-w-md lg:max-w-lg xl:max-w-xl 2xl:max-w-2xl h-auto"
        >
          <img
            src="../../../../assets/testTakingImages/online-test-amico.png"
            alt="Online test illustration"
            class="w-full h-auto object-contain max-h-[120px] sm:max-h-[180px] md:max-h-[280px] lg:max-h-[350px] xl:max-h-[450px] 2xl:max-h-[550px]"
          />
        </div>
        <!-- Duration Info -->
        <div
          class="absolute top-[200px] mr-11 right-0 flex-shrink-0 text-left mt-1 sm:mt-0"
        >
          <p
            class="font-bold text-blue-B700 text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl leading-tight"
          >
            Assessment Duration
          </p>
          <p
            class="font-bold text-blue-B700 text-base sm:text-lg md:text-xl lg:text-2xl xl:text-3xl mt-1 leading-tight"
          >
            {{ assessmentDuration }}
          </p>
        </div>
      </div>

      <!-- Button Section -->
      <div
        class="flex-shrink-0 flex justify-center sm:justify-end mt-2 sm:mt-3 md:mt-4"
      >
        <app-custom-button-test-taker
          (clicked)="onBeginAssessment()"
          (keyBoardEvent)="onBeginAssessment()"
          [spinner]="isButtonLoading"
          [disabled]="isButtonLoading"
          class="w-full sm:w-auto max-w-[200px] sm:max-w-xs"
        >
          {{ isButtonLoading ? '' : 'Begin' }}
        </app-custom-button-test-taker>
      </div>
    </div>
  }
}
@if (isShareScreen) {
  <app-custom-mini-modal
    [headerTitle]="'Share Screen'"
    [textAlign]="'center'"
    [left]="'Don\'t Share'"
    [right]="'Share'"
    [visibleModal]="isShareScreen"
    [bodyText]="'You are required to share your screen to take this test'"
    [width]="'min(446px, 90vw)'"
    [height]="'auto'"
    (leftClickEvent)="declineShare()"
    (rightClickEvent)="confirmShareScreen()"
    (visibleModalChange)="declineShare()"
  >
  </app-custom-mini-modal>
}

@if (apiScreenAccessState) {
  <app-custom-mini-modal
    [headerTitle]="'Screen Sharing Required'"
    [textAlign]="'center'"
    [left]="'Cancel Test'"
    [right]="'Re-share Screen'"
    [visibleModal]="apiScreenAccessState"
    [bodyText]="
      'It seems you’ve blocked screen sharing, which is required to take the test. Please enable it now to continue, or you may have to restart the test'
    "
    [width]="'min(486px, 90vw)'"
    [height]="'auto'"
    (leftClickEvent)="declineShare()"
    (rightClickEvent)="confirmShareScreen()"
    [caution]="true"
    [closeButton]="false"
  >
  </app-custom-mini-modal>
}

<app-custom-mini-modal
  [visibleModal]="showInformationModal"
  [headerTitle]="'Assessment Information'"
  [bodyText]="
    'You may have started the assessment in another tab or window. Please close all other tabs and click Continue to proceed here.'
  "
  (visibleModalChange)="showInformationModal = false"
  [width]="'min(486px, 90vw)'"
  [height]="'auto'"
  [caution]="true"
  [closeButton]="false"
  [left]="'Close'"
  [right]="'Continue'"
  (rightClickEvent)="refreshPage()"
  (leftClickEvent)="showInformationModal = false"
></app-custom-mini-modal>
