import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  OnInit,
  OnDestroy,
  ViewChild,
} from '@angular/core';
import { Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { TestTakingService } from '../../../services/test-taking-service/test-taking.service';
import { ToastService } from '../../../services/toast-service/toast.service';
import { CustomButtonTestTakerComponent } from '../../components/custom-button-test-taker/custom-button-test-taker.component';
import { CommonModule } from '@angular/common';
import { secondsToDigitalClock } from '../../../utils/testTaking';
import { CustomMiniModalComponent } from '../../../components/custom-mini-modal/custom-mini-modal.component';
import { decodeResponse } from '../../../utils/testTakerConstants';
import { ProctoringService } from '../../../services/proctoring.service';
import { getFrontendEstimatedEnd } from '../../../services/TestTakingGuard/test-taking-phase.guard';

@Component({
  selector: 'app-first-warm-up-page',
  standalone: true,
  imports: [
    CustomButtonTestTakerComponent,
    CommonModule,
    CustomMiniModalComponent,
  ],
  templateUrl: './first-warm-up-page.component.html',
  styleUrl: './first-warm-up-page.component.css',
})
export class FirstWarmUpPageComponent
  implements AfterViewInit, OnInit, OnDestroy
{
  private readonly subscription: Subscription = new Subscription();
  @ViewChild('shareBanner') shareBanner!: ElementRef<HTMLCanvasElement>;
  takerId = '';
  assessment: string | undefined;
  numberOfTest: number | undefined;
  test: string | undefined;
  assessmentDuration: string | undefined;
  isLoading = true;
  proctorFeatures: string[] = [];
  isShareScreen: boolean = false;
  identificationInfo = '';
  testId: string = '';
  estimatedDuration: number = 0;
  apiScreenAccessState: boolean = false;
  isButtonLoading = false;
  showInformationModal: boolean = false;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly testTakingService: TestTakingService,
    private readonly toastService: ToastService,
    private readonly cdr: ChangeDetectorRef,
    private readonly proctoringService: ProctoringService
  ) {}

  ngOnInit(): void {
    const denialSub = this.proctoringService.denialModalState$.subscribe(
      (status) => (this.apiScreenAccessState = status)
    );
    this.subscription.add(denialSub);
  }
  ngAfterViewInit(): void {
    this.takerId = this.route.snapshot.queryParams['testTakerId'];
    this.identificationInfo = this.route.snapshot.queryParams['id'];

    if (this.takerId) {
      const apiDataSub = this.testTakingService.testTakerApiData$.subscribe({
        next: (response) => {
          this.assessment = response?.assessmentTaker.assessment.title;
          this.numberOfTest = response?.assessmentTaker.assessment.tests.length;
          this.test = response?.assessmentTaker.assessment.tests[0].title;
          this.assessmentDuration = secondsToDigitalClock(
            response?.assessmentTaker.assessmentDuration as number
          );

          // Ensure proctorFeatures is defined and is an array
          this.proctorFeatures = response?.assessmentTaker
            .proctorFeatures as string[];

          const shouldShareScreen =
            this.proctorFeatures.includes('Screen Capture');
          this.isShareScreen = shouldShareScreen;
          this.isLoading = false;

          // Manually trigger change detection
          this.cdr.detectChanges();
        },

        error: (error) => {
          this.toastService.onShow('error', error, true, 'error');
        },
      });
      this.subscription.add(apiDataSub);
    }
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onBeginAssessment() {
    this.isButtonLoading = true;
    let testTakerIdentification;
    if (this.identificationInfo) {
      const decoded = decodeResponse(this.identificationInfo);

      testTakerIdentification = {
        ...(decoded.idCard ? { linkId: decoded.idCard } : {}),
        linkHead: decoded.headShotLocationInS3,
      };
    }

    this.testTakingService
      .getAssessmentInformation(
        this.takerId,
        testTakerIdentification ? { identity: testTakerIdentification } : {}
      )
      .subscribe({
        next: (response) => {
          this.testId = response.data.testList[0];
          this.testTakingService.inProgressReturnedData = response.data;
          const { estimatedEndTime, currentDate } =
            this.testTakingService.inProgressReturnedData;
          this.testTakingService.inProgressReturnedData.estimatedEndTime =
            getFrontendEstimatedEnd(estimatedEndTime, currentDate);
          this.router.navigate(['test-taking/exams-phase'], {
            queryParams: { testTakerId: this.takerId, testId: this.testId },
          });

          this.estimatedDuration = Math.floor(
            (new Date(
              this.testTakingService.inProgressReturnedData?.estimatedEndTime
            ).getTime() -
              new Date().getTime()) /
              1000
          );
          localStorage.setItem(
            'assessmentTimeRemaining',
            this.estimatedDuration.toString()
          );
          this.isButtonLoading = false;
        },
        error: (error) => {
          if (error === 'Assessment already in progress') {
            this.showInformationModal = true;
          } else {
            this.toastService.onShow('error', error, true, 'error');
          }

          this.isButtonLoading = false;
        },
      });

    this.testTakingService.assessmentData.startTime = new Date().toISOString();
    localStorage.setItem(
      'assessmentResults',
      JSON.stringify(this.testTakingService.assessmentData)
    );
  }

  onReady() {}

  declineShare() {
    this.isShareScreen = false;
    this.router.navigate(['test-taker/system-instruction'], {
      queryParams: { testTakerId: this.takerId },
    });
  }

  shareScreenBanner() {
    const canvas = this.shareBanner.nativeElement;

    canvas.style.display = 'block';

    setTimeout(() => {
      canvas.width = 0;
      canvas.height = 0;
      canvas.style.display = 'none';
    }, 19000);
  }

  confirmShareScreen() {
    this.isShareScreen = false;

    this.shareScreenBanner();

    this.proctoringService.shareScreen();
  }

  refreshPage() {
    window.location.reload();
  }
}
