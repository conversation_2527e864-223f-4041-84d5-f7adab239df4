@if (!surveyDone) {
  <div
    class="feedback-wrapper w-full flex flex-col items-center justify-center px-4"
  >
    <div class="grid justify-items-center">
      <div class="feedback-container w-full mt-7 max-w-md">
        <header>
          <div class="Feedback">
            <h1 class="text-2xl font-bold mb-5 text-[#0C4767]">Feedback</h1>
          </div>
        </header>

        <main>
          <ng-container *ngFor="let feedBack of submissions; let i = index">
            <div class="text-sm text-[#474D66]">
              {{ feedBack.questionText }}
            </div>
            <div
              *ngIf="
                i !== submissions.length - 1 && feedBack.rating !== undefined
              "
              class="mt-2"
            >
              <mat-icon
                *ngFor="let star of maxRatingArr; let index = index"
                (click)="onClickToRate(feedBack, index)"
                (keydown.enter)="onClickToRate(feedBack, index)"
                class="cursor-pointer text-[#0C4767]"
                [ngClass]="{ checked: feedBack.rating > index }"
              >
                {{ feedBack.rating > index ? 'star' : 'star_outline' }}
              </mat-icon>
            </div>
            <div
              class="mb-5 text-[0.8rem] text-[#8C8F9A]"
              *ngIf="i !== submissions.length - 1"
            >
              {{ remarks(feedBack) }}
            </div>
            <div class="mt-2" *ngIf="feedBack.type === 'comment'">
              <textarea
                (input)="onCommentChange(feedBack.id, $event)"
                [cols]="30"
                [rows]="4"
                class="w-full border p-3 border-[#000000] rounded-lg"
                placeholder="Type your answer here"
              ></textarea>
            </div>
          </ng-container>
        </main>
      </div>
      <div class="w-full mt-5">
        <button
          class="bg-[#0c4767] flex justify-center h-10 rounded-lg items-center cursor-pointer w-40 min-w-[8rem] font-semibold text-base text-[#ffffff]"
          *ngIf="!isLoadingQuestion"
          (click)="onSubmit()"
        >
          Submit Feedback
        </button>
      </div>
    </div>
  </div>
}
@if (surveyDone) {
  <div class="flex flex-col mx-auto text-center w-[54.0625rem] mt-[4rem]">
    <header>
      <h1 class="text-3xl font-bold mb-5 text-[#0C4767]">Feedback Submitted</h1>
    </header>
    <p class="text-[#0c4767] text-[16px] mt-8">
      Thank you for sharing your experience! Your feedback helps us to improve
      the platform and the overall usability continuously!.
    </p>
    <div class="mx-auto">
      <img
        src="../../../../assets/generalImages/Done-rafiki 2.svg"
        alt="done Rafiki"
      />
    </div>
  </div>
}
