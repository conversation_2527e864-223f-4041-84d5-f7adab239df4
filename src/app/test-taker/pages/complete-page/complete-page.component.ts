import { Component, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-complete-page',
  standalone: true,
  imports: [],
  templateUrl: './complete-page.component.html',
})
export class CompletePageComponent implements OnInit, OnDestroy {
  private readonly subscription: Subscription = new Subscription();
  organizationId: string = '';
  testTakerId: string = '';
  showSurvey: boolean = false;
  showResult!: boolean;
  constructor(
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly ngZone: NgZone
  ) {}
  ngOnInit(): void {
    const surveyState = JSON.parse(
      localStorage.getItem('surveyState') as string
    );
    const resultState = JSON.parse(
      localStorage.getItem('resultState') as string
    );
    this.showSurvey = surveyState;
    this.showResult = resultState;
    const queryParamsSub = this.route.queryParams.subscribe((params) => {
      this.organizationId = params['organizationId'];
      this.testTakerId = params['testTakerId'];
    });
    this.subscription.add(queryParamsSub);
  }
  ngOnDestroy(): void {
    localStorage.removeItem('testProgress');
    this.subscription.unsubscribe();
  }
  viewResult() {
    this.ngZone.run(() => {
      this.router.navigate(['test-taker/assessment-results'], {
        queryParams: {
          organizationId: this.organizationId,
          testTakerId: this.testTakerId,
        },
      });
    });
  }
  feedbackPage() {
    this.ngZone.run(() => {
      this.router.navigate(['test-taker/feedback'], {
        queryParams: {
          organizationId: this.organizationId,
          testTakerId: this.testTakerId,
        },
      });
    });
  }
}
