<div class="honor-code-wrapper w-full flex flex-col items-center">
  <div class="honor-code-container w-[80%]">
    <div
      class="honor-code-header-test text-[#0C4767] text-[1.75rem] font-semibold mt-8"
    >
      Honour Code
    </div>
    <p class="plegde mb-6 mt-8">
      As a candidate in this online assessment, I hereby pledge to uphold the
      following honor code:
    </p>

    <div class="flex flex-col text-[#474D66]">
      <label
        *ngFor="let item of checkboxItems"
        class="hover:bg-black/5 px-1.5 py-1 rounded-lg checkbox-label mb-1.5 flex gap-x-4 items-start"
      >
        <div class="custom-checkbox relative flex flex-row pt-1">
          <input type="checkbox" class="h-5 w-5" [(ngModel)]="item.checked" />
          <span class="checkmark"></span>
        </div>
        <div class="label-text flex-1 cursor-pointer text-[#474d66]">
          <span class="text-[#000] font-medium text-base"
            >{{ item.title }}:</span
          >
          {{ item.label }}
        </div>
      </label>
    </div>

    <div class="mt-3 ml-7">
      <p class="final-consent text-[0.875rem] text-[#474D66]">
        By proceeding with this online assessment, I agree to abide by this
        honor code and understand that any violation could lead to penalties,
        including but not limited to disqualification from the assessment, loss
        of grades, and further disciplinary actions. I also understand that
        upholding this honor code not only reflects on my personal character but
        also upholds the integrity and reputation of this platform.
      </p>
    </div>
    <div class="button flex w-full bottom-0 justify-center gap-6 mt-[2.625rem]">
      <app-custom-button-test-taker
        [variant]="'secondary'"
        [spinner]="isButtonLoading"
        (clicked)="onDecline()"
      >
        Decline
      </app-custom-button-test-taker>
      <app-custom-button-test-taker
        [spinner]="isButtonLoading"
        [variant]="'primary'"
        (clicked)="onAccept()"
      >
        Accept
      </app-custom-button-test-taker>
    </div>
  </div>
</div>
