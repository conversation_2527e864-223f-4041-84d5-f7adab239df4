@if (processedInstructions !== null) {
  @defer {
    <div class="flex flex-col overflow-x-hidden h-[85vh] justify-between pr-4">
      <div
        class="header-title text-[#0C4767] text-[1.5rem] font-semibold pt-[2.125rem] max-h-[10vh] ml-12 flex w-full"
      >
        Assessment Instructions
      </div>

      <div class="flex flex-col h-[75vh] justify-between">
        <div class="flex w-full ml-10">
          <app-custom-text-editor
            [showToolbar]="false"
            [isViewerMode]="true"
            [maxHeight]="'700px'"
            [maxWidth]="'657px'"
            [formControl]="getFormControl(processedInstructions)"
          ></app-custom-text-editor>
        </div>

        <div class="flex w-full bottom-0 justify-end">
          <app-custom-button-test-taker
            [spinner]="isButtonLoading"
            (clicked)="onStart()"
            (keyBoardEvent)="onStart()"
          >
            Proceed
          </app-custom-button-test-taker>
        </div>
      </div>
    </div>
  } @loading {
    <div class="flex items-center justify-center h-[50vh]">
      <app-dodokpo-loader></app-dodokpo-loader>
    </div>
  } @error {
    <p>please reload the page</p>
  }
} @else {
  <app-refresh-loader></app-refresh-loader>
}
