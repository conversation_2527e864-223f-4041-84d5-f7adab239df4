<main class="testme">
  <div class="text-blue-B700 text-2xl ml-[12rem] font-bold">
    <p style="font-family: 'Work Sans', sans-serif">ID Verification</p>
  </div>
  @if (!isIdCaptured) {
    <section class="page-body grid place-items-center">
      <div class="notice">
        <p
          class="notice-one text-blue-B700 flex justify-center items-center text-2xl font-medium"
        >
          Kindly display your ID in the frame
        </p>
        <p class="flex justify-center items-center text-blue-B700 text-base">
          Align ID with the red marks before capturing
        </p>
        <p class="flex justify-center items-center text-blue-B700 text-center">
          If you decide to upload your ID, it must be in a pdf, jpg or png
          format. .
          <br />File size should be less than 2mb
        </p>
      </div>

      <div class="video-container relative mt-1">
        <video #video autoplay></video>
        <div
          class="red-frame absolute inset-0 flex justify-center items-center"
        >
          @if (isLoading) {
            <div class="flex justify-center items-center mt-13">
              <app-refresh-loader
                [loadingText]="'Loading...'"
              ></app-refresh-loader>
            </div>
          }

          <!-- Processing Overlay -->
          @if (isCapturingAndProcessing || isVerifying) {
            <div
              class="absolute inset-0 bg-black bg-opacity-50 flex flex-col justify-center items-center z-10"
            >
              <div
                class="animate-spin rounded-full h-12 w-12 border-b-2 border-white mb-4"
              ></div>
              <div class="text-white text-center">
                @if (isCapturingAndProcessing && !isVerifying) {
                  <p class="text-lg font-medium">Capturing ID...</p>
                  <p class="text-sm opacity-80">Please wait</p>
                } @else if (isVerifying) {
                  <p class="text-lg font-medium">Verifying ID...</p>
                  <p class="text-sm opacity-80">Analyzing document</p>
                }
              </div>
            </div>
          }

          <div id="reaction">
            <div id="top-left" class="corner"></div>
            <div id="top-right" class="corner"></div>
            <div id="bottom-right" class="corner"></div>
            <div id="bottom-left" class="corner"></div>
          </div>
        </div>
      </div>
      <canvas #canvas width="400" height="300" style="display: none"></canvas>

      <div class="button-placement flex justify-center mt-1 gap-4">
        <button
          (click)="idCapture()"
          class="bg-[#0c4767] text-white w-[157px] rounded-lg h-11 text-base disabled:bg-[#B5B5C3] flex items-center justify-center gap-2"
          [disabled]="isLoading || isCapturingAndProcessing || isVerifying"
        >
          @if (isCapturingAndProcessing || isVerifying) {
            <div
              class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"
            ></div>
            <span>Processing...</span>
          } @else {
            Take Photo
          }
        </button>
        <label
          for="file"
          class="text-blue-B700 w-[15rem] cursor-pointer border-2 border-[#0C4767] rounded-lg text-center bg-[#fff] py-2 mt-3 flex items-center justify-center gap-2"
          [class.opacity-50]="isVerifying"
          [class.cursor-not-allowed]="isVerifying"
        >
          @if (isVerifying) {
            <div
              class="animate-spin rounded-full h-4 w-4 border-b-2 border-[#0C4767]"
            ></div>
            <span>Verifying...</span>
          } @else {
            Click to Select File
          }
          <input
            class="hidden"
            id="file"
            type="file"
            (change)="handleFileInput($event)"
            [disabled]="isVerifying"
          />
        </label>
      </div>
    </section>
  }
  @if (isIdCaptured) {
    <section
      class="camera-container grid place-items-center h-[calc(100vh-10rem)]"
    >
      <div class="preview-container grid place-items-center">
        <p
          class="notice-one text-blue-B700 flex justify-center items-center text-2xl font-medium"
        >
          {{ isUploaded ? 'File to be uploaded' : ' ID shot taken' }}
        </p>
        @if (selectedImage?.type === 'application/pdf') {
          <p class="text-[32px] text-[#474D66]">PDF</p>
          <p>{{ selectedImage?.name }}</p>
          <a class="text-[16px]" href="{{ pictureOfId }}" download
            >Click to download file</a
          >
        } @else {
          <div class="max-w-[400px] max-h-[300px] overflow-hidden">
            <img [src]="imageSrc || pictureOfId" alt="" />
          </div>
        }

        <!-- ID Verification Status -->
        @if (isProcessing) {
          <div
            class="flex flex-col items-center mt-6 p-4 bg-blue-50 rounded-lg"
          >
            <div
              class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-2"
            ></div>
            <p class="text-blue-700 font-medium">
              {{ getVerificationStatus() }}
            </p>
            @if (isCapturingAndProcessing && !isVerifying) {
              <p class="text-sm text-blue-600 mt-1">
                Capturing and preparing image...
              </p>
            } @else if (isVerifying) {
              <p class="text-sm text-blue-600 mt-1">Analyzing document ...</p>
            } @else {
              <p class="text-sm text-blue-600 mt-1">
                Please wait while we verify your ID...
              </p>
            }
          </div>
        }

        @if (shouldShowVerificationResult) {
          <div class="mt-1 p-4 rounded-lg">
            <!-- Retry Options for Failed Verification -->
            @if (!verificationResult?.isValid) {
              <div class="flex justify-center mt-4">
                <button
                  (click)="retryVerification()"
                  class="px-4 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  [disabled]="isVerificationLoading"
                >
                  Retry Verification
                </button>
              </div>
            }
          </div>
        }

        <div class="flex flex-wrap justify-center mt-8 gap-4 mb-8 pb-6">
          <button
            (click)="goBack()"
            class="bg-[#0c4767] text-white min-w-[157px] px-4 rounded-lg h-11 text-base"
          >
            {{ isUploaded ? 'Select a different file' : 'Retake' }}
          </button>
          <div class="flex pt-[10px]">
            <app-custombutton
              [type]="'submit'"
              [spinner]="imageUploading || isVerificationLoading"
              [disabled]="
                isVerificationLoading ||
                imageUploading ||
                (shouldShowVerificationResult && !verificationResult?.isValid)
              "
              (clicked)="submitImage()"
            >
              @if (isVerificationLoading) {
                Verifying...
              } @else if (
                shouldShowVerificationResult && !verificationResult?.isValid
              ) {
                Verification Failed
              } @else {
                {{ isUploaded ? 'Upload' : imageUploading ? '' : 'Proceed' }}
              }
            </app-custombutton>
          </div>
        </div>
      </div>
    </section>
  }
</main>

@if (isCameraAccessPermission === false && isIdCaptured === false) {
  <app-custom-mini-modal
    [headerTitle]="'Camera Access Required'"
    [textAlign]="'center'"
    [right]="'Re-enable Camera'"
    [visibleModal]="isCameraAccessPermission === false"
    [bodyText]="
      'Let\'s resume your test by granting us permission to your camera. Select icon <img src=\'../../../assets/icons/page-info.svg\' alt=\'info icon\' class=\'h-6 w-6 inline align-text-bottom\'>/<img src=\'../../../assets/icons/privacy.svg\' alt=\'info icon\' class=\'h-6 w-6 inline align-text-bottom\'> at the left side of your address bar, then toggle on the camera permission. After that, click on &quot;Re-enable Camera&quot; to continue.</div>'
    "
    [width]="'546px'"
    [height]="'325px'"
    (rightClickEvent)="startCamera()"
    [caution]="true"
    [closeButton]="false"
    [showLeftButton]="false"
  >
  </app-custom-mini-modal>
}
