@switch (isHeadCaptured) {
  @case (false) {
    <main class="testme">
      <div class="text-blue-B700 text-2xl ml-[15rem] font-bold">
        <p style="font-family: 'Work Sans', sans-serif">Headshot</p>
      </div>

      <section class="page-body grid place-items-center">
        <p
          class="text-blue-B700 notice flex justify-center items-center text-2xl font-medium"
        >
          Kindly make sure your head is in the frame
        </p>
        <div class="video-container relative mt-3">
          <video #video autoplay></video>
          <div
            class="red-frame absolute inset-0 flex justify-center items-center"
          >
            @if (isLoading) {
              <div class="flex justify-center items-center mt-13">
                <app-refresh-loader
                  [loadingText]="'Loading...'"
                ></app-refresh-loader>
              </div>
            }
            <div id="reaction">
              <div id="top-left" class="corner"></div>
              <div id="top-right" class="corner"></div>
              <div id="bottom-right" class="corner"></div>
              <div id="bottom-left" class="corner"></div>
            </div>
          </div>
        </div>
        <canvas #canvas width="400" height="300" style="display: none"></canvas>

        <div class="button-placement flex justify-center mt-[10px] gap-4">
          <button
            (click)="captureHead()"
            class="bg-[#0c4767] text-white w-[157px] rounded-lg h-11 text-base disabled:bg-[#B5B5C3]"
            [disabled]="isLoading"
          >
            Take Photo
          </button>
        </div>
      </section>
    </main>
  }

  @case (true) {
    <main class="testme">
      <div class="text-blue-B700 text-2xl ml-[15rem] font-bold">
        <p style="font-family: 'Work Sans', sans-serif">Headshot</p>
      </div>
      <section class="page-body grid place-items-center">
        <div class="preview-container grid place-items-center">
          <p
            class="text-blue-B700 notice flex justify-center items-center text-2xl font-medium"
          >
            Headshot image that was taken.
          </p>
          <img [src]="imageSrc" alt="CapturedImage" />
          <div class="flex justify-center mt-8 gap-4">
            <button
              (click)="goBack()"
              class="bg-[#0c4767] text-white w-[157px] rounded-lg h-11 text-base"
            >
              {{ isUploaded ? 'Select a different file' : 'Retake' }}
            </button>
            <div class="flex pt-[10px]">
              <app-custombutton
                [type]="'submit'"
                [spinner]="uploadingImage"
                (clicked)="submitImage()"
              >
                {{ isUploaded ? 'Upload' : uploadingImage ? '' : 'Proceed' }}
              </app-custombutton>
            </div>
          </div>
        </div>
      </section>
    </main>
  }
}

@if (isCameraAccessPermission === false) {
  <app-custom-mini-modal
    [headerTitle]="'Camera Access Required'"
    [textAlign]="'center'"
    [right]="'Re-enable Camera'"
    [visibleModal]="isCameraAccessPermission === false"
    [bodyText]="
      'Let\'s resume your test by granting us permission to your camera. Select icon <img src=\'../../../assets/icons/page-info.svg\' alt=\'info icon\' class=\'h-6 w-6 inline align-text-bottom\'>/<img src=\'../../../assets/icons/privacy.svg\' alt=\'info icon\' class=\'h-6 w-6 inline align-text-bottom\'> at the left side of your address bar, then toggle on the camera permission. After that, click on &quot;Re-enable Camera&quot; to continue.'
    "
    [width]="'546px'"
    [height]="'325px'"
    (rightClickEvent)="startCamera()"
    [caution]="true"
    [closeButton]="false"
    [showLeftButton]="false"
  >
  </app-custom-mini-modal>
}
