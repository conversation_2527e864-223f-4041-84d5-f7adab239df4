@switch (isLoading || (proctoringInfo && proctoringInfo.length === 0)) {
  @case (true) {
    <p>loading/p></p>
  }
  @case (false) {
    @if (proctoringInfo && proctoringInfo.includes('ID Capture')) {
      <app-id-capture></app-id-capture>
    } @else if (
      proctoringInfo && proctoringInfo.includes('Candidate Capture')
    ) {
      <app-face-capture></app-face-capture>

    } @else {
      <app-assessment-instructions></app-assessment-instructions>
    }
  }
}
