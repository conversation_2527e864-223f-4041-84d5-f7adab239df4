import { Component, TemplateRef, ViewChild } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BehaviorSubject } from 'rxjs';
import { FeatureFlagDirective } from './feature-flag.directive';
import { FeatureFlagService, FeatureFlagConfig } from '../services/feature-flag.service';

// Mock FeatureFlagService
class MockFeatureFlagService {
  private flagsSubject = new BehaviorSubject<Partial<FeatureFlagConfig>>({
    enableAICodeGeneration: false,
    enableFaceDetection: true,
    enableDarkTheme: true
  });

  getFlag(flagName: keyof FeatureFlagConfig) {
    return this.flagsSubject.pipe(
      map(flags => flags[flagName] ?? false)
    );
  }

  setFlag(flagName: keyof FeatureFlagConfig, value: boolean) {
    const currentFlags = this.flagsSubject.value;
    this.flagsSubject.next({ ...currentFlags, [flagName]: value });
  }
}

// Test component
@Component({
  template: `
    <div *appFeatureFlag="'enableAICodeGeneration'" data-testid="ai-content">
      AI Feature Content
    </div>
    
    <div *appFeatureFlag="'enableFaceDetection'; else elseTemplate" data-testid="face-content">
      Face Detection Content
    </div>
    
    <ng-template #elseTemplate>
      <div data-testid="else-content">Alternative Content</div>
    </ng-template>
    
    <div *appFeatureFlag="'enableUnknownFeature'" data-testid="unknown-content">
      Unknown Feature Content
    </div>
  `,
  standalone: true,
  imports: [FeatureFlagDirective]
})
class TestComponent {
  @ViewChild('elseTemplate') elseTemplate!: TemplateRef<any>;
}

describe('FeatureFlagDirective', () => {
  let component: TestComponent;
  let fixture: ComponentFixture<TestComponent>;
  let mockFeatureFlagService: MockFeatureFlagService;

  beforeEach(async () => {
    mockFeatureFlagService = new MockFeatureFlagService();

    await TestBed.configureTestingModule({
      imports: [TestComponent, FeatureFlagDirective],
      providers: [
        { provide: FeatureFlagService, useValue: mockFeatureFlagService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(TestComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should hide content when feature flag is disabled', () => {
    fixture.detectChanges();
    
    const aiContent = fixture.nativeElement.querySelector('[data-testid="ai-content"]');
    expect(aiContent).toBeNull();
  });

  it('should show content when feature flag is enabled', () => {
    fixture.detectChanges();
    
    const faceContent = fixture.nativeElement.querySelector('[data-testid="face-content"]');
    expect(faceContent).toBeTruthy();
    expect(faceContent.textContent.trim()).toBe('Face Detection Content');
  });

  it('should show else template when feature flag is disabled', () => {
    // Disable face detection
    mockFeatureFlagService.setFlag('enableFaceDetection', false);
    fixture.detectChanges();
    
    const faceContent = fixture.nativeElement.querySelector('[data-testid="face-content"]');
    const elseContent = fixture.nativeElement.querySelector('[data-testid="else-content"]');
    
    expect(faceContent).toBeNull();
    expect(elseContent).toBeTruthy();
    expect(elseContent.textContent.trim()).toBe('Alternative Content');
  });

  it('should react to flag changes', () => {
    fixture.detectChanges();
    
    // Initially AI content should be hidden
    let aiContent = fixture.nativeElement.querySelector('[data-testid="ai-content"]');
    expect(aiContent).toBeNull();
    
    // Enable AI feature
    mockFeatureFlagService.setFlag('enableAICodeGeneration', true);
    fixture.detectChanges();
    
    // Now AI content should be visible
    aiContent = fixture.nativeElement.querySelector('[data-testid="ai-content"]');
    expect(aiContent).toBeTruthy();
    expect(aiContent.textContent.trim()).toBe('AI Feature Content');
    
    // Disable AI feature again
    mockFeatureFlagService.setFlag('enableAICodeGeneration', false);
    fixture.detectChanges();
    
    // AI content should be hidden again
    aiContent = fixture.nativeElement.querySelector('[data-testid="ai-content"]');
    expect(aiContent).toBeNull();
  });

  it('should handle unknown feature flags', () => {
    fixture.detectChanges();
    
    const unknownContent = fixture.nativeElement.querySelector('[data-testid="unknown-content"]');
    expect(unknownContent).toBeNull();
  });

  it('should clean up subscriptions on destroy', () => {
    fixture.detectChanges();
    
    const directive = fixture.debugElement.children[0].injector.get(FeatureFlagDirective);
    const unsubscribeSpy = jest.spyOn(directive['subscription'], 'unsubscribe');
    
    fixture.destroy();
    
    expect(unsubscribeSpy).toHaveBeenCalled();
  });
});

// Additional test for directive without else template
@Component({
  template: `
    <div *appFeatureFlag="'enableDarkTheme'" data-testid="theme-content">
      Dark Theme Content
    </div>
  `,
  standalone: true,
  imports: [FeatureFlagDirective]
})
class SimpleTestComponent {}

describe('FeatureFlagDirective - Simple Usage', () => {
  let component: SimpleTestComponent;
  let fixture: ComponentFixture<SimpleTestComponent>;
  let mockFeatureFlagService: MockFeatureFlagService;

  beforeEach(async () => {
    mockFeatureFlagService = new MockFeatureFlagService();

    await TestBed.configureTestingModule({
      imports: [SimpleTestComponent, FeatureFlagDirective],
      providers: [
        { provide: FeatureFlagService, useValue: mockFeatureFlagService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(SimpleTestComponent);
    component = fixture.componentInstance;
  });

  it('should show content when flag is enabled and no else template', () => {
    fixture.detectChanges();
    
    const themeContent = fixture.nativeElement.querySelector('[data-testid="theme-content"]');
    expect(themeContent).toBeTruthy();
    expect(themeContent.textContent.trim()).toBe('Dark Theme Content');
  });

  it('should hide content when flag is disabled and no else template', () => {
    mockFeatureFlagService.setFlag('enableDarkTheme', false);
    fixture.detectChanges();
    
    const themeContent = fixture.nativeElement.querySelector('[data-testid="theme-content"]');
    expect(themeContent).toBeNull();
  });
});

// Import map function for the mock service
import { map } from 'rxjs/operators';
