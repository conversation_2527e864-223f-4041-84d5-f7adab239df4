import { 
  Directive, 
  Input, 
  TemplateRef, 
  ViewContainerRef, 
  OnInit, 
  OnDestroy 
} from '@angular/core';
import { Subscription } from 'rxjs';
import { FeatureFlagService, FeatureFlagConfig } from '../services/feature-flag.service';

@Directive({
  selector: '[appFeatureFlag]',
  standalone: true
})
export class FeatureFlagDirective implements OnInit, OnDestroy {
  private subscription?: Subscription;
  private flagName?: keyof FeatureFlagConfig;
  private hasView = false;

  @Input() set appFeatureFlag(flagName: keyof FeatureFlagConfig) {
    this.flagName = flagName;
    this.updateView();
  }

  @Input() appFeatureFlagElse?: TemplateRef<unknown>;

  constructor(
    private templateRef: TemplateRef<unknown>,
    private viewContainer: ViewContainerRef,
    private featureFlagService: FeatureFlagService
  ) {}

  ngOnInit(): void {
    this.updateView();
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  private updateView(): void {
    if (!this.flagName) {
      return;
    }

    // Clean up existing subscription
    if (this.subscription) {
      this.subscription.unsubscribe();
    }

    // Subscribe to flag changes
    this.subscription = this.featureFlagService.getFlag(this.flagName).subscribe(
      (isEnabled: boolean) => {
        this.viewContainer.clear();
        this.hasView = false;

        if (isEnabled) {
          this.viewContainer.createEmbeddedView(this.templateRef);
          this.hasView = true;
        } else if (this.appFeatureFlagElse) {
          this.viewContainer.createEmbeddedView(this.appFeatureFlagElse);
          this.hasView = true;
        }
      }
    );
  }
}
