import { HttpErrorResponse, HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { from, throwError } from 'rxjs';
import { switchMap, catchError } from 'rxjs/operators';
import { isInternetAvailable } from './utils/constants';
import { AuthService } from './services/auth.service';
import { Router } from '@angular/router';
import { ErrorMessage } from './services/authServiceInterfaces';

export const errorInterceptor: HttpInterceptorFn = (req, next) => {
  const authService = inject(AuthService);
  const router = inject(Router);

  return next(req).pipe(
    catchError((error: HttpErrorResponse | ErrorMessage) => {
      // Handle status 0: possible network/server issues
      if (error.status === 0) {
        return from(isInternetAvailable()).pipe(
          switchMap((hasInternet) => {
            const message = hasInternet
              ? 'Error communicating with Server. Please try again later'
              : ' Please check your network.';

            error = { ...error.error, message };

            return handleErrorAndThrow(error, authService, router);
          })
        );
      }

      // Handle other error codes
      if (error.status === 502) {
        error.error.message =
          'Error communicating with Server. Please try again later';
      }
      if (error.status === 503) {
        error.error.message =
          error.error?.data?.message ??
          'The server is currently undergoing recovery. Please try again later.';
      }
      if (error.status === 504) {
        error.error.message =
          'Server is currently not available. Please try again later';
      }

      return handleErrorAndThrow(error, authService, router);
    })
  );
};

// Extracted error handling to avoid duplication
function handleErrorAndThrow(
  error: HttpErrorResponse | ErrorMessage,
  authService: AuthService,
  router: Router
) {
  const newerror =
    error.error?.message ??
    error.error?.error?.message ??
    error.error?.error ??
    error.error?.data?.message ??
    error['message'] ??
    error;

  if (error.status === 404) {
    window.history.back();
    return throwError(() => newerror ?? 'File not found');
  }

  if (
    typeof newerror === 'string' &&
    ['invalid token', 'expired token', 'missing token'].includes(
      newerror.toLowerCase()
    )
  ) {
    authService.logOut();
  }

  if (
    newerror ===
    'Your account has been deactivated, Contact your organization admin.'
  ) {
    throwError(() => newerror);
    setTimeout(() => {
      authService.logOut();
    }, 5000);
  }

  switch (newerror?.toLowerCase?.()) {
    case 'invalid':
      router.navigate(['invalid-link']);
      break;
    case 'undue':
      router.navigate(['test-taker/assessment-not-due'], {
        queryParams: { commenceDate: error.error?.data?.commenceDate },
      });
      break;
    case 'expired':
      router.navigate(['link-expired']);
      break;
    case 'invalid token':
    case 'expired token':
    case 'missing token':
      authService.logOut();
      break;
  }

  return throwError(
    () =>
      newerror ?? 'Server is currently not available. Please try again later'
  );
}
