<main
  class="w-full h-full min-w-[18.75rem] lg:w-[30.1875rem] max-w-[30.1875rem] lg:min-h-[38.75rem] lg:max-h-[45rem] rounded-xl bg-white notifications-card z-10 overflow-hidden"
>
  <header class="flex justify-between px-6 py-4">
    <div class="flex">
      <h1 class="text-[#0C4767] text-[18px] font-semibold lg:mr-2">
        Notifications
      </h1>
      @if (soundEnabled) {
        <button class="cursor-pointer" (click)="toggleSound($event)">
          <img
            alt="notification sound icon"
            src="assets/notificationImages/notification-bell.svg"
          />
        </button>
      } @else {
        <button class="cursor-pointer" (click)="toggleSound($event)">
          <img
            alt="notification sound icon"
            src="assets/notificationImages/notification-bell-no-sound.svg"
          />
        </button>
      }
    </div>
    @if (notifications.length) {
      <button
        class="text-[#2F55F0] text-[10px] font-medium"
        (click)="markAllAsRead()"
      >
        Mark all as read
      </button>
    }
  </header>
  <div class="line w-full h-[1px] bg-[#D9D9D9] mt-3 mb-6"></div>
  <nav class="w-full">
    <app-dashboard-navbar
      [isNotifications]="true"
      [navbarData]="navbarNotificationDropDownData"
    ></app-dashboard-navbar>
  </nav>

  <div class="flex justify-between px-6 mb-4">
    <p class="text-[#474D66] text-[14px] font-medium">{{ DayOrMonth }}</p>
    <button
      class="underline text-[10px] font-medium text-[#0C4767]"
      (click)="routeToNotifications()"
    >
      View all
    </button>
  </div>

  <section class="w-full max-h-[400px] overflow-y-auto bg-white">
    @if (notifications.length === 0) {
      @if (isLoading) {
        <div
          class="flex justify-center items-center h-[250px] text-[#474D66] text-[14px] font-medium"
        >
          <p>Loading...</p>
        </div>
      } @else {
        <div
          class="flex justify-center items-center h-[250px] text-[#474D66] text-[14px] font-medium"
        >
          No new {{ notificationName }} notifications
        </div>
      }
    } @else {
      @for (item of notifications; track item; let index = $index) {
        <div
          class="relative w-full h-fit min-h-[137px] hover:bg-[#DFE8F4] border-t border-[#D9D9D9] pb-3"
          [attr.data-index]="index"
          #notificationElement
        >
          <header class="flex justify-between items-center mt-3 mx-4 sm:mx-6">
            <div class="avatar w-10 h-10 rounded-full bg-[lightgray] mr-4">
              <img src="" alt="" />
            </div>
            <div class="title flex-1 text-[14px] font-semibold text-[#474D66]">
              <p>{{ item.notificationTitle }}</p>
            </div>

            <div class="action-button">
              <app-action-button
                (actionButtonClicked)="actionButtonClicked(item, index)"
              ></app-action-button>
            </div>
          </header>
          <section class="pl-4 sm:pl-20">
            <div class="middle text-[10px] font-normal text-[#474D66] lg:mb-3">
              {{ item.timeLapse }}
            </div>
            <div class="body"></div>
          </section>
          <div
            class="pl-4 pr-5 sm:pl-20 text-[12px] font-normal text-[#474D66]"
          >
            <p [appSafeHtml]="item.notificationContent"></p>
          </div>
          @if (requireAction(item.type)) {
            <div class="w-[20px] ml-20 mt-3">
              <app-custombutton
                class="button"
                variant="tertiary"
                [fontSize]="10"
                (clicked)="viewQuestion(item)"
                >{{ buttonText }}</app-custombutton
              >
            </div>
          }
          @if (selectedIndexValue === index) {
            <div
              class="absolute"
              [ngStyle]="{
                top: index === notifications.length - 1 ? '-2.4rem' : '-1.8rem',
                right: index === notifications.length - 1 ? '0.5rem' : '0',
              }"
            >
              <app-action-items-pop-up
                [actionButtonItems]="originalActionButtonItems"
                [isNotification]="true"
                (closeModal)="closeModal()"
              ></app-action-items-pop-up>
            </div>
          }
        </div>
      }
    }
  </section>
</main>

@if (showPreviewModal && question) {
  <app-custom-modal
    [headerTitle]="question.questionType | truncateLongText"
    (visibleModalChange)="previewQuestion()"
  >
    <app-preview-data [question]="question"></app-preview-data>
  </app-custom-modal>
}
