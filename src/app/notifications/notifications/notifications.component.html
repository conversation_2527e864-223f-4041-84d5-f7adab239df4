<main>
  <app-dashboard-navbar
    [navbarData]="navbarNotificationData"
  ></app-dashboard-navbar>
  <section>
    <span
      class="close text-[1.5em] cursor-pointer text-[#0C4767] absolute top-[100px] right-[90px]"
      aria-hidden="true"
      (click)="onCloseLogs()"
      (keydown)="onCloseLogs()"
      >&times;</span
    >
    <header class="lg:flex lg:justify-between lg:items-center w-[80%] mb-7">
      <div>
        <app-custom-search-input
          [placeholder]="'Search notifications...'"
          (searchTermChange)="onSearch($event)"
        ></app-custom-search-input>
      </div>
      <div class="filter relative">
        <p
          class="lg:absolute lg:top-[-20px] text-[14px] font-normal text-[#474D66]"
        >
          Filter by Timeframe
        </p>
        <div class="lg:flex gap-x-4">
          <div class="flex items-center gap-x-2">
            <p class="text-[14px] font-normal text-[#474D66]">From</p>
            <div
              class="w-[214px] h-[40px] bg-[#fff] rounded-lg overflow-hidden"
            >
              <input
                class="w-full h-full px-4 rounded-lg"
                type="date"
                name="startDate"
                id="startDate"
                [max]="currentDate"
                (input)="onStartDateChange($event)"
              />
            </div>
          </div>

          <div class="flex items-center gap-x-4 mt-2 lg:mt-0">
            <p class="text-[14px] font-normal text-[#474D66]">To</p>
            <div
              [ngClass]="{
                'w-[214px] h-[40px] rounded-lg overflow-hidden': true,
                'bg-[#DEDEDE]': !isEndDateActive,
                'bg-[#fff]': isEndDateActive,
              }"
            >
              <input
                class="w-full h-full px-4 rounded-lg"
                type="date"
                name="endDate"
                id="endDate"
                [min]="startDate"
                [max]="currentDate"
                [value]="startDate"
                [disabled]="!isEndDateActive"
                [ngClass]="{
                  'cursor-not-allowed text-gray-400': !isEndDateActive,
                  'cursor-pointer text-black': isEndDateActive,
                }"
                (input)="onEndDateChange($event)"
              />
            </div>
          </div>
        </div>
      </div>
    </header>
    <div class="lg:flex">
      <div class="flex gap-x-3 lg:mr-20">
        <app-custom-check-box
          [value]="'AllNotificationsSelectedOnPage'"
          [checked]="isAllNotificationsSelectedOnPage()"
          (valueChange)="selectAllNotificationsOnPage()"
        ></app-custom-check-box>

        <p class="text-[14px] font-normal text-[#474D66]">Select All</p>
      </div>
      @if (selectedNotifications.length > 0) {
        <div class="flex gap-x-4">
          <button
            class="text-[14px] font-normal text-[#71717A]"
            (click)="markAllAsRead()"
          >
            {{
              selectedNotifications.length > 1
                ? 'Mark all as read'
                : 'Mark as read'
            }}
          </button>
          <button
            class="text-[14px] font-normal text-[#71717A]"
            (click)="markAllAsUnread()"
          >
            {{
              selectedNotifications.length > 1
                ? 'Mark all as unread'
                : 'Mark as unread'
            }}
          </button>
          <button
            class="text-[14px] font-normal text-[#71717A]"
            (click)="deleteAll()"
          >
            {{ selectedNotifications.length > 1 ? 'Delete all' : 'Delete' }}
          </button>
          <p></p>
        </div>
      }
    </div>
    @if (isLoading) {
      <div class="flex items-center justify-center h-[50vh]">
        <p>loading...</p>
      </div>
    } @else {
      @if (notifications.length === 0) {
        <app-no-result-found
          [search]="searchTerm"
          [message]="'No notifications found'"
        ></app-no-result-found>
      } @else {
        <div class="container max-h-[calc(100vh-375px)] mb-2 overflow-y-auto">
          @for (
            item of notifications
              | paginate
                : {
                    itemsPerPage: pageSize,
                    currentPage: page,
                    totalItems: totalNumberOfNotifications,
                  };
            track item;
            let index = $index
          ) {
            <div class="flex items-center gap-x-4">
              <div>
                <app-custom-check-box
                  [id]="item.id"
                  [value]="item"
                  [checked]="isNotificationSelected(item)"
                  (valueChange)="selectNotification($event)"
                ></app-custom-check-box>
              </div>
              <div
                class="flex items-center w-full gap-x-2 pt-3 pb-6"
                [ngStyle]="{
                  'border-bottom':
                    index === notifications.length - 1
                      ? 'none'
                      : '1px solid lightgray',
                }"
              >
                <div class="flex items-center gap-x-2">
                  @if (!item.read) {
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="5"
                      height="5"
                      viewBox="0 0 5 5"
                      fill="none"
                    >
                      <circle
                        cx="2.5"
                        cy="2.5"
                        r="2.5"
                        transform="matrix(-1 0 0 1 5 0)"
                        fill="#0C4767"
                      />
                    </svg>
                  }
                  <div
                    class="min-w-10 max-h-10 min-h-10 grid place-items-center rounded-full bg-[lightgray]"
                  >
                    {{
                      item.addOns &&
                        initials(item.addOns.first_name, item.addOns.last_name)
                    }}
                  </div>
                </div>
                <div aria-hidden="true" (clicked)="clickToRead(item)">
                  <p
                    [ngClass]="
                      item.read
                        ? 'text-[#999999] text-[16px] font-medium mb-1'
                        : 'text-[#333342] text-[16px] font-bold mb-1 '
                    "
                  >
                    {{ item.notificationTitle }}
                  </p>
                  <p
                    class="text-[#474D66] text-[12px] font-normal mb-1"
                    [appSafeHtml]="item.notificationContent"
                  ></p>
                  <p class="text-[#474D66] text-[12px] font-normal">
                    {{ item.timeLapse }}
                  </p>
                  @if (requireAction(item.type)) {
                    <div class="w-fit mt-3">
                      <app-custombutton
                        class="button"
                        variant="tertiary"
                        [fontSize]="10"
                        (clicked)="viewQuestion(item)"
                        >{{ buttonText }}</app-custombutton
                      >
                    </div>
                  }
                </div>
              </div>
            </div>
          }
        </div>
      }
    }
  </section>
</main>

@if (!isLoading && notifications.length > 0) {
  <div class="w-full flex flex-col lg:flex-row lg:justify-between">
    <div class="text-[#474D66] text-[16px] font-medium">
      {{ range }}
    </div>
    <app-custom-pagination
      [total]="this.totalNumberOfNotifications"
      [size]="pageSize"
      (pagechange)="onPageChange($event)"
      (sizeSelect)="onSizeChange($event)"
    ></app-custom-pagination>
  </div>
}

@if (showPreviewModal && question) {
  <app-custom-modal
    [headerTitle]="question.questionType | truncateLongText"
    (visibleModalChange)="previewQuestion()"
  >
    <app-preview-data [question]="question"></app-preview-data>
  </app-custom-modal>
}
