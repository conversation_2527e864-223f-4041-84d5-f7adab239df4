import { ComponentFixture, TestBed } from '@angular/core/testing';
import { OrganisationsComponent } from './organisations.component';
import { of, Subject } from 'rxjs';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { HttpClientTestingModule } from '@angular/common/http/testing';

// Mock form-factory-service
import { OrganizationService } from '../../services/usermanagement-service/organization.service';
import { ToastService } from '../../services/toast-service/toast.service';
import { AuthService } from '../../services/auth.service';
import { AnalyticsService } from '../../services/analytics.service';

const mockOrganizationService = {
  OrganisationData$: new Subject(),
  isOrganisationLoaded$: of(true),
  getAllOrganizations: jest.fn(),
  CreateOrganization: jest.fn(() => of({})),
};

const mockToastService = {
  onShow: jest.fn(),
};

const mockAuthService = {
  getLoginUser: jest.fn(() => ({
    permissions: [],
  })),
};

const mockAnalyticsService = {
  track: jest.fn(() => Promise.resolve()),
};

describe('OrganisationsComponent (Expanded Jest Test)', () => {
  let component: OrganisationsComponent;
  let fixture: ComponentFixture<OrganisationsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        OrganisationsComponent,
        NoopAnimationsModule,
        HttpClientTestingModule,
      ],
      providers: [
        { provide: OrganizationService, useValue: mockOrganizationService },
        { provide: ToastService, useValue: mockToastService },
        { provide: AuthService, useValue: mockAuthService },
        { provide: AnalyticsService, useValue: mockAnalyticsService },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(OrganisationsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create component', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with empty values', () => {
    const form = component.addOrganisationForm;
    expect(form.value).toEqual({
      organizationName: '',
      address: '',
      email: '',
    });
  });

  it('should open and reset add organisation modal', () => {
    component.openAddOrganisationModal();
    expect(component.addOrganisationModal).toBe(true);
    expect(component.addOrganisationForm.value).toEqual({
      organizationName: null,
      address: null,
      email: null,
    });
  });

  it('should add organisation and call form-factory-service on success', () => {
    const form = component.addOrganisationForm;
    form.setValue({
      organizationName: 'Org 1',
      address: '123 Test St',
      email: '<EMAIL>',
    });

    const spy = jest.spyOn(component, 'getAllOrganizations');
    component.addOrganisation();

    expect(mockOrganizationService.CreateOrganization).toHaveBeenCalled();
    expect(spy).toHaveBeenCalled();
    expect(mockToastService.onShow).toHaveBeenCalledWith(
      'success',
      'Organisation created successfully',
      true,
      'success'
    );
    expect(mockAnalyticsService.track).toHaveBeenCalledWith(
      'Organisation Created',
      {
        organizationName: 'Org 1',
        address: '123 Test St',
        email: '<EMAIL>',
      }
    );
    expect(component.addOrganisationModal).toBe(false);
  });

  it('should sort by organization name', () => {
    component.onSortColumn('organizationName');
    expect(component.sortName).toBe('asc');
    expect(component.sortEmail).toBe('');
    expect(component.sortAddress).toBe('');

    expect(mockOrganizationService.getAllOrganizations).toHaveBeenCalled();
  });

  it('should sort by email', () => {
    component.onSortColumn('email');
    expect(component.sortEmail).toBe('asc');
  });

  it('should sort by address', () => {
    component.onSortColumn('address');
    expect(component.sortAddress).toBe('asc');
    expect(mockOrganizationService.getAllOrganizations).toHaveBeenCalled();
  });

  it('should reset all sorts when clicking on a column twice', () => {
    component.onSortColumn('organizationName');
    component.onSortColumn('organizationName');
    expect(component.sortName).toBe('desc');
    expect(mockOrganizationService.getAllOrganizations).toHaveBeenCalled();
  });

  it('should change page number on pagination', () => {
    const spy = jest.spyOn(component, 'getAllOrganizations');
    component.onPageChange(2);
    expect(component.page).toBe(2);
    expect(spy).toHaveBeenCalled();
  });

  it('should change page size on pagination', () => {
    const spy = jest.spyOn(component, 'getAllOrganizations');
    component.onSizeChange(50);
    expect(component.size).toBe(50);
    expect(component.page).toBe(1);
    expect(spy).toHaveBeenCalled();
  });

  it('should handle search input and call searchSubject.next', () => {
    const searchSpy = jest.spyOn(component.searchSubject, 'next');
    component.onSearch('Test Org');
    expect(searchSpy).toHaveBeenCalledWith('Test Org');
  });

  it('should debounce search term', () => {
    jest.useFakeTimers(); // Mock timers to control debounce
    const searchSpy = jest.spyOn(component.searchSubject, 'next');

    component.onSearch('Test');
    jest.advanceTimersByTime(1200); // Fast-forward to trigger the debounce
    expect(searchSpy).toHaveBeenCalledWith('Test');

    jest.useRealTimers(); // Clean up fake timers
  });

  it('should show NoResultFoundComponent if no organizations are found', () => {
    component.OrganizationData = [];
    fixture.detectChanges();
    const noResults = fixture.nativeElement.querySelector(
      'app-no-result-found'
    );
    expect(noResults).toBeTruthy();
  });

  it('should close modal on form submission', () => {
    component.addOrganisationForm.setValue({
      organizationName: 'Test Org',
      address: '123 Test St',
      email: '<EMAIL>',
    });
    component.addOrganisation();
    expect(component.addOrganisationModal).toBe(false);
  });
});
