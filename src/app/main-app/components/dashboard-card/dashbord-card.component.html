<div
  class="card-container min-w-[265px] lg:min-w-[274px] max-w-[20vw] md:max-w-[19vw]"
>
  <div
    class="hover-card rounded-xl bg-[#fff] pl-[1.5vw] pr-[1vw] pt-[2.5vw] pb-[1.5vw] md:pl-[1vw] md:pr-[0.75vw] md:pt-[2vw] md:pb-[1vw]"
  >
    <div class="card-header flex gap-x-[1.5vw] h-10 items-center">
      <div
        class="image min-w-[2.5rem] min-h-[2.5rem] lg:min-w-[4rem] lg:min-h-[4rem] max-w-[4vw] max-h-[4vw] rounded-lg grid place-items-center"
        style.background-color="{{ iconBackgroundColor }}"
      >
        <img src="{{ image }}" alt="{{ title }}" />
      </div>
      <h1
        class="font-medium leading-none lg:leading-normal text-[2vw] md:text-[1.4rem] text-[#0C4767] capitalize"
      >
        {{ title }}
      </h1>
    </div>
    <p
      class="text-[#0C4767] text-[1.5vw] font-normal mt-[1vw] md:text-[1rem] h-16"
    >
      {{ description }}
    </p>
    <div class="w-fit mt-[1.5vw] md:mt-[1vw]">
      <app-custombutton
        [variant]="'secondary'"
        (keydown.enter)="navigateToLink(buttonLink)"
        (click)="navigateToLink(buttonLink)"
        >{{ buttonText }}</app-custombutton
      >
    </div>
  </div>
</div>
