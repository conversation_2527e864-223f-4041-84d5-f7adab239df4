<nav
  class="z-50"
  [style.backgroundColor]="navBgColor"
  [ngClass]="{
    'tabs-container': isTestMode,
    'flex flex-col gap-y-4 md:flex-row md:gap-x-6 md:items-center': !isTestMode,
    'bg-white': isNotifications && !isTestMode,
    'px-6 py-4': isNotifications && !isTestMode,
  }"
>
  @if (isTestMode) {
    <div class="tabs-header" [class.coding-mode-header]="isCodingMode">
      @for (item of navbarData; track item) {
        @if (item.link) {
          <button
            routerLink="{{ item.link }}"
            routerLinkActive="active"
            class="tab-button"
            [disabled]="item.disabled"
            [ngClass]="{
              active: isActive(item),
              'coding-mode': isCodingMode,
              disabled: item.disabled,
            }"
          >
            {{ item.title }}
          </button>
        } @else {
          <button
            class="tab-button"
            [disabled]="item.disabled"
            [ngClass]="{
              active: isActive(item),
              'coding-mode': isCodingMode,
              disabled: item.disabled,
            }"
            (click)="setActive(item)"
          >
            <span class="flex items-center gap-2">
              <ng-container *ngIf="iconTemplate">
                <ng-container
                  *ngTemplateOutlet="iconTemplate; context: { $implicit: item }"
                ></ng-container>
              </ng-container>
              {{ item.title }}
            </span>
          </button>
        }
      }
    </div>
  }

  @if (!isTestMode) {
    <section class="relative">
      <button
        class="menu-icon md:hidden"
        aria-label="Toggle Menu"
        (click)="toggleMenu(navId)"
      >
        <span></span>
        <span></span>
        <span></span>
      </button>
      <ul
        #navbarRef
        [id]="navId"
        class="links md:flex md:gap-x-[58px]"
        [ngClass]="{ 'menu-open': isMenuOpen }"
      >
        @for (item of navbarData; track item) {
          @if (item.link) {
            <li
              routerLink="{{ item.link }}"
              routerLinkActive="active font-medium"
              class="nav-item font-semibold cursor-pointer text-[#B5B5C3] text-[16px] md:text-base mb-3"
            >
              {{ item.title }}
            </li>
          } @else {
            <li
              class="nav-item font-medium cursor-pointer text-[#B5B5C3] text-[16px] md:text-base mb-3"
            >
              <button
                class="w-full"
                (click)="setActive(item)"
                [ngClass]="{ notificationActive: isActive(item) }"
                [ngStyle]="{
                  marginLeft: isNotifications ? '14px' : '0px',
                  textAlign: isNotifications ? 'center' : 'left',
                }"
              >
                {{ item.title }}
              </button>
            </li>
          }
        }
      </ul>
    </section>
  }
</nav>

@if (showLine && !isTestMode) {
  <div
    class="line bg-[#D9D9D9] rounded-sm h-[2px] mb-[32px]"
    [ngStyle]="{ marginRight: isNotifications ? '0px' : '8px' }"
  ></div>
}
