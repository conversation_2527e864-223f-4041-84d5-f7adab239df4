@defer {
  <main
    class="bg-[#EFEFEF] h-screen max-h-[screen] overflow-y-hidden flex items-start justify-start"
  >
    <app-side-bar></app-side-bar>
    <section class="w-full grid grid-cols-1 relative">
      <div class="pl-[10px] md:pl-[32px] md:pr-[40px]">
        <app-navbar></app-navbar>
      </div>
      <div
        class="w-full max-h-[calc(100vh-110px)] pb-[20px] md:pb-[44px] overflow-y-auto"
      >
        <div class="ml-[10px] md:mr-[40px] md:ml-[32px]">
          <router-outlet></router-outlet>
        </div>
      </div>
    </section>
  </main>

  <app-custom-mini-modal
    [headerTitle]="'Extend Session'"
    [textAlign]="'center'"
    [closeButton]="false"
    [left]="'No, log me out'"
    [right]="'Yes, extend session'"
    [visibleModal]="showContinueSessionModal"
    [bodyText]="
      'Your session is about to expire. To stay logged in, please extend your session. Would you like to extend it now?'
    "
    (rightClickEvent)="confirmSessionContinue()"
    (leftClickEvent)="closeCancelModal()"
    (visibleModalChange)="closeCancelModal()"
    [width]="'30rem'"
    [height]="'17rem'"
    [caution]="true"
  />
} @loading {
  <div class="flex justify-center items-center h-screen max-h-[screen]">
    <app-refresh-loader></app-refresh-loader>
  </div>
} @error {
  <p>please refresh the page</p>
}
