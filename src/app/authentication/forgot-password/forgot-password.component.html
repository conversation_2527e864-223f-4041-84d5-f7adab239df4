<app-auth-layout>
  <app-auth-glass-wrapper>
    <div class="max-w-[450px] grid place-items-center">
      <div class="max-w-[333px]">
        <app-form-title
          title="Password Reset"
          description="
    Kindly enter your signup email, and we'll send reset instructions."
        ></app-form-title>
      </div>
      <div class="w-full">
        <form [formGroup]="passwordForm" (ngSubmit)="onSubmit($event)">
          <div class="mt-[24px] relative">
            <app-authenticationcustominput
              [icon]="'../../../assets/authImages/sms.svg'"
              type="email"
              id="forgot-password-email"
              placeholder="Enter email"
              type="email"
              label="Email"
              [control]="passwordForm.controls.email"
              [required]="true"
            ></app-authenticationcustominput>
          </div>
          <div *ngIf="showErrorHeader" class="text-red-R400 error-ref">
            <p class="text-[11.25px]">
              No user with email
              <i>{{ passwordForm.value.email?.trim() }}</i>
              found
            </p>
          </div>
          <div
            class="surface:flex-col surface:gap-y-2 flex gap-x-4 items-center mt-5"
          >
            <div class="w-[217px]">
              <app-custombutton routerLink="/login" [variant]="'secondary'"
                >Cancel</app-custombutton
              >
            </div>
            <div class="w-[217px]">
              @if (isLoading) {
                <app-custombutton
                  [type]="'submit'"
                  [spinner]="isLoading"
                  [disabled]="passwordForm.invalid"
                ></app-custombutton>
              } @else {
                <app-custombutton
                  [type]="'submit'"
                  [disabled]="passwordForm.invalid"
                  [spinner]="isLoading"
                  >Continue</app-custombutton
                >
              }
            </div>
            <div></div>
          </div>
        </form>
      </div>
    </div>
  </app-auth-glass-wrapper>
</app-auth-layout>
