<div class="custom-input-container">
  <div class="custom-label-container">
    <label
      [for]="id"
      class="custom-label text-[12px] font-normal text-[#474D66] mt-2"
      >{{ label }}</label
    >
  </div>

  <div
    class="custom-input-wrapper lg:w-[450px]"
    [ngClass]="{ 'with-icon': icon }"
  >
    <app-custom-input
      [placeholder]="placeholder"
      [backgroundColor]="'transparent'"
      [borderColor]="'0.3px solid #0C4767'"
      [formControl]="control"
      [autocomplete]="autocomplete"
      [type]="type"
      [id]="id"
    ></app-custom-input>
  </div>

  <ng-container *ngIf="!isLoginUrl && control.invalid && control.touched">
    <div
      *ngFor="let error of control.errors! | keyvalue"
      class="custom-input-error error-ref text-[0.7rem] max-w-[26rem]"
    >
      <span class="text-red-R400"> {{ errorMessage[error.key] }}</span>
    </div>
  </ng-container>
</div>
