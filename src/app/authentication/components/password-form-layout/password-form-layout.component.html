<div>
  <app-form-title
    [description]="formDescription"
    [title]="formTitle"
  ></app-form-title>

  <div>
    <form [formGroup]="passwordForm" (ngSubmit)="Submit(passwordForm.value)">
      <div class="relative mb-4 mt-3">
        <app-authenticationcustominput
          [icon]="'../../../../assets/authImages/lock copy.svg'"
          [control]="$any(passwordForm.get('password'))"
          type="{{ showPassword ? 'text' : 'password' }}"
          [id]="'password'"
          placeholder="New Password"
          [required]="true"
        ></app-authenticationcustominput>
        <i
          class="pi show-passwordicon absolute right-0 top-5 translate-y-[-50%] mr-3"
          [ngClass]="showPassword ? ' pi-eye-slash ' : 'pi-eye'"
          (click)="toggleShowPassword()"
          (keyup)="toggleShowPasswordKeyboard($event)"
          aria-hidden="true"
        ></i>
      </div>

      <div class="relative mb-4">
        <app-authenticationcustominput
          [icon]="'../../../../assets/authImages/lock copy.svg'"
          [control]="$any(passwordForm.get('confirmPassword'))"
          type="{{ showConfirmPassword ? 'text' : 'password' }}"
          [id]="'confirm-password'"
          placeholder="Confirm Password"
          [required]="true"
        ></app-authenticationcustominput>
        <i
          class="pi show-passwordicon absolute right-0 top-5 translate-y-[-50%] mr-3"
          [ngClass]="showConfirmPassword ? ' pi-eye-slash ' : 'pi-eye'"
          (click)="toggleShowConfirmPassword()"
          (keyup)="toggleShowConfirmPassword()"
          aria-hidden="true"
        ></i>
      </div>
      <app-password-strength-meter
        [password]="passwordForm.get('password')?.value"
      >
        <div
          *ngIf="
            passwordForm?.errors?.['passwordMismatchError'] &&
            passwordForm.get('confirmPassword')?.touched
          "
          class="text-red-600"
        >
          Password do not match
        </div>
      </app-password-strength-meter>

      <div class="flex items-start h-12 my-4">
        <div class="mr-4 w-[2px] h-full bg-orange-O400"></div>
        <div class="font-[400] text-neutral-N700 text-[13px] h-full">
          You will be using this email address to login:
          <p class="font-semibold text-base text-neutral-N900">
            {{ email ? email : '' }}
          </p>
        </div>
      </div>
      <app-custombutton
        [type]="'submit'"
        variant="primary"
        [disabled]="passwordForm.invalid"
        [spinner]="isLoading"
      >
        <p *ngIf="!isLoading">{{ buttonLabel }}</p>
      </app-custombutton>
    </form>
  </div>
</div>
