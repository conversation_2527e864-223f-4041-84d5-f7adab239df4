<main class="glass-wrapper">
  @if (showLogo) {
    <div class="w-full flex justify-end">
      <img
        class="mr-28 surface:mr-10"
        src="../../../../assets/icons/dodopkoIcon.svg"
        alt=""
      />
    </div>
  }
  <div class="bottom-corner"></div>
  <div class="absolute top-0 left-0 surface: top-corner">
    <svg
      width="809"
      height="739"
      viewBox="0 0 809 739"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g filter="url(#filter0_f_2166_19116)">
        <path
          d="M80.8293 -226.413C-62.582 -273.506 -231.765 -121.31 -165.511 9.60403C-99.2575 140.519 -20.7482 61.193 70.0357 130.264C157.157 196.548 198.794 296.598 286.736 333.502C339.051 355.457 394.45 301.482 407.075 205.898C431.089 24.0754 226.092 -178.713 80.8293 -226.413Z"
          fill="url(#paint0_linear_2166_19116)"
          fill-opacity="0.9"
        />
      </g>
      <defs>
        <filter
          id="filter0_f_2166_19116"
          x="-580.323"
          y="-635.164"
          width="1389.33"
          height="1373.54"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feGaussianBlur
            stdDeviation="200"
            result="effect1_foregroundBlur_2166_19116"
          />
        </filter>
        <linearGradient
          id="paint0_linear_2166_19116"
          x1="-180.939"
          y1="-55.0418"
          x2="388.408"
          y2="-103.298"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#6B9EFF" />
          <stop offset="0.521993" stop-color="#0C4767" />
          <stop offset="1" stop-color="#67E8F9" />
        </linearGradient>
      </defs>
    </svg>
  </div>
  <div class="left-side"></div>
  <div class="bottom-border"></div>
  <div class="relative w-full">
    <ng-content></ng-content>
  </div>
</main>
