@defer {
  <app-auth-layout>
    <app-auth-glass-wrapper>
      <div class="grid items-center">
        <app-form-title
          class="mb-4"
          title="Sign in"
          description="Enter your credentials to access your account"
        ></app-form-title>

        <form
          [formGroup]="loginForm"
          (ngSubmit)="onLogin($event)"
          class="flex flex-col place-items-center"
          aria-labelledby="form-title"
        >
          <div class="min-w-full max-w-[450px]">
            <app-authenticationcustominput
              [label]="'Email'"
              [icon]="'../../../assets/authImages/sms.svg'"
              [control]="loginForm.controls.email"
              type="email"
              id="email"
              placeholder="Enter email"
              [required]="true"
            ></app-authenticationcustominput>
          </div>

          <div class="relative min-w-full max-w-[450px]">
            <app-authenticationcustominput
              [label]="'Password'"
              [icon]="'../../../assets/authImages/lock.svg'"
              [control]="loginForm.controls.password"
              [type]="this.showPassword ? 'text' : 'password'"
              id="password"
              placeholder="Enter password"
              [required]="true"
            ></app-authenticationcustominput>
            <i
              class="pi show-passwordicon absolute"
              [ngClass]="showPassword ? ' pi-eye-slash ' : 'pi-eye'"
              (click)="toggleShowPassword()"
              (keydown)="toggleShowPasswordKeyboard($event)"
              aria-hidden="true"
            ></i>
          </div>

          <div class="w-full max-w-[450px]">
            <div>
              <a
                (click)="forgotPassword()"
                href="javascript:void(0)"
                class="forgot-password text-[12px] font-medium text-[#2F55F0] capitalize"
              >
                Forgot password?
              </a>
            </div>
            <div class="mt-6">
              <app-custombutton
                [disabled]="!loginForm.valid || isLoading"
                [type]="'submit'"
                [spinner]="isLoading"
              >
                {{ isLoading ? '' : 'Sign in' }}
              </app-custombutton>
            </div>
          </div>
        </form>
      </div>
    </app-auth-glass-wrapper>
  </app-auth-layout>
} @loading {
  <div class="flex justify-center items-center h-[80vh]">
    <app-refresh-loader></app-refresh-loader>
  </div>
} @error {
  <p>please refresh the page</p>
}
