import { is<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, APP_INITIALIZER } from '@angular/core';
import {
  provideHttpClient,
  withFetch,
  withInterceptors,
} from '@angular/common/http';
import {
  PreloadAllModules,
  provideRouter,
  withPreloading,
  Router,
} from '@angular/router';
import { loggerInterceptor } from './logger.interceptor';
import { errorInterceptor } from './error.interceptor';
import {
  provideCacheableAnimationLoader,
  provideLottieOptions,
} from 'ngx-lottie';
import player from 'lottie-web/build/player/lottie_svg';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { provideStoreDevtools } from '@ngrx/store-devtools';
import * as Sentry from '@sentry/angular';
import { provideHighlightOptions } from 'ngx-highlightjs';
import { routes } from './app.routes';

import { MonacoLoaderService } from './services/monaco-loader.service'; // adjust path as needed

// ✅ Preload Monaco on app startup
export function preloadMonaco(
  monacoLoader: MonacoLoaderService
): () => Promise<void> {
  return () =>
    monacoLoader
      .loadMonaco()
      .then(() => void 0)
      .catch((err) => {
        console.warn('Monaco preload failed:', err);
        return void 0;
      });
}

export const appProviders = [
  provideHttpClient(
    withInterceptors([loggerInterceptor, errorInterceptor]),
    withFetch()
  ),

  provideLottieOptions({
    player: () => player,
  }),
  provideCacheableAnimationLoader(),

  provideRouter(routes, withPreloading(PreloadAllModules)),

  provideAnimationsAsync(),

  provideStoreDevtools({
    autoPause: true,
    trace: false,
    traceLimit: 75,
    connectInZone: true,
    maxAge: 25,
    logOnly: !isDevMode(),
  }),

  provideHighlightOptions({
    fullLibraryLoader: () => import('highlight.js'),
  }),

  {
    provide: ErrorHandler,
    useValue: Sentry.createErrorHandler(),
  },
  {
    provide: Sentry.TraceService,
    deps: [Router],
  },
  {
    provide: APP_INITIALIZER,
    useFactory: () => () => {},
    deps: [Sentry.TraceService],
    multi: true,
  },

  // ✅ Monaco service + eager preload
  {
    provide: MonacoLoaderService,
    useClass: MonacoLoaderService,
  },
  {
    provide: APP_INITIALIZER,
    useFactory: preloadMonaco,
    deps: [MonacoLoaderService],
    multi: true,
  },
];
