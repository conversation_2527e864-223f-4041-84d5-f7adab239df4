import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router, NavigationEnd } from '@angular/router';
import { AppComponent } from './app.component';
import { AnalyticsService } from './services/analytics.service';
import { ToastService } from './services/toast-service/toast.service';
import { Subject } from 'rxjs';

describe('AppComponent', () => {
  let component: AppComponent;
  let fixture: ComponentFixture<AppComponent>;
  let mockAnalyticsService: jest.Mocked<AnalyticsService>;
  let mockToastService: jest.Mocked<ToastService>;
  let mockRouter: jest.Mocked<Router>;
  let routerEventsSubject: Subject<unknown>;

  beforeEach(async () => {
    // Create mock form-factory-service
    mockAnalyticsService = {
      page: jest.fn(),
    } as unknown as jest.Mocked<AnalyticsService>;

    mockToastService = {
      onShow: jest.fn(),
    } as unknown as jest.Mocked<ToastService>;

    routerEventsSubject = new Subject();
    mockRouter = {
      events: routerEventsSubject.asObservable(),
    } as unknown as jest.Mocked<Router>;

    await TestBed.configureTestingModule({
      imports: [AppComponent],
      providers: [
        { provide: AnalyticsService, useValue: mockAnalyticsService },
        { provide: ToastService, useValue: mockToastService },
        { provide: Router, useValue: mockRouter },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(AppComponent);
    component = fixture.componentInstance;
  });

  it('should create the app', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize component properties', () => {
    expect(component.subscriptions).toEqual([]);
    expect(component.connectionStatusMessage).toBeUndefined();
    expect(component.connectionStatus).toBeUndefined();
  });

  describe('ngOnInit', () => {
    it('should set up online and offline event observables', () => {
      component.ngOnInit();

      expect(component.onlineEvent).toBeDefined();
      expect(component.offlineEvent).toBeDefined();
    });

    it('should handle online event correctly', () => {
      component.ngOnInit();

      // Simulate online event
      const onlineEvent = new Event('online');
      window.dispatchEvent(onlineEvent);

      expect(component.connectionStatusMessage).toBe('Back to online');
      expect(component.connectionStatus).toBe('online');
      expect(mockToastService.onShow).toHaveBeenCalledWith(
        'Network Status',
        'Back to online',
        true,
        'information',
        'bottom-left'
      );
    });

    it('should handle offline event correctly', () => {
      component.ngOnInit();

      // Simulate offline event
      const offlineEvent = new Event('offline');
      window.dispatchEvent(offlineEvent);

      expect(component.connectionStatusMessage).toBe(
        'Connection lost! You are not connected to internet'
      );
      expect(component.connectionStatus).toBe('offline');
      expect(mockToastService.onShow).toHaveBeenCalledWith(
        'Network Status',
        'Connection lost! You are not connected to internet',
        true,
        'warning',
        'bottom-left'
      );
    });

    it('should subscribe to router navigation events', () => {
      component.ngOnInit();

      const navigationEndEvent = new NavigationEnd(
        1,
        '/test-url',
        '/test-url-after-redirects'
      );

      routerEventsSubject.next(navigationEndEvent);

      expect(mockAnalyticsService.page).toHaveBeenCalledWith(
        undefined,
        '/test-url-after-redirects'
      );
    });

    it('should filter non-NavigationEnd events', () => {
      component.ngOnInit();

      // Send a non-NavigationEnd event
      routerEventsSubject.next({ type: 'SomeOtherEvent' });

      expect(mockAnalyticsService.page).not.toHaveBeenCalled();
    });

    it('should create subscriptions for all events', () => {
      component.ngOnInit();

      // Trigger events to ensure subscriptions are active
      window.dispatchEvent(new Event('online'));
      routerEventsSubject.next(new NavigationEnd(1, '/test', '/test'));

      expect(component.subscriptions.length).toBe(3);
    });
  });

  describe('ngOnDestroy', () => {
    it('should unsubscribe from all subscriptions', () => {
      component.ngOnInit();

      // Create spy on each subscription's unsubscribe method
      const unsubscribeSpies = component.subscriptions.map((subscription) =>
        jest.spyOn(subscription, 'unsubscribe')
      );

      component.ngOnDestroy();

      unsubscribeSpies.forEach((spy) => {
        expect(spy).toHaveBeenCalled();
      });
    });

    it('should handle empty subscriptions array', () => {
      component.subscriptions = [];

      expect(() => component.ngOnDestroy()).not.toThrow();
    });
  });

  describe('Event handling integration', () => {
    it('should properly handle multiple online/offline events', () => {
      component.ngOnInit();

      // Test multiple online events
      window.dispatchEvent(new Event('online'));
      window.dispatchEvent(new Event('offline'));
      window.dispatchEvent(new Event('online'));

      expect(mockToastService.onShow).toHaveBeenCalledTimes(3);
      expect(component.connectionStatus).toBe('online');
      expect(component.connectionStatusMessage).toBe('Back to online');
    });

    it('should handle router events with different URLs', () => {
      component.ngOnInit();

      const urls = ['/dashboard', '/profile', '/settings'];
      urls.forEach((url, index) => {
        routerEventsSubject.next(new NavigationEnd(index + 1, url, url));
      });

      expect(mockAnalyticsService.page).toHaveBeenCalledTimes(3);
      urls.forEach((url) => {
        expect(mockAnalyticsService.page).toHaveBeenCalledWith(undefined, url);
      });
    });
  });
});
