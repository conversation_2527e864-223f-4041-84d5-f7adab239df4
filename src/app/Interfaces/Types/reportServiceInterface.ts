export interface OrganizationAssessmentObject {
  data: OrganizationAssessment[];
  success: boolean;
}

export interface StackedChartData {
  totalCandidates: number[];
  completedCandidates: number[];
}
export interface OrganizationAssessment {
  attempted: boolean;
  assessmentId: string;
  title: string;
  instructions: string;
  averageScore: number;
  numberOfCandidates: number;
  numberOfSections: number;
  createdAt: string;
  updatedAt: string;
  numberOfFeedbacks: number;
  numberOfCompleted: number;
  numberOfIncomplete: number;
}

export interface Assessment extends BaseAssessment {
  submissionType?: string;
  proctorFeatures?: ProctorFeatures[];
}

export interface BaseAssessment {
  id: string;
  email: string;
  startTime: string;
  endTime: string;
  commenceDate: string;
  status: string;
  invalid: boolean;
  proctor: string;
  expireDate: string;
  createdAt: string;
}
export interface ProctorFeatures {
  id: string;
  name: string;
  description: string;
}
export interface OneAssessment {
  success: boolean;
  data: Data;
}
export interface Data {
  id: string;
  title: string;
}
export interface AssessmentResultInterface {
  success: boolean;
  data: Data;
}

interface ReportStats {
  totalCandidatesInvited: number;
  averageScore: number;
  passRate: number;
  passMark: number;
  completionRate: number;
  totalCandidatesCompleted: number;
  highestScore: string;
  lowestScore: string;
}

export interface ReportAssessmentData {
  stats?: ReportStats;
  candidates: Assessment[];
  totalItems: number;
  id: string;
  title: string;
}

export interface Data {
  candidates: Assessment[];
  totalItems: number;
  stats?: ReportStats;
}

export interface CandidateMetrics {
  candidateId: string;
  candidateEmail: string;
  candidateMarks: string;
  integrityScore: number;
  scorePercentage: string;
  proctoringLevel: string;
  riskLevel: string;
}

export interface CandidateSectionDataApi {
  data: CandidateSectionData;
  success: boolean;
}

export interface CandidateSectionData {
  assessmentTitle: string;
  assessmentTime: number;
  assessmentTimeTaken: number;
  assessmentCandidateScore: number;
  assessmentOverallScore: number;
  assessmentCandidatePercentage: number;
  assessmentWindowViolationCount: number;
  assessmentWindowViolationDuration: number;
  assessmentTakerShotCount: number;
  assessmentTakerViolationShotCount: number;
  windowShotCount: number;
  windowViolationShotCount: number;
  screenshotsInterval: number;
  camerashotsInterval: number;
  integrityScore: number;
  status: string;
  proctor: string;
  proctorLevel: string;
  commenceDate: string;
  expireDate: string;
  assessmentEndTime: string;
  assessmentStartTime: string;
  testResults: CandidateTestResult[];
}

export interface CandidateTestResult {
  name: string;
  questionsAnswered: number;
  totalNumQuestions: number;
  testTime: number;
  candidateScore: number;
  overallScore: number;
  percentage: number;
  numberOfQuestionsFailed: number;
  numberOfQuestionsPassed: number;
  numberOfQuestionsAnswered: number;
  numberOfQuestionsUnanswered: number;
  numberOfQuestions: number;
  testWindowViolationDuration: number;
  testWindowViolationCount: number;
  testTakerShotCount: number;
  testTakerViolationShotCount: number;
  testWindowShotCount: number;
  testWindowViolationShotCount: number;
  status: string;
  passage: string | null;
  questionResults: QuestionResult[];
}

export interface QuestionResult {
  pk: string;
  sk: string;
  organizationId: string;
  createdAt: string;
  updatedAt: string;
  candidateId: string;
  questionId: string;
  candidateEmail: string;
  questionText: string;
  questionType: string;
  totalScore: number;
  candidateMarks: number;
  difficultyLevel: string;
  timeLimit: number;
  isFlagged: boolean;
  testTakerAnswers: string[];
  optionAnswers: string[];
  correctAnswers: string[];
  answerCorrect: boolean;
  comprehension: boolean;
  passage: string;
  domain: Domain;
  codeTemplates: CodeTemplate[];
  CodeConstraint: CodeConstraint;
  scored: number;
  idleTime: number;
  isAnswered: boolean;
  codeResults: CodeResult[] | null;
  codeReview: string | null;
  isAnswerCorrect: string | null;
  codeExecutionSummary: CodeExecutionSummary | null;
  strictMark: boolean | null;
  referenceSolution: CodeTemplate[];
  multipleChoiceAnswer: BaseQuestionAnswer | null;
  multipleSelectAnswer: BaseQuestionAnswer | null;
  trueOrFalseAnswer: BaseQuestionAnswer | null;
  essayAnswer: EssayAnswer | null;
  fillInAnswer: FillInQuestionAnswer | null;
  matchMatrixAnswer: MatrixQuestionResult | null;
}

export interface EssayAnswer {
  rubrics: string;
}
export interface BaseQuestionAnswer {
  options: string[];
  answer: string[];
}
export interface MatrixQuestionResult {
  questions: MatrixQuestion[];
  options: string[];
}

export interface MatrixQuestion {
  id: string;
  subquestion: string;
  answer: string[];
  matchMatrixAnswerId: string;
}

export interface FillInQuestionAnswer {
  options: FillInResultOption[];
  answer: string[];
}

export interface FillInResultOption {
  id: string;
  fillInAnswerId: string;
  blank: number;
  value: string;
}
export interface Domain {
  id: string;
  name: string;
}
export interface CodeConstraint {
  id: string;
  timeLimit: number;
  memoryLimit: number;
  timeComplexity: string;
  spaceComplexity: string;
  questionId: string;
}

export interface CodeExecutionSummary {
  allPassed: boolean;
  totalTests: number;
  failedTests: number;
  passedTests: number;
  inQueueTests: number;
  stillProcessing: boolean;
  performance: Performance | null;
}
export interface Performance {
  maxMemory: number;
  totalMemory: number;
  averageMemory: number;
  maxExecutionTime: number;
  totalExecutionTime: number;
  averageExecutionTime: number;
}
export interface CodeResult {
  error: null;
  memory: number;
  inQueue: boolean;
  statusId: number;
  isCorrect: boolean;
  actualOutput: string;
  test_case_id: string;
  executionTime: string;
  statusDescription: string;
}

export interface CodeTemplate {
  id: string;
  codeType: string;
  body: string;
  questionId: string;
  languageId: string;
  Language: Language | null;
}

export interface Language {
  id: string;
  name: string;
  judgeLanguageId: number;
}

export interface CodeTestTakerAnswer {
  code: string;
  languageId: number;
}

export interface ProctoringProtocolsResponse {
  success: boolean;
  data: ProctoringProtocolsData;
}

export interface ProctoringProtocolsData {
  configuration: Configuration;
}

export interface Configuration {
  conductSurvey: boolean;
  showClock: boolean;
  showResults: boolean;
  screenshotsInterval: string;
  camerashotsInterval: string;
  proctorFeatures: ProctorFeature[];
}

export interface ProctorFeature {
  name: string;
}

export interface AssessmentScreenShot {
  id: number;
  imageURL: string;
  isViolationShot: boolean;
  isIntegrityShot?: boolean;
  createdAt: string;
  imageTitle?: string;
}

export interface ViewIndices {
  initialIndex: number;
  finalIndex: number;
}

export interface ColorMappings {
  condition: string;
  backgroundColor: string;
  textColor: string;
}

export interface FeedbackQuestions {
  id: string;
  statement: string;
  type: string;
}

export interface SurveyResponses {
  testTakerId: string;
  surveyQuestions: SurveyQuestions[];
}
export interface SurveyQuestions {
  id: string;
  questionAnswer?: number | string;
  questionText: string;
  comment?: string;
  type: string;
  rating?: number;
}

export interface SurveyAssessmentTakerDetails {
  id: string;
  assessmentId: string;
  organizationId: string;
  email: string;
}

export interface CandidateReportResponse {
  success: boolean;
  data: {
    candidates: Array<{
      id: string;
      email: string;
      startTime: string;
      endTime: string;
      commenceDate: string;
      status: string;
      invalid: boolean;
      duration?: number;
      assessmentTakerScore: string;
      assessmentTakerScorePercentage: string;
      assessmentScore: number;
      submissionType: string;
      createdAt: string;
      expireDate: string;
      proctorFeatures: Array<{
        name: string;
      }>;
    }>;
    totalItems: number;
  };
}

export enum AssessmentStatus {
  Completed = 'Completed',
  InProgress = 'InProgress',
  NotStarted = 'NotStarted',
  Expired = 'Expired',
  Viewed = 'Viewed',
  All = 'All',
}

export interface AssessmentScreenShots {
  success: boolean;
  data: {
    assessmentScreenShots: AssessmentScreenShot[];
  };
}

export interface AssessmentTakerCaptureShots {
  success: boolean;
  data: {
    assessmentTakerCaptureShots: AssessmentScreenShot[];
  };
}

export interface Feedback {
  candidateEmail: string;
  testRating: number;
  overallRating: number;
  comment: string;
}

export interface AssessmentDetailedResultsInterface {
  candidates: candidateAssessmentData[];
  totalItems: number;
  id: string;
  title: string;
  stats?: ReportStats;
}

export interface candidateAssessmentData {
  candidateId: string;
  candidateEmail: string;
  candidateName: string;
  assessmentStatus: string;
  startTime: string;
  endTime: string;
  duration: number;
  tests: candidateTestData[];
}

export interface candidateTestData {
  testId: string;
  testTitle: string;
  testScore: number;
  maxTestScore: number;
  testPercentage: number;
  questions: candidateQuestionData[];
}

export interface candidateQuestionData {
  questionId: string;
  questionText: string;
  questionType: string;
  questionTitle: string;
  questionNumber: number;
  maxScore: number;
  scoreAwarded: number;
  answerStatus: string;
  isAnswered: boolean;
  candidateAnswer: string[];
  correctAnswer:
    | BaseQuestionAnswer
    | MatrixQuestionResult
    | FillInQuestionAnswer
    | EssayAnswer
    | null;
}
