import { MatrixSubquestion } from '../../test-management/Questions/main-questions-page/create-questions/questionTypes/matrix-questions/matrix-table/matrix-table.component';
import { MatrixInfo } from './testTakingInterface';

export interface EmailDetails {
  email: string;
  dispatchId: string;
}

export interface VerifyTestTakerPayload {
  email: string;
  assessmentTakerId: string;
}

export interface CreateTestTakerApiResponse {
  response: string;
  data: {
    assessmentLink: string;
  };
}

export interface Track {
  type: string;
  currentPhase: string;
}

export interface AssessmentTakerApiResponse {
  success: boolean;
  data: TestTakingTrackingData;
}

export interface TestTakingTrackingData {
  track: Track;
  assessmentTaker: TestTakingInformation;
}

export interface Track {
  type: string;
  currentPhase: string;
}

export interface TestTakingInformation {
  id: string;
  assessmentId: string;
  assessmentName: string;
  dispatcher: string;
  showResults: boolean;
  organizationId: string;
  email: string;
  assessmentLink: string;
  assessmentWindowViolationCount: number;
  assessmentWindowViolationDuration: number;
  assessmentTakerShotCount: number;
  assessmentTakerViolationShotCount: number;
  windowShotCount: number;
  windowViolationShotCount: number;
  assessmentDuration: number;
  estimatedEndTime: string;
  expireDate: string;
  commenceDate: string;
  startTime: string;
  endTime: string;
  proctor: string;
  proctorFeatures: string[];
  screenshotsInterval: number;
  camerashotsInterval: number;
  testList: string[];
  submittedTests: Test[];
  phase: string;
  linkStatus: string;
  status: string;
  logSummary: string[];
  createdAt: string;
  updatedAt: string;
  assessment: Assessment;
  conductSurvey: boolean;
  currentDate: string;
}

export interface Assessment {
  id: string;
  title: string;
  instructions: string;
  showClock: boolean;
  conductSurvey: boolean;
  screenshotsInterval: string;
  camerashotsInterval: string;
  isDispatched: boolean;
  system: boolean;
  attempted: boolean;
  createdAt: string;
  updatedAt: string;
  proctor: string;
  organizationId: string;
  testOrder: string[];
  Survey: string[];
  tests: Test[];
  proctorFeatures: ProctorFeature[];
}

export interface Test {
  id: string;
  title: string;
  isActivated: boolean;
  system: boolean;
  useDomainQuestions: boolean;
  passMark: number;
  noOfQuestions?: number;
  description: string;
  instructions: string;
  passage: string;
  duration: number;
  difficultyLevel: string;
  organizationId: string;
  createdAt: string;
  updatedAt: string;
  domainId: string;
  questions: Question[];
  domain: Domain;
}

export interface Question {
  id: string;
  questionText: string;
  isComprehension: boolean;
  questionType: string;
  score: number;
  categoryId: string;
  domainId: string;
  system: boolean;
  organizationId: string;
  timeLimit: string;
  hash: string;
  flagged: boolean;
  multipleChoiceAnswer: BaseQuestionAnswer;
  trueOrFalseAnswer: BaseQuestionAnswer;
  multipleSelectAnswer: BaseQuestionAnswer;
  fillInAnswer: FillInTheBlanksAnswer;
  matchMatrixAnswer: MatrixInfo;
  CodeConstraint: CodeConstraint;
  codeTemplates: CodeTemplate[];
}

export interface FillInTheBlanksAnswer {
  id: string;
  questionId: string;
  options: FillInTheBlanksOptions[];
}

export interface FillInTheBlanksOptions {
  id: string;
  fillInAnswerId: string;
  blank: string;
  value: string;
}

export interface BaseQuestionAnswer {
  id: string;
  questionId: string;
  options: string[];
}

export interface CodeConstraint {
  id?: string;
  timeLimit: number;
  memoryLimit: number;
  timeComplexity: string;
  spaceComplexity: string;
  questionId?: string;
}

export interface CodeTemplate {
  id: string;
  codeType: string;
  body: string;
  questionId: string;
  languageId: string;
  Language: CodeLanguage;
}

export interface CodeLanguage {
  id: string;
  name: string;
  judgeLanguageId: number;
}

export interface CodeTestCases {
  test_case_id: string;
  question_id: string;
  input_data: string;
  output_data: string;
  visibility: string;
  validation_status: string;
  s3_location: string;
  consoleOutput: string;
}

export interface Answer {}
export interface Domain {
  name: string;
}

export interface ProctorFeature {
  id: string;
  name: string;
  description: string;
  createdAt: string;
  updatedAt: string;
}
export interface TestSubmission {
  id: string;
  title: string;
  submitted: boolean;
  duration: number;
  startTime?: string;
  endTime?: string;
  numberOfQuestionsAnswered: number;
  numberOfQuestions: number;
}

export interface AssessmentResponses {
  startTime: string;
  finishTime: string;
  testResults: ActualTestResult[];
  screenMonitoring: IntervalScreenshot[];
  candidateMonitoring: IntervalScreenshot[];
  isAutoSubmission: boolean;
}
export interface IntervalScreenshot {
  imageURL?: string;
  timeStamp?: string;
  violation?: boolean;
  violationNumber?: number;
  testId?: string;
  testNumber?: number;
  questionId?: string;
  questNumber?: number;
  startTime?: string;
  endTime?: string;
  shots?: ViolationScreenShot[];
  isIntegrityShot?: boolean;
}
export interface ViolationScreenShot {
  imageURL: string;
  timeStamp: string;
}

export interface WindowViolation {
  timeStamp: string;
  testId: string;
  questionId: string;
  screenshot: string;
  timeSpent: number;
  assessmentId: string;
  testTakerId: string;
}

export interface BasicViolationData {
  violation: boolean;
  startTime: string;
  endTime: string;
  violationNumber: number;
  questionId: string | undefined;
  questNumber: number;
  testId: string;
  testNumber: number;
  isIntegrityShot?: boolean;
}
export interface FlagQuestion {
  testId: string;
  questionId: string;
  reasonOfFlagging?: string[];
  questionText?: string;
}

export interface ActualTestResult {
  testId: string;
  questionResults: QuestionsResults[];
}
export interface QuestionsResults {
  questionId: string;
  testTakerAnswers: string[] | MatrixSubquestion[] | CodingAnswer[];
  idleTime: number;
  questionType: string;
}

export interface LinkStatus {
  track: {
    currentPhase: 'CODE_CONDUCT_SIGNING' | 'TEST_TAKING';
    type: 'OLD' | 'NEW';
    testSubmissions: TestSubmission[];
  };
  assessmentTaker: TestTakingInformation | TestTakingTrackingData;
}

export interface CodingAnswer {
  code: string;
  languageId: number;
}
export interface Result {
  questionId: string;
  testTakerAnswers: string[] | MatrixSubquestion[];
  idleTime: number;
  questionType?: string;
}

export interface ResultReturnBody {
  data: AssessmentResultPageData;
  success: boolean;
}
export interface AssessmentResultPageData {
  assessmentTakerResult: AssessmentTakerResult;
}

export interface AssessmentTakerResult {
  id: string;
  assessmentName: string;
  showResults: boolean;
  startTime: string;
  endTime: string;
  assessmentDuration: number;
  assessmenTakerDuration: number;
  testResults: TestResult[];
  overallAssessmentPassStatus?: string | null;
  overallAssessmentPercentage?: string | null;
  overallAssessmentScore?: number | null;
  overallAssessmentPassScore?: number | null;
}

export interface TestResult {
  testId: string;
  title: string;
  startTime: string;
  finishTime: string;
  testPercentage: number;
  totalScore: number;
  totalPassedScore: number;
  duration: number;
  numberOfQuestions: number;
  numberOfQuestionsPassed: number;
  numberOfQuestionsAnswered: number;
  numberOfQuestionsFailed: number;
  status: string;
  passStatus: string | null;
}

export interface FlagQuestion {
  testId: string;
  questionId: string;
  reasonOfFlagging?: string[];
  questionText?: string;
}

export interface ViolationScreenShot {
  imageURL: string;
  timeStamp: string;
}

export interface RunSolution {
  code: string;
  language_id: number;
  testCases: SolutionTestCases[];
  codeConstraint?: CodeConstraint;
}

export interface SolutionTestCases {
  test_case_id: string;
  input: string;
  output: string;
  consoleOutput?: string;
}
export interface RunSolutionResponse {
  success: boolean;
  data: SolutionResponseData;
}
export interface SolutionResponseData {
  summary: SolutionSummary;
  results: SolutionResult[];
}

export interface SolutionSummary {
  totalTests: number;
  passedTests: number;
  failedTests: number;
  inQueueTests: number;
  allPassed: boolean;
  stillProcessing: boolean;
}
export interface SolutionResult {
  test_case_id: string;
  question_id?: string;
  input: string;
  output: string;
  actualOutput: string;
  consoleOutput?: string;
  isCorrect: boolean;
  executionTime: string;
  memory: number;
  statusId: number;
  statusDescription: string;
  error: null;
  inQueue: boolean;
}

export interface StateData {
  assessmentTakerResult: AssessmentTakerResult;
  assessmentTaker: TestTakingInformation;
  track: {
    testSubmissions: TestSubmission[];
  };
}

export interface VerifyUserResponse {
  success: boolean;
  data: VerifyUserData;
}

export interface VerifyUserData {
  verified: boolean;
  testTaker: TestTaker;
}

export interface TestTaker {
  id: string;
  email: string;
  assessmentName: string;
  status: string;
  phase: string;
  linkStatus: string;
}

export type SelectedAnswers = string[] | MatrixSubquestion[] | CodingAnswer[];
