export interface CodeExecutionResult {
  success: boolean;
  output?: string;
  error?: string;
  testResults?: TestCaseResult[];
  executionTime?: number;
  memoryUsed?: number;
}

export interface TestCase {
  input: string;
  output: unknown;
  visibility: 'private' | 'public';
  weight: number;
}

export interface TestCaseResult {
  testCaseId: string;
  passed: boolean;
  actualOutput?: string;
  expectedOutput: string;
  executionTime?: number;
}

export interface EditorOptions {
  readOnly?: boolean;
  minimap?: {
    enabled: boolean;
  };
  lineNumbers?: 'on' | 'off';
  scrollBeyondLastLine?: boolean;
  automaticLayout?: boolean;
  fontSize?: number;
  tabSize?: number;
  theme?: string;
}

export interface CodeExecutionResponse {
  success: boolean;
  data: { output: string };
}

export interface CreateCodeQuestionInterface {
  language: {
    languageName: string;
    judge0Id: number;
  };
  referenceSolution: string;
  codeTemplate?: string;
  testCases: TestCase[];
  domainId: string;
  categoryId: string;
  score: string;
  difficultyLevel: string;
  questionType: string;
  questionText: string;
  questionTitle?: string;
  codeConstraints?: {
    timeComplexity?: string;
    spaceComplexity?: string;
    timeLimit?: number;
    memoryLimit?: number;
  };
}

export interface CodeApInterface {
  success: boolean;
  data: ResponseData;
}

export interface ResponseData {
  id: string;
  questionText: string;
  isComprehension: boolean;
  questionType: string;
  score: number;
  creator: unknown;
  createdAt: string;
  updatedAt: string;
  categoryId: string;
  domainId: string;
  system: boolean;
  organizationId: string;
  timeLimit: number;
  isActive: boolean;
  difficultyLevel: string;
  strictMark: boolean;
  hash: string;
  category: Category;
  domain: Domain;
  codeConstraints: CodeConstraints;
  referenceSolution: ReferenceSolution[];
  codeTemplate: CodeTemplate[];
  testCases: TestCaseFromResponse[];
}

export interface Category {
  id: string;
  name: string;
}

export interface Domain {
  id: string;
  name: string;
}

export interface CodeConstraints {
  id: string;
  timeLimit: number;
  memoryLimit: number;
  timeComplexity: string;
  spaceComplexity: string;
  questionId: string;
}

export interface ReferenceSolution {
  id: string;
  codeType: string;
  body: string;
  questionId: string;
  languageId: string;
  Language: Language;
}

export interface Language {
  id: string;
  name: string;
  judgeLanguageId: number;
}

export interface CodeTemplate {
  id: string;
  codeType: string;
  body: string;
  questionId: string;
  languageId: string;
  Language: Language2;
}

export interface Language2 {
  id: string;
  name: string;
  judgeLanguageId: number;
}

export interface TestCaseFromResponse {
  pk: string;
  sk: string;
  test_case_id: string;
  question_id: string;
  visibility: 'private' | 'public';
  weight: number;
  created_at: string;
  updated_at: string;
  created_by: string;
  version: number;
  s3_location: string;
  validation_status: string;
  input_data: string;
  output_data: string;
}

export interface APIAutoGenerateTestCasePayloadInterface {
  problem_definition: string;
  reference_solution: string;
  language: string;
}

export interface AIResponseObject {
  expected_output: unknown;
  description: string;
  hidden: boolean;
}

export interface APIAutoGenerateTestCaseResponseInterface
  extends AIResponseObject {
  input: string | unknown[];
}

export interface AIToJudge0Interface extends AIResponseObject {
  input: string;
}

export interface AIToJudgeOPayloadInterface {
  code: string;
  language_id: number;
  testCases: AIToJudge0Interface[];
}

export interface GenerateSolutionPayloadInterface {
  problem_definition: string;
  examples?: unknown;
  language: string;
}

export interface GenerateSolutionResponseInterface {
  solution: string;
}

export interface GenerateBoilerplateResponseInterface {
  boilerplate: string;
}

export interface GenerateQuestionResponseInterface {
  question_definition: string;
  reference_solution: string;
  boilerplate: string;
  test_cases: APIAutoGenerateTestCaseResponseInterface[];
}

export interface BulkTestCasesJudgeOResponse {
  success: boolean;
  data: ValidatedTestCases;
}

export interface ValidatedTestCases {
  validatedTestCases: AIToJudge0Interface[];
}
