<main>
  <div class="header lg:flex lg:justify-between items-center mb-[2.5rem]">
    <div class="surface:mb-3 search">
      <app-custom-search-input
        [placeholder]="'Search assessment...'"
        [searchTerm]="searchTerm"
        (searchTermChange)="onSearch($event)"
      ></app-custom-search-input>
    </div>

    <div class="buttons flex gap-x-3">
      <app-custom-dropdown
        placeholder="Filter By"
        [options]="getDisplayNames()"
        [isDefaultDropdown]="true"
        [icon]="filterIconSrc"
        height="45px"
        (selectionChange)="filterAssessment($event)"
      ></app-custom-dropdown>
      @if (showCreateAssessmentButtons) {
        <app-custombutton
          [variant]="'primary'"
          (clicked)="creatAssessmentPage()"
        >
          + Create new assessment
        </app-custombutton>
      }
    </div>
  </div>

  @if (isLoading && assessments.length === 0) {
    <div class="flex justify-center items-center h-[50vh]">
      <app-refresh-loader [loadingText]="'Loading...'"></app-refresh-loader>
    </div>
  } @else {
    @if (assessments.length > 0) {
      <div>
        <section
          class="grid items-start gap-y-5 gap-x-[calc(1.25rem-0.5rem)] w-full xl:max-h-[calc(100vh-24rem)] overflow-auto"
          style="grid-template-columns: repeat(auto-fill, minmax(320px, 1fr))"
        >
          @for (
            assessment of assessments
              | paginate
                : {
                    itemsPerPage: pageSize,
                    currentPage: page,
                    totalItems: totalNumberOfAssessments,
                  };
            track assessment;
            let i = $index
          ) {
            <div class="relative mr-2">
              <app-custom-assessements-card
                [AssessmentsTitle]="assessment.title"
                [numberOfTests]="assessment.tests.length"
                [dispatchedState]="
                  getDispatchStatus(assessment)
                    ? 'Dispatched'
                    : 'Not Dispatched'
                "
                [instructionMiniDetails]="assessment.instructions"
                [showFooter]="getDispatchStatus(assessment)"
                [timeCreated]="
                  (getTimeCreatedForAssessment(assessment.id) | async) ||
                  timeAgoService.getTimeAgo(assessment.createdAt)
                "
                [timeUpdated]="
                  (getTimeUpdatedForAssessment(assessment.id) | async) ||
                  timeAgoService.getTimeAgo(assessment.updatedAt)
                "
                [duration]="assessment.duration || 0"
                (actionButtonClicked)="onActionClick(assessment, i)"
                (cardClicked)="onCardClick(assessment)"
              >
              </app-custom-assessements-card>

              @if (selectedCardIndex === i) {
                <div class="absolute right-0 top-12">
                  <app-action-items-pop-up
                    [actionButtonItems]="actionButtonItems"
                    (closeModal)="closeModal()"
                  ></app-action-items-pop-up>
                </div>
              }
            </div>
          }
        </section>

        <footer class="mt-5 mb-4">
          <div class="grid md:flex justify-between items-center">
            <div class="text-[#474D66] text-[16px] font-medium">
              {{ range }}
            </div>

            <app-custom-pagination
              [total]="totalNumberOfAssessments"
              [size]="pageSize"
              (pagechange)="onPageChange($event)"
              (sizeSelect)="onSizeChange($event)"
            ></app-custom-pagination>
          </div>
        </footer>
      </div>
    } @else {
      <div class="h-[30%]">
        <app-no-result-found
          [message]="'No Assessments found'"
          [search]="searchTerm"
        ></app-no-result-found>
      </div>
    }
  }
</main>

@if (this.showDeleteModal) {
  <app-custom-mini-modal
    [headerTitle]="'Confirm Assessment Deletion'"
    [textAlign]="'center'"
    [left]="'Cancel'"
    [right]="'Delete'"
    [visibleModal]="showDeleteModal"
    [bodyText]="'Are you sure you want to delete this assessment?'"
    (leftClickEvent)="closeDeleteModal()"
    (rightClickEvent)="confirmDeleteAssessment()"
    (visibleModalChange)="closeDeleteModal()"
    [width]="'446px'"
    [height]="'223px'"
  />
}
