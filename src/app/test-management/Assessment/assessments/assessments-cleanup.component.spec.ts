import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AssessmentsComponent } from './assessments.component';
import { Router } from '@angular/router';
import { ToastService } from '../../../services/toast-service/toast.service';
import { AssessmentService } from '../../../services/test-management-service/assessment-service/assessment.service';
import { AuthService } from '../../../services/auth.service';
import { SharedService } from '../../../services/shared/shared.service';
import { AssessmentStore } from '../../../stores/assessments-store/assessment.store';
import { BehaviorSubject, of, Subscription } from 'rxjs';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('AssessmentsComponent - Resource Leak Fix', () => {
  let component: AssessmentsComponent;
  let fixture: ComponentFixture<AssessmentsComponent>;

  beforeAll(() => {
    jest.useFakeTimers();
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  beforeEach(async () => {
    const mockRouter = { navigate: jest.fn() };
    const mockToastService = { onShow: jest.fn() };
    const mockAssessmentService = {
      isAssessmentLoaded$: new BehaviorSubject<boolean>(false),
      getAllAssessment: jest.fn(),
      deleteAssessment: jest.fn(),
    };
    const mockAuthService = {
      getLoginUser: jest.fn().mockReturnValue({
        permissions: ['admin', 'manage assessments'],
      }),
    };
    const mockSharedService = {
      addMenuItem: jest.fn(),
      setBreadcrumbDetails: jest.fn(),
      clearMenuItem: jest.fn(),
    };
    const mockAssessmentStore = {
      selectAllAssessments$: of([]),
      assessmentsLoading$: of(false),
      totalAssessments$: of(0),
      fetchAssessment: jest.fn(),
      resetAssessment: jest.fn(),
    };

    await TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule,
        NoopAnimationsModule,
        AssessmentsComponent,
      ],
      providers: [
        { provide: Router, useValue: mockRouter },
        { provide: ToastService, useValue: mockToastService },
        { provide: AssessmentService, useValue: mockAssessmentService },
        { provide: AuthService, useValue: mockAuthService },
        { provide: SharedService, useValue: mockSharedService },
        { provide: AssessmentStore, useValue: mockAssessmentStore },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(AssessmentsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  afterEach(() => {
    if (component) {
      component.ngOnDestroy();
    }
    if (fixture) {
      fixture.destroy();
    }
    jest.clearAllTimers();
    jest.clearAllMocks();
  });

  it('should create and properly clean up resources', () => {
    expect(component).toBeTruthy();
    expect(component.subscriptions).toBeDefined();
    expect(component.searchSubject).toBeDefined();
  });

  it('should complete search subject on destroy', () => {
    const completeSpy = jest.spyOn(component.searchSubject, 'complete');
    component.ngOnDestroy();
    expect(completeSpy).toHaveBeenCalled();
  });

  it('should unsubscribe from all subscriptions on destroy', () => {
    const unsubscribeSpy = jest.fn();
    const mockSubscription = new Subscription();
    mockSubscription.unsubscribe = unsubscribeSpy;
    component.subscriptions.push(mockSubscription);

    component.ngOnDestroy();

    expect(unsubscribeSpy).toHaveBeenCalled();
  });
});
