import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import { Router } from '@angular/router';
import { NgxPaginationModule } from 'ngx-pagination';
import { Subject, Subscription, Observable, of } from 'rxjs';
import { debounceTime, map } from 'rxjs/operators';
import { AssessmentWithTest } from '../../../Interfaces/Types/assessmentInterface';
import { ActionButtonInterface } from '../../../Interfaces/Types/userInterface';
import { ActionItemsPopUpComponent } from '../../../components/action-items-pop-up/action-items-pop-up.component';
import { CustomAssessementsCardComponent } from '../../../components/custom-assessements-card/custom-assessements-card.component';
import { CustomDropdownComponent } from '../../../components/custom-dropdown/custom-dropdown.component';
import { CustomMiniModalComponent } from '../../../components/custom-mini-modal/custom-mini-modal.component';
import { CustomPaginationComponent } from '../../../components/custom-pagination/custom-pagination.component';
import { CustomSearchInputComponent } from '../../../components/custom-search-input/custom-search-input.component';
import { CustomButtonComponent } from '../../../components/custombutton/custombutton.component';
import { RefreshLoaderComponent } from '../../../components/refresh-loader/refresh-loader.component';
import { NoResultFoundComponent } from '../../../no-result-found/no-result-found.component';
import { AuthService } from '../../../services/auth.service';
import { LoginUserInfo } from '../../../services/authServiceInterfaces';
import { AssessmentService } from '../../../services/test-management-service/assessment-service/assessment.service';
import { ToastService } from '../../../services/toast-service/toast.service';
import { AssessmentStore } from '../../../stores/assessments-store/assessment.store';
import { TimeAgoService } from '../../../services/time-ago/time-ago.service';
import {
  checkPermission,
  PermissionAccessGroups,
} from '../../../utils/permissions';
import { calculateRange } from '../../../utils/questionsConstants';

@Component({
  selector: 'app-assessments',
  standalone: true,
  imports: [
    CommonModule,
    CustomAssessementsCardComponent,
    CustomButtonComponent,
    CustomPaginationComponent,
    NgxPaginationModule,
    ActionItemsPopUpComponent,
    CustomMiniModalComponent,
    CustomSearchInputComponent,
    NoResultFoundComponent,
    RefreshLoaderComponent,
    CustomDropdownComponent,
  ],
  templateUrl: './assessments.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AssessmentsComponent implements OnInit, OnDestroy {
  assessments: AssessmentWithTest[] = [];
  searchTerm: string = '';
  showDeleteModal = false;
  pageSize: number = 25;
  page: number = 1;
  range = '';
  totalNumberOfAssessments = 0;
  assessmentTitle: string = '';
  selectedCardIndex: number | null = null;
  subscriptions: Subscription[] = [];
  searchSubject: Subject<string> = new Subject();
  assessmentUrl = '/dashboard/test-management/assessments/';
  isLoading = false;
  noResultsFound = false;
  dataForPreview: AssessmentWithTest | undefined;
  userInfo: LoginUserInfo | null = null;
  actionButtonItems: ActionButtonInterface[] = [];
  assessmentId: string | undefined;
  showCreateAssessmentButtons = false;
  title = 'All Assesments';
  forceFetchAssessment = false;
  isFilterDropdown = false;
  selectedDispatchStatus!: boolean | undefined;
  filterIconSrc = '../../../../../assets/reportsManagement/Filters lines.svg';

  // Real-time time tracking
  assessmentTimeUpdates$: Observable<
    Record<string, { created: string; updated: string }>
  > = of({});

  constructor(
    private readonly router: Router,
    private readonly toast: ToastService,
    private readonly assessmentService: AssessmentService,
    private readonly authService: AuthService,
    private readonly assessmentStore: AssessmentStore,
    public readonly timeAgoService: TimeAgoService,
    private readonly cdr: ChangeDetectorRef
  ) {
    this.loadAssessment();
  }

  ngOnInit(): void {
    this.userInfo = this.authService.getLoginUser();
    if (this.userInfo?.permissions) {
      this.showCreateAssessmentButtons = checkPermission(
        PermissionAccessGroups.createAssessmentsAccessGroup,
        this.userInfo.permissions
      );
    }

    this.assessmentStore.selectAllAssessments$.subscribe((data) => {
      if (data) {
        this.assessments = data;
        this.updateTimeObservables();
        this.cdr.detectChanges(); // Ensure change detection runs when data updates
      }
    });

    this.assessmentStore.assessmentsLoading$.subscribe((data) => {
      this.isLoading = data;
    });

    this.assessmentStore.totalAssessments$.subscribe((totalNumberOfItems) => {
      this.totalNumberOfAssessments = totalNumberOfItems as number;
      this.range = calculateRange(
        this.page,
        this.pageSize,
        this.totalNumberOfAssessments
      );
    });

    this.subscriptions.push(
      this.searchSubject.pipe(debounceTime(1200)).subscribe((searchTerm) => {
        if (searchTerm !== this.searchTerm) {
          this.searchTerm = searchTerm;
          this.page = 1;
          this.forceFetchAssessment = true;
          this.loadAssessment();
        }
      })
    );

    // Initialize time observables
    this.updateTimeObservables();

    localStorage.removeItem('breadcrumb');
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());

    // Complete the search subject to prevent memory leaks
    this.searchSubject.complete();

    if (this.searchTerm !== '' || this.page !== 1) {
      this.forceFetchAssessment = true;
      this.searchTerm = '';
      this.page = 1;
      this.loadAssessment();
    }
  }

  toggleDropdownFilterVisibility() {
    this.isFilterDropdown = !this.isFilterDropdown;
  }

  getDisplayNames(): string[] {
    return ['All', 'Dispatched', 'Not Dispatched'];
  }

  filterAssessment(selectedOption: string) {
    switch (selectedOption) {
      case 'Dispatched':
        this.selectedDispatchStatus = true;
        break;
      case 'Not Dispatched':
        this.selectedDispatchStatus = false;
        break;
      default:
        this.selectedDispatchStatus = undefined;
        break;
    }
    this.page = 1;
    this.forceFetchAssessment = true;
    this.loadAssessment();
  }

  onPageChange(pageNumber: number) {
    if (pageNumber < 0 || pageNumber > this.totalNumberOfAssessments) return;
    if (pageNumber === this.page) return;
    this.page = pageNumber;
    this.selectedCardIndex = null;
    this.forceFetchAssessment = true;
    this.loadAssessment();
  }

  onSizeChange(event: number) {
    this.pageSize = event;
    this.page = 1;
    this.forceFetchAssessment = true;
    this.loadAssessment();
  }

  onActionClick(event: AssessmentWithTest, index: number) {
    this.resetActionState(event, index);
    this.setActionButtonItems(event);
  }

  private resetActionState(event: AssessmentWithTest, index: number): void {
    this.actionButtonItems = [];
    this.dataForPreview = event;
    this.assessmentTitle = event.title;
    this.selectedCardIndex = index === this.selectedCardIndex ? null : index;
    this.assessmentId = event.id;
  }

  private setActionButtonItems(event: AssessmentWithTest): void {
    if (this.showCreateAssessmentButtons) {
      this.setCreateAssessmentButtons(event);
    } else {
      this.setDefaultButtons(event);
    }
  }

  private setCreateAssessmentButtons(event: AssessmentWithTest): void {
    if (event.isDispatched) {
      this.actionButtonItems = this.getDispatchedButtons();
    } else {
      this.actionButtonItems = this.getNotDispatchedButtons();
    }
  }

  private getDispatchedButtons() {
    return [
      this.createButton('Preview', 'preview', () => this.navigateToPreview()),
      this.createButton('Dispatch', 'dispatch', () =>
        this.navigateToDispatch()
      ),
      this.createButton('View Reports', 'chart-bar', () =>
        this.navigateToReports()
      ),
      this.createButton('History', 'history', () => this.navigateToHistory()),
    ];
  }

  private getNotDispatchedButtons() {
    return [
      this.createButton('Preview', 'preview', () => this.navigateToPreview()),
      this.createButton('History', 'history', () => this.navigateToHistory()),
      this.createButton('Edit', 'edit', () => this.navigateToEdit()),
      this.createButton('Dispatch', 'dispatch', () =>
        this.navigateToDispatch()
      ),
      this.createButton(
        'Delete',
        'delete-icon',
        () => (this.showDeleteModal = true)
      ),
    ];
  }

  private setDefaultButtons(event: AssessmentWithTest): void {
    const defaultButtons = [
      this.createButton('Preview', 'preview', () => this.navigateToPreview()),
      this.createButton('History', 'history', () => this.navigateToHistory()),
    ];
    if (event.isDispatched) {
      defaultButtons.splice(
        1,
        0,
        this.createButton('View Reports', 'chart-bar', () =>
          this.navigateToReports()
        )
      );
    }

    this.actionButtonItems = defaultButtons;
  }

  private createButton(name: string, icon: string, onClick: () => void) {
    return {
      name,
      icon: `../../../../../assets/icons/${icon}.svg`,
      onClick,
    };
  }

  private navigateToPreview(): void {
    this.router.navigate([
      `${this.assessmentUrl}/preview/${this.assessmentId}`,
    ]);
    this.selectedCardIndex = null;
  }

  navigateToDispatch(): void {
    const queryParams = this.assessmentId
      ? {
          assessmentId: this.assessmentId,
          assessmentTitle: this.assessmentTitle.replaceAll(/ /g, '--'),
        }
      : {};
    this.router.navigate([`${this.assessmentUrl}/dispatch`], { queryParams });
  }

  navigateToReports(): void {
    if (this.dataForPreview) {
      this.router.navigate(
        [
          '/dashboard/report-management/assessment-candidates/' +
            this.dataForPreview.id,
        ],
        {
          queryParams: {
            fromAssessments: true,
          },
        }
      );
    }
    this.selectedCardIndex = null;
  }

  private navigateToHistory(): void {
    this.router.navigate([
      `${this.assessmentUrl}/history/${this.assessmentId}`,
    ]);
    this.selectedCardIndex = null;
  }

  public navigateToEdit(): void {
    this.assessmentStore.resetAssessment();
    const queryParams = this.assessmentId
      ? { assessmentId: this.assessmentId }
      : {};
    this.router.navigate([`${this.assessmentUrl}/edit-assessments`], {
      queryParams,
    });
  }

  creatAssessmentPage() {
    this.assessmentStore.resetAssessment();
    this.router.navigate([
      '/dashboard/test-management/assessments/create-assessments',
    ]);
  }

  loadAssessment() {
    this.assessmentStore.fetchAssessment({
      isDispatched: this.selectedDispatchStatus,
      searchTerm: this.searchTerm,
      limit: this.pageSize,
      page: this.page,
      forceFetch: this.forceFetchAssessment,
    });

    this.range = calculateRange(
      this.page,
      this.pageSize,
      this.totalNumberOfAssessments
    );
  }

  closeDeleteModal() {
    this.showDeleteModal = false;
  }

  confirmDeleteAssessment() {
    this.assessmentService.deleteAssessment(this.assessmentId!).subscribe({
      next: () => {
        this.toast.onShow(
          'Assessment deleted',
          'Assessment deleted successfully',
          true,
          'success'
        );
        this.forceFetchAssessment = true;
        this.loadAssessment();
        this.showDeleteModal = false;
        this.selectedCardIndex = null;
        // Manually trigger change detection to ensure immediate re-rendering
        this.cdr.detectChanges();
        this.router.navigate(['/dashboard/test-management/assessments']);
      },
      error: (error) => {
        this.toast.onShow('error', error, true, 'error');
        this.showDeleteModal = false;
        this.cdr.detectChanges();
      },
    });
  }

  onSearch($event: string) {
    this.searchSubject.next($event);
  }

  onReportAvailable(data: AssessmentWithTest) {
    this.router.navigate(
      ['/dashboard/report-management/assessment-candidates/' + data.id],
      {
        queryParams: {
          fromAssessments: true,
        },
      }
    );
  }

  closeModal() {
    this.selectedCardIndex = null;
  }

  onCardClick(assessment: AssessmentWithTest) {
    this.router.navigate([this.assessmentUrl + '/preview/' + assessment.id]);
    this.selectedCardIndex = null;
  }

  getDispatchStatus(assessment: AssessmentWithTest) {
    return assessment.isDispatched;
  }

  /**
   * Update time observables for all assessments to enable real-time updates
   */
  private updateTimeObservables(): void {
    if (this.assessments.length === 0) {
      this.assessmentTimeUpdates$ = of({});
      return;
    }

    // Create a map of assessment timestamps for batch processing
    const timestamps: string[] = [];

    this.assessments.forEach((assessment) => {
      if (assessment.createdAt) {
        timestamps.push(assessment.createdAt);
      }
      if (assessment.updatedAt) {
        timestamps.push(assessment.updatedAt);
      }
    });

    // Get real-time updates for all timestamps
    this.assessmentTimeUpdates$ = this.timeAgoService
      .getMultipleTimeAgo(timestamps)
      .pipe(
        map((timeMap) => {
          const result: Record<string, { created: string; updated: string }> =
            {};

          this.assessments.forEach((assessment) => {
            result[assessment.id] = {
              created:
                timeMap[assessment.createdAt] ||
                this.timeAgoService.getTimeAgo(assessment.createdAt),
              updated:
                timeMap[assessment.updatedAt] ||
                this.timeAgoService.getTimeAgo(assessment.updatedAt),
            };
          });

          return result;
        })
      );
  }

  /**
   * Get real-time time created for a specific assessment
   * @param assessmentId - The assessment ID
   * @returns Observable of time created string
   */
  getTimeCreatedForAssessment(assessmentId: string): Observable<string> {
    return this.assessmentTimeUpdates$.pipe(
      map((updates) => updates[assessmentId]?.created || '')
    );
  }

  /**
   * Get real-time time updated for a specific assessment
   * @param assessmentId - The assessment ID
   * @returns Observable of time updated string
   */
  getTimeUpdatedForAssessment(assessmentId: string): Observable<string> {
    return this.assessmentTimeUpdates$.pipe(
      map((updates) => updates[assessmentId]?.updated || '')
    );
  }
}
