import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AssessmentsComponent } from './assessments.component';
import { Router } from '@angular/router';
import { ToastService } from '../../../services/toast-service/toast.service';
import { AssessmentService } from '../../../services/test-management-service/assessment-service/assessment.service';
import { AuthService } from '../../../services/auth.service';
import { SharedService } from '../../../services/shared/shared.service';
import { AssessmentStore } from '../../../stores/assessments-store/assessment.store';
import { TimeAgoService } from '../../../services/time-ago/time-ago.service';
import { BehaviorSubject, of, Subscription, throwError } from 'rxjs';
import { AssessmentWithTest } from '../../../Interfaces/Types/assessmentInterface';
import { dummyAssessmentWithTest } from '../../../utils/assessment/assessment';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

// Fixed resource leaks and cleanup issues
describe('AssessmentsComponent', () => {
  let component: AssessmentsComponent;
  let fixture: ComponentFixture<AssessmentsComponent>;
  let mockRouter: jest.Mocked<Router>;
  let mockToastService: jest.Mocked<ToastService>;
  let mockAssessmentService: jest.Mocked<AssessmentService>;
  let mockAuthService: jest.Mocked<AuthService>;
  let mockSharedService: jest.Mocked<SharedService>;
  let mockAssessmentStore: jest.Mocked<AssessmentStore>;
  let mockTimeAgoService: jest.Mocked<TimeAgoService>;

  beforeAll(() => {
    jest.useFakeTimers();
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  beforeEach(() => {
    mockRouter = { navigate: jest.fn() } as unknown as jest.Mocked<Router>;
    mockToastService = {
      onShow: jest.fn(),
    } as unknown as jest.Mocked<ToastService>;
    mockAssessmentService = {
      isAssessmentLoaded$: new BehaviorSubject<boolean>(false),
      getAllAssessment: jest.fn(),
      deleteAssessment: jest.fn(),
    } as unknown as jest.Mocked<AssessmentService>;
    mockAuthService = {
      getLoginUser: jest.fn().mockReturnValue({
        permissions: ['admin', 'manage assessments'],
      }),
    } as unknown as jest.Mocked<AuthService>;
    mockSharedService = {
      addMenuItem: jest.fn(),
      setBreadcrumbDetails: jest.fn(),
      clearMenuItem: jest.fn(),
    } as unknown as jest.Mocked<SharedService>;

    mockAssessmentStore = {
      selectAllAssessments$: of([]),
      assessmentsLoading$: of(false),
      totalAssessments$: of(0),
      fetchAssessment: jest.fn(),
      resetAssessment: jest.fn(),
    } as unknown as jest.Mocked<AssessmentStore>;

    mockTimeAgoService = {
      getMultipleTimeAgo: jest.fn().mockReturnValue(of({})),
      getTimeAgo: jest.fn().mockReturnValue('default time'),
    } as unknown as jest.Mocked<TimeAgoService>;

    TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule,
        NoopAnimationsModule,
        AssessmentsComponent,
      ],
      providers: [
        { provide: Router, useValue: mockRouter },
        { provide: ToastService, useValue: mockToastService },
        { provide: AssessmentService, useValue: mockAssessmentService },
        { provide: AuthService, useValue: mockAuthService },
        { provide: SharedService, useValue: mockSharedService },
        { provide: AssessmentStore, useValue: mockAssessmentStore },
        {
          provide: TimeAgoService,
          useValue: mockTimeAgoService,
        },
      ],
    }).compileComponents();
    jest.clearAllMocks();

    fixture = TestBed.createComponent(AssessmentsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  afterEach(() => {
    // Properly clean up component and its resources
    if (component) {
      component.ngOnDestroy();
    }
    if (fixture) {
      fixture.destroy();
    }
    jest.clearAllTimers();
    jest.clearAllMocks();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should show create assessment buttons when user has correct permissions', () => {
    component.ngOnInit();
    expect(component.showCreateAssessmentButtons).toBe(true);
  });

  it('should navigate to create assessment page', () => {
    component.creatAssessmentPage();
    expect(mockRouter.navigate).toHaveBeenCalledWith([
      '/dashboard/test-management/assessments/create-assessments',
    ]);
  });
  it('should change the page when a valid page number is provided', () => {
    component.totalNumberOfAssessments = 10;
    component.page = 1;
    const loadAssessmentSpy = jest.spyOn(component, 'loadAssessment');

    component.onPageChange(2);

    expect(component.page).toBe(2);
    expect(component.selectedCardIndex).toBeNull();
    expect(loadAssessmentSpy).toHaveBeenCalled();
  });
  it('should delete assessment and show success toast', () => {
    mockAssessmentService.deleteAssessment.mockReturnValue(of({}));
    component.assessmentId = dummyAssessmentWithTest.id;
    component.confirmDeleteAssessment();
    expect(mockAssessmentService.deleteAssessment).toHaveBeenCalledWith(
      dummyAssessmentWithTest.id
    );
    expect(mockToastService.onShow).toHaveBeenCalledWith(
      'Assessment deleted',
      'Assessment deleted successfully',
      true,
      'success'
    );
    expect(mockRouter.navigate).toHaveBeenCalledWith([
      '/dashboard/test-management/assessments',
    ]);
  });

  it('should navigate to report management on report available', () => {
    component.onReportAvailable(dummyAssessmentWithTest);
    expect(mockRouter.navigate).toHaveBeenCalledWith(
      ['/dashboard/report-management/assessment-candidates/assessment-123'],
      { queryParams: { fromAssessments: true } }
    );
  });
  it('should not change page if new page is invalid', () => {
    const initialPage = component.page;
    component.onPageChange(-1);
    expect(component.page).toBe(initialPage);
  });

  it('should update action button items based on assessment dispatch status', () => {
    const dispatchedAssessment: AssessmentWithTest = {
      ...dummyAssessmentWithTest,
      isDispatched: true,
    };

    component.onActionClick(dispatchedAssessment, 0);

    expect(component.actionButtonItems.length).toBe(4);
    expect(component.actionButtonItems[0].name).toBe('Preview');
    expect(component.actionButtonItems[1].name).toBe('Dispatch');
  });

  it('should update actionButtonItems correctly when an action button is clicked', () => {
    component.showCreateAssessmentButtons = true;
    component.onActionClick(dummyAssessmentWithTest, 1);
    expect(component.actionButtonItems.length).toBe(5);
    expect(component.actionButtonItems[0].name).toBe('Preview');
    expect(component.actionButtonItems[1].name).toBe('History');
    expect(component.actionButtonItems[2].name).toBe('Edit');
    expect(component.actionButtonItems[3].name).toBe('Dispatch');
  });
  it('should handle null or undefined event parameter with errors', () => {
    component.showCreateAssessmentButtons = true;
    expect(() =>
      component.onActionClick(undefined as unknown as AssessmentWithTest, 0)
    ).toThrow();
    expect(component.actionButtonItems.length).toBe(0);
  });
  it('should handle error when deleting assessment', () => {
    mockAssessmentService.deleteAssessment.mockReturnValue(
      throwError(() => 'Delete error')
    );
    component.assessmentId = dummyAssessmentWithTest.id;
    component.confirmDeleteAssessment();
    expect(mockToastService.onShow).toHaveBeenCalledWith(
      'error',
      'Delete error',
      true,
      'error'
    );
  });
  it('should navigate to the correct URL when "Preview" is clicked', () => {
    component.showCreateAssessmentButtons = true;
    component.onActionClick(dummyAssessmentWithTest, 0);
    const previewButton = component.actionButtonItems.find(
      (item) => item.name === 'Preview'
    );
    previewButton?.onClick();
    expect(mockRouter.navigate).toHaveBeenCalledWith([
      component.assessmentUrl + '/preview/' + dummyAssessmentWithTest.id,
    ]);
    expect(component.selectedCardIndex).toBeNull();
  });
  it('should navigate to the correct URL with query parameters when Dispatch is clicked', () => {
    component.showCreateAssessmentButtons = true;
    component.onActionClick(
      { ...dummyAssessmentWithTest, isDispatched: true },
      0
    );

    expect(component.actionButtonItems.length).toBe(4);
    expect(component.actionButtonItems[1].name).toBe('Dispatch');
    component.actionButtonItems[1].onClick();
  });

  it('should navigate to the correct URL with query parameters when Edit is clicked', () => {
    component.showCreateAssessmentButtons = true;
    component.onActionClick(dummyAssessmentWithTest, 0);
    const editButton = component.actionButtonItems.find(
      (item) => item.name === 'Edit'
    );
    editButton?.onClick();
    expect(mockRouter.navigate).toHaveBeenCalledWith(
      [component.assessmentUrl + '/edit-assessments'],
      { queryParams: { assessmentId: dummyAssessmentWithTest.id } }
    );
  });
  it('should close delete modal', () => {
    component.showDeleteModal = true;
    component.closeDeleteModal();
    expect(component.showDeleteModal).toBe(false);
  });

  it('should close action modal', () => {
    component.selectedCardIndex = 0;
    component.closeModal();
    expect(component.selectedCardIndex).toBeNull();
  });

  it('should handle onSizeChange and reset page to 1', () => {
    component.page = 5;
    const loadAssessmentSpy = jest.spyOn(component, 'loadAssessment');

    component.onSizeChange(50);

    expect(component.pageSize).toBe(50);
    expect(component.page).toBe(1);
    expect(component.forceFetchAssessment).toBe(true);
    expect(loadAssessmentSpy).toHaveBeenCalled();
  });

  it('should handle onSearch and trigger searchSubject', () => {
    const searchSubjectSpy = jest.spyOn(component.searchSubject, 'next');

    component.onSearch('test search');

    expect(searchSubjectSpy).toHaveBeenCalledWith('test search');
  });

  it('should handle onCardClick and navigate to preview', () => {
    component.onCardClick(dummyAssessmentWithTest);

    expect(mockRouter.navigate).toHaveBeenCalledWith([
      component.assessmentUrl + '/preview/' + dummyAssessmentWithTest.id,
    ]);
    expect(component.selectedCardIndex).toBeNull();
  });

  it('should get dispatch status from assessment', () => {
    const dispatchedAssessment = {
      ...dummyAssessmentWithTest,
      isDispatched: true,
    };
    const notDispatchedAssessment = {
      ...dummyAssessmentWithTest,
      isDispatched: false,
    };

    expect(component.getDispatchStatus(dispatchedAssessment)).toBe(true);
    expect(component.getDispatchStatus(notDispatchedAssessment)).toBe(false);
  });

  it('should toggle dropdown filter visibility', () => {
    component.isFilterDropdown = false;

    component.toggleDropdownFilterVisibility();
    expect(component.isFilterDropdown).toBe(true);

    component.toggleDropdownFilterVisibility();
    expect(component.isFilterDropdown).toBe(false);
  });

  it('should return correct display names', () => {
    const displayNames = component.getDisplayNames();

    expect(displayNames).toEqual(['All', 'Dispatched', 'Not Dispatched']);
  });

  it('should filter assessments by "Dispatched" status', () => {
    const loadAssessmentSpy = jest.spyOn(component, 'loadAssessment');

    component.filterAssessment('Dispatched');

    expect(component.selectedDispatchStatus).toBe(true);
    expect(component.page).toBe(1);
    expect(component.forceFetchAssessment).toBe(true);
    expect(loadAssessmentSpy).toHaveBeenCalled();
  });

  it('should filter assessments by "Not Dispatched" status', () => {
    const loadAssessmentSpy = jest.spyOn(component, 'loadAssessment');

    component.filterAssessment('Not Dispatched');

    expect(component.selectedDispatchStatus).toBe(false);
    expect(component.page).toBe(1);
    expect(component.forceFetchAssessment).toBe(true);
    expect(loadAssessmentSpy).toHaveBeenCalled();
  });

  it('should filter assessments by "All" (reset filter)', () => {
    const loadAssessmentSpy = jest.spyOn(component, 'loadAssessment');

    component.filterAssessment('All');

    expect(component.selectedDispatchStatus).toBeUndefined();
    expect(component.page).toBe(1);
    expect(component.forceFetchAssessment).toBe(true);
    expect(loadAssessmentSpy).toHaveBeenCalled();
  });

  it('should not change page when pageNumber equals current page', () => {
    component.page = 2;
    const loadAssessmentSpy = jest.spyOn(component, 'loadAssessment');

    component.onPageChange(2);

    expect(component.page).toBe(2);
    expect(loadAssessmentSpy).not.toHaveBeenCalled();
  });

  it('should not change page when pageNumber exceeds total assessments', () => {
    component.totalNumberOfAssessments = 10;
    component.page = 1;
    const loadAssessmentSpy = jest.spyOn(component, 'loadAssessment');

    component.onPageChange(15);

    expect(component.page).toBe(1);
    expect(loadAssessmentSpy).not.toHaveBeenCalled();
  });

  it('should handle action click when selectedCardIndex equals current index', () => {
    component.selectedCardIndex = 2;

    component.onActionClick(dummyAssessmentWithTest, 2);

    expect(component.selectedCardIndex).toBeNull();
  });

  it('should create assessment page and reset store', () => {
    const resetAssessmentSpy = jest.spyOn(
      component['assessmentStore'],
      'resetAssessment'
    );

    component.creatAssessmentPage();

    expect(resetAssessmentSpy).toHaveBeenCalled();
    expect(mockRouter.navigate).toHaveBeenCalledWith([
      '/dashboard/test-management/assessments/create-assessments',
    ]);
  });

  it('should handle user with no permissions', () => {
    mockAuthService.getLoginUser.mockReturnValue({
      activated: true,
      first_name: 'Test',
      last_name: 'User',
      email: '<EMAIL>',
      id: '1',
      permissions: ['read-only'],
      role: '',
      password: '',
      password_reset_token: null,
      phone: '',
      createdAt: '',
      updatedAt: '',
      organizationId: '',
    });

    component.ngOnInit();

    expect(component.showCreateAssessmentButtons).toBe(false);
  });

  it('should handle user with null permissions', () => {
    mockAuthService.getLoginUser.mockReturnValue({
      permissions: [],
      activated: false,
      first_name: '',
      last_name: '',
      email: '',
      password: '',
      role: '',
      password_reset_token: null,
      phone: '',
      createdAt: '',
      updatedAt: '',
      organizationId: '',
    });

    component.ngOnInit();

    expect(component.showCreateAssessmentButtons).toBe(false);
  });

  it('should cleanup and reset state in ngOnDestroy when search term exists', () => {
    component.searchTerm = 'test';
    component.page = 3;
    const subscription = new Subscription();
    component.subscriptions = [subscription];
    const unsubscribeSpy = jest.spyOn(subscription, 'unsubscribe');
    const loadAssessmentSpy = jest.spyOn(component, 'loadAssessment');

    component.ngOnDestroy();

    expect(unsubscribeSpy).toHaveBeenCalled();
    expect(component.forceFetchAssessment).toBe(true);
    expect(component.searchTerm).toBe('');
    expect(component.page).toBe(1);
    expect(loadAssessmentSpy).toHaveBeenCalled();
  });

  it('should not reset state in ngOnDestroy when search term is empty and page is 1', () => {
    component.searchTerm = '';
    component.page = 1;
    const loadAssessmentSpy = jest.spyOn(component, 'loadAssessment');

    component.ngOnDestroy();

    expect(loadAssessmentSpy).not.toHaveBeenCalled();
  });

  it('should handle default buttons for dispatched assessment without create permissions', () => {
    component.showCreateAssessmentButtons = false;
    const dispatchedAssessment = {
      ...dummyAssessmentWithTest,
      isDispatched: true,
    };

    component.onActionClick(dispatchedAssessment, 0);

    expect(component.actionButtonItems.length).toBe(3);
    expect(component.actionButtonItems[0].name).toBe('Preview');
    expect(component.actionButtonItems[1].name).toBe('View Reports');
    expect(component.actionButtonItems[2].name).toBe('History');
  });

  it('should handle default buttons for non-dispatched assessment without create permissions', () => {
    component.showCreateAssessmentButtons = false;
    const nonDispatchedAssessment = {
      ...dummyAssessmentWithTest,
      isDispatched: false,
    };

    component.onActionClick(nonDispatchedAssessment, 0);

    expect(component.actionButtonItems.length).toBe(2);
    expect(component.actionButtonItems[0].name).toBe('Preview');
    expect(component.actionButtonItems[1].name).toBe('History');
  });

  it('should navigate to history correctly', () => {
    component.showCreateAssessmentButtons = true;
    component.onActionClick(dummyAssessmentWithTest, 0);

    const historyButton = component.actionButtonItems.find(
      (item) => item.name === 'History'
    );
    historyButton?.onClick();

    expect(mockRouter.navigate).toHaveBeenCalledWith([
      component.assessmentUrl + '/history/' + dummyAssessmentWithTest.id,
    ]);
    expect(component.selectedCardIndex).toBeNull();
  });

  it('should navigate to reports correctly', () => {
    component.showCreateAssessmentButtons = true;
    const dispatchedAssessment = {
      ...dummyAssessmentWithTest,
      isDispatched: true,
    };
    component.onActionClick(dispatchedAssessment, 0);

    const reportsButton = component.actionButtonItems.find(
      (item) => item.name === 'View Reports'
    );
    reportsButton?.onClick();

    expect(mockRouter.navigate).toHaveBeenCalledWith(
      [
        '/dashboard/report-management/assessment-candidates/' +
          dispatchedAssessment.id,
      ],
      { queryParams: { fromAssessments: true } }
    );
    expect(component.selectedCardIndex).toBeNull();
  });

  it('should handle delete button click', () => {
    component.showCreateAssessmentButtons = true;
    component.onActionClick(dummyAssessmentWithTest, 0);

    const deleteButton = component.actionButtonItems.find(
      (item) => item.name === 'Delete'
    );
    deleteButton?.onClick();

    expect(component.showDeleteModal).toBe(true);
  });

  it('should handle empty assessmentId in navigateToDispatch', () => {
    component.assessmentId = undefined;
    component.assessmentTitle = 'Test Assessment';

    component.navigateToDispatch();

    expect(mockRouter.navigate).toHaveBeenCalledWith(
      [component.assessmentUrl + '/dispatch'],
      { queryParams: {} }
    );
  });

  it('should handle undefined dataForPreview in navigateToReports', () => {
    component.dataForPreview = undefined;

    component.navigateToReports();

    expect(component.selectedCardIndex).toBeNull();
  });

  it('should handle empty assessmentId in navigateToEdit', () => {
    component.assessmentId = undefined;
    const resetAssessmentSpy = jest.spyOn(
      component['assessmentStore'],
      'resetAssessment'
    );

    component.navigateToEdit();

    expect(resetAssessmentSpy).toHaveBeenCalled();
    expect(mockRouter.navigate).toHaveBeenCalledWith(
      [component.assessmentUrl + '/edit-assessments'],
      { queryParams: {} }
    );
  });

  describe('Real-time time updates functionality', () => {
    beforeEach(() => {
      // Reset mock implementation for each test
      mockTimeAgoService.getMultipleTimeAgo.mockReturnValue(
        of({
          '2023-01-01T10:00:00Z': '1 hour ago',
          '2023-01-01T11:00:00Z': '30 minutes ago',
        })
      );
      mockTimeAgoService.getTimeAgo.mockReturnValue('fallback time');
    });

    describe('updateTimeObservables', () => {
      it('should create empty observable when no assessments', () => {
        component.assessments = [];
        component['updateTimeObservables']();

        component.assessmentTimeUpdates$.subscribe((updates) => {
          expect(updates).toEqual({});
        });
      });

      it('should create time observables for assessments', () => {
        const mockAssessments: AssessmentWithTest[] = [
          {
            ...dummyAssessmentWithTest,
            id: '1',
            createdAt: '2023-01-01T10:00:00Z',
            updatedAt: '2023-01-01T11:00:00Z',
          },
          {
            ...dummyAssessmentWithTest,
            id: '2',
            createdAt: '2023-01-02T10:00:00Z',
            updatedAt: '2023-01-02T11:00:00Z',
          },
        ];

        component.assessments = mockAssessments;
        component['updateTimeObservables']();

        expect(
          component.timeAgoService.getMultipleTimeAgo
        ).toHaveBeenCalledWith([
          '2023-01-01T10:00:00Z',
          '2023-01-01T11:00:00Z',
          '2023-01-02T10:00:00Z',
          '2023-01-02T11:00:00Z',
        ]);
      });

      it('should handle assessments with missing timestamps', () => {
        const mockAssessments: AssessmentWithTest[] = [
          {
            ...dummyAssessmentWithTest,
            id: '1',
            createdAt: '2023-01-01T10:00:00Z',
            updatedAt: '',
          },
        ];

        component.assessments = mockAssessments;
        component['updateTimeObservables']();

        component.assessmentTimeUpdates$.subscribe((updates) => {
          expect(updates['1']).toEqual({
            created: '1 hour ago',
            updated: 'fallback time',
          });
        });
      });
    });

    describe('getTimeCreatedForAssessment', () => {
      it('should return time created observable for valid assessment', () => {
        component.assessmentTimeUpdates$ = of({
          '1': { created: '2 hours ago', updated: '1 hour ago' },
        });

        component.getTimeCreatedForAssessment('1').subscribe((time) => {
          expect(time).toBe('2 hours ago');
        });
      });

      it('should return empty string for non-existent assessment', () => {
        component.assessmentTimeUpdates$ = of({});

        component
          .getTimeCreatedForAssessment('non-existent')
          .subscribe((time) => {
            expect(time).toBe('');
          });
      });
    });

    describe('getTimeUpdatedForAssessment', () => {
      it('should return time updated observable for valid assessment', () => {
        component.assessmentTimeUpdates$ = of({
          '1': { created: '2 hours ago', updated: '1 hour ago' },
        });

        component.getTimeUpdatedForAssessment('1').subscribe((time) => {
          expect(time).toBe('1 hour ago');
        });
      });

      it('should return empty string for non-existent assessment', () => {
        component.assessmentTimeUpdates$ = of({});

        component
          .getTimeUpdatedForAssessment('non-existent')
          .subscribe((time) => {
            expect(time).toBe('');
          });
      });
    });

    describe('integration with assessments store', () => {
      it('should update time observables when assessments change', () => {
        const mockAssessments: AssessmentWithTest[] = [
          {
            ...dummyAssessmentWithTest,
            id: '1',
            createdAt: '2023-01-01T10:00:00Z',
            updatedAt: '2023-01-01T11:00:00Z',
          },
        ];

        // Set assessments directly and trigger the time observable update logic
        component.assessments = mockAssessments;
        component['updateTimeObservables'](); // Call private method directly to trigger mock

        // Test that the getTimeCreatedForAssessment method works
        component.getTimeCreatedForAssessment('1').subscribe((time) => {
          expect(typeof time).toBe('string');
        });

        expect(component.timeAgoService.getMultipleTimeAgo).toHaveBeenCalled();
      });

      it('should handle subscription cleanup on ngOnDestroy', () => {
        const subscription = new Subscription();
        component.subscriptions.push(subscription);
        const unsubscribeSpy = jest.spyOn(subscription, 'unsubscribe');

        component.ngOnDestroy();

        expect(unsubscribeSpy).toHaveBeenCalled();
      });
    });
  });
});
