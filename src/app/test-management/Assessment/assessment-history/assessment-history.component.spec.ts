import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AssessmentHistoryComponent } from './assessment-history.component';
import { ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { ToastService } from '../../../services/toast-service/toast.service';
import { of } from 'rxjs';
import { AssessmentService } from '../../../services/test-management-service/assessment-service/assessment.service';

describe('AssessmentHistoryComponent', () => {
  let component: AssessmentHistoryComponent;
  let fixture: ComponentFixture<AssessmentHistoryComponent>;
  let mockAssessmentService: jest.Mocked<AssessmentService>;
  let mockRouter: jest.Mocked<Router>;
  let mockLocation: jest.Mocked<Location>;
  let mockToastService: jest.Mocked<ToastService>;
  let mockDialog: jest.Mocked<MatDialog>;

  const mockAssessmentId = 'test-assessment-id';
  const mockAssessmentTitle = 'Test Assessment';

  const mockDispatchHistory = {
    data: {
      history: [
        {
          id: '1',
          dispatchType: 'GenericLink',
          dispatchLink: 'http://test.com/generic',
          status: 'Active',
          createdAt: new Date(),
          expireDate: new Date(),
          allowedEmailList: [],
        },
        {
          id: '2',
          dispatchType: 'CopyLink',
          dispatchLink: 'http://test.com/copy',
          status: 'Active',
          createdAt: new Date(),
          expireDate: new Date(),
          allowedEmailList: ['<EMAIL>'],
        },
        {
          id: '3',
          dispatchType: 'Email',
          dispatchLink: '',
          status: 'Active',
          createdAt: new Date(),
          expireDate: new Date(),
          allowedEmailList: ['<EMAIL>'],
        },
      ],
    },
  };

  beforeEach(async () => {
    mockAssessmentService = {
      getAssessmentDispatchHistory: jest
        .fn()
        .mockReturnValue(of(mockDispatchHistory)),
      getOneAssessment: jest
        .fn()
        .mockReturnValue(of({ data: { title: mockAssessmentTitle } })),
      allowedEmailsSubject: { next: jest.fn() },
      allowedEmails$: of(['<EMAIL>']),
    } as unknown as jest.Mocked<AssessmentService>;

    mockRouter = {
      navigate: jest.fn(),
    } as unknown as jest.Mocked<Router>;

    mockLocation = {
      back: jest.fn(),
    } as unknown as jest.Mocked<Location>;

    mockToastService = {
      onShow: jest.fn(),
    } as unknown as jest.Mocked<ToastService>;

    mockDialog = {
      open: jest.fn(),
    } as unknown as jest.Mocked<MatDialog>;

    await TestBed.configureTestingModule({
      imports: [AssessmentHistoryComponent],
      providers: [
        FormBuilder,
        { provide: AssessmentService, useValue: mockAssessmentService },
        { provide: Router, useValue: mockRouter },
        { provide: Location, useValue: mockLocation },
        { provide: ToastService, useValue: mockToastService },
        { provide: MatDialog, useValue: mockDialog },
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              paramMap: {
                get: () => mockAssessmentId,
              },
            },
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(AssessmentHistoryComponent);
    component = fixture.componentInstance;
    jest.spyOn(component, 'formatDate').mockReturnValue('mocked date');
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with assessment ID and fetch assessment title', () => {
    expect(component.assessmentId).toBe(mockAssessmentId);
    expect(mockAssessmentService.getOneAssessment).toHaveBeenCalledWith(
      mockAssessmentId
    );
    expect(component.assessmentTitle).toBe(mockAssessmentTitle);
  });

  it('should fetch and categorize dispatch history on init', () => {
    expect(
      mockAssessmentService.getAssessmentDispatchHistory
    ).toHaveBeenCalledWith(mockAssessmentId);
    expect(component.genericLinks.length).toBe(1);
    expect(component.copyLinkTypes).toBeDefined();
    expect(component.emailDispatchesType).toBeDefined();
  });

  it('should copy link to clipboard', () => {
    const mockLink = 'http://test.com';
    const mockClipboard = { writeText: jest.fn() };
    Object.assign(navigator, { clipboard: mockClipboard });

    component.copyLink(mockLink);

    expect(mockClipboard.writeText).toHaveBeenCalledWith(mockLink);
    expect(mockToastService.onShow).toHaveBeenCalledWith(
      'Copy Link',
      'Link copied to clipboard',
      true
    );
  });

  it('should call history.back when goBack is called', () => {
    const backSpy = jest.spyOn(window.history, 'back');
    component.goBack();
    expect(backSpy).toHaveBeenCalled();
  });

  it('should navigate to preview page', () => {
    component.navigateToPreview();
    expect(mockRouter.navigate).toHaveBeenCalledWith([
      `/dashboard/test-management/assessments/preview/${mockAssessmentId}`,
    ]);
  });

  it('should return correct icon for dispatch type', () => {
    expect(component.getTypeIcon('Email')).toBe('mail');
    expect(component.getTypeIcon('GenericLink')).toBe('link');
    expect(component.getTypeIcon('CopyLink')).toBe('link');
    expect(component.getTypeIcon('Invalid')).toBe('');
  });

  it('should #isValidEmail return true for valid email', () => {
    expect(component.isValidEmail('<EMAIL>')).toBe(true);
  });

  test('should return false if any date or time field is missing', () => {
    component.updateCopyLinkForm = {
      value: {
        commencementDate: '2025-03-27',
        expireDate: '',
        commencementTime: '12:00',
        expireTime: '14:00',
      },
    } as FormGroup;

    expect(component.validateCommenceDateTime()).toBe(false);
  });

  test('should return false if commencement or expire date is invalid', () => {
    component.updateCopyLinkForm = {
      value: {
        commencementDate: 'invalid-date',
        expireDate: '2025-03-27',
        commencementTime: '12:00',
        expireTime: '14:00',
      },
    } as FormGroup;

    expect(component.validateCommenceDateTime()).toBe(false);
  });

  test('should return false if any field is invalid', () => {
    jest.spyOn(component, 'isFieldInvalid').mockReturnValue(true);

    component.updateCopyLinkForm = {
      value: {
        commencementDate: '2025-03-27',
        expireDate: '2025-03-28',
        commencementTime: '12:00',
        expireTime: '14:00',
      },
    } as FormGroup;
    expect(component.validateCommenceDateTime()).toBe(false);
  });

  test('should return true if commence date is equal to or after expire date', () => {
    component.updateCopyLinkForm = new FormBuilder().group({
      commencementDate: '2025-03-28',
      expireDate: '2025-03-26',
      commencementTime: '15:00',
      expireTime: '14:00',
    });
    expect(component.validateCommenceDateTime()).toBe(true);
  });

  test('should return false if commence date is before expire date', () => {
    component.updateCopyLinkForm = new FormBuilder().group({
      commencementDate: '2025-03-27',
      expireDate: '2025-03-27',
      commencementTime: '12:00',
      expireTime: '14:00',
    });
    expect(component.validateCommenceDateTime()).toBe(false);
  });

  it('should #dateValidator return null if date is valid', () => {
    const date = new FormControl('2025-03-25');
    expect(component.dateValidator(date)).toEqual({
      pastDate: true,
    });
  });
  it('should #dateValidator return null if date is valid', () => {
    const date = new FormControl(new Date());
    expect(component.dateValidator(date)).toBeNull();
  });

  it('should #handleRemoveEmail remove email from allowedEmails', () => {
    component.updatedEmails = ['<EMAIL>'];
    component.handleRemoveEmail(0);
    expect(component.updatedEmails).toEqual([]);
  });

  it('should #handleAddEmail add email to allowedEmails', () => {
    const input: HTMLInputElement = document.createElement('input');
    input.value = '<EMAIL>';
    component.handleAddEmail(input);
    expect(component.updatedEmails).toEqual(['<EMAIL>']);
  });

  it('should #handleAddEmail not add  email if email is not valid ', () => {
    const input: HTMLInputElement = document.createElement('input');
    input.value = 'test@gmail';
    component.handleAddEmail(input);
    expect(mockToastService.onShow).toHaveBeenCalledWith(
      'Error',
      'Invalid email format',
      false,
      'error'
    );
  });
  it('should #handleAddEmail not add  email if email already exists ', () => {
    component.updatedEmails = ['<EMAIL>'];
    const input: HTMLInputElement = document.createElement('input');
    input.value = '<EMAIL>';
    component.handleAddEmail(input);
    expect(mockToastService.onShow).toHaveBeenCalledWith(
      'Error',
      'Email already added',
      false,
      'error'
    );
  });

  it('should #closeUpdateCopyLinkModal close the modal', () => {
    component.closeUpdateCopyLinkModal();
    expect(component.showUpdateCopyLinkModal()).toBeFalsy();
  });

  it('should #areEmailListsDifferent return false if email lists are the same', () => {
    component.updatedEmails = ['<EMAIL>'];
    component.originalEmails = ['<EMAIL>'];
    expect(component.areEmailListsDifferent()).toBeFalsy();
  });

  it('should #areEmailListsDifferent return true if email lists are different', () => {
    component.updatedEmails = ['<EMAIL>', '<EMAIL>'];
    component.originalEmails = ['<EMAIL>'];
    expect(component.areEmailListsDifferent()).toBeTruthy();
  });
});
