import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatDialogModule } from '@angular/material/dialog';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';
import { QuestionTitleComponent } from '../../Questions/main-questions-page/components/question-title/question-title.component';
import { TruncateLongTextPipe } from '../../../pipes/truncate-long-text/truncate-long-text.pipe';
import { CustomButtonComponent } from '../../../components/custombutton/custombutton.component';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastService } from '../../../services/toast-service/toast.service';
import { AssessmentService } from '../../../services/test-management-service/assessment-service/assessment.service';
import {
  DispatchHistory,
  History,
} from '../../../Interfaces/Types/assessmentInterface';
import { Subject, take, takeUntil } from 'rxjs';
import { checkMinDate } from '../../../utils/assessment/assessment';
import { CustomSearchInputComponent } from '../../../components/custom-search-input/custom-search-input.component';
import { CustomModalComponent } from '../../../components/custom-modal/custom-modal.component';
import { CustomInputComponent } from '../../../components/custom-input/custom-input.component';
import { AssessmentHistoryCardComponent } from './assessment-history-card/assessment-history-card.component';
import { emailSchema } from '../../../utils/constants';

@Component({
  selector: 'app-assessment-history',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatButtonModule,
    MatInputModule,
    MatDialogModule,
    MatTableModule,
    MatChipsModule,
    MatIconModule,
    QuestionTitleComponent,
    TruncateLongTextPipe,
    CustomButtonComponent,
    ReactiveFormsModule,
    CustomSearchInputComponent,
    CustomModalComponent,
    CustomInputComponent,
    AssessmentHistoryCardComponent,
  ],
  templateUrl: './assessment-history.component.html',
})
export class AssessmentHistoryComponent implements OnInit, OnDestroy {
  title = 'Assessment History';
  assessmentId!: string;
  assessmentTitle!: string;
  searchTerm: string = '';
  genericLinks: History[] = [];
  copyLinkTypes: History[] = [];
  emailDispatchesType: History[] = [];
  assessmentDispatchHistory!: DispatchHistory;
  readonly #destroyer: Subject<void> = new Subject();
  minDate!: string;
  updatedEmails: string[] = [];
  dispatchId!: string;
  updateCopyLinkForm!: FormGroup;
  showEmailListsModal = signal(false);
  details!: History;
  modalHeader!: string;
  emailLists!: string[];
  displayEmailLists: string[] = [];
  showGenericLinks = signal(false);
  isUpdateLinkDisabled = signal(true);
  isLoading = signal(false);
  originalExpireDate!: string;
  originalExpireTime!: string;
  originalEmails: string[] = [];
  constructor(
    private readonly router: Router,
    private readonly toast: ToastService,
    private readonly assessmentService: AssessmentService,
    private readonly route: ActivatedRoute,
    private readonly fb: FormBuilder
  ) {}
  initForm(): void {
    this.updateCopyLinkForm = this.fb.group({
      expireDate: new FormControl('', [
        Validators.required,
        this.dateValidator.bind(this),
      ]),
      expireTime: new FormControl('', Validators.required),
      commencementDate: ['', [this.dateValidator]],
      commencementTime: [''],
    });
  }
  ngOnInit(): void {
    this.initForm();

    this.minDate = checkMinDate();
    this.assessmentId = this.route.snapshot.paramMap.get('assessmentId')!;
    this.getAssessmentTitle(this.assessmentId);

    this.assessmentService
      .getAssessmentDispatchHistory(this.assessmentId)
      .pipe(takeUntil(this.#destroyer))
      .subscribe((dispatchHistory) => {
        this.assessmentDispatchHistory = dispatchHistory.data;
        this.genericLinks = this.assessmentDispatchHistory.history.filter(
          (history) => history.dispatchType === 'GenericLink'
        );
        this.copyLinkTypes = this.assessmentDispatchHistory.history.filter(
          (history) => history.dispatchType === 'CopyLink'
        );
        this.emailDispatchesType =
          this.assessmentDispatchHistory.history.filter(
            (history) => history.dispatchType === 'Email'
          );
      });
  }

  copyLink(link: string) {
    this.closeModal();
    if (link) {
      navigator.clipboard.writeText(link);
      this.toast.onShow('Copy Link', 'Link copied to clipboard', true);
    }
  }

  isLinkExpired(history: History): boolean {
    if (!history.expireDate) return false;
    return new Date(history.expireDate) < new Date();
  }

  formatDate(isoDateString?: string | Date): string {
    if (!isoDateString) return '';

    const date =
      isoDateString instanceof Date ? isoDateString : new Date(isoDateString);
    if (isNaN(date.getTime())) return '';

    const day = String(date.getUTCDate()).padStart(2, '0');
    const month = String(date.getUTCMonth() + 1).padStart(2, '0');
    const year = date.getUTCFullYear();

    let hours = date.getUTCHours();
    const minutes = String(date.getUTCMinutes()).padStart(2, '0');
    const amPm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12 || 12;

    return `${day}/${month}/${year} at ${hours}:${minutes} ${amPm}`;
  }

  goBack() {
    history.back();
  }

  navigateToPreview() {
    this.router.navigate([
      `/dashboard/test-management/assessments/preview/${this.assessmentId}`,
    ]);
  }

  getAssessmentTitle(id: string) {
    this.assessmentService
      .getOneAssessment(id)
      .pipe(take(1))
      .subscribe((assessment) => {
        this.assessmentTitle = assessment.data.title;
      });
  }

  getTypeIcon(type: string): string {
    switch (type) {
      case 'Email':
        return 'mail';
      case 'GenericLink':
        return 'link';
      case 'CopyLink':
        return 'link';
      default:
        return '';
    }
  }

  onSearch(searchTerm: string) {
    if (searchTerm !== '') {
      this.displayEmailLists = this.emailLists.filter((email) =>
        email.toLowerCase().includes(searchTerm.toLowerCase())
      );
    } else {
      this.displayEmailLists = this.emailLists;
    }
  }

  showLinkDetails(index: number, dispatchType: string) {
    this.showEmailListsModal.update((v) => !v);
    if (dispatchType === 'copyLink') {
      this.details = this.copyLinkTypes[index];
      this.emailLists = this.copyLinkTypes[index].allowedEmailList;
      this.displayEmailLists = this.copyLinkTypes[index].allowedEmailList;
      this.modalHeader = `Dispatch ${index + 1}`;
    } else if (dispatchType === 'email') {
      this.details = this.emailDispatchesType[index];
      this.emailLists = this.emailDispatchesType[index].allowedEmailList;
      this.displayEmailLists = this.emailDispatchesType[index].allowedEmailList;
      this.modalHeader = `Link ${index + 1}`;
    }
  }
  closeModal() {
    this.isLoading.set(false);
    this.showEmailListsModal.update((v) => !v);
    this.updateCopyLinkForm.reset();
  }

  showUpdateCopyLinkModal = signal(false);
  originalCommenceDate!: string;
  originalCommenceTime!: string;
  updateLink(index: number) {
    this.emailLists = this.copyLinkTypes[index].allowedEmailList;
    this.updatedEmails = [...this.copyLinkTypes[index].allowedEmailList];
    this.showUpdateCopyLinkModal.set(true);
    this.closeModal();

    const expiryDate = new Date(this.copyLinkTypes[index].expireDate);
    const commencementDate = new Date(this.copyLinkTypes[index].commenceDate);
    this.dispatchId = this.copyLinkTypes[index].id;

    this.originalExpireDate = expiryDate.toISOString().split('T')[0];
    this.originalExpireTime = expiryDate
      .toUTCString()
      .split(' ')[4]
      .slice(0, 5);
    this.originalEmails = [...this.updatedEmails];
    this.originalCommenceDate = commencementDate.toISOString().split('T')[0];
    this.originalCommenceTime = commencementDate
      .toUTCString()
      .split(' ')[4]
      .slice(0, 5);
    this.updateCopyLinkForm.patchValue({
      expireDate: this.originalExpireDate,
      expireTime: this.originalExpireTime,
      commencementTime: commencementDate
        .toUTCString()
        .split(' ')[4]
        .slice(0, 5),
      commencementDate: commencementDate.toISOString().split('T')[0],
    });

    this.isUpdateLinkDisabled.set(true);

    this.updateCopyLinkForm.valueChanges
      .pipe(takeUntil(this.#destroyer))
      .subscribe(() => {
        this.checkFormChanges();
      });
  }
  checkFormChanges() {
    const currentExpireDate = this.updateCopyLinkForm.get('expireDate')?.value;
    const currentExpireTime = this.updateCopyLinkForm.get('expireTime')?.value;
    const currentCommenceDate =
      this.updateCopyLinkForm.get('commencementDate')?.value;
    const currentCommenceTime =
      this.updateCopyLinkForm.get('commencementTime')?.value;

    const isDateChanged = currentExpireDate !== this.originalExpireDate;
    const isTimeChanged = currentExpireTime !== this.originalExpireTime;
    const isCommenceDateChanged =
      currentCommenceDate !== this.originalCommenceDate;
    const isCommenceTimeChanged =
      currentCommenceTime !== this.originalCommenceTime;
    const areEmailsChanged = this.areEmailListsDifferent();

    const isDateValid = !this.isFieldInvalid('expireDate');
    const isTimeValid = !this.isFieldInvalid('expireTime');
    const isCommenceDateValid = !this.isFieldInvalid('commencementDate');
    const isCommenceTimeValid = !this.isFieldInvalid('commencementTime');

    this.isUpdateLinkDisabled.set(
      !(
        (isDateChanged ||
          isTimeChanged ||
          areEmailsChanged ||
          isCommenceDateChanged ||
          isCommenceTimeChanged) &&
        isDateValid &&
        isTimeValid &&
        isCommenceDateValid &&
        isCommenceTimeValid &&
        !this.validateCommenceDateTime()
      )
    );
  }

  areEmailListsDifferent(): boolean {
    if (this.updatedEmails.length !== this.originalEmails.length) {
      return true;
    }

    return this.updatedEmails.some(
      (email, index) => email !== this.originalEmails[index]
    );
  }
  closeUpdateCopyLinkModal() {
    this.showUpdateCopyLinkModal.set(false);
  }
  handleAddEmail(email: HTMLInputElement) {
    const emailValue = email.value.trim();

    if (emailSchema.validate(emailValue).error) {
      this.toast.onShow('Error', 'Invalid email format', false, 'error');
      return;
    }
    if (this.updatedEmails.includes(emailValue)) {
      this.toast.onShow('Error', 'Email already added', false, 'error');
      return;
    }

    this.updatedEmails.push(emailValue);
    email.value = '';

    this.checkFormChanges();
  }

  handleRemoveEmail(index: number) {
    this.updatedEmails.splice(index, 1);

    this.checkFormChanges();
  }
  handleUpdateLink() {
    this.isLoading.set(true);
    Object.keys(this.updateCopyLinkForm.controls).forEach((field) => {
      const control = this.updateCopyLinkForm.get(field);
      control?.markAsTouched();
    });

    if (this.updatedEmails.length === 0) {
      this.toast.onShow(
        'Error',
        'Please add at least one email',
        false,
        'error'
      );
      return;
    }

    if (this.updateCopyLinkForm.invalid) {
      this.isLoading.set(false);
      this.toast.onShow(
        'Error',
        'Please correct the form errors',
        false,
        'error'
      );
      return;
    }

    const invalidEmails = this.updatedEmails.filter(
      (email) =>
        !/^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$/.test(email.toLowerCase())
    );

    if (invalidEmails.length > 0) {
      this.toast.onShow('Error', 'Some emails are invalid', false, 'error');
      return;
    }

    const expireDate = this.updateCopyLinkForm.get('expireDate')?.value;
    const expireTime = this.updateCopyLinkForm.get('expireTime')?.value;
    const commenceDate = this.updateCopyLinkForm.get('commencementDate')?.value;
    const commenceTime = this.updateCopyLinkForm.get('commencementTime')?.value;
    this.assessmentService
      .updateCopyLink(this.assessmentId, this.dispatchId, {
        expireDate: `${expireDate}T${expireTime}`,
        commenceDate: `${commenceDate}T${commenceTime}`,
        email: this.updatedEmails,
      })
      .pipe(takeUntil(this.#destroyer))
      .subscribe({
        next: () => {
          this.assessmentService
            .getAssessmentDispatchHistory(this.assessmentId)
            .pipe(take(1))
            .subscribe((dispatchHistory) => {
              this.assessmentDispatchHistory = dispatchHistory.data;
              this.genericLinks = this.assessmentDispatchHistory.history.filter(
                (history) => history.dispatchType === 'GenericLink'
              );
              this.copyLinkTypes =
                this.assessmentDispatchHistory.history.filter(
                  (history) => history.dispatchType === 'CopyLink'
                );
              this.emailDispatchesType =
                this.assessmentDispatchHistory.history.filter(
                  (history) => history.dispatchType === 'Email'
                );

              this.showUpdateCopyLinkModal.set(false);
              this.isLoading.set(false);
              this.toast.onShow(
                'Success',
                'Copy link updated successfully',
                true
              );
            });
        },
        error: () => {
          this.isLoading.set(false);
          this.toast.onShow(
            'Error',
            'Failed to update copy link',
            true,
            'error'
          );
        },
      });
  }

  dateValidator(control: FormControl): { [key: string]: boolean } | null {
    if (!control.value) return null;

    const selectedDate = new Date(control.value);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    return selectedDate >= today ? null : { pastDate: true };
  }

  isFieldInvalid(controlName: string): boolean {
    const isPastDateTime = (dateField: string, timeField: string): boolean => {
      const dateControl = this.updateCopyLinkForm.get(dateField);
      const timeControl = this.updateCopyLinkForm.get(timeField);

      if (dateControl?.value && timeControl?.value) {
        const selectedDate = new Date(
          `${dateControl.value}T${timeControl.value}`
        );
        const now = new Date();

        const isToday =
          selectedDate.getFullYear() === now.getFullYear() &&
          selectedDate.getMonth() === now.getMonth() &&
          selectedDate.getDate() === now.getDate();

        return isToday && selectedDate <= now;
      }
      return false;
    };

    if (
      controlName === 'expireTime' &&
      isPastDateTime('expireDate', 'expireTime')
    ) {
      return true;
    }

    if (
      controlName === 'commencementTime' &&
      isPastDateTime('commencementDate', 'commencementTime')
    ) {
      return true;
    }

    return false;
  }

  isValidEmail(email: string): boolean {
    if (!email) return true;

    return emailSchema.validate(email).error === undefined;
  }

  validateCommenceDateTime(): boolean {
    const { commencementDate, expireDate, commencementTime, expireTime } =
      this.updateCopyLinkForm.value;

    if (!commencementDate || !expireDate || !commencementTime || !expireTime)
      return false;

    const commence = new Date(`${commencementDate}T${commencementTime}`);
    const expire = new Date(`${expireDate}T${expireTime}`);

    if (isNaN(commence.getTime()) || isNaN(expire.getTime())) {
      return false;
    }

    if (
      this.isFieldInvalid('commencementDate') ||
      this.isFieldInvalid('commencementTime') ||
      this.isFieldInvalid('expireDate') ||
      this.isFieldInvalid('expireTime')
    )
      return false;
    if (commence >= expire) return true;
    return false;
  }

  ngOnDestroy(): void {
    this.#destroyer.next();
    this.#destroyer.unsubscribe();
  }
}
