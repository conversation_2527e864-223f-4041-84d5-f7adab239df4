<button
  (click)="onCardClicked(index)"
  class="bg-white rounded-lg p-6 shadow-sm w-[698px] flex justify-between gap-y-1 h-[119px]"
>
  <div class="flex flex-col gap-1">
    <div class="flex items-center gap-3">
      <p class="text-lg text-[#0C4767] font-medium">Dispatch {{ index + 1 }}</p>
      <button
        *ngIf="cardType !== 'email'"
        class="text-[#2F55F0] underline text-sm"
        (click)="copyLink(link.dispatchLink)"
      >
        Copy Link
      </button>
    </div>
    <div class="flex w-full h-[40px] justify-between flex-col items-start">
      <p class="text-xs text-[#474D66]" *ngIf="createdAt">
        <span class="text-[#0C4767] font-semibold"> Created: </span
        >{{ createdAt }}
      </p>
      <div class="flex gap-5" *ngIf="commencedDate && expireDate">
        <p class="text-xs text-gray-500">
          <span class="text-[#0C4767] font-semibold"> Commenced Date: </span>
          {{ commencedDate }}
        </p>
        <p class="text-xs text-gray-500">
          <span class="text-[#0C4767] font-semibold"> Expiry Date: </span>
          {{ expireDate }}
        </p>
      </div>
    </div>
  </div>
  <div class="flex flex-col gap-4 justify-between pt-3">
    <div
      class="w-28 h-7 flex items-center justify-center rounded-full text-xs"
      [class]="
        isLinkExpired
          ? 'text-[#B44949] bg-[#FFD9D0]'
          : 'bg-[#A8FFD0] text-[#0B6725]'
      "
    >
      {{ isLinkExpired ? 'Expired' : 'Active' }}
    </div>
    <button
      *ngIf="cardType !== 'email'"
      class="text-[#2F55F0] underline text-sm"
      (click)="updateLink(index)"
    >
      Update
    </button>
  </div>
</button>
