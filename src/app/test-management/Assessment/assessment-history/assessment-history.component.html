<div class="space-y-8">
  <div class="mb-6">
    <div class="flex justify-between items-start">
      <div>
        <app-question-title
          [questionType]="assessmentTitle | truncateLongText"
          (backRoute)="goBack()"
        ></app-question-title>
      </div>

      <app-custombutton variant="primary" (clicked)="navigateToPreview()">
        Preview Assessment
      </app-custombutton>
    </div>
  </div>
  <div class="flex flex-col gap-5">
    @if (copyLinkTypes.length > 0) {
      <div class="flex flex-col gap-3">
        <div>
          <p class="text-lg font-medium text-[#0C4767] mb-2 dispatch wrapper">
            Dispatch via Copied Link
          </p>
          <p class="text-xs font-normal text-[#474D66]">
            Anyone with this link can access the assessment
          </p>
        </div>
        <div class="flex flex-col gap-y-3">
          @for (link of copyLinkTypes; track $index) {
            <app-assessment-history-card
              [isLinkExpired]="isLinkExpired(link)"
              [createdAt]="formatDate(link?.createdAt)"
              [commencedDate]="formatDate(link?.commenceDate)"
              [expireDate]="formatDate(link?.expireDate)"
              [index]="$index"
              (updateLinkClicked)="updateLink($index)"
              (copyLinkClicked)="copyLink(link.dispatchLink)"
              (showDetails)="showLinkDetails($index, 'copyLink')"
              [link]="link"
            ></app-assessment-history-card>
          }
        </div>
      </div>
    } @else {
      <div class="bg-white rounded-lg p-6 shadow-sm text-gray-500 text-center">
        No Copied Links Available
      </div>
    }

    @if (emailDispatchesType.length > 0) {
      <div class="flex flex-col gap-3">
        <div>
          <p class="text-lg font-medium text-[#0C4767] mb-2">
            Dispatch via Email
          </p>
          <p class="text-xs font-normal text-[#474D66]">
            Anyone with this link can access the assessment
          </p>
        </div>
        <div class="flex flex-col gap-y-3">
          @for (link of emailDispatchesType; track $index) {
            <app-assessment-history-card
              [isLinkExpired]="isLinkExpired(link)"
              [createdAt]="formatDate(link?.createdAt)"
              [commencedDate]="formatDate(link?.commenceDate)"
              [expireDate]="formatDate(link?.expireDate)"
              [index]="$index"
              (showDetails)="showLinkDetails($index, 'email')"
              [link]="link"
              [cardType]="'email'"
            ></app-assessment-history-card>
          }
        </div>
      </div>
    } @else {
      <div class="bg-white rounded-lg p-6 shadow-sm text-gray-500 text-center">
        No Data for Dispatch by Email
      </div>
    }
  </div>
</div>
@if (showEmailListsModal()) {
  <app-custom-modal
    width="538px"
    [headerTitle]="modalHeader"
    [center]="false"
    (visibleModalChange)="closeModal()"
    ><p
      description
      class="text-xs text-gray-500"
      *ngIf="details && details.createdAt"
    >
      Created: {{ formatDate(details.createdAt) }}
    </p>
    <div class="px-[31px] flex flex-col gap-5">
      <app-custom-search-input
        placeholder="Search emails..."
        (searchTermChange)="onSearch($event)"
      ></app-custom-search-input>
      @if (displayEmailLists.length > 0) {
        <div class="max-h-[382px]">
          @for (email of displayEmailLists; track $index) {
            <p class="h-10 text-sm font-normal text-[#474D66]">{{ email }}</p>
          }
        </div>
      } @else {
        <p class="text-center text-sm font-normal text-[#474D66]">
          No emails available
        </p>
      }
    </div>
  </app-custom-modal>
}

@if (showUpdateCopyLinkModal()) {
  <app-custom-modal
    width="600px"
    headerTitle="Update Link"
    [center]="false"
    (visibleModalChange)="closeUpdateCopyLinkModal()"
  >
    <p class="text-xs text-gray-500" description>
      Add or remove email and extend links expiry date
    </p>
    <div class="px-[31px] flex flex-col w-[610px]">
      <form [formGroup]="updateCopyLinkForm" class="mb-2">
        <label for="commencementDate">
          <span class="text-[#474D66] font-medium text-[12px]"
            >Commencement Date</span
          >
          <app-custom-input
            id="commencementDate"
            placeholder="Enter commencement date"
            formControlName="commencementDate"
            type="date"
          ></app-custom-input>
          @if (isFieldInvalid('commencementDate')) {
            <p class="text-red-500 text-xs">
              Please select a valid future date
            </p>
          }
        </label>
        <label for="commencementTime">
          <span class="text-[#9ba0b5] font-medium text-[12px]"
            >Commencement Time</span
          >
          <app-custom-input
            id="commencementTime"
            placeholder="Enter commencencement time"
            formControlName="commencementTime"
            type="time"
          ></app-custom-input>
          @if (isFieldInvalid('commencementTime')) {
            <p class="text-red-500 text-xs">
              Please select a valid future time
            </p>
          }
        </label>
        <label for="expireDate">
          <span class="text-[#474D66] font-medium text-[12px]"
            >Expiry Date</span
          >
          <app-custom-input
            id="expireDate"
            placeholder="Enter Expiry Date"
            formControlName="expireDate"
            type="date"
          ></app-custom-input>
          @if (isFieldInvalid('expireDate')) {
            <p class="text-red-500 text-xs">
              Please select a valid future date
            </p>
          }
        </label>

        <label for="expireTime">
          <span class="text-[#474D66] font-medium text-[12px]"
            >Expiry Time</span
          >
          <app-custom-input
            id="expireTime"
            placeholder="Enter Expiry Time"
            formControlName="expireTime"
            type="time"
          ></app-custom-input>
          @if (isFieldInvalid('expireTime')) {
            <p class="text-red-500 text-xs">
              Please select a valid future time
            </p>
          }
        </label>
        @if (validateCommenceDateTime()) {
          <p class="text-red-500 text-xs">
            Commencement date should be before expiry date
          </p>
        }
        <div>
          <span class="text-[#474D66] font-medium text-[12px]">Email</span>
          <div
            class="flex items-center justify-between border rounded-lg w-full pl-2"
          >
            <input
              type="text"
              placeholder="Enter email"
              class="outline-none w-[68%]"
              #email
            />
            <app-custombutton
              [disabled]="!email.value || !isValidEmail(email.value)"
              (clicked)="handleAddEmail(email)"
              >Add email</app-custombutton
            >
          </div>
          @if (email.value && !isValidEmail(email.value)) {
            <p class="text-red-500 text-xs">Please enter a valid email</p>
          }
        </div>
        <div class="w-[548px] mt-3 mb-6 flex flex-wrap gap-2">
          @for (email of updatedEmails; track $index) {
            <div
              class="bg-[#BEBCBC] flex items-center gap-1 p-1 text-[#BEBCBC] text-sm rounded-md"
            >
              <span class="text-[#0C4767]">{{ email }}</span
              ><button
                (click)="handleRemoveEmail($index)"
                class="flex items-center justify-center"
              >
                <mat-icon class="text-[#A03737] text-sm">close</mat-icon>
              </button>
            </div>
          }
        </div>
        <div class="flex w-full items-center gap-5 mb-2">
          <app-custombutton
            variant="secondary"
            class="w-full"
            (clicked)="closeUpdateCopyLinkModal()"
            >Cancel</app-custombutton
          >
          @if (!isLoading()) {
            <app-custombutton
              class="w-full"
              [disabled]="isUpdateLinkDisabled() || updateCopyLinkForm.invalid"
              (clicked)="handleUpdateLink()"
              >Update Link</app-custombutton
            >
          } @else {
            <app-custombutton
              class="w-full"
              [spinner]="isLoading()"
            ></app-custombutton>
          }
        </div>
      </form>
    </div>
  </app-custom-modal>
}
