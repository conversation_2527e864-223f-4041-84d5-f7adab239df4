import {
  Component,
  HostListener,
  inject,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  ChangeDetectorRef,
} from '@angular/core';
import { AssessmentService } from '../../../services/test-management-service/assessment-service/assessment.service';
import {
  ActivatedRoute,
  NavigationEnd,
  Router,
  RouterOutlet,
  Event as NavigationEvent,
} from '@angular/router';
import {
  configurations,
  isValidTestData,
  navigateWithAssessmentId,
} from '../../../utils/assessment/assessment';
import { QuestionTitleComponent } from '../../Questions/main-questions-page/components/question-title/question-title.component';
import {
  BasicAndConfig,
  BasicInfoWithTest,
  GetOneAssessmentApiResponse,
} from '../../../Interfaces/Types/assessmentInterface';
import { TestsData } from '../../../Interfaces/Types/test';
import { AssessmentStore } from '../../../stores/assessments-store/assessment.store';
import { StepperComponent } from '../../../components/stepper/stepper.component';
import { filter, Subscription, take, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ToastService } from '../../../services/toast-service/toast.service';

@Component({
  selector: 'app-create-assessment',
  standalone: true,
  imports: [QuestionTitleComponent, RouterOutlet, StepperComponent],
  templateUrl: './create-assessment.component.html',
  styleUrl: './create-assessment.component.css',
})
export class CreateAssessmentComponent implements OnInit, OnDestroy {
  steps = ['Basic Information', 'Test Assignment', 'Preview & Save'];
  activeStepIndex = 0;
  assessmentId: string = '';
  isEditMode = false;
  isDataLoaded = false;
  proctors = configurations;
  selectedTests: TestsData[] = [];
  hideButtons = false;
  assessmentStore = inject(AssessmentStore);

  // Subject for handling component destruction and preventing memory leaks
  private readonly destroy$ = new Subject<void>();
  basicInfo: BasicAndConfig | undefined;
  subscription: Subscription[] = [];
  isValid = false;
  routeUrl = '';
  routesTracking: string[] = [];
  selectTestPageNumber = 1;
  previewPageNumber = 2;
  disabledStepNumbers: number[] = [
    this.selectTestPageNumber,
    this.previewPageNumber,
  ];

  @HostListener('window:beforeunload', ['$event'])
  beforeunloadHandler(): void {
    localStorage.setItem('basicInfo', JSON.stringify(this.basicInfo));
    localStorage.setItem('selectedTests', JSON.stringify(this.selectedTests));
  }

  constructor(
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router,
    private readonly assessmentService: AssessmentService,
    private readonly toast: ToastService,
    private readonly cdr: ChangeDetectorRef
  ) {}

  ngOnDestroy(): void {
    // Complete the destroy subject to trigger takeUntil for all subscriptions
    this.destroy$.next();
    this.destroy$.complete();

    // Additional cleanup for any remaining subscriptions in the array
    this.subscription.forEach((subscription) => subscription.unsubscribe());
    this.subscription = [];
    localStorage.removeItem('basicInfo');
    localStorage.removeItem('selectedTests');
  }

  ngOnInit() {
    this.updateRouteTracking();
    this.loadDataFromLocalStorage();
    this.setupQueryParams();
    this.editMode();
    this.setupStoreSubscriptions();
  }

  convertToHours(value: number, unit: string): number {
    if (!value || value <= 0) return 0;

    switch (unit) {
      case 'days':
        return value * 24;
      case 'months':
        return value * 24 * 30;
      default:
        return value * 24;
    }
  }

  convertFromHours(hours: number): { value: number; unit: 'days' | 'months' } {
    if (!hours || hours <= 0) {
      return { value: 1, unit: 'days' };
    }

    if (hours % 720 === 0) {
      return { value: hours / 720, unit: 'months' };
    }

    if (hours % 24 === 0) {
      return { value: hours / 24, unit: 'days' };
    }

    return { value: Math.ceil(hours / 24), unit: 'days' };
  }

  private updateRouteTracking(): void {
    this.updateRouteData();
    if (this.routesTracking.length !== 1) {
      this.routesTracking.push(this.router.url);
    }

    this.subscription.push(
      this.router.events
        .pipe(
          filter(
            (event: NavigationEvent): event is NavigationEnd =>
              event instanceof NavigationEnd
          ),
          takeUntil(this.destroy$)
        )
        .subscribe((navEnd: NavigationEnd) => {
          this.routesTracking.push(navEnd.urlAfterRedirects);
          this.routesTracking = [
            ...Array.from(new Set(this.routesTracking).values()),
          ];
          this.updateRouteData();
        })
    );

    if (this.router.url.includes('edit')) {
      this.isEditMode = true;
    }
  }

  private loadDataFromLocalStorage(): void {
    const basicInfo = localStorage.getItem('basicInfo');
    const selectedTests = localStorage.getItem('selectedTests');

    if (basicInfo && basicInfo !== 'undefined') {
      try {
        this.assessmentStore.setBasicInfo(JSON.parse(basicInfo));
      } catch (error) {
        console.error('Error parsing basicInfo:', error);
      }
    }

    if (selectedTests && selectedTests !== 'undefined') {
      try {
        this.assessmentStore.setSelectedTests(JSON.parse(selectedTests));
      } catch (error) {
        console.error('Error parsing selectedTests:', error);
      }
    }

    localStorage.removeItem('selectedTests');
    localStorage.removeItem('basicInfo');
  }

  private setupQueryParams(): void {
    this.subscription.push(
      this.activatedRoute.queryParams
        .pipe(takeUntil(this.destroy$))
        .subscribe((params) => {
          this.assessmentId = params['assessmentId'];
        })
    );
  }

  private setupStoreSubscriptions(): void {
    this.subscription.push(
      this.assessmentService.basicInfoAndConfig$
        .pipe(takeUntil(this.destroy$))
        .subscribe((basicInfoAndConfig) => {
          this.basicInfo = basicInfoAndConfig;
          this.isValid =
            (basicInfoAndConfig.isValid ?? false) &&
            isValidTestData({
              basicInfo: basicInfoAndConfig,
              tests: this.selectedTests,
            } as BasicInfoWithTest);

          // Update disabled steps when basicInfo changes
          this.updateDisabledSteps();

          // Trigger change detection to update stepper state
          this.cdr.detectChanges();
        })
    );

    this.subscription.push(
      this.assessmentStore.dataForPreview$
        .pipe(takeUntil(this.destroy$))
        .subscribe((item) => {
          this.isValid = isValidTestData(item as BasicInfoWithTest);
          this.selectedTests = item.tests;

          // Update basicInfo from store, but preserve service data structure if available
          if (item.basicInfo) {
            // If we have service data, merge it with store data to preserve validation state
            if (this.basicInfo?.isValid !== undefined) {
              this.basicInfo = {
                ...item.basicInfo,
                isValid: this.basicInfo.isValid, // Preserve form validation state
              };
            } else {
              this.basicInfo = item.basicInfo;
            }
          }

          // Update disabled steps when store data changes
          this.updateDisabledSteps();

          // Trigger change detection to update stepper state
          this.cdr.detectChanges();

          if (!this.isValid) {
            const testsWithNoQuestions = item.tests.filter(
              (test) =>
                !Array.isArray(test.questions) || test.questions.length === 0
            );

            if (testsWithNoQuestions.length > 0) {
              const testTitles = testsWithNoQuestions
                .map((test) => `• ${test.title}`)
                .join('\n');
              this.toast.onShow(
                'no questions in some tests',
                `The following test(s) have no questions:\n${testTitles}`,
                true,
                'error'
              );
            }
          }
        })
    );
  }

  routeToPage() {
    history.back();
  }

  proctorFeatures(
    response: GetOneAssessmentApiResponse,
    feature: { value: string }
  ) {
    return response.data.proctorFeatures.some(
      (item) => item.name === feature.value
    );
  }

  generatePageTitle() {
    if (this.routesTracking.length == 1) {
      return 'All Assessments';
    } else if (this.router.url.includes('BasicInfo-And-Config')) {
      this.routeUrl =
        this.routesTracking.find((route) =>
          route.includes('all-assessments')
        ) ?? '';
      return 'All Assessments';
    } else if (this.router.url.includes('Assign-Test-To-Assessment')) {
      this.routeUrl =
        this.routesTracking.find((route) =>
          route.includes('BasicInfo-And-Config')
        ) ?? '';
      return 'Basic Information And Config';
    } else if (this.router.url.includes('preview-assessments')) {
      if (this.isEditMode) {
        this.routeUrl = `/dashboard/test-management/assessments/edit-assessments/Assign-Test-To-Assessment?assessmentId=${this.assessmentId}`;
      } else {
        this.routeUrl =
          this.routesTracking.find((route) =>
            route.includes('Assign-Test-To-Assessment')
          ) ?? '';
      }
      return 'Assign Test To Assessment';
    }

    return 'All Assessments';
  }

  // Custom validation for enabling Test Assignment step
  // Only requires title, instructions, and passMark (the core form fields)
  private isBasicFormValid(basicInfo: BasicAndConfig | undefined): boolean {
    if (!basicInfo) {
      return false;
    }

    return (
      typeof basicInfo.title === 'string' &&
      basicInfo.title.trim() !== '' &&
      typeof basicInfo.instructions === 'string' &&
      basicInfo.instructions.trim() !== '' &&
      typeof basicInfo.passMark === 'number' &&
      basicInfo.passMark > 0
    );
  }

  /**
   * Updates the disabled steps array based on current form validation state
   * Step 1 (Test Assignment): Enabled when basic form fields are valid
   * Step 2 (Preview & Save): Enabled when all validation passes
   */
  private updateDisabledSteps(): void {
    const disabled: number[] = [];

    // Use more lenient validation for Test Assignment step
    // Only require title, instructions, and passMark
    if (!this.isBasicFormValid(this.basicInfo)) {
      disabled.push(1);
    }

    if (!this.isValid) {
      disabled.push(2);
    }

    this.disabledStepNumbers = disabled;
  }

  onStepClick(stepIndex: number) {
    if (this.disabledStepNumbers.includes(stepIndex)) {
      return;
    }

    const routes = [
      'BasicInfo-And-Config',
      'Assign-Test-To-Assessment',
      'preview-assessments',
    ];

    // Always sync current basicInfo to store before navigation
    // This ensures data persistence when navigating between steps
    if (this.basicInfo) {
      this.assessmentStore.setBasicInfo(this.basicInfo);
    }

    // If navigating to preview (step 2) from basic info (step 0),
    // get the latest data from the service and sync to store
    if (stepIndex === 2 && this.activeStepIndex === 0) {
      // Get the current latest value from the service (which has the converted data)
      const currentBasicInfo = this.basicInfo;
      if (currentBasicInfo) {
        this.assessmentStore.setBasicInfo(currentBasicInfo);
      }
    }

    navigateWithAssessmentId(
      this.router,
      this.isEditMode,
      this.assessmentId,
      routes[stepIndex]
    );

    this.activeStepIndex = stepIndex;

    this.cdr.detectChanges();
  }

  updateRouteData() {
    const childRoute = this.activatedRoute.firstChild;

    if (childRoute) {
      const sub = childRoute.data
        .pipe(takeUntil(this.destroy$))
        .subscribe((data) => {
          this.hideButtons = !!data['hideButtons'];

          if (this.router.url.includes('BasicInfo-And-Config')) {
            this.activeStepIndex = 0;
          } else if (data['index'] !== undefined) {
            this.activeStepIndex = data['index'];
          }
        });

      this.subscription.push(sub);
    }
  }

  editMode() {
    if (this.isEditMode && this.assessmentId) {
      this.subscription.push(
        this.assessmentStore.basicInfo$.pipe(take(1)).subscribe((basicInfo) => {
          if (!basicInfo?.title) {
            this.assessmentService
              .getOneAssessment(this.assessmentId)
              .subscribe({
                next: (response) => {
                  const proctorFeatures = this.proctors.map((feature) => {
                    if (feature.name === 'Enable Survey After Test') {
                      return {
                        ...feature,
                        selected: response.data.conductSurvey,
                      };
                    }

                    if (feature.name === 'Display Clock') {
                      return { ...feature, selected: response.data.showClock };
                    }

                    const isSelected = this.proctorFeatures(response, feature);
                    return { ...feature, selected: isSelected };
                  });

                  const retakeDelayData = this.convertFromHours(
                    response.data.retakeDelayHours ?? 0
                  );
                  const hasRetakeDelay =
                    !!response.data.retakeDelayHours &&
                    response.data.retakeDelayHours > 0;

                  const basicAndConfig = {
                    title: response.data.title,
                    instructions: response.data.instructions,
                    passMark: response.data.passMark,
                    camerashotsInterval: response.data.camerashotsInterval,
                    screenshotsInterval: response.data.screenshotsInterval,
                    configurations: proctorFeatures,
                    enableRetakeDelay: hasRetakeDelay,
                    retakeDelayDays: hasRetakeDelay ? retakeDelayData.value : 1,
                    retakeDelayUnit: hasRetakeDelay
                      ? retakeDelayData.unit
                      : 'days', // This ensures the correct unit is set
                    retakeDelayHours: response.data.retakeDelayHours,
                  };

                  this.assessmentStore.setBasicInfo(basicAndConfig);
                  this.assessmentStore.setSelectedTests(response.data.tests);
                  this.isDataLoaded = true;
                },
                error: () => {
                  this.toast.onShow(
                    'Load Failed',
                    'Failed to load assessment data for editing',
                    true,
                    'error'
                  );
                  this.router.navigate([
                    '/dashboard/test-management/assessments',
                  ]);
                },
              });
          }
        })
      );
    }
  }
}
