import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { CustomButtonComponent } from '../../../../components/custombutton/custombutton.component';
import {
  BasicAndConfig,
  BasicInfoWithTest,
  CreateAssessmentInterface,
  PreviewDataFormat,
} from '../../../../Interfaces/Types/assessmentInterface';
import { convertDurationPreview } from '../../../../utils/testManagement';
import { GeneralLongCardComponent } from '../../../../components/general-long-card/general-long-card.component';
import { TestsData } from '../../../../Interfaces/Types/test';
import { ActivatedRoute, Router } from '@angular/router';
import {
  distinctUntilChanged,
  map,
  Observable,
  Subscription,
  take,
} from 'rxjs';
import {
  isValidTestData,
  mapToBackendFormat,
  processConfigurations,
} from '../../../../utils/assessment/assessment';
import { AssessmentStore } from '../../../../stores/assessments-store/assessment.store';
import { AsyncPipe } from '@angular/common';
import { SafeHtmlDirective } from '../../../../directives/safe-html.directive';

@Component({
  selector: 'app-assessmemt-preview-creation',
  standalone: true,
  imports: [
    CustomButtonComponent,
    GeneralLongCardComponent,
    AsyncPipe,
    SafeHtmlDirective,
  ],
  templateUrl: './assessmemt-preview-creation.component.html',
})
export class AssessmemtPreviewCreationComponent implements OnInit, OnDestroy {
  isEdit = false;
  title = 'Assessment Preview';
  assessmentId = '';
  basicInformationForPreview: BasicAndConfig | undefined;
  testInformationForPreview: TestsData | undefined;
  previewData: PreviewDataFormat | undefined;
  selectedProctors$!: Observable<string[] | undefined>;
  proctorFeatures: string[] = [];
  assessmentDuration$!: Observable<number>;
  selectedTests: TestsData[] = [];
  basicInfoPreviewData$!: Observable<BasicAndConfig>;
  conductSurvey = false;
  isLoading = false;
  subscriptions: Subscription[] = [];
  isValid = false;
  securityIcons = [
    'assets/icons/asses-file.svg',
    'assets/icons/id-card.svg',
    'assets/icons/face-capturee.svg',
    'assets/icons/screen-capturee.svg',
    'assets/icons/idle-time.svg',
    'assets/icons/danger.svg',
    'assets/icons/clipboard.svg',
    'assets/icons/retake.svg',
  ];
  testIds!: string[];
  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly assessmentStore: AssessmentStore
  ) {}

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => subscription.unsubscribe());
  }
  ngOnInit(): void {
    if (this.router.url.includes('edit')) this.isEdit = true;

    this.route.queryParams.subscribe((params) => {
      this.assessmentId = params['assessmentId'];
    });

    this.subscriptions.push(
      this.assessmentStore.dataForPreview$
        .pipe(
          distinctUntilChanged(
            (prev, curr) => JSON.stringify(prev) === JSON.stringify(curr)
          )
        )
        .subscribe((item) => {
          this.isValid = isValidTestData(item as BasicInfoWithTest);
          this.basicInformationForPreview = {
            ...item.basicInfo,
            title: item.basicInfo?.title ?? '',
            instructions: item.basicInfo?.instructions ?? '',
            passMark: item.basicInfo?.passMark ?? 0,
            configurations: item.basicInfo?.configurations ?? [],
          };
          this.selectedTests = [...item.tests];
        })
    );

    this.subscriptions.push(
      this.assessmentStore.basicInfo$
        .pipe(distinctUntilChanged())
        .subscribe((basicInfo) => {
          if (basicInfo) {
            this.basicInformationForPreview = { ...basicInfo };
          }
        })
    );

    this.subscriptions.push(
      this.assessmentStore.selectedTests$
        .pipe(distinctUntilChanged())
        .subscribe((tests) => {
          this.selectedTests = [...tests];
        })
    );

    this.assessmentDuration$ = this.assessmentStore.selectedTestsTotalDuration$;
    this.selectedProctors$ = this.assessmentStore.proctors$;

    this.subscriptions.push(
      this.assessmentStore.createEditLoader$
        .pipe(distinctUntilChanged())
        .subscribe((loading: boolean) => {
          this.isLoading = loading;
        })
    );
  }

  getBackLabel() {
    return 'Test Assignment';
  }

  convertDuration(item: number | undefined) {
    if (!item) {
      return;
    }
    return convertDurationPreview(item);
  }

  getConfigurationsWithIntervals(): {
    name: string;
    interval?: number;
    iconIndex: number;
  }[] {
    const result: { name: string; interval?: number; iconIndex: number }[] = [];

    if (!this.basicInformationForPreview?.configurations) {
      return result;
    }

    this.basicInformationForPreview.configurations.forEach(
      (config: { name: string; selected: boolean }, index: number) => {
        if (config.selected) {
          const configItem: {
            name: string;
            interval?: number;
            iconIndex: number;
          } = {
            name: config.name,
            iconIndex: index,
          };

          if (
            config.name === 'Candidate Capture' &&
            this.basicInformationForPreview?.camerashotsInterval
          ) {
            configItem.interval = Number(
              this.basicInformationForPreview.camerashotsInterval
            );
          } else if (
            config.name === 'Screen Capture' &&
            this.basicInformationForPreview?.screenshotsInterval
          ) {
            configItem.interval = Number(
              this.basicInformationForPreview.screenshotsInterval
            );
          }

          result.push(configItem);
        }
      }
    );

    return result;
  }

  getRetakeDelayInfo(): { value: number; unit: string } | null {
    if (
      !this.basicInformationForPreview?.enableRetakeDelay ||
      !this.basicInformationForPreview?.retakeDelayDays
    ) {
      return null;
    }

    // The store contains backend format (days), but we need to display user-friendly format
    const daysValue = this.basicInformationForPreview.retakeDelayDays;

    // Convert back to the best display format
    const userFriendlyFormat = this.detectBestUnitFromDays(daysValue);

    return {
      value: userFriendlyFormat.value,
      unit: userFriendlyFormat.unit,
    };
  }

  private detectBestUnitFromDays(days: number): {
    unit: 'days' | 'months';
    value: number;
  } {
    if (!days || days <= 0) return { unit: 'days', value: 1 };

    // If it's divisible by 30 and >= 30, prefer months
    if (days >= 30 && days % 30 === 0) {
      return { unit: 'months', value: days / 30 };
    }

    // Otherwise use days
    return { unit: 'days', value: days };
  }

  getRetakeDelayDisplayText(): string {
    if (!this.basicInformationForPreview?.enableRetakeDelay) {
      return 'No retake delay';
    }

    const retakeInfo = this.getRetakeDelayInfo();
    if (!retakeInfo) {
      return 'No retake delay';
    }

    const { value, unit } = retakeInfo;
    let unitDisplay: string;
    if (unit === 'days') {
      unitDisplay = value === 1 ? 'day' : 'days';
    } else {
      unitDisplay = value === 1 ? 'month' : 'months';
    }

    return `${value} ${unitDisplay}`;
  }

  createOrEditAssessment() {
    this.isLoading = true;
    const settings = {
      showClock: true,
      conductSurvey: this.conductSurvey,
    };
    let selectedProctors: string[] = [];

    this.selectedProctors$
      .pipe(
        take(1),
        map((configs) => configs?.map((config) => mapToBackendFormat(config)))
      )
      .subscribe((proctors) => {
        if (proctors) {
          selectedProctors = [...proctors];
        }
      });

    this.testIds = this.selectedTests.map((test) => test.id);

    const processedConfigurations = processConfigurations(
      selectedProctors,
      settings
    );
    this.proctorFeatures = processedConfigurations.proctorFeatures;

    let retakeDelayValue: number | undefined = undefined;

    if (this.basicInformationForPreview?.enableRetakeDelay) {
      retakeDelayValue = this.basicInformationForPreview.retakeDelayDays;
    }

    const assessmentBody: CreateAssessmentInterface = {
      title: this.basicInformationForPreview?.title,
      instructions: this.basicInformationForPreview?.instructions,
      passMark: this.basicInformationForPreview?.passMark,
      retakeDelayDays: retakeDelayValue,

      proctorFeatures: this.proctorFeatures,
      tests: this.testIds,
      showClock: true,
      conductSurvey: processedConfigurations.conductSurvey,
    };

    const cameraShotIntervalConfig = this.proctorFeatures.find(
      (feature) => feature === 'Candidate Capture'
    );
    const screenShotIntervalConfig = this.proctorFeatures.find(
      (feature) => feature === 'Screen Capture'
    );

    if (cameraShotIntervalConfig) {
      assessmentBody['camerashotsInterval'] = this.basicInformationForPreview
        ?.camerashotsInterval as unknown as string;
    }
    if (screenShotIntervalConfig) {
      assessmentBody['screenshotsInterval'] = this.basicInformationForPreview
        ?.screenshotsInterval as unknown as string;
    }

    if (this.isEdit) {
      this.assessmentStore.editAssessment({
        assessment: assessmentBody,
        assessmentId: this.assessmentId,
      });
    } else {
      this.assessmentStore.createAssessment({ assessment: assessmentBody });
    }
  }
}
