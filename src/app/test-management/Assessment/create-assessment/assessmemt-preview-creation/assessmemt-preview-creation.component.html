<div class="bg-[#fafafa] rounded-lg p-6 mt-4">
  <div class="border-b border-[#E5E7EB] pb-3 mb-3">
    <h1 class="font-medium text-[1.25rem] text-[#082230E5]">Preview & Save</h1>
    <p class="text-[#474D66] text-[0.875rem]">
      Review your assessment configuration before saving. You can go back to
      make changes if needed.
    </p>
  </div>
  @if (basicInformationForPreview) {
    <section class="w-full mt-10">
      <div class="title mb-[14px]">
        <label
          for="assessmentTitle"
          class="text-[#262626] font-medium mb-1 gap-x-2"
          >Assessment Title
        </label>
        <p
          class="bg-[#0c47660a] rounded-md text-[#474D66] font-normal text-base p-6 mb-4"
        >
          {{ basicInformationForPreview.title }}
        </p>
      </div>

      <div class="instructions mb-[14px]">
        <label
          for="assessmentInstruction"
          class="text-[#262626] font-medium mb-1"
          >Assessment Instruction
        </label>
        <div
          class="bg-[#0c47660a] rounded-md text-[#474D66] font-normal text-base p-6 mb-4 break-all"
          [appSafeHtml]="basicInformationForPreview.instructions"
        ></div>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="space-y-1">
          <label for="passMark" class="text-[#262626] font-medium mb-1"
            >Pass Mark</label
          >
          <p class="bg-[#0c47660a] rounded-md text-[#474D66] text-sm p-3">
            {{ basicInformationForPreview.passMark }}
          </p>
        </div>
        <div class="test-assigned mb-[40px]">
          <p class="text-[#262626] font-medium mb-1">Assessment Duration</p>
          <p class="bg-[#0c47660a] rounded-md text-[#474D66] text-sm p-3">
            {{ convertDuration((assessmentDuration$ | async)!) }}
          </p>
        </div>
      </div>
      <div class="configurations mb-[40px] mt-[27px]">
        <p class="text-[#262626] font-medium mb-1">
          Security Configurations Selected
        </p>
        <ul
          class="grid grid-cols-1 md:grid-cols-2 gap-4 bg-[#0c47660a] rounded-md text-[#474D66] font-normal text-base p-6 mb-4 list-none pl-5 rounded-lg border border-[#E5E7EB]"
        >
          @for (
            item of getConfigurationsWithIntervals();
            track item.name;
            let i = $index
          ) {
            <li class="flex items-center gap-2">
              <img
                [src]="securityIcons[item.iconIndex]"
                alt="Security icon"
                class="w-5 h-5"
              />
              <p class="text-[#474D66] font-medium text-[1.125rem]">
                {{ item.name }}
                @if (item.interval) {
                  ({{ item.interval }} seconds)
                }
              </p>
            </li>
          }
        </ul>
      </div>

      <div class="flex gap-6 mb-[40px]">
        <div class="test-assigned w-1/2">
          <p class="text-[#262626] text-[16px] font-semibold">Test Assigned</p>
          <p class="bg-[#0c47660a] rounded-md text-[#474D66] text-sm p-3">
            {{ selectedTests.length }}
            {{ selectedTests.length === 1 ? 'test' : 'tests' }} assigned
          </p>
        </div>
        <div class="retake-delay w-1/2">
          <p class="text-[#262626] text-[16px] font-semibold">Retake Delay</p>
          <p class="bg-[#0c47660a] rounded-md text-[#474D66] text-sm p-3">
            {{ getRetakeDelayDisplayText() }}
          </p>
        </div>
      </div>
      <div class="list-of-test">
        @for (item of selectedTests; track $index) {
          <div class="surface:min-w-[22rem] min-w-[30rem]">
            <app-general-long-card
              [id]="item.id"
              [title]="item.title"
              [middleContent]="item.isActivated ? 'Active' : 'Inactive'"
              [textColor]="'white'"
              [showRightSide]="false"
            >
              <div
                class="leftSideBody1 flex items-center gap-x-2"
                #leftSideBody1
              >
                <div>{{ item.domain.name }}</div>
              </div>
              <p
                class="leftSideBody2 text-[#474D66] text-[12px]"
                #leftSideBody2
              >
                Duration: <span>{{ convertDuration(item.duration) }}</span>
              </p>
            </app-general-long-card>
          </div>
        }
      </div>
    </section>
  }
  <div class="flex flex-wrap justify-end">
    <app-custombutton
      class="ml-2"
      [disabled]="!isValid"
      [spinner]="isLoading"
      (clicked)="createOrEditAssessment()"
      >{{ isEdit && !isLoading ? 'Update Assessment' : '' }}
      {{ !isEdit && !isLoading ? 'Save Assessment' : '' }}
    </app-custombutton>
  </div>
</div>
