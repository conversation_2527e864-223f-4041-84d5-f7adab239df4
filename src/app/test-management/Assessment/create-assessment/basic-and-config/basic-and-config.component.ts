import { Component, <PERSON><PERSON>nit, <PERSON><PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CustomInputComponent } from '../../../../components/custom-input/custom-input.component';
import { CustomTextEditorComponent } from '../../../../components/custom-text-editor/custom-text-editor.component';
import { CustomButtonComponent } from '../../../../components/custombutton/custombutton.component';
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  configurations,
  proctoringDetails,
} from '../../../../utils/assessment/assessment';
import { ActivatedRoute, Router } from '@angular/router';
import { AssessmentStore } from '../../../../stores/assessments-store/assessment.store';
import {
  isFieldInvalid,
  getValidationErrorMessage,
} from '../../../../utils/testManagement';
import { AssessmentService as AssessmentServiceService } from '../../../../services/test-management-service/assessment-service/assessment.service';
import { Subscription, Subject } from 'rxjs';
import { debounceTime, takeUntil } from 'rxjs/operators';
import { CharacterLimitComponent } from '../../../../components/character-limit/character-limit.component';
import { SafeHtmlDirective } from '../../../../directives/safe-html.directive';
import { RequiredSvgComponent } from '../../../../components/required-svg/required-svg.component';

@Component({
  selector: 'app-basic-and-config',
  standalone: true,
  imports: [
    CustomInputComponent,
    CustomTextEditorComponent,
    CustomButtonComponent,
    ReactiveFormsModule,
    CommonModule,
    CharacterLimitComponent,
    SafeHtmlDirective,
    RequiredSvgComponent,
  ],
  templateUrl: './basic-and-config.component.html',
})
export class BasicAndConfigComponent implements OnInit, OnDestroy {
  BasicInfoAndConfigurationForm: FormGroup;
  title = 'Basic Information';
  proctoringDetails = proctoringDetails;
  isEdit = false;
  assessmentId = '';
  subscription: Subscription[] = [];

  // Subject for handling component destruction and preventing memory leaks
  private readonly destroy$ = new Subject<void>();

  // Track if we're currently updating to prevent circular conversions
  private isUpdatingFromConversion = false;

  constructor(
    private readonly fb: FormBuilder,
    private readonly assessmentService: AssessmentServiceService,
    private readonly assessmentStore: AssessmentStore,
    private readonly cdr: ChangeDetectorRef,
    private readonly router: Router,
    private readonly activatedRoute: ActivatedRoute
  ) {
    this.BasicInfoAndConfigurationForm = this.fb.group({
      title: ['', [Validators.required]],
      instructions: ['', [Validators.required]],
      passMark: ['', [Validators.required]],
      enableRetakeDelay: [false],
      retakeDelayDays: [1],
      retakeDelayUnit: ['days'],
      camerashotsInterval: [15, [Validators.min(15), Validators.max(120)]],
      screenshotsInterval: [15, [Validators.min(15), Validators.max(120)]],
      configurations: this.fb.array([]),
    });

    this.initFormWithDefaultConfigurations();

    this.subscription.push(
      this.BasicInfoAndConfigurationForm.valueChanges
        .pipe(debounceTime(300), takeUntil(this.destroy$))
        .subscribe(() => {
          // Prevent circular updates during conversion
          if (this.isUpdatingFromConversion) {
            return;
          }

          const formValue = this.BasicInfoAndConfigurationForm.getRawValue();

          let dataToSend;

          // Process retake delay conversion if enabled
          if (formValue.enableRetakeDelay) {
            const convertedDays = this.convertToDays(
              formValue.retakeDelayDays,
              formValue.retakeDelayUnit
            );

            dataToSend = {
              ...formValue,
              retakeDelayDays: convertedDays,
              // Ensure unit is always 'days' for backend consistency
              retakeDelayUnit: 'days',
              isValid: this.BasicInfoAndConfigurationForm.valid,
            };
          } else {
            // Send form data without conversion
            dataToSend = {
              ...formValue,
              isValid: this.BasicInfoAndConfigurationForm.valid,
            };
          }

          this.assessmentService.setBasicInfoAndConfig(dataToSend);
        })
    );

    this.subscription.push(
      this.assessmentStore.basicInfo$
        .pipe(takeUntil(this.destroy$))
        .subscribe((basicInfo) => {
          if (basicInfo) {
            const currentFormValue =
              this.BasicInfoAndConfigurationForm.getRawValue();
            const isFormEmpty =
              !currentFormValue.title || currentFormValue.title === '';
            const isBackendData = !!basicInfo.retakeDelayHours;

            if (isFormEmpty || isBackendData) {
              const patchData = {
                ...basicInfo,
                camerashotsInterval: basicInfo.camerashotsInterval ?? 15,
                screenshotsInterval: basicInfo.screenshotsInterval ?? 15,
              };

              // Auto-detect the best unit and value from backend hours
              if (basicInfo.retakeDelayHours) {
                const detected = this.detectBestUnitFromHours(
                  basicInfo.retakeDelayHours
                );
                patchData.retakeDelayDays = detected.value;
                patchData.retakeDelayUnit = detected.unit;
              } else if (basicInfo.retakeDelayDays && isBackendData) {
                const hoursEquivalent = basicInfo.retakeDelayDays * 24;
                const detected = this.detectBestUnitFromHours(hoursEquivalent);
                patchData.retakeDelayDays = detected.value;
                patchData.retakeDelayUnit = detected.unit;
              }

              // Set flag to prevent circular updates
              this.isUpdatingFromConversion = true;
              this.BasicInfoAndConfigurationForm.patchValue(patchData);

              setTimeout(() => {
                this.isUpdatingFromConversion = false;
              }, 0);
            }

            // Ensure the first configuration remains checked and disabled after patching
            if (
              basicInfo.configurations &&
              this.configurationsArray.length > 0
            ) {
              const firstConfig = this.configurationsArray.at(0);
              if (firstConfig) {
                firstConfig.patchValue({
                  selected: true,
                  locked: true,
                });
                firstConfig.get('selected')?.disable();
              }
            }
          }
        })
    );

    this.isEdit = this.router.url.includes('edit');
    if (this.isEdit) {
      this.subscription.push(
        this.activatedRoute.queryParams
          .pipe(takeUntil(this.destroy$))
          .subscribe((param: { [key: string]: string }) => {
            this.assessmentId = param['assessmentId'];
          })
      );
    }
  }

  securityIcons = [
    'assets/icons/asses-file.svg',
    'assets/icons/id-card.svg',
    'assets/icons/face-capturee.svg',
    'assets/icons/screen-capturee.svg',
    'assets/icons/idle-time.svg',
    'assets/icons/danger.svg',
    'assets/icons/clipboard.svg',
  ];

  ngOnInit(): void {
    const enableRetakeDelayControl =
      this.BasicInfoAndConfigurationForm.get('enableRetakeDelay');
    if (enableRetakeDelayControl) {
      const enableRetakeDelaySubscription =
        enableRetakeDelayControl.valueChanges
          .pipe(takeUntil(this.destroy$))
          .subscribe((enable) => {
            const retakeDelayControl =
              this.BasicInfoAndConfigurationForm.get('retakeDelayDays');
            const unitControl =
              this.BasicInfoAndConfigurationForm.get('retakeDelayUnit');

            if (enable) {
              retakeDelayControl?.setValidators([
                Validators.required,
                Validators.min(this.getMinRetakeDelay()),
              ]);
              unitControl?.setValidators([Validators.required]);

              retakeDelayControl?.setValue(1);
              retakeDelayControl?.enable();
            } else {
              retakeDelayControl?.clearValidators();
              unitControl?.clearValidators();
              retakeDelayControl?.setValue(null);
              retakeDelayControl?.disable();
            }

            retakeDelayControl?.updateValueAndValidity();
            unitControl?.updateValueAndValidity();
          });
      this.subscription.push(enableRetakeDelaySubscription);
    }

    const retakeDelayUnitControl =
      this.BasicInfoAndConfigurationForm.get('retakeDelayUnit');
    if (retakeDelayUnitControl) {
      const retakeDelayUnitSubscription = retakeDelayUnitControl.valueChanges
        .pipe(takeUntil(this.destroy$))
        .subscribe(() => {
          if (
            this.BasicInfoAndConfigurationForm.get('enableRetakeDelay')?.value
          ) {
            const retakeDelayControl =
              this.BasicInfoAndConfigurationForm.get('retakeDelayDays');

            const currentValue = retakeDelayControl?.value;

            if (!currentValue || currentValue <= 0) {
              this.isUpdatingFromConversion = true;
              retakeDelayControl?.setValue(1);

              // Reset flag immediately after setValue
              setTimeout(() => {
                this.isUpdatingFromConversion = false;
              }, 0);
            }

            retakeDelayControl?.setValidators([
              Validators.required,
              Validators.min(1),
            ]);

            retakeDelayControl?.updateValueAndValidity();

            // Force change detection for radio button UI updates
            this.cdr.detectChanges();
          }
        });
      this.subscription.push(retakeDelayUnitSubscription);
    }
  }

  ngOnDestroy(): void {
    // Complete the destroy subject to trigger takeUntil for all subscriptions
    this.destroy$.next();
    this.destroy$.complete();

    // Additional cleanup for any remaining subscriptions in the array
    this.subscription.forEach((subscription) => subscription.unsubscribe());
    this.subscription = [];
  }

  getMinRetakeDelay(): number {
    return 1;
  }

  isCameraShotsIntervalInvalid(): boolean {
    const control = this.getFormControl('camerashotsInterval');
    return control?.invalid && (control.dirty || control.touched);
  }

  isScreenShotsIntervalInvalid(): boolean {
    const control = this.getFormControl('screenshotsInterval');
    return control?.invalid && (control.dirty || control.touched);
  }

  getFormControl(control: string): FormControl {
    return this.BasicInfoAndConfigurationForm.get(control) as FormControl;
  }

  nextPage() {
    this.processFormDataForStore();

    const baseUrl = 'dashboard/test-management/assessments';
    const path = this.isEdit
      ? 'edit-assessments/Assign-Test-To-Assessment'
      : 'create-assessments/Assign-Test-To-Assessment';

    const queryParams = this.isEdit ? { assessmentId: this.assessmentId } : {};

    this.router.navigate([`${baseUrl}/${path}`], { queryParams });
  }

  processFormDataForStore() {
    const formValue = this.BasicInfoAndConfigurationForm.getRawValue();

    // We need to convert the UI values to backend format (days)
    // The form still shows user-friendly values like "3 months"
    const convertedDays = this.convertToDays(
      formValue.retakeDelayDays,
      formValue.retakeDelayUnit
    );

    const dataToStore = {
      ...formValue,
      // Convert the current form values to days for backend
      retakeDelayDays: convertedDays,
      // Ensure unit is always 'days' for backend consistency
      retakeDelayUnit: 'days',
    };

    this.assessmentStore.setBasicInfo(dataToStore);
  }

  onRadioClick(unit: string) {
    this.BasicInfoAndConfigurationForm.get('retakeDelayUnit')?.setValue(unit);

    this.cdr.detectChanges();
  }

  onRadioKeydown(event: KeyboardEvent, unit: string) {
    // Prevent default behavior for space and enter keys
    if (event.key === ' ' || event.key === 'Enter') {
      event.preventDefault();
      this.onRadioClick(unit);
    }
  }

  cancel() {
    // Navigate to assessments page to cancel the whole process
    const baseUrl = 'dashboard/test-management/all-assessments';
    this.router.navigate([baseUrl]);
  }

  get configurationsArray(): FormArray {
    return this.BasicInfoAndConfigurationForm.get(
      'configurations'
    ) as FormArray;
  }

  checkMinMax(control: string) {
    if (
      this.getFormControl(control).value < 15 ||
      this.getFormControl(control).value > 120
    ) {
      return false;
    }
    return true;
  }

  onIntervalInputChange(controlName: string, event: Event) {
    const input = event.target as HTMLInputElement;
    const value = input.value;
    const numValue = parseInt(value);
    if (!value || isNaN(numValue) || numValue < 15) {
      setTimeout(() => {
        this.BasicInfoAndConfigurationForm.get(controlName)?.setValue(15);
      }, 0);
    }
  }

  onIntervalInputBlur(controlName: string, event: Event) {
    const input = event.target as HTMLInputElement;
    const value = input.value;
    const numValue = parseInt(value);

    if (!value || isNaN(numValue) || numValue < 15) {
      this.BasicInfoAndConfigurationForm.get(controlName)?.setValue(15);
    } else if (numValue > 120) {
      this.BasicInfoAndConfigurationForm.get(controlName)?.setValue(120);
    }
  }

  onIntervalKeyDown(event: KeyboardEvent) {
    const input = event.target as HTMLInputElement;
    const value = input.value;

    const allowedKeys = [
      'Backspace',
      'Delete',
      'Tab',
      'Escape',
      'Enter',
      'Decimal',
      'Home',
      'End',
      'ArrowLeft',
      'ArrowRight',
      'ArrowUp',
      'ArrowDown',
    ];
    if (
      allowedKeys.includes(event.key) ||
      (event.key.toLowerCase() === 'a' && event.ctrlKey === true) ||
      (event.key.toLowerCase() === 'c' && event.ctrlKey === true) ||
      (event.key.toLowerCase() === 'v' && event.ctrlKey === true) ||
      (event.key.toLowerCase() === 'x' && event.ctrlKey === true)
    ) {
      if (
        (event.key === 'Backspace' || event.key === 'Delete') &&
        value.length <= 2
      ) {
        event.preventDefault();
        return;
      }
      return;
    }

    if (!/^\d$/.test(event.key)) {
      event.preventDefault();
    }
  }

  toggleControl(index: number, setting: { name: string; selected: boolean }) {
    if (index === 2) {
      if (setting.selected) {
        this.BasicInfoAndConfigurationForm.addControl(
          'camerashotsInterval',
          new FormControl(15, [
            Validators.required,
            Validators.min(15),
            Validators.max(120),
          ])
        );

        const camerashotsIntervalControl =
          this.BasicInfoAndConfigurationForm.get('camerashotsInterval');
        if (camerashotsIntervalControl) {
          const camerashotsIntervalSubscription =
            camerashotsIntervalControl.valueChanges
              .pipe(takeUntil(this.destroy$))
              .subscribe((value) => {
                if (!value || value === '' || value < 15) {
                  this.BasicInfoAndConfigurationForm.get(
                    'camerashotsInterval'
                  )?.setValue(15, { emitEvent: false });
                }
              });
          this.subscription.push(camerashotsIntervalSubscription);
        }
      } else {
        this.BasicInfoAndConfigurationForm.removeControl('camerashotsInterval');
      }
    } else if (index === 3) {
      if (setting.selected) {
        this.BasicInfoAndConfigurationForm.addControl(
          'screenshotsInterval',
          new FormControl(15, [
            Validators.required,
            Validators.min(15),
            Validators.max(120),
          ])
        );

        const screenshotsIntervalControl =
          this.BasicInfoAndConfigurationForm.get('screenshotsInterval');
        if (screenshotsIntervalControl) {
          const screenshotsIntervalSubscription =
            screenshotsIntervalControl.valueChanges
              .pipe(takeUntil(this.destroy$))
              .subscribe((value) => {
                if (!value || value === '' || value < 15) {
                  this.BasicInfoAndConfigurationForm.get(
                    'screenshotsInterval'
                  )?.setValue(15, { emitEvent: false });
                }
              });
          this.subscription.push(screenshotsIntervalSubscription);
        }
      } else {
        this.BasicInfoAndConfigurationForm.removeControl('screenshotsInterval');
      }
    }
  }

  convertToDays(value: number, unit: string): number {
    if (!value || value <= 0) return 0;

    let result: number;
    switch (unit) {
      case 'days':
        result = value; // Already in days, no conversion needed
        break;
      case 'months':
        result = value * 30; // Convert months to days (30 days per month)
        break;
      default:
        result = value; // Default to days
    }

    return result;
  }

  detectBestUnitFromHours(hours: number): {
    unit: 'days' | 'months';
    value: number;
  } {
    if (!hours || hours <= 0) return { unit: 'days', value: 1 };

    // Convert hours to days first
    const days = Math.round(hours / 24);

    // If it's divisible by 30 and >= 30, prefer months
    if (days >= 30 && days % 30 === 0) {
      return { unit: 'months', value: days / 30 };
    }

    // Otherwise use days
    return { unit: 'days', value: days };
  }

  convertFromHours(hours: number, unit: string): number {
    if (!hours || hours <= 0) return 0;

    // First convert hours to days
    const days = Math.round(hours / 24);

    let result: number;
    switch (unit) {
      case 'days':
        result = days;
        break;
      case 'months':
        result = Math.round(days / 30);
        break;
      default:
        result = days;
    }

    return result;
  }

  isFieldInvalid(fieldName: string): boolean {
    return isFieldInvalid(fieldName, this.BasicInfoAndConfigurationForm);
  }

  getErrorMessage(fieldName: string): string {
    return getValidationErrorMessage(
      fieldName,
      this.BasicInfoAndConfigurationForm
    );
  }

  initFormWithDefaultConfigurations() {
    this.configurationsArray.clear();

    configurations.forEach((config, index) => {
      const isFirst = index === 0;
      const formGroup = this.fb.group({
        selected: [isFirst], // First one is always selected (true)
        name: [config.name],
        value: [config.value],
        locked: [isFirst], // First one is always locked (true)
      });

      // Disable the selected control if it's locked (first configuration)
      if (isFirst) {
        formGroup.get('selected')?.disable();
      }

      this.configurationsArray.push(formGroup);
    });
  }
}
