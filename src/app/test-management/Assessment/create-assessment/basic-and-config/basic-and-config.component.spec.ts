import { ComponentFixture, TestBed } from '@angular/core/testing';
import {
  Form<PERSON>uilder,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Location } from '@angular/common';
import { BasicAndConfigComponent } from './basic-and-config.component';
import { SharedService } from '../../../../services/shared/shared.service';
import { AssessmentService } from '../../../../services/test-management-service/assessment-service/assessment.service';
import { of } from 'rxjs';
import * as TestManagementUtils from '../../../../utils/testManagement';
import { AssessmentStore } from '../../../../stores/assessments-store/assessment.store';

describe('BasicAndConfigComponent', () => {
  let component: BasicAndConfigComponent;
  let fixture: ComponentFixture<BasicAndConfigComponent>;
  let mockAssessmentStore: jest.Mocked<AssessmentStore>;
  let mockRouter: jest.Mocked<Router>;
  let mockSharedService: jest.Mocked<SharedService>;
  let mockAssessmentService: jest.Mocked<AssessmentService>;
  let mockLocation: jest.Mocked<Location>;

  const mockFormBuilder = new FormBuilder();

  // Set test timeout to prevent hanging
  jest.setTimeout(10000);

  beforeEach(async () => {
    const mockActivatedRoute = {
      queryParams: of({
        assessmentId: '123',
      }),
    };

    mockRouter = {
      url: '/test-url',
      navigate: jest.fn(),
    } as unknown as jest.Mocked<Router>;

    mockSharedService = {
      setBreadcrumbDetails: jest.fn(),
      removeMenuItems: jest.fn(),
    } as unknown as jest.Mocked<SharedService>;

    mockAssessmentStore = {
      setBasicInfo: jest.fn(),
      basicInfo$: of(null),
    } as unknown as jest.Mocked<AssessmentStore>;

    mockAssessmentService = {
      setBasicInfoAndConfig: jest.fn(),
    } as unknown as jest.Mocked<AssessmentService>;

    mockLocation = {
      back: jest.fn(),
    } as unknown as jest.Mocked<Location>;

    TestBed.configureTestingModule({
      imports: [ReactiveFormsModule, BasicAndConfigComponent],
      providers: [
        { provide: AssessmentStore, useValue: mockAssessmentStore },
        { provide: Router, useValue: mockRouter },
        { provide: SharedService, useValue: mockSharedService },
        { provide: AssessmentService, useValue: mockAssessmentService },
        { provide: FormBuilder, useValue: mockFormBuilder },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: Location, useValue: mockLocation },
      ],
    }).compileComponents();
    fixture = TestBed.createComponent(BasicAndConfigComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  afterEach(() => {
    // Clean up component and fixture
    if (component) {
      component.ngOnDestroy();
    }
    if (fixture) {
      fixture.destroy();
    }

    // Clear any pending timers
    jest.clearAllTimers();
    jest.clearAllMocks();

    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with required controls', () => {
    expect(component.BasicInfoAndConfigurationForm.get('title')).toBeTruthy();
    expect(
      component.BasicInfoAndConfigurationForm.get('instructions')
    ).toBeTruthy();
    expect(
      component.BasicInfoAndConfigurationForm.get('configurations')
    ).toBeTruthy();
  });

  it('should validate title field as required', () => {
    const titleControl = component.BasicInfoAndConfigurationForm.get('title');
    expect(titleControl?.errors?.['required']).toBeTruthy();

    titleControl?.setValue('Test Title');
    expect(titleControl?.errors).toBeNull();
  });

  it('should validate instructions field as required', () => {
    const instructionsControl =
      component.BasicInfoAndConfigurationForm.get('instructions');
    expect(instructionsControl?.errors?.['required']).toBeTruthy();

    instructionsControl?.setValue('Test Instructions');
    expect(instructionsControl?.errors).toBeNull();
  });

  it('should toggle camera shots interval control correctly', () => {
    const setting = { name: 'Camera Shots', selected: true };

    component.toggleControl(2, setting);
    expect(
      component.BasicInfoAndConfigurationForm.get('camerashotsInterval')
    ).toBeTruthy();

    setting.selected = false;
    component.toggleControl(2, setting);
    expect(
      component.BasicInfoAndConfigurationForm.get('camerashotsInterval')
    ).toBeNull();
  });

  it('should toggle screen shots interval control correctly', () => {
    const setting = { name: 'Screen Shots', selected: true };

    component.toggleControl(3, setting);
    expect(
      component.BasicInfoAndConfigurationForm.get('screenshotsInterval')
    ).toBeTruthy();

    setting.selected = false;
    component.toggleControl(3, setting);
    expect(
      component.BasicInfoAndConfigurationForm.get('screenshotsInterval')
    ).toBeNull();
  });

  it('should check min/max values correctly', () => {
    const control = new FormControl('');
    component.BasicInfoAndConfigurationForm.addControl('testControl', control);

    control.setValue('14');
    expect(component.checkMinMax('testControl')).toBeFalsy();

    control.setValue('30');
    expect(component.checkMinMax('testControl')).toBeTruthy();

    control.setValue('121');
    expect(component.checkMinMax('testControl')).toBeFalsy();
  });

  describe('isCameraShotsIntervalInvalid', () => {
    it('should return true when camera shots interval control is invalid, dirty and touched', () => {
      const formControl = new FormControl('', Validators.required);
      formControl.markAsDirty();
      formControl.markAsTouched();
      component.BasicInfoAndConfigurationForm = new FormGroup({
        camerashotsInterval: formControl,
      });

      const result = component.isCameraShotsIntervalInvalid();

      expect(result).toBe(true);
    });

    it('should return false when camera shots interval control is null', () => {
      component.BasicInfoAndConfigurationForm = new FormGroup({});

      const result = component.isCameraShotsIntervalInvalid();

      expect(result).toBe(undefined);
    });
  });

  describe('isScreenShotsIntervalInvalid', () => {
    it('should return false when screenshots interval control is null', () => {
      const mockControl = new FormControl();
      jest.spyOn(component, 'getFormControl').mockReturnValue(mockControl);

      const result = component.isScreenShotsIntervalInvalid();

      expect(result).toBe(false);
    });
  });

  it('should return the correct error message from utility', () => {
    const fieldName = 'title';
    const mockMessage = 'Title is required';

    // Arrange: create a form group with the field
    component.BasicInfoAndConfigurationForm = new FormGroup({
      title: new FormControl('', Validators.required),
    });

    // Spy and mock the utility function
    const spy = jest
      .spyOn(TestManagementUtils, 'getValidationErrorMessage')
      .mockReturnValue(mockMessage);

    // Act
    const result = component.getErrorMessage(fieldName);

    // Assert
    expect(spy).toHaveBeenCalledWith(
      fieldName,
      component.BasicInfoAndConfigurationForm
    );
    expect(result).toBe(mockMessage);

    // Cleanup
    spy.mockRestore();
  });

  describe('Retake Delay Functionality', () => {
    beforeEach(() => {
      component.BasicInfoAndConfigurationForm =
        component.BasicInfoAndConfigurationForm ||
        new FormGroup({
          enableRetakeDelay: new FormControl(false),
          retakeDelayDays: new FormControl(1),
          retakeDelayUnit: new FormControl('days'),
        });
    });

    afterEach(() => {
      // Clean up form subscriptions
      if (component.BasicInfoAndConfigurationForm) {
        component.BasicInfoAndConfigurationForm.reset();
      }
    });

    describe('enableRetakeDelay toggle', () => {
      it('should add validators when enableRetakeDelay is true', () => {
        const enableControl =
          component.BasicInfoAndConfigurationForm.get('enableRetakeDelay');
        const retakeDelayControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayDays');
        const unitControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayUnit');

        enableControl?.setValue(true);

        retakeDelayControl?.setValidators([
          Validators.required,
          Validators.min(1),
        ]);
        unitControl?.setValidators([Validators.required]);

        retakeDelayControl?.setValue(1);
        unitControl?.setValue('days');
        retakeDelayControl?.updateValueAndValidity();
        unitControl?.updateValueAndValidity();

        expect(
          retakeDelayControl?.value === 24 || retakeDelayControl?.value === 1
        ).toBeTruthy();
        expect(
          unitControl?.value === 'hours' || unitControl?.value === 'days'
        ).toBeTruthy();
        expect(retakeDelayControl?.valid).toBeTruthy();
        expect(unitControl?.valid).toBeTruthy();
      });

      it('should remove validators when enableRetakeDelay is false', () => {
        const enableControl =
          component.BasicInfoAndConfigurationForm.get('enableRetakeDelay');
        const retakeDelayControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayDays');
        const unitControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayUnit');

        enableControl?.setValue(true);
        enableControl?.setValue(false);

        expect(retakeDelayControl?.errors).toBeNull();
        expect(unitControl?.errors).toBeNull();
        expect(retakeDelayControl?.value).toBeNull();
      });

      it('should set default value to 1 when enabled with days unit', () => {
        const enableControl =
          component.BasicInfoAndConfigurationForm.get('enableRetakeDelay');
        const retakeDelayControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayDays');
        const unitControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayUnit');

        unitControl?.setValue('days');
        enableControl?.setValue(true);

        expect(retakeDelayControl?.value).toBe(1);
      });
    });

    describe('retakeDelayUnit changes', () => {
      beforeEach(() => {
        component.BasicInfoAndConfigurationForm.get(
          'enableRetakeDelay'
        )?.setValue(true);
      });

      it('should update retakeDelay value and validators when unit changes to days', () => {
        const retakeDelayControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayDays');
        const unitControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayUnit');

        unitControl?.setValue('days');

        expect(retakeDelayControl?.value).toBe(1);
        retakeDelayControl?.setValue(0);
        expect(retakeDelayControl?.hasError('min')).toBeTruthy();
      });

      it('should update retakeDelay value and validators when unit changes to hours', () => {
        const retakeDelayControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayDays');
        const unitControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayUnit');

        unitControl?.setValue('hours');

        expect(retakeDelayControl?.value).toBe(1);
        retakeDelayControl?.setValue(0);
        expect(retakeDelayControl?.hasError('min')).toBeTruthy();
      });

      it('should not update values when enableRetakeDelay is false', () => {
        const enableControl =
          component.BasicInfoAndConfigurationForm.get('enableRetakeDelay');
        const retakeDelayControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayDays');
        const unitControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayUnit');

        enableControl?.setValue(false);
        const initialValue = retakeDelayControl?.value;
        unitControl?.setValue('days');

        expect(retakeDelayControl?.value).toBe(initialValue);
      });
    });
  });

  describe('Retake Delay Functionality - Enhanced Tests', () => {
    beforeEach(() => {
      component.BasicInfoAndConfigurationForm = mockFormBuilder.group({
        enableRetakeDelay: [false],
        retakeDelayDays: [1],
        retakeDelayUnit: ['days'],
        title: ['', Validators.required],
        instructions: ['', Validators.required],
        passMark: ['', Validators.required],
        configurations: mockFormBuilder.array([]),
      });
    });

    afterEach(() => {
      // Clean up form subscriptions and reset
      if (component.BasicInfoAndConfigurationForm) {
        component.BasicInfoAndConfigurationForm.reset();
      }
    });

    describe('Initial State', () => {
      it('should have retake delay disabled by default', () => {
        const enableControl =
          component.BasicInfoAndConfigurationForm.get('enableRetakeDelay');
        expect(enableControl?.value).toBe(false);
      });

      it('should have default retake delay value of 1', () => {
        const retakeDelayControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayDays');
        expect(retakeDelayControl?.value).toBe(1);
      });

      it('should have default retake delay unit as days', () => {
        const unitControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayUnit');
        expect(unitControl?.value).toBe('days');
      });
    });

    describe('Enable Retake Delay Toggle', () => {
      it('should add required validators when enableRetakeDelay is set to true', () => {
        const enableControl =
          component.BasicInfoAndConfigurationForm.get('enableRetakeDelay');
        const retakeDelayControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayDays');
        const unitControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayUnit');

        enableControl?.setValue(true);

        retakeDelayControl?.setValidators([
          Validators.required,
          Validators.min(1),
        ]);
        unitControl?.setValidators([Validators.required]);
        retakeDelayControl?.updateValueAndValidity();
        unitControl?.updateValueAndValidity();

        expect(
          retakeDelayControl?.hasValidator(Validators.required)
        ).toBeTruthy();
        expect(unitControl?.hasValidator(Validators.required)).toBeTruthy();
      });

      it('should clear validators when enableRetakeDelay is set to false', () => {
        const enableControl =
          component.BasicInfoAndConfigurationForm.get('enableRetakeDelay');
        const retakeDelayControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayDays');
        const unitControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayUnit');

        enableControl?.setValue(true);
        retakeDelayControl?.setValidators([
          Validators.required,
          Validators.min(1),
        ]);
        unitControl?.setValidators([Validators.required]);

        enableControl?.setValue(false);
        retakeDelayControl?.clearValidators();
        unitControl?.clearValidators();
        retakeDelayControl?.setValue(null);
        retakeDelayControl?.updateValueAndValidity();
        unitControl?.updateValueAndValidity();

        expect(retakeDelayControl?.errors).toBeNull();
        expect(unitControl?.errors).toBeNull();
        expect(retakeDelayControl?.value).toBeNull();
      });

      it('should set retakeDelay to 1 when enabled', () => {
        const enableControl =
          component.BasicInfoAndConfigurationForm.get('enableRetakeDelay');
        const retakeDelayControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayDays');

        enableControl?.setValue(true);
        retakeDelayControl?.setValue(1);

        expect(retakeDelayControl?.value).toBe(1);
      });
    });

    describe('Retake Delay Validation', () => {
      beforeEach(() => {
        const enableControl =
          component.BasicInfoAndConfigurationForm.get('enableRetakeDelay');
        const retakeDelayControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayDays');
        const unitControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayUnit');

        enableControl?.setValue(true);
        retakeDelayControl?.setValidators([
          Validators.required,
          Validators.min(1),
        ]);
        unitControl?.setValidators([Validators.required]);
        retakeDelayControl?.updateValueAndValidity();
        unitControl?.updateValueAndValidity();
      });

      it('should be invalid when retakeDelay is empty', () => {
        const retakeDelayControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayDays');

        retakeDelayControl?.setValue('');
        retakeDelayControl?.updateValueAndValidity();

        expect(retakeDelayControl?.hasError('required')).toBeTruthy();
        expect(retakeDelayControl?.invalid).toBeTruthy();
      });

      it('should be invalid when retakeDelay is less than minimum (1)', () => {
        const retakeDelayControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayDays');

        retakeDelayControl?.setValue(0);
        retakeDelayControl?.updateValueAndValidity();

        expect(retakeDelayControl?.hasError('min')).toBeTruthy();
        expect(retakeDelayControl?.invalid).toBeTruthy();
      });

      it('should be valid when retakeDelay is 1 or greater', () => {
        const retakeDelayControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayDays');

        retakeDelayControl?.setValue(1);
        retakeDelayControl?.updateValueAndValidity();
        expect(retakeDelayControl?.valid).toBeTruthy();

        retakeDelayControl?.setValue(24);
        retakeDelayControl?.updateValueAndValidity();
        expect(retakeDelayControl?.valid).toBeTruthy();

        retakeDelayControl?.setValue(100);
        retakeDelayControl?.updateValueAndValidity();
        expect(retakeDelayControl?.valid).toBeTruthy();
      });

      it('should be invalid when retakeDelayUnit is empty', () => {
        const unitControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayUnit');

        unitControl?.setValue('');
        unitControl?.updateValueAndValidity();

        expect(unitControl?.hasError('required')).toBeTruthy();
        expect(unitControl?.invalid).toBeTruthy();
      });

      it('should be valid with valid unit values', () => {
        const unitControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayUnit');

        unitControl?.setValue('hours');
        unitControl?.updateValueAndValidity();
        expect(unitControl?.valid).toBeTruthy();

        unitControl?.setValue('days');
        unitControl?.updateValueAndValidity();
        expect(unitControl?.valid).toBeTruthy();
      });
    });

    describe('Unit Change Behavior', () => {
      beforeEach(() => {
        const enableControl =
          component.BasicInfoAndConfigurationForm.get('enableRetakeDelay');
        enableControl?.setValue(true);
      });

      it('should reset retakeDelay to 1 when unit changes and retake delay is enabled', () => {
        const retakeDelayControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayDays');
        const unitControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayUnit');

        retakeDelayControl?.setValue(5);

        unitControl?.setValue('hours');
        retakeDelayControl?.setValue(1);
        retakeDelayControl?.setValidators([
          Validators.required,
          Validators.min(1),
        ]);
        retakeDelayControl?.updateValueAndValidity();

        expect(retakeDelayControl?.value).toBe(1);
      });

      it('should not change retakeDelay when unit changes but retake delay is disabled', () => {
        const enableControl =
          component.BasicInfoAndConfigurationForm.get('enableRetakeDelay');
        const retakeDelayControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayDays');
        const unitControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayUnit');

        enableControl?.setValue(false);
        const initialValue = 5;
        retakeDelayControl?.setValue(initialValue);

        unitControl?.setValue('hours');

        expect(retakeDelayControl?.value).toBe(initialValue);
      });
    });

    describe('getMinRetakeDelay Method', () => {
      it('should return minimum retake delay value of 1', () => {
        const minDelay = component.getMinRetakeDelay();
        expect(minDelay).toBe(1);
      });
    });

    describe('Form Integration', () => {
      it('should include retake delay fields in form value when enabled', () => {
        const enableControl =
          component.BasicInfoAndConfigurationForm.get('enableRetakeDelay');
        const retakeDelayControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayDays');
        const unitControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayUnit');

        enableControl?.setValue(true);
        retakeDelayControl?.setValue(2);
        unitControl?.setValue('days');

        const formValue = component.BasicInfoAndConfigurationForm.getRawValue();

        expect(formValue.enableRetakeDelay).toBe(true);
        expect(formValue.retakeDelayDays).toBe(2);
        expect(formValue.retakeDelayUnit).toBe('days');
      });

      it('should have null retakeDelay when disabled', () => {
        const enableControl =
          component.BasicInfoAndConfigurationForm.get('enableRetakeDelay');
        const retakeDelayControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayDays');

        enableControl?.setValue(false);
        retakeDelayControl?.setValue(null);

        const formValue = component.BasicInfoAndConfigurationForm.getRawValue();

        expect(formValue.enableRetakeDelay).toBe(false);
        expect(formValue.retakeDelayDays).toBeNull();
      });

      it('should affect form validity when retake delay is enabled but invalid', () => {
        const enableControl =
          component.BasicInfoAndConfigurationForm.get('enableRetakeDelay');
        const retakeDelayControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayDays');

        enableControl?.setValue(true);
        retakeDelayControl?.setValidators([
          Validators.required,
          Validators.min(1),
        ]);
        retakeDelayControl?.setValue('');
        retakeDelayControl?.updateValueAndValidity();

        expect(component.BasicInfoAndConfigurationForm.invalid).toBeTruthy();
      });
    });

    describe('Edge Cases', () => {
      it('should handle decimal values in retakeDelay', () => {
        const enableControl =
          component.BasicInfoAndConfigurationForm.get('enableRetakeDelay');
        const retakeDelayControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayDays');

        enableControl?.setValue(true);
        retakeDelayControl?.setValidators([
          Validators.required,
          Validators.min(1),
        ]);
        retakeDelayControl?.setValue(1.5);
        retakeDelayControl?.updateValueAndValidity();

        expect(retakeDelayControl?.valid).toBeTruthy();
      });

      it('should handle very large values in retakeDelay', () => {
        const enableControl =
          component.BasicInfoAndConfigurationForm.get('enableRetakeDelay');
        const retakeDelayControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayDays');

        enableControl?.setValue(true);
        retakeDelayControl?.setValidators([
          Validators.required,
          Validators.min(1),
        ]);
        retakeDelayControl?.setValue(9999);
        retakeDelayControl?.updateValueAndValidity();

        expect(retakeDelayControl?.valid).toBeTruthy();
      });

      it('should handle string values that can be converted to numbers', () => {
        const enableControl =
          component.BasicInfoAndConfigurationForm.get('enableRetakeDelay');
        const retakeDelayControl =
          component.BasicInfoAndConfigurationForm.get('retakeDelayDays');

        enableControl?.setValue(true);
        retakeDelayControl?.setValidators([
          Validators.required,
          Validators.min(1),
        ]);
        retakeDelayControl?.setValue('5');
        retakeDelayControl?.updateValueAndValidity();

        expect(retakeDelayControl?.valid).toBeTruthy();
      });
    });
  });

  describe('Input event handlers', () => {
    it('should set value to 15 when input is empty in onIntervalInputChange', (done) => {
      const event = { target: { value: '' } } as unknown as Event;

      component.onIntervalInputChange('camerashotsInterval', event);

      setTimeout(() => {
        const value = component.BasicInfoAndConfigurationForm.get(
          'camerashotsInterval'
        )?.value;
        expect(value).toBe(15);
        done();
      }, 10);
    });

    it('should set value to 15 when input is less than 15 in onIntervalInputChange', (done) => {
      const event = { target: { value: '10' } } as unknown as Event;

      component.onIntervalInputChange('camerashotsInterval', event);

      setTimeout(() => {
        const value = component.BasicInfoAndConfigurationForm.get(
          'camerashotsInterval'
        )?.value;
        expect(value).toBe(15);
        done();
      }, 10);
    });

    it('should not change value when input is valid in onIntervalInputChange', () => {
      const event = { target: { value: '30' } } as unknown as Event;
      component.BasicInfoAndConfigurationForm.get(
        'camerashotsInterval'
      )?.setValue(30);

      component.onIntervalInputChange('camerashotsInterval', event);

      expect(
        component.BasicInfoAndConfigurationForm.get('camerashotsInterval')
          ?.value
      ).toBe(30);
    });

    it('should set value to 15 when input is less than 15 in onIntervalInputBlur', () => {
      const event = { target: { value: '10' } } as unknown as Event;

      component.onIntervalInputBlur('camerashotsInterval', event);

      expect(
        component.BasicInfoAndConfigurationForm.get('camerashotsInterval')
          ?.value
      ).toBe(15);
    });

    it('should set value to 120 when input is greater than 120 in onIntervalInputBlur', () => {
      const event = { target: { value: '150' } } as unknown as Event;

      component.onIntervalInputBlur('camerashotsInterval', event);

      expect(
        component.BasicInfoAndConfigurationForm.get('camerashotsInterval')
          ?.value
      ).toBe(120);
    });

    it('should keep valid value unchanged in onIntervalInputBlur', () => {
      const event = { target: { value: '30' } } as unknown as Event;
      component.BasicInfoAndConfigurationForm.get(
        'camerashotsInterval'
      )?.setValue(30);

      component.onIntervalInputBlur('camerashotsInterval', event);

      expect(
        component.BasicInfoAndConfigurationForm.get('camerashotsInterval')
          ?.value
      ).toBe(30);
    });

    it('should allow numeric keys in onIntervalKeyDown', () => {
      const mockEvent = {
        key: '5',
        target: { value: '30' },
        preventDefault: jest.fn(),
      } as unknown as KeyboardEvent;

      component.onIntervalKeyDown(mockEvent);
      expect(mockEvent.preventDefault).not.toHaveBeenCalled();
    });

    it('should allow special keys in onIntervalKeyDown', () => {
      const allowedKeys = [
        'Backspace',
        'Delete',
        'Tab',
        'ArrowLeft',
        'ArrowRight',
      ];

      for (const key of allowedKeys) {
        const mockEvent = {
          key,
          target: { value: '300' }, // Use longer value to avoid preventDefault for Backspace/Delete
          preventDefault: jest.fn(),
        } as unknown as KeyboardEvent;

        component.onIntervalKeyDown(mockEvent);
        expect(mockEvent.preventDefault).not.toHaveBeenCalled();
      }
    });

    it('should prevent non-numeric keys in onIntervalKeyDown', () => {
      const mockEvent = {
        key: 'a',
        target: { value: '30' },
        preventDefault: jest.fn(),
      } as unknown as KeyboardEvent;

      const result = component.onIntervalKeyDown(mockEvent);

      expect(result).toBeFalsy();
      expect(mockEvent.preventDefault).toHaveBeenCalled();
    });
  });

  describe('Radio button interactions', () => {
    describe('onRadioClick', () => {
      it('should update retakeDelayUnit when radio is clicked', () => {
        component.onRadioClick('months');

        expect(
          component.BasicInfoAndConfigurationForm.get('retakeDelayUnit')?.value
        ).toBe('months');
      });
    });

    describe('onRadioKeydown', () => {
      it('should trigger radio click on Enter key', () => {
        const event = new KeyboardEvent('keydown', { key: 'Enter' });
        jest.spyOn(event, 'preventDefault');
        jest.spyOn(component, 'onRadioClick');

        component.onRadioKeydown(event, 'months');

        expect(event.preventDefault).toHaveBeenCalled();
        expect(component.onRadioClick).toHaveBeenCalledWith('months');
      });

      it('should trigger radio click on Space key', () => {
        const event = new KeyboardEvent('keydown', { key: ' ' });
        jest.spyOn(event, 'preventDefault');
        jest.spyOn(component, 'onRadioClick');

        component.onRadioKeydown(event, 'days');

        expect(event.preventDefault).toHaveBeenCalled();
        expect(component.onRadioClick).toHaveBeenCalledWith('days');
      });

      it('should not trigger radio click on other keys', () => {
        const event = new KeyboardEvent('keydown', { key: 'a' });
        jest.spyOn(event, 'preventDefault');
        jest.spyOn(component, 'onRadioClick');

        component.onRadioKeydown(event, 'days');

        expect(event.preventDefault).not.toHaveBeenCalled();
        expect(component.onRadioClick).not.toHaveBeenCalled();
      });
    });
  });

  describe('Navigation methods', () => {
    describe('cancel', () => {
      it('should navigate to all-assessments page', () => {
        component.cancel();

        expect(mockRouter.navigate).toHaveBeenCalledWith([
          'dashboard/test-management/all-assessments',
        ]);
      });
    });
  });

  describe('Form validation helpers', () => {
    describe('checkMinMax', () => {
      beforeEach(() => {
        component.BasicInfoAndConfigurationForm.addControl(
          'testControl',
          new FormControl(30)
        );
      });

      it('should return false for values less than 15', () => {
        component.BasicInfoAndConfigurationForm.get('testControl')?.setValue(
          10
        );

        const result = component.checkMinMax('testControl');

        expect(result).toBe(false);
      });

      it('should return false for values greater than 120', () => {
        component.BasicInfoAndConfigurationForm.get('testControl')?.setValue(
          150
        );

        const result = component.checkMinMax('testControl');

        expect(result).toBe(false);
      });

      it('should return true for values within range', () => {
        component.BasicInfoAndConfigurationForm.get('testControl')?.setValue(
          30
        );

        const result = component.checkMinMax('testControl');

        expect(result).toBe(true);
      });
    });

    describe('isFieldInvalid', () => {
      it('should return true for invalid required field', () => {
        const titleControl =
          component.BasicInfoAndConfigurationForm.get('title');
        titleControl?.setValue('');
        titleControl?.markAsTouched();

        const result = component.isFieldInvalid('title');

        expect(result).toBe(true);
      });

      it('should return false for valid field', () => {
        const titleControl =
          component.BasicInfoAndConfigurationForm.get('title');
        titleControl?.setValue('Valid Title');

        const result = component.isFieldInvalid('title');

        expect(result).toBe(false);
      });
    });
  });

  describe('Unit conversion methods', () => {
    describe('convertToDays', () => {
      it('should convert days to days (no change)', () => {
        const result = component.convertToDays(5, 'days');
        expect(result).toBe(5);
      });

      it('should convert months to days', () => {
        const result = component.convertToDays(2, 'months');
        expect(result).toBe(60); // 2 months * 30 days
      });

      it('should handle zero values', () => {
        const result = component.convertToDays(0, 'days');
        expect(result).toBe(0);
      });

      it('should handle negative values', () => {
        const result = component.convertToDays(-5, 'days');
        expect(result).toBe(0);
      });

      it('should default to days for unknown units', () => {
        const result = component.convertToDays(5, 'unknown');
        expect(result).toBe(5);
      });
    });

    describe('detectBestUnitFromHours', () => {
      it('should return days for small hour values', () => {
        const result = component.detectBestUnitFromHours(48); // 2 days
        expect(result).toEqual({ unit: 'days', value: 2 });
      });

      it('should return months for hour values divisible by 720 (30 days)', () => {
        const result = component.detectBestUnitFromHours(720); // 30 days = 1 month
        expect(result).toEqual({ unit: 'months', value: 1 });
      });

      it('should return default for zero hours', () => {
        const result = component.detectBestUnitFromHours(0);
        expect(result).toEqual({ unit: 'days', value: 1 });
      });

      it('should return days for values not divisible by 720', () => {
        const result = component.detectBestUnitFromHours(840); // 35 days
        expect(result).toEqual({ unit: 'days', value: 35 });
      });
    });

    describe('convertFromHours', () => {
      it('should convert hours to days', () => {
        const result = component.convertFromHours(48, 'days'); // 48 hours = 2 days
        expect(result).toBe(2);
      });

      it('should convert hours to months', () => {
        const result = component.convertFromHours(720, 'months'); // 720 hours = 30 days = 1 month
        expect(result).toBe(1);
      });

      it('should handle zero hours', () => {
        const result = component.convertFromHours(0, 'days');
        expect(result).toBe(0);
      });

      it('should default to days for unknown units', () => {
        const result = component.convertFromHours(48, 'unknown');
        expect(result).toBe(2);
      });
    });
  });

  describe('processFormDataForStore', () => {
    it('should convert form data to store format', () => {
      component.BasicInfoAndConfigurationForm.patchValue({
        title: 'Test Assessment',
        instructions: 'Test Instructions',
        retakeDelayDays: 1,
        retakeDelayUnit: 'months',
        enableRetakeDelay: true,
      });

      component.processFormDataForStore();

      expect(mockAssessmentStore.setBasicInfo).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'Test Assessment',
          instructions: 'Test Instructions',
          retakeDelayDays: 30, // 1 month converted to days
          retakeDelayUnit: 'days',
          enableRetakeDelay: true,
        })
      );
    });
  });
});
