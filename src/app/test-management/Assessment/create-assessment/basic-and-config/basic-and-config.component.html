<form
  [formGroup]="BasicInfoAndConfigurationForm"
  class="h-[calc(100vh-350px)] mt-4"
>
  <main class="bg-[#fafafa] rounded-lg border-b-[#082230] p-6">
    <div class="flex flex-col mt-4">
      <label
        class="text-[#262626] flex items-center font-medium mb-1 gap-x-2"
        for="assessmentTitle"
      >
        Assessment title
        <app-required-svg />
      </label>
      <div>
        <app-custom-input
          [maxLength]="70"
          [placeholder]="'Type title here'"
          [id]="'title'"
          [formControl]="getFormControl('title')"
        ></app-custom-input>
        <div class="flex justify-between relative my-1">
          <div
            *ngIf="
              getFormControl('title').invalid && getFormControl('title').touched
            "
            class="text-red-500 text-xs"
          >
            Title is required
          </div>
          <div class="absolute -mt-3 right-0">
            <app-character-limit
              [limit]="70"
              [control]="getFormControl('title')"
            >
            </app-character-limit>
          </div>
        </div>
      </div>

      <label
        for="testTitle"
        class="text-[#262626] font-medium mb-1 flex items-center gap-x-2"
        >Assessment Instructions
        <app-required-svg />
      </label>
      <div>
        <app-custom-text-editor
          [placeholder]="'Type instruction here'"
          [formControl]="getFormControl('instructions')"
        ></app-custom-text-editor>
      </div>
      <div
        *ngIf="
          getFormControl('instructions').invalid &&
          getFormControl('instructions').touched
        "
        class="text-red-500 text-xs mt-1"
      >
        Instructions are required
      </div>

      <div class="flex flex-col mt-4 w-[30%]">
        <label
          for="passMark"
          class="text-[#262626] font-medium mb-1 flex items-center gap-x-2"
          >Pass Mark (%)
          <app-required-svg />
        </label>
        <input
          type="number"
          class="bg-white text-[#474D66] text-base font-normal rounded-lg h-[40px] px-3 mb-2 outline-none focus:border border-[0.5px]"
          [placeholder]="'Type pass mark here'"
          formControlName="passMark"
          [min]="1"
          [max]="100"
          [class]="
            isFieldInvalid('passMark')
              ? 'border-red-500 focus:border-red-500'
              : 'focus:border-[#0C4767]'
          "
        />
        @if (isFieldInvalid('passMark')) {
          <span class="text-sm text-red-500 text-right">
            {{ getErrorMessage('passMark') }}
          </span>
        }
      </div>
      <p class="text-[#262626] font-medium mb-[24px] mt-[32px]">
        Security Configurations
      </p>

      <div formArrayName="configurations" class="grid gap-4">
        @for (
          setting of configurationsArray.controls;
          track setting;
          let i = $index
        ) {
          <div class="flex items-start gap-4">
            <div class="bg-white rounded-lg border border-[#E1E3EA] p-4 flex-1">
              <div [formGroupName]="i" class="flex items-start gap-4">
                <label
                  class="inline-flex items-center mt-1"
                  [class.cursor-not-allowed]="setting.value.locked"
                  [class.cursor-pointer]="!setting.value.locked"
                >
                  <input
                    #check
                    type="checkbox"
                    formControlName="selected"
                    class="hidden"
                    (click)="
                      setting.value.locked ? $event.preventDefault() : null
                    "
                    (keydown.space)="
                      setting.value.locked ? $event.preventDefault() : null
                    "
                  />
                  <span
                    class="relative w-5 h-5 border rounded transition-all duration-200 flex-shrink-0"
                    [class]="
                      setting.value.locked && setting.get('selected')?.value
                        ? 'bg-gray-200 border-gray-400'
                        : setting.value.locked
                          ? 'bg-gray-100 border-gray-300'
                          : setting.get('selected')?.value
                            ? 'bg-[#0C4766CC] border-[#0C4766CC]'
                            : 'bg-white border-gray-300'
                    "
                  >
                    <svg
                      *ngIf="setting.get('selected')?.value"
                      class="absolute inset-0 w-full h-full p-0.5"
                      [class.text-white]="!setting.value.locked"
                      [class.text-gray-400]="setting.value.locked"
                      viewBox="0 0 20 20"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M16.7071 5.29289C17.0976 5.68342 17.0976 6.31658 16.7071 6.70711L8.70711 14.7071C8.31658 15.0976 7.68342 15.0976 7.29289 14.7071L3.29289 10.7071C2.90237 10.3166 2.90237 9.68342 3.29289 9.29289C3.68342 8.90237 4.31658 8.90237 4.70711 9.29289L8 12.5858L15.2929 5.29289C15.6834 4.90237 16.3166 4.90237 16.7071 5.29289Z"
                        fill="currentColor"
                      />
                    </svg>
                  </span>
                </label>

                <div class="flex-1">
                  <div class="flex items-center gap-2">
                    <img [src]="securityIcons[i]" alt="" class="w-8 h-8 mr-1" />
                    <span class="text-[#262626] font-medium">{{
                      setting.value.name
                    }}</span>
                  </div>
                  <p class="text-[#082230CC] text-sm mt-1">
                    {{ proctoringDetails[i] }}
                  </p>

                  @if (i === 2 || i === 3) {
                    <div
                      class="transition-all duration-300 ease-in-out overflow-hidden"
                      [ngStyle]="{
                        maxHeight: setting.get('selected')?.value
                          ? '200px'
                          : '0',
                        opacity: setting.get('selected')?.value ? '1' : '0',
                        paddingTop: setting.get('selected')?.value
                          ? '12px'
                          : '0',
                        paddingBottom: setting.get('selected')?.value
                          ? '12px'
                          : '0',
                      }"
                    >
                      <label
                        class="text-[#262626] font-medium block mb-1"
                        [for]="
                          i === 2
                            ? 'cameraShotsInterval'
                            : 'screenShotsInterval'
                        "
                        >Capture interval (15-120 seconds)</label
                      >
                      <div class="flex items-center gap-2">
                        <div>
                          <app-custom-input
                            [id]="
                              i === 2
                                ? 'cameraShotsInterval'
                                : 'screenShotsInterval'
                            "
                            type="number"
                            [min]="15"
                            [max]="120"
                            [formControl]="
                              i === 2
                                ? getFormControl('camerashotsInterval')
                                : getFormControl('screenshotsInterval')
                            "
                            [class.error-border]="
                              i === 2
                                ? isCameraShotsIntervalInvalid()
                                : isScreenShotsIntervalInvalid()
                            "
                            (input)="
                              onIntervalInputChange(
                                i === 2
                                  ? 'camerashotsInterval'
                                  : 'screenshotsInterval',
                                $event
                              )
                            "
                            (blur)="
                              onIntervalInputBlur(
                                i === 2
                                  ? 'camerashotsInterval'
                                  : 'screenshotsInterval',
                                $event
                              )
                            "
                            (keydown)="onIntervalKeyDown($event)"
                          />
                        </div>
                        <span class="-mt-2">sec</span>
                      </div>
                      <span
                        class="text-red-500 text-xs mt-1 block"
                        *ngIf="
                          i === 2
                            ? isCameraShotsIntervalInvalid()
                            : isScreenShotsIntervalInvalid()
                        "
                      >
                        Interval must be between 15 and 120 seconds
                      </span>
                    </div>
                  }
                </div>
              </div>

              <div
                class="w-auto max-w-[20rem] z-20 text-white bg-[#0C4767] rounded-lg px-1.5 hidden group-hover:block"
                [appSafeHtml]="proctoringDetails[i]"
              ></div>
            </div>
          </div>
        }
      </div>
      <div class="flex items-start gap-4 mt-4">
        <div class="bg-white rounded-lg border border-[#E1E3EA] p-4 flex-1">
          <div class="flex items-start gap-4">
            <label class="inline-flex items-center mt-1 cursor-pointer">
              <input
                type="checkbox"
                formControlName="enableRetakeDelay"
                class="hidden"
              />
              <span
                class="relative w-5 h-5 border rounded transition-all duration-200 flex-shrink-0"
                [class]="
                  BasicInfoAndConfigurationForm.get('enableRetakeDelay')?.value
                    ? 'bg-[#0C4766CC] border-[#0C4766CC]'
                    : 'bg-white border-gray-300'
                "
              >
                <svg
                  *ngIf="
                    BasicInfoAndConfigurationForm.get('enableRetakeDelay')
                      ?.value
                  "
                  class="absolute inset-0 w-full h-full p-0.5 text-white"
                  viewBox="0 0 20 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M16.7071 5.29289C17.0976 5.68342 17.0976 6.31658 16.7071 6.70711L8.70711 14.7071C8.31658 15.0976 7.68342 15.0976 7.29289 14.7071L3.29289 10.7071C2.90237 10.3166 2.90237 9.68342 3.29289 9.29289C3.68342 8.90237 4.31658 8.90237 4.70711 9.29289L8 12.5858L15.2929 5.29289C15.6834 4.90237 16.3166 4.90237 16.7071 5.29289Z"
                    fill="currentColor"
                  />
                </svg>
              </span>
            </label>

            <div class="flex-1">
              <div class="flex items-center gap-2">
                <img
                  src="assets/icons/retake.svg"
                  alt=""
                  class="w-8 h-8 mr-1"
                />
                <span class="text-[#262626] font-medium">Retake Delay</span>
              </div>
              <p class="text-[#082230CC] text-sm mt-1">
                Set the delay period before a candidate can retake the
                assessment.
              </p>

              @if (
                BasicInfoAndConfigurationForm.get('enableRetakeDelay')?.value
              ) {
                <div
                  class="transition-all duration-300 ease-in-out overflow-hidden"
                  [ngStyle]="{
                    maxHeight: BasicInfoAndConfigurationForm.get(
                      'enableRetakeDelay'
                    )?.value
                      ? '200px'
                      : '0',
                    opacity: BasicInfoAndConfigurationForm.get(
                      'enableRetakeDelay'
                    )?.value
                      ? '1'
                      : '0',
                    paddingTop: BasicInfoAndConfigurationForm.get(
                      'enableRetakeDelay'
                    )?.value
                      ? '12px'
                      : '0',
                    paddingBottom: BasicInfoAndConfigurationForm.get(
                      'enableRetakeDelay'
                    )?.value
                      ? '12px'
                      : '0',
                  }"
                >
                  <p class="text-[#262626] font-medium mb-3">Time Unit</p>

                  <div class="flex items-center gap-6 mb-3">
                    <div class="flex items-center gap-6">
                      <label class="flex items-center gap-2 cursor-pointer">
                        <input
                          type="radio"
                          name="retakeDelayUnit"
                          value="days"
                          formControlName="retakeDelayUnit"
                          class="absolute w-px h-px p-0 m-0 overflow-hidden whitespace-nowrap border-0"
                          style="clip: rect(0, 0, 0, 0)"
                          (click)="onRadioClick('days')"
                          (keydown)="onRadioKeydown($event, 'days')"
                        />
                        <span
                          class="relative w-5 h-5 border border-gray-300 rounded-full transition-all duration-200 flex-shrink-0"
                          [class]="
                            BasicInfoAndConfigurationForm.get('retakeDelayUnit')
                              ?.value === 'days'
                              ? 'border-[#0C4766CC] bg-white'
                              : 'bg-white border-gray-300'
                          "
                        >
                          <span
                            *ngIf="
                              BasicInfoAndConfigurationForm.get(
                                'retakeDelayUnit'
                              )?.value === 'days'
                            "
                            class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-2.5 h-2.5 bg-[#0C4766CC] rounded-full"
                          ></span>
                        </span>
                        <span class="text-[#262626] text-sm font-medium"
                          >Days</span
                        >
                      </label>
                      <label class="flex items-center gap-2 cursor-pointer">
                        <input
                          type="radio"
                          name="retakeDelayUnit"
                          value="months"
                          formControlName="retakeDelayUnit"
                          class="absolute w-px h-px p-0 m-0 overflow-hidden whitespace-nowrap border-0"
                          style="clip: rect(0, 0, 0, 0)"
                          (click)="onRadioClick('months')"
                          (keydown)="onRadioKeydown($event, 'months')"
                        />
                        <span
                          class="relative w-5 h-5 border border-gray-300 rounded-full transition-all duration-200 flex-shrink-0"
                          [class]="
                            BasicInfoAndConfigurationForm.get('retakeDelayUnit')
                              ?.value === 'months'
                              ? 'border-[#0C4766CC] bg-white'
                              : 'bg-white border-gray-300'
                          "
                        >
                          <span
                            *ngIf="
                              BasicInfoAndConfigurationForm.get(
                                'retakeDelayUnit'
                              )?.value === 'months'
                            "
                            class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-2.5 h-2.5 bg-[#0C4766CC] rounded-full"
                          ></span>
                        </span>
                        <span class="text-[#262626] text-sm font-medium"
                          >Months</span
                        >
                      </label>
                    </div>

                    <div class="flex items-center gap-2 min-w-[180px]">
                      <div class="w-24">
                        <app-custom-input
                          type="number"
                          [placeholder]="'1'"
                          [formControl]="getFormControl('retakeDelayDays')"
                          [min]="1"
                          [class.error-border]="
                            isFieldInvalid('retakeDelayDays')
                          "
                        />
                      </div>
                      <span class="text-[#474D66] text-sm font-medium">
                        {{
                          BasicInfoAndConfigurationForm.get('retakeDelayUnit')
                            ?.value === 'months'
                            ? +BasicInfoAndConfigurationForm.get(
                                'retakeDelayDays'
                              )?.value === 1
                              ? 'month'
                              : 'months'
                            : +BasicInfoAndConfigurationForm.get(
                                  'retakeDelayDays'
                                )?.value === 1
                              ? 'day'
                              : 'days'
                        }}
                      </span>
                    </div>

                    @if (isFieldInvalid('retakeDelayDays')) {
                      <span class="text-sm text-red-500 mt-1 block">
                        Minimum delay is 1
                        {{
                          BasicInfoAndConfigurationForm.get('retakeDelayUnit')
                            ?.value === 'months'
                            ? 'month'
                            : 'day'
                        }}
                      </span>
                    }
                  </div>
                </div>
              }
            </div>
          </div>
        </div>
      </div>

      <div class="flex gap-x-2 mt-4 mb-2 justify-end pb-3">
        <app-custombutton variant="secondary" (clicked)="cancel()"
          >Cancel</app-custombutton
        >
        <app-custombutton
          [disabled]="BasicInfoAndConfigurationForm.invalid"
          (clicked)="nextPage()"
          >Next</app-custombutton
        >
      </div>
    </div>
  </main>
</form>
