import { Routes } from '@angular/router';
import { CreateAssessmentComponent } from './create-assessment.component';
import { LayoutWrapperComponent } from '../../Questions/main-questions-page/shared/layout-wrapper/layout-wrapper.component';

export const CreateAssessmentsRoute: Routes = [
  {
    path: '',
    component: LayoutWrapperComponent,
    children: [
      {
        path: '',
        component: CreateAssessmentComponent,
        children: [
          { path: '', redirectTo: 'BasicInfo-And-Config', pathMatch: 'full' },
          {
            path: 'BasicInfo-And-Config',
            loadComponent: () =>
              import('./basic-and-config/basic-and-config.component').then(
                (c) => c.BasicAndConfigComponent
              ),
            data: {
              breadcrumb: 'Basic Information',
              index: 0,
            },
          },
          {
            path: 'Assign-Test-To-Assessment',
            loadComponent: () =>
              import('./select-test-page/select-test-page.component').then(
                (c) => c.SelectTestPageComponent
              ),
            data: {
              breadcrumb: 'Assign Test To Assessment',
              index: 1,
            },
          },
          {
            path: 'preview-assessments',
            loadComponent: () =>
              import(
                './assessmemt-preview-creation/assessmemt-preview-creation.component'
              ).then((c) => c.AssessmemtPreviewCreationComponent),
            data: {
              index: 2,
              breadcrumb: 'Preview Assessment On Creation',
              hideButtons: false,
            },
          },
        ],
      },
    ],
  },
];
