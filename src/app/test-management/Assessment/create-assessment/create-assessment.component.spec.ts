import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CreateAssessmentComponent } from './create-assessment.component';
import { Router, ActivatedRoute, NavigationEnd } from '@angular/router';
import { FormBuilder } from '@angular/forms';
import { Store } from '@ngrx/store';
import { SharedService } from '../../../services/shared/shared.service';
import { BehaviorSubject, of, throwError } from 'rxjs';
import { AssessmentService } from '../../../services/test-management-service/assessment-service/assessment.service';
import {
  AssessmentWithTest,
  BasicAndConfig,
  GetOneAssessmentApiResponse,
} from '../../../Interfaces/Types/assessmentInterface';
import { AssessmentStore } from '../../../stores/assessments-store/assessment.store';
import { ToastService } from '../../../services/toast-service/toast.service';
import { TestsData } from '../../../Interfaces/Types/test';

describe('CreateAssessmentComponent', () => {
  let component: CreateAssessmentComponent;
  let fixture: ComponentFixture<CreateAssessmentComponent>;
  let mockRouter: jest.Mocked<Router>;
  let mockActivatedRoute: Partial<ActivatedRoute>;
  let mockAssessmentService: jest.Mocked<AssessmentService>;
  let mockStore: jest.Mocked<Store>;
  let mockSharedService: jest.Mocked<SharedService>;
  let mockAssessmentStore: Partial<AssessmentStore>;
  let mockToastService: jest.Mocked<ToastService>;

  let basicInfoSubject: BehaviorSubject<BasicAndConfig | undefined>;
  let dataForPreviewSubject: BehaviorSubject<{
    tests: TestsData[];
    basicInfo: BasicAndConfig;
  }>;

  const mockBasicInfo: BasicAndConfig = {
    title: 'Test Assessment',
    instructions: 'Test Instructions',
    camerashotsInterval: '30',
    screenshotsInterval: '30',
    configurations: [],
    passMark: 50,
  };

  beforeEach(async () => {
    mockRouter = {
      url: '/edit',
      navigate: jest.fn(),
      navigateByUrl: jest.fn(),
      events: of(new NavigationEnd(1, '/start', '/edit')),
    } as unknown as jest.Mocked<Router>;

    const mockFirstChild = {
      data: of({ hideButtons: false, index: 0 }),
      snapshot: {},
      routeConfig: null,
      children: [],
      outlet: 'primary',
      component: null,
      url: of([]),
      params: of({}),
      queryParams: of({}),
      fragment: of(''),
      firstChild: null,
    } as unknown as ActivatedRoute;

    mockActivatedRoute = {
      queryParams: of({ assessmentId: '123' }),
      firstChild: mockFirstChild,
    };

    mockAssessmentService = {
      getOneAssessment: jest.fn().mockReturnValue(
        of({
          data: {
            title: 'Test Assessment',
            instructions: 'Test Instructions',
            camerashotsInterval: 30,
            screenshotsInterval: 30,
            proctorFeatures: [],
            tests: [],
            conductSurvey: false,
            showClock: true,
            passMark: 50,
          },
        })
      ),
      setBasicInfoAndConfig: jest.fn(),
      basicInfoAndConfig$: of(mockBasicInfo),
    } as unknown as jest.Mocked<AssessmentService>;

    mockStore = {
      dispatch: jest.fn(),
      select: jest.fn().mockReturnValue(of(mockBasicInfo)),
    } as unknown as jest.Mocked<Store>;

    mockSharedService = {
      addMenuItem: jest.fn(),
      setBreadcrumbDetails: jest.fn(),
      removeMenuItems: jest.fn(),
    } as unknown as jest.Mocked<SharedService>;

    basicInfoSubject = new BehaviorSubject<BasicAndConfig | undefined>(
      undefined
    );

    dataForPreviewSubject = new BehaviorSubject<{
      tests: TestsData[];
      basicInfo: BasicAndConfig;
    }>({
      tests: [],
      basicInfo: mockBasicInfo,
    });

    mockAssessmentStore = {
      setBasicInfo: jest.fn(),
      setSelectedTests: jest.fn(),
      basicInfo$: basicInfoSubject.asObservable(),
      dataForPreview$: dataForPreviewSubject.asObservable(),
    };

    mockToastService = {
      onShow: jest.fn(),
    } as unknown as jest.Mocked<ToastService>;

    const testingModule = {
      declarations: [],
      imports: [],
      providers: [
        FormBuilder,
        { provide: Router, useValue: mockRouter },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: AssessmentService, useValue: mockAssessmentService },
        { provide: Store, useValue: mockStore },
        { provide: SharedService, useValue: mockSharedService },
        { provide: AssessmentStore, useValue: mockAssessmentStore },
        { provide: ToastService, useValue: mockToastService },
      ],
    };

    await TestBed.configureTestingModule(testingModule).compileComponents();
    fixture = TestBed.createComponent(CreateAssessmentComponent);
    component = fixture.componentInstance;
    component.assessmentStore = mockAssessmentStore as AssessmentStore;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set edit mode when URL includes "edit"', () => {
    Object.defineProperty(mockRouter, 'url', { value: '/edit' });
    component.ngOnInit();
    expect(component.isEditMode).toBeTruthy();
  });

  it('should load assessment data when in edit mode with assessment ID', () => {
    component.isEditMode = true;
    component.assessmentId = '123';

    component.editMode();

    expect(mockAssessmentService.getOneAssessment).toHaveBeenCalledWith('123');
    expect(mockAssessmentStore.setBasicInfo).toHaveBeenCalled();
    expect(mockAssessmentStore.setSelectedTests).toHaveBeenCalled();
    expect(component.isDataLoaded).toBe(true);
  });

  it('should handle errors when loading assessment data fails', () => {
    component.isEditMode = true;
    component.assessmentId = '123';
    mockAssessmentService.getOneAssessment = jest
      .fn()
      .mockReturnValue(throwError(() => new Error('Failed to load')));

    component.editMode();

    expect(mockToastService.onShow).toHaveBeenCalledWith(
      'Load Failed',
      'Failed to load assessment data for editing',
      true,
      'error'
    );
    expect(mockRouter.navigate).toHaveBeenCalledWith([
      '/dashboard/test-management/assessments',
    ]);
  });

  it('should not fetch assessment data if basicInfo already exists in store', () => {
    component.isEditMode = true;
    component.assessmentId = '123';
    mockAssessmentService.getOneAssessment.mockClear();

    basicInfoSubject.next({
      title: 'Existing Assessment',
      instructions: 'Some instructions',
      camerashotsInterval: '30',
      screenshotsInterval: '30',
      configurations: [],
      passMark: 60,
    });
    component.editMode();

    expect(mockAssessmentService.getOneAssessment).not.toHaveBeenCalled();
  });

  describe('proctorFeatures', () => {
    it('should return true when feature value matches proctor feature name', () => {
      const mockResponse: GetOneAssessmentApiResponse = {
        success: true,
        data: {
          proctorFeatures: [{ name: 'feature1' }, { name: 'feature2' }],
        } as AssessmentWithTest,
      };

      const feature = { value: 'feature1' };

      const result = component.proctorFeatures(mockResponse, feature);

      expect(result).toBe(true);
    });

    it('should return false when proctorFeatures array is empty', () => {
      const mockResponse: GetOneAssessmentApiResponse = {
        success: true,
        data: {
          proctorFeatures: [],
        } as unknown as AssessmentWithTest,
      };

      const feature = { value: 'feature1' };

      const result = component.proctorFeatures(mockResponse, feature);

      expect(result).toBe(false);
    });
  });

  describe('setupStoreSubscriptions', () => {
    it('should subscribe to basicInfoAndConfig$ and dataForPreview$', () => {
      component['setupStoreSubscriptions']();
      expect(component.subscription.length).toBeGreaterThan(0);
      expect(component.basicInfo).toEqual(mockBasicInfo);
    });

    it('should show toast when tests have no questions', () => {
      dataForPreviewSubject.next({
        tests: [
          {
            title: 'Test with no questions',
            questions: [],
            id: '1',
            description: 'Description',
            duration: 30,
            categoryId: 'category1',
            createdAt: new Date().toISOString(),
            difficultyLevel: 'Easy',
            domainId: 'domain1',
            isActivated: true,
            isActive: true,
            testManagerId: 'manager1',
            updatedAt: new Date().toISOString(),
            instructions: 'Test instructions',
            passage: 'Test passage',
            passMark: 50,
            system: false,
            organizationId: 'org1',
            type: 'type1',
            domain: {
              id: 'domain1',
              name: 'Domain 1',
              description: 'Domain description',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              system: false,
              organizationId: 'org1',
            },
          },
        ],
        basicInfo: mockBasicInfo,
      });

      component['setupStoreSubscriptions']();
      expect(mockToastService.onShow).toHaveBeenCalledWith(
        'no questions in some tests',
        expect.stringContaining('Test with no questions'),
        true,
        'error'
      );
    });
  });

  describe('beforeunloadHandler', () => {
    it('should save data to localStorage when beforeunload event occurs', () => {
      const setItemSpy = jest
        .spyOn(Storage.prototype, 'setItem')
        .mockImplementation();

      component.basicInfo = mockBasicInfo;
      component.selectedTests = [
        {
          id: '1',
          title: 'Test 1',
          questions: [],
          description: 'Description',
          duration: 30,
          categoryId: 'category1',
          createdAt: new Date().toISOString(),
          difficultyLevel: 'Easy',
          domainId: 'domain1',
          isActivated: true,
          isActive: true,
          testManagerId: 'manager1',
          updatedAt: new Date().toISOString(),
          instructions: 'Test instructions',
          passage: 'Test passage',
          passMark: 50,
          system: false,
          organizationId: 'org1',
          type: 'type1',
          domain: {
            id: 'domain1',
            name: 'Domain 1',
            description: 'Domain description',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            system: false,
            organizationId: 'org1',
          },
        },
      ];

      component.beforeunloadHandler();

      expect(setItemSpy).toHaveBeenCalledWith(
        'basicInfo',
        JSON.stringify(mockBasicInfo)
      );
      expect(setItemSpy).toHaveBeenCalledWith(
        'selectedTests',
        JSON.stringify(component.selectedTests)
      );

      setItemSpy.mockRestore();
    });
  });

  describe('loadDataFromLocalStorage', () => {
    it('should load data from localStorage when available', () => {
      const mockBasicInfoString = JSON.stringify(mockBasicInfo);
      const getItemSpy = jest
        .spyOn(Storage.prototype, 'getItem')
        .mockReturnValueOnce(mockBasicInfoString)
        .mockReturnValueOnce('[]');

      component['loadDataFromLocalStorage']();

      expect(getItemSpy).toHaveBeenCalledWith('basicInfo');
      expect(getItemSpy).toHaveBeenCalledWith('selectedTests');
      expect(component.basicInfo).toEqual(mockBasicInfo);

      getItemSpy.mockRestore();
    });

    it('should handle missing data in localStorage', () => {
      const getItemSpy = jest
        .spyOn(Storage.prototype, 'getItem')
        .mockReturnValue(null);

      // Reset the component's basicInfo to undefined before test
      component.basicInfo = undefined;

      component['loadDataFromLocalStorage']();

      expect(getItemSpy).toHaveBeenCalledWith('basicInfo');
      expect(getItemSpy).toHaveBeenCalledWith('selectedTests');
      expect(component.basicInfo).toBe(undefined);
      expect(component.selectedTests).toEqual([]);

      getItemSpy.mockRestore();
    });
  });

  describe('updateRouteTracking and generatePageTitle', () => {
    it('should generate correct page title for single route', () => {
      component.routesTracking = ['/dashboard/assessments'];

      const title = component['generatePageTitle']();

      expect(title).toBe('All Assessments');
    });

    it('should generate correct page title for BasicInfo-And-Config route', () => {
      Object.defineProperty(mockRouter, 'url', {
        value: '/BasicInfo-And-Config',
        writable: true,
      });
      component.routesTracking = ['/dashboard/all-assessments', '/basic-info'];

      const title = component['generatePageTitle']();

      expect(title).toBe('All Assessments');
      expect(component.routeUrl).toBe('/dashboard/all-assessments');
    });

    it('should generate correct page title for Assign-Test-To-Assessment route', () => {
      Object.defineProperty(mockRouter, 'url', {
        value: '/Assign-Test-To-Assessment',
        writable: true,
      });
      component.routesTracking = ['/BasicInfo-And-Config', '/assign-test'];

      const title = component['generatePageTitle']();

      expect(title).toBe('Basic Information And Config');
      expect(component.routeUrl).toBe('/BasicInfo-And-Config');
    });

    it('should generate correct page title for preview-assessments route in edit mode', () => {
      Object.defineProperty(mockRouter, 'url', {
        value: '/preview-assessments',
        writable: true,
      });
      component.isEditMode = true;
      component.assessmentId = 'test-id';
      component.routesTracking = [
        '/dashboard/all-assessments',
        '/BasicInfo-And-Config',
        '/Assign-Test-To-Assessment',
      ];

      const title = component['generatePageTitle']();

      expect(title).toBe('Assign Test To Assessment');
      expect(component.routeUrl).toBe(
        '/dashboard/test-management/assessments/edit-assessments/Assign-Test-To-Assessment?assessmentId=test-id'
      );
    });

    it('should generate correct page title for preview-assessments route in create mode', () => {
      Object.defineProperty(mockRouter, 'url', {
        value: '/preview-assessments',
        writable: true,
      });
      component.isEditMode = false;
      component.routesTracking = [
        '/BasicInfo-And-Config',
        '/Assign-Test-To-Assessment',
        '/preview',
      ];

      const title = component['generatePageTitle']();

      expect(title).toBe('Assign Test To Assessment');
      expect(component.routeUrl).toBe('/Assign-Test-To-Assessment');
    });
  });

  describe('routeToPage', () => {
    it('should call history.back()', () => {
      const historyBackSpy = jest.spyOn(history, 'back').mockImplementation();

      component.routeToPage();

      expect(historyBackSpy).toHaveBeenCalled();

      historyBackSpy.mockRestore();
    });
  });

  describe('editMode error handling', () => {
    it('should handle API error during edit mode initialization', () => {
      component.isEditMode = true;
      component.assessmentId = 'test-id';

      basicInfoSubject.next(undefined); // No existing basic info
      mockAssessmentService.getOneAssessment.mockReturnValue(
        throwError(() => new Error('API Error'))
      );

      component.editMode();

      expect(mockToastService.onShow).toHaveBeenCalledWith(
        'Load Failed',
        'Failed to load assessment data for editing',
        true,
        'error'
      );
      expect(mockRouter.navigate).toHaveBeenCalledWith([
        '/dashboard/test-management/assessments',
      ]);
    });
  });
});
