<main>
  <div class="bg-[#fafafa] rounded-lg border-b-[#082230] p-3 md:p-4 mt-6 mb-3">
    <div class="border-b border-[#E5E7EB] pb-3 mb-3">
      <p class="font-medium text-[1.1rem] md:text-[1.25rem]">Test Assignment</p>
      <p class="mb-2">Select tests to include in your assessment</p>
    </div>

    <div class="flex justify-between items-center gap-4 mb-3">
      <div class="flex gap-x-3 items-center">
        <div class="relative input-grey">
          <input
            type="checkbox"
            class="w-5 h-5 accent-transparent checked:accent-[#0c4767] cursor-pointer"
            (change)="selectAllPageTests(page)"
            [checked]="checkAll[page]"
          />
          @if (selectedTests.length > 0 && (partialCheck || !checkAll[page])) {
            <button
              class="absolute h-3 w-3 bg-[#0c4767] flex items-center justify-center top-1 left-1"
              (click)="selectAllPageTests(page)"
            >
              <span></span>
            </button>
          }
        </div>
        <div>
          <p class="text-[#0C4767] text-sm font-normal mb-1">
            Select All {{ selectedTests.length }} test(s) selected
          </p>
        </div>
      </div>

      <div class="flex gap-x-4">
        <app-custom-search-input
          [searchTerm]="searchTerm"
          (searchTermChange)="searchTests($event)"
          [placeholder]="'Search Test'"
        ></app-custom-search-input>

        <app-custom-dropdown
          placeholder="Filter By"
          [options]="getDisplayNames()"
          [isDefaultDropdown]="true"
          [icon]="filterIconSrc"
          height="45px"
          (selectionChange)="filterTests($event)"
        ></app-custom-dropdown>
      </div>
    </div>
  </div>

  @if (tests.length === 0) {
    <app-no-result-found
      [search]="searchTerm"
      [message]="
        searchTerm
          ? 'No tests found'
          : 'No tests found for ' + currentFilterName
      "
      [firstMessage]="'Sorry, no tests available'"
    ></app-no-result-found>
  } @else {
    <div>
      @for (
        item of tests
          | paginate
            : {
                itemsPerPage: pageSize,
                currentPage: page,
                totalItems: totalNumberOfTests,
              };
        track $index
      ) {
        <label [for]="item.id">
          <div class="flex items-center gap-x-4">
            <app-custom-check-box
              [id]="item.id"
              [value]="item"
              [checked]="isTestSelected(item)"
              (valueChange)="selectTest($event, page)"
            ></app-custom-check-box>

            <div class="w-full">
              <app-general-long-card
                [id]="item.id"
                [title]="item.title"
                [middleContent]="item.isActivated ? 'Active' : 'Inactive'"
                [textColor]="'white'"
                [showRightSide]="false"
              >
                <div class="leftSideBody1 flex items-center gap-x-2">
                  <span class="text-[#DD9926CC]"><app-folder-icon-svg /></span>
                  <div>{{ item.domain.name }}</div>
                </div>
                <p
                  class="leftSideBody1 flex items-center gap-x-2 text-[#474D66] text-sm"
                >
                  <img
                    src="../../../../../assets/icons/new-clock.svg"
                    alt="Clock Icon"
                  />
                  <span>{{ convertDuration(item.duration) }}</span>
                </p>
              </app-general-long-card>
            </div>
          </div>
        </label>
      }
    </div>
  }
</main>

<app-custom-mini-modal
  [visibleModal]="showDeleteModal"
  [headerTitle]="'Confirm Assessment Cancellation'"
  [textAlign]="'center'"
  [left]="'No'"
  [right]="'Yes'"
  [visibleModal]="showDeleteModal"
  [bodyText]="'Are you sure you want to cancel?'"
  (leftClickEvent)="closeAssessmentCancelModal()"
  (rightClickEvent)="confirmCancelAssessmentCreation()"
  (visibleModalChange)="closeAssessmentCancelModal()"
  [width]="'446px'"
  [height]="'223px'"
/>

<footer class="mt-5">
  <div class="grid md:flex justify-between items-center">
    <div class="text-[#474D66] text-[16px] font-medium">
      {{ range }}
    </div>

    <app-custom-pagination
      [total]="totalNumberOfTests"
      [size]="pageSize"
      (pagechange)="onPageChange($event)"
      (sizeSelect)="onSizeChange($event)"
    ></app-custom-pagination>
  </div>
  <div class="button2 flex flex-wrap justify-end gap-x-4 mt-6">
    <app-custombutton
      [variant]="'secondary'"
      (clicked)="showCancelAssessmentModal()"
      >Cancel</app-custombutton
    >
    <app-custombutton
      (clicked)="routeToPreviewAssessment()"
      [disabled]="!isValid"
      >Proceed</app-custombutton
    >
  </div>
</footer>
