import {
  Component,
  EventEmitter,
  inject,
  Input,
  OnDestroy,
  OnInit,
  Output,
  signal,
} from '@angular/core';
import { debounceTime, Subject, Subscription } from 'rxjs';
import { TestsData } from '../../../../Interfaces/Types/test';
import { calculateRange } from '../../../../utils/questionsConstants';
import { NgxPaginationModule } from 'ngx-pagination';
import { GeneralLongCardComponent } from '../../../../components/general-long-card/general-long-card.component';
import { CustomPaginationComponent } from '../../../../components/custom-pagination/custom-pagination.component';
import { CustomButtonComponent } from '../../../../components/custombutton/custombutton.component';
import {
  BasicAndConfig,
  BasicInfoWithTest,
} from '../../../../Interfaces/Types/assessmentInterface';
import { ToastService } from '../../../../services/toast-service/toast.service';
import { ActivatedRoute, Router } from '@angular/router';
import { CustomCheckBoxComponent } from '../../../../components/custom-check-box/custom-check-box.component';
import { CommonModule } from '@angular/common';
import { CustomDropdownComponent } from '../../../../components/custom-dropdown/custom-dropdown.component';
import { DomainService } from '../../../../services/domain.service';
import { DomainData } from '../../../../Interfaces/domainInterface';
import { convertDurationPreview } from '../../../../utils/testManagement';
import { TestStore } from '../../../../stores/tests-store/test.store';
import { AssessmentStore } from '../../../../stores/assessments-store/assessment.store';
import { NoResultFoundComponent } from '../../../../no-result-found/no-result-found.component';
import { CustomMiniModalComponent } from '../../../../components/custom-mini-modal/custom-mini-modal.component';
import {
  navigateWithAssessmentId,
  isValidTestData,
  timeAgo,
} from '../../../../utils/assessment/assessment';
import { CustomSearchInputComponent } from '../../../../components/custom-search-input/custom-search-input.component';
import { RefreshLoaderComponent } from '../../../../components/refresh-loader/refresh-loader.component';
import { FolderIconSvgComponent } from '../../../../components/folder-icon-svg/folder-icon-svg.component';

@Component({
  selector: 'app-select-test-page',
  standalone: true,
  imports: [
    NgxPaginationModule,
    GeneralLongCardComponent,
    CustomPaginationComponent,
    CustomButtonComponent,
    CustomCheckBoxComponent,
    CommonModule,
    CustomDropdownComponent,
    NoResultFoundComponent,
    CustomMiniModalComponent,
    CustomSearchInputComponent,
    RefreshLoaderComponent,
    FolderIconSvgComponent,
  ],
  templateUrl: './select-test-page.component.html',
  styleUrl: './select-test-page.component.css',
})
export class SelectTestPageComponent implements OnInit, OnDestroy {
  @Output() previousPageEmiter = new EventEmitter();
  basicInformationForPreview: BasicAndConfig | undefined;
  tests: TestsData[] = [];
  selectedTests: TestsData[] = [];
  @Input() title = '';
  @Input() page1 = '';
  showClock: boolean = false;
  isEditMode = false;
  showCancelModal: boolean = false;
  showDeleteModal = false;
  conductSurvey: boolean = false;
  page = 1;
  pageSize = 25;
  searchTerm = '';
  partialCheck: boolean = false;
  domain = signal('');
  totalNumberOfTests = 0;
  checkAll: boolean[] = [];
  range = '';
  isEdit = false;
  subscription: Subscription[] = [];
  assessmentId: string = '';
  allDomains: DomainData[] = [];
  assessmentDuration: number = 0;
  forceFetchTest = false;
  currentFilterName: string = 'All Domains';
  basicInfo: BasicAndConfig | undefined;
  isValid = false;
  private readonly assessmentStore = inject(AssessmentStore);
  searchSubject: Subject<string> = new Subject();

  filterIconSrc = '../../../../../assets/reportsManagement/Filters lines.svg';
  constructor(
    private readonly toast: ToastService,
    private readonly router: Router,
    private readonly activatedRoute: ActivatedRoute,
    private readonly domainService: DomainService,
    private readonly testStore: TestStore
  ) {}

  ngOnDestroy(): void {
    this.subscription.forEach((sub) => sub.unsubscribe());
  }

  ngOnInit(): void {
    this.subscription.push(
      this.activatedRoute.queryParams.subscribe((params) => {
        this.assessmentId = params['assessmentId'];
      })
    );

    if (this.router.url.includes('edit')) {
      this.isEdit = true;
      this.isEditMode = true;
    }
    this.loadAllTestData();

    this.subscription.push(
      this.testStore.totalItems$.subscribe((totalItems) => {
        this.totalNumberOfTests = totalItems;
        this.range = calculateRange(
          this.page,
          this.pageSize,
          this.totalNumberOfTests
        );
      })
    );

    this.subscription.push(
      this.testStore.allTests$.subscribe({
        next: (tests) => {
          if (tests) {
            this.tests = tests;
            this.autoCheckAll();
          }
        },
        error: (error) => {
          this.toast.onShow('error', error as string, true, 'error');
        },
      })
    );

    this.subscription.push(
      this.assessmentStore.selectedTests$.subscribe((tests: TestsData[]) => {
        this.selectedTests = tests;
        this.updateValidation();
      })
    );

    this.subscription.push(
      this.assessmentStore.selectedTestsTotalDuration$.subscribe(
        (duration: number) => {
          this.assessmentDuration = duration;
        }
      )
    );

    this.subscription.push(
      this.assessmentStore.basicInfo$.subscribe((basicInfo) => {
        this.basicInfo = basicInfo;
        this.updateValidation();
      })
    );

    this.domainService.getSortedDomain();
    this.subscription.push(
      this.domainService.domainData.subscribe((data) => {
        this.allDomains = data.data;
      })
    );

    this.subscription.push(
      this.searchSubject.pipe(debounceTime(1200)).subscribe((searchTerm) => {
        if (searchTerm !== this.searchTerm) {
          this.searchTerm = searchTerm;
          this.page = 1;
          this.forceFetchTest = true;
          this.loadAllTestData();
        }
      })
    );
  }

  updateValidation() {
    if (this.basicInfo && this.selectedTests) {
      this.isValid = isValidTestData({
        basicInfo: this.basicInfo,
        tests: this.selectedTests,
      } as BasicInfoWithTest);
    }
  }

  autoCheckAll() {
    if (this.selectedTests) {
      const isAllSelected = this.tests.every((item) =>
        this.selectedTests.some((t) => t.id === item.id)
      );

      this.checkAll[this.page] = this.tests.length > 0 && isAllSelected;
      this.partialCheck = !this.checkAll[this.page] && !isAllSelected;
    }
  }

  searchTests($event: string) {
    this.searchSubject.next($event);
  }

  loadAllTestData() {
    this.testStore.fetchTests({
      page: this.page,
      size: this.pageSize,
      forceFetch: this.forceFetchTest,
      config: {
        keyword: this.searchTerm,
        domainId: this.domain(),
      },
    });

    this.range = calculateRange(
      this.page,
      this.pageSize,
      this.totalNumberOfTests
    );
  }

  onPageChange(page: number): void {
    if (page < 1 || page > this.totalNumberOfTests) return;
    if (this.page === page) return;
    this.page = page;
    this.forceFetchTest = true;
    this.loadAllTestData();
    this.autoCheckAll();
  }

  onSizeChange(event: number): void {
    this.pageSize = event;
    this.page = 1;
    this.forceFetchTest = true;
    this.loadAllTestData();
  }

  previousPage() {
    const baseUrl = 'dashboard/test-management/assessments';
    const path = this.isEdit
      ? 'edit-assessments/BasicInfo-And-Config'
      : 'create-assessments/BasicInfo-And-Config';

    const queryParams = this.isEdit ? { assessmentId: this.assessmentId } : {};

    this.router.navigate([`${baseUrl}/${path}`], { queryParams });
  }

  selectTest(test: TestsData, page: number) {
    const index = this.selectedTests.findIndex((t) => t.id === test.id);
    if (index === -1) {
      this.assessmentStore.addSelectedTest(test);
    } else {
      this.assessmentStore.removeSelectedTest(test.id);
    }

    const allSelected = this.tests.every((item) =>
      this.selectedTests.some((t) => t.id === item.id)
    );
    const noneSelected = this.tests.every(
      (item) => !this.selectedTests.some((t) => t.id === item.id)
    );
    this.checkAll[page] = allSelected;
    this.partialCheck = !this.checkAll[page] && !noneSelected;
  }

  isTestSelected(test: TestsData): boolean {
    return this.selectedTests.some((t) => t.id === test.id);
  }

  filterTests(item: string) {
    this.currentFilterName = item;

    if (item === 'All Domains') {
      this.domain.set('');
    } else {
      this.domain.set(item);
    }
    this.forceFetchTest = true;
    this.loadAllTestData();
  }

  getDisplayNames() {
    const domains = this.allDomains.map((domain) => domain.name);
    return ['All Domains', ...domains];
  }

  convertDuration(item: number | undefined) {
    if (!item) {
      return;
    }
    return convertDurationPreview(item);
  }

  convertDate(date: string) {
    return timeAgo(date);
  }
  selectAllPageTests(page: number) {
    if (!this.checkAll[page]) {
      this.tests.forEach((item) => {
        if (!this.selectedTests.some((t) => t.id === item.id)) {
          this.assessmentStore.addSelectedTest(item);
        }
      });
      this.checkAll[page] = true;
    } else {
      this.tests.forEach((item) => {
        this.assessmentStore.removeSelectedTest(item.id);
      });
      this.checkAll[page] = false;
    }
    this.partialCheck = false;
  }

  showCancelAssessmentModal() {
    this.showDeleteModal = true;
  }

  closeAssessmentCancelModal() {
    this.showDeleteModal = false;
  }

  confirmCancelAssessmentCreation() {
    this.showDeleteModal = false;
    this.router.navigate(['/dashboard/test-management/assessments']);
  }

  routeToPreviewAssessment() {
    this.assessmentStore.setBasicInfo(this.basicInfo as BasicAndConfig);
    this.assessmentStore.setSelectedTests(this.selectedTests);

    navigateWithAssessmentId(
      this.router,
      this.isEditMode,
      this.assessmentId,
      'preview-assessments'
    );
  }
}
