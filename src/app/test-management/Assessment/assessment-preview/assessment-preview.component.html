<main class="max-w-[1440px] mx-auto">
  @if (isLoading) {
    <p>Loading...</p>
  } @else {
    @if (assessmentData) {
      <div class="flex justify-between gap-3 flex-wrap mb-8">
        <app-question-title
          [questionType]="'Preview Assessment'"
          [newDesign]="true"
          [separateLines]="true"
          (backRoute)="goBack()"
        ></app-question-title>

        <div class="flex gap-x-6 items-center">
          <div class="flex gap-x-3">
            @if (assessmentData.isDispatched) {
              <button
                (click)="onViewReport()"
                class="flex items-center gap-x-[10px] border-none font-semibold text-[#0C4767]"
              >
                <img
                  class="w-[24px] h-[24px]"
                  src="../../../assets/icons/bar-chart.svg"
                  alt="view report"
                />
                <p>View report</p>
              </button>
            }
            <button
              (click)="navigateToHistory()"
              class="flex items-center gap-x-[10px] border-none font-semibold text-[#0C4767]"
            >
              <img
                class="w-[24px] h-[24px]"
                src="../../../assets/icons/history.svg"
                alt="history"
              />
              <p>History</p>
            </button>
            <button
              (click)="navigateToEdit()"
              class="flex items-center gap-x-[10px] border-none font-semibold text-[#0C4767]"
            >
              <img
                class="w-[24px] h-[24px]"
                src="../../../assets/icons/new-edit.svg"
                alt="edit"
              />
              <p>Edit</p>
            </button>
          </div>
          <app-custombutton (clicked)="dispatchAssessment()"
            >Dispatch Assessment</app-custombutton
          >
        </div>
      </div>

      <div class="bg-[#fafafa] rounded-lg border border-[#0822301A] p-6">
        <section class="w-full">
          <div class="title mb-4">
            <p class="text-[#082230E5] text-[1.125rem] mb-2 font-medium">
              Assessment Title
            </p>
            <p
              class="text-[1.125rem] text-[#082230CC] bg-[#08223008] py-4 px-6 rounded"
            >
              {{ assessmentData.title }}
            </p>
          </div>

          <div class="instructions mb-4">
            <p class="text-[#082230E5] font-medium mb-2">
              Assessment Instructions
            </p>
            <div
              class="text-[#082230E5] text-[16px] bg-[#08223008] p-4 rounded font-normal break-all"
              [appSafeHtml]="assessmentData.instructions"
            ></div>
          </div>
          <div class="grid grid-cols-2 gap-3">
            <div>
              <p class="text-[#082230E5] text-[1.125rem] mb-2 font-medium">
                Pass Mark
              </p>
              <p
                class="text-[#082230E5] text-[1.125rem] bg-[#08223008] py-4 px-6 rounded font-normal break-all"
              >
                {{ assessmentData.passMark }}%
              </p>
            </div>
            <div>
              <p class="text-[#082230E5] text-[1.125rem] mb-2 font-medium">
                Assessment Duration
              </p>
              <p
                class="text-[#082230CC] text-[1.125rem] bg-[#08223008] py-4 px-6 rounded font-normal break-all"
              >
                {{ convertDuration(assessmentData.duration) }}
              </p>
            </div>
          </div>
          <div class="configurations mb-[16px] mt-4">
            <p class="text-[#082230E5] text-[1.125rem] font-medium mb-[8px]">
              Security Configurations Selected
            </p>

            <ul
              class="grid grid-cols-1 md:grid-cols-2 gap-4 bg-[#08223008] p-8 rounded font-medium"
            >
              @for (
                item of getConfigurationsWithIntervals();
                track item.name;
                let i = $index
              ) {
                <li class="flex items-center gap-2">
                  <img
                    [src]="getFeatureIcon(item.name)"
                    alt="{{ item.name }} icon"
                    class="w-8 h-8"
                  />
                  <p class="text-[#082230CC] font-medium text-[1.125rem]">
                    {{ item.name }}
                    @if (item.interval) {
                      <span>(Every {{ item.interval }} Secs)</span>
                    }
                  </p>
                </li>
              }
            </ul>
          </div>

          <p class="text-[#082230E5] text-[1.125rem] font-medium mb-[16px]">
            Tests Selected ({{ assessmentData.tests.length }})
          </p>

          <div class="list-of-test">
            @for (
              item of assessmentData.tests;
              track item;
              let index = $index
            ) {
              <div class="surface:min-w-[22rem] min-w-[30rem] max-w-[90rem]">
                <app-general-long-card
                  [id]="item.id"
                  [title]="item.title"
                  [middleContent]="item.isActivated ? 'Active' : 'Inactive'"
                  [textColor]="'white'"
                  [showRightSide]="false"
                  [customRight]="true"
                >
                  <div class="leftSideBody1 flex items-center gap-x-2">
                    <span class="text-[#082230CC]"
                      ><app-folder-icon-svg
                    /></span>
                    <div>{{ item.domain.name }}</div>
                  </div>
                  <p
                    class="leftSideBody1 flex items-center gap-x-2 text-[#474D66] text-sm"
                  >
                    <span class="text-[#082230CC]"><app-alarm-svg /></span>
                    <span>{{ convertDuration(item.duration) }}</span>
                  </p>
                  <button
                    class="rightSide underline text-blue-400 text-sm hover:cursor-pointer"
                    (click)="onRouteClick(item)"
                  >
                    View Test
                  </button>
                </app-general-long-card>
              </div>
            }
          </div>
        </section>
      </div>
    } @else {
      <app-no-result-found></app-no-result-found>
    }
  }
</main>
