import { Location } from '@angular/common';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ActivatedRoute, Router } from '@angular/router';
import { of, throwError } from 'rxjs';
import { TestsData } from '../../../Interfaces/Types/test';
import { AssessmentService } from '../../../services/test-management-service/assessment-service/assessment.service';
import { ToastService } from '../../../services/toast-service/toast.service';
import {
  dummyAssessmentWithTest,
  mockApiResponse,
  mockTestData,
} from '../../../utils/assessment/assessment';
import { AssessmentPreviewComponent } from './assessment-preview.component';

describe('AssessmentPreviewComponent', () => {
  let component: AssessmentPreviewComponent;
  let fixture: ComponentFixture<AssessmentPreviewComponent>;
  let mockAssessmentService: jest.Mocked<AssessmentService>;
  let mockRouter: jest.Mocked<Router>;
  let mockActivatedRoute: Partial<ActivatedRoute>;
  let mockLocation: jest.Mocked<Location>;
  let mockToastService: jest.Mocked<ToastService>;

  beforeEach(() => {
    mockAssessmentService = {
      getOneAssessment: jest.fn(),
    } as unknown as jest.Mocked<AssessmentService>;

    mockRouter = {
      navigate: jest.fn(),
    } as unknown as jest.Mocked<Router>;

    mockActivatedRoute = {
      params: of({ assessmentId: '123' }),
    };

    mockLocation = {
      back: jest.fn(),
    } as unknown as jest.Mocked<Location>;

    mockToastService = {
      onShow: jest.fn(),
    } as unknown as jest.Mocked<ToastService>;

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        { provide: AssessmentService, useValue: mockAssessmentService },
        { provide: Router, useValue: mockRouter },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: Location, useValue: mockLocation },
        { provide: ToastService, useValue: mockToastService },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(AssessmentPreviewComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should fetch assessment data on init', () => {
    mockAssessmentService.getOneAssessment.mockReturnValue(of(mockApiResponse));
    component.ngOnInit();
    expect(mockAssessmentService.getOneAssessment).toHaveBeenCalledWith('123');
    expect(component.assessmentData).toEqual(dummyAssessmentWithTest);
  });
  it('should handle error when fetching assessment data', () => {
    const errorMessage = 'Error fetching assessment';
    mockAssessmentService.getOneAssessment.mockReturnValue(
      throwError(() => new Error(errorMessage))
    );
    component.ngOnInit();
    expect(mockToastService.onShow).toHaveBeenCalledWith(
      'error',
      new Error(errorMessage),
      true,
      'error'
    );
  });

  it('should get icon from feature name', () => {
    const icon = component.getFeatureIcon('ID Capture');
    expect(icon).toBe('assets/icons/id-card.svg');
  });

  it('should return undefined icon if key is not found', () => {
    const icon = component.getFeatureIcon('Unknown Feature');
    expect(icon).toBeUndefined();
  });

  it('should navigate to edit assessment', () => {
    component.assessmentId = '123';
    component.navigateToEdit();
    expect(mockRouter.navigate).toHaveBeenCalledWith(
      ['/dashboard/test-management/assessments//edit-assessments'],
      { queryParams: { assessmentId: '123' } }
    );
  });
  it('should navigate to history page', () => {
    component.assessmentId = '123';
    component.navigateToHistory();
    expect(mockRouter.navigate).toHaveBeenCalledWith([
      '/dashboard/test-management/assessments//history/123',
    ]);
  });

  it('should navigate to view report page', () => {
    component.assessmentId = '456';
    component.onViewReport();
    expect(mockRouter.navigate).toHaveBeenCalledWith(
      ['/dashboard/report-management/assessment-candidates/456'],
      {
        queryParams: {
          fromAssessments: true,
        },
      }
    );
  });

  it('should convert duration correctly', () => {
    expect(component.convertDuration(3600)).toBe('1hrs  0 mins');
    expect(component.convertDuration(5400)).toBe('1hrs  30 mins');
    expect(component.convertDuration(undefined)).toBeUndefined();
  });

  it('should navigate to test preview', () => {
    component.onRouteClick(mockTestData);

    expect(mockRouter.navigate).toHaveBeenCalledWith(
      ['dashboard/test-management/tests/preview-test'],
      { queryParams: { testId: 'test-1' } }
    );
  });
  it('should handle null or undefined item without throwing errors', () => {
    expect(() =>
      component.onRouteClick(null as unknown as TestsData)
    ).not.toThrow();
    expect(() =>
      component.onRouteClick(undefined as unknown as TestsData)
    ).not.toThrow();
    expect(mockRouter.navigate).not.toHaveBeenCalled();
  });
  it('should go back when goBack is called', () => {
    component.goBack();
    expect(mockLocation.back).toHaveBeenCalled();
  });
  it('should display assessment data correctly after retrieval', () => {
    mockAssessmentService.getOneAssessment.mockReturnValue(of(mockApiResponse));

    component.assessmentId = 'assessment-123';
    component.getOneAssessment();

    expect(mockAssessmentService.getOneAssessment).toHaveBeenCalledWith(
      'assessment-123'
    );
    expect(component.assessmentData).toEqual(dummyAssessmentWithTest);
    expect(component.isLoading).toBe(false);
  });
  it('should navigate to dispatch assessment', () => {
    component.assessmentId = '789';
    component.assessmentData = dummyAssessmentWithTest;
    component.dispatchAssessment();

    expect(mockRouter.navigate).toHaveBeenCalledWith(
      ['dashboard/test-management/assessments/dispatch'],
      {
        queryParams: {
          assessmentId: '789',
          assessmentTitle: 'Mathematics--Assessment',
        },
      }
    );
  });
});
