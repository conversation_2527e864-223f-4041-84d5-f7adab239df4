import { Component, OnInit } from '@angular/core';
import { AssessmentService } from '../../../services/test-management-service/assessment-service/assessment.service';
import { ActivatedRoute, Router } from '@angular/router';
import { AssessmentWithTest } from '../../../Interfaces/Types/assessmentInterface';
import { GeneralLongCardComponent } from '../../../components/general-long-card/general-long-card.component';
import { convertDurationPreview } from '../../../utils/testManagement';
import { TestsData } from '../../../Interfaces/Types/test';
import { QuestionTitleComponent } from '../../Questions/main-questions-page/components/question-title/question-title.component';
import { TruncateLongTextPipe } from '../../../pipes/truncate-long-text/truncate-long-text.pipe';
import { Location } from '@angular/common';
import { NoResultFoundComponent } from '../../../no-result-found/no-result-found.component';
import { ToastService } from '../../../services/toast-service/toast.service';
import { CustomButtonComponent } from '../../../components/custombutton/custombutton.component';
import { SharedService } from '../../../services/shared/shared.service';
import { AnalyticsService } from '../../../services/analytics.service';
import { SafeHtmlDirective } from '../../../directives/safe-html.directive';
import {
  ConfigWithInterval,
  getConfigurationsWithIntervals,
} from '../../../utils/assessment/assessmentConfigurationSecurity';
import { AlarmSvgComponent } from '../../../components/alarm-svg/alarm-svg.component';
import { FolderIconSvgComponent } from '../../../components/folder-icon-svg/folder-icon-svg.component';

@Component({
  selector: 'app-assessment-preview',
  standalone: true,
  imports: [
    GeneralLongCardComponent,
    QuestionTitleComponent,
    TruncateLongTextPipe,
    NoResultFoundComponent,
    CustomButtonComponent,
    SafeHtmlDirective,
    AlarmSvgComponent,
    FolderIconSvgComponent,
  ],
  templateUrl: './assessment-preview.component.html',
})
export class AssessmentPreviewComponent implements OnInit {
  assessmentId = '';
  assessmentData: AssessmentWithTest | undefined;
  selectedProctors: string[] = [];
  isLoading = false;
  title = 'Assessment Preview';
  assessmentUrl = '/dashboard/test-management/assessments/';
  securityIcons: { [key: string]: string } = {
    'ID Capture': 'assets/icons/id-card.svg',
    'Idle Time Tracking': 'assets/icons/idle-time.svg',
    'Honour Code': 'assets/icons/asses-file.svg',
    'Screen Capture': 'assets/icons/screen-capturee.svg',
    'Window Violation': 'assets/icons/danger.svg',
    'Candidate Capture': 'assets/icons/face-capturee.svg',
    'Enable Survey After Test': 'assets/icons/clipboard.svg',
  };

  constructor(
    private readonly assessmentService: AssessmentService,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly location: Location,
    private readonly toastService: ToastService,
    private readonly sharedService: SharedService,
    private readonly analyticsService: AnalyticsService
  ) {}
  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      this.assessmentId = params['assessmentId'];

      if (this.assessmentId) {
        this.getOneAssessment();
      }
    });
  }

  getOneAssessment() {
    this.isLoading = true;
    this.assessmentService.getOneAssessment(this.assessmentId).subscribe({
      next: (res) => {
        this.assessmentData = res.data;
        if (this.assessmentData) {
          this.getConfigurationsWithIntervals();
          this.isLoading = false;
        }
      },
      error: (err) => {
        this.toastService.onShow('error', err, true, 'error');
      },
    });
  }

  convertDuration(item: number | undefined) {
    if (!item) {
      return;
    }
    return convertDurationPreview(item);
  }

  onRouteClick(item: TestsData) {
    if (!item) {
      return;
    }

    this.router.navigate(['dashboard/test-management/tests/preview-test'], {
      queryParams: { testId: item.id },
    });

    this.analyticsService.track('Test Previewed', { testId: item.id }).then();
  }

  goBack() {
    this.location.back();
  }

  getFeatureIcon(featureName: string): string {
    return this.securityIcons[featureName];
  }

  navigateToEdit(): void {
    const queryParams = this.assessmentId
      ? { assessmentId: this.assessmentId }
      : {};
    this.router.navigate([`${this.assessmentUrl}/edit-assessments`], {
      queryParams,
    });
  }

  navigateToHistory(): void {
    this.router.navigate([
      `${this.assessmentUrl}/history/${this.assessmentId}`,
    ]);
  }

  onViewReport() {
    this.router.navigate(
      [
        '/dashboard/report-management/assessment-candidates/' +
          this.assessmentId,
      ],
      {
        queryParams: {
          fromAssessments: true,
        },
      }
    );
  }

  getConfigurationsWithIntervals(): ConfigWithInterval[] {
    const baseConfigs = (this.assessmentData?.proctorFeatures ?? []).map(
      (feature, index) => ({
        name: feature.name,
        selected: true,
        iconIndex: index,
      })
    );

    if (this.assessmentData?.conductSurvey) {
      baseConfigs.push({
        name: 'Enable Survey After Test',
        selected: true,
        iconIndex: baseConfigs.length,
      });
    }

    return getConfigurationsWithIntervals(
      baseConfigs,
      this.assessmentData?.camerashotsInterval,
      this.assessmentData?.screenshotsInterval
    );
  }

  dispatchAssessment() {
    this.router.navigate(['dashboard/test-management/assessments/dispatch'], {
      queryParams: {
        assessmentId: this.assessmentId,
        assessmentTitle: this.assessmentData?.title?.replaceAll(/ /g, '--'),
      },
    });
  }
}
