import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DispatchAssessmentComponent } from './dispatch-assessment.component';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription, of, throwError } from 'rxjs';
import { HttpClientModule } from '@angular/common/http';
import { AssessmentService } from '../../../services/test-management-service/assessment-service/assessment.service';
import { ToastService } from '../../../services/toast-service/toast.service';
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import {
  DispatchedAssessmentResponse,
  ProctorFeaturesApiResponse,
} from '../../../Interfaces/Types/assessmentInterface';
import { provideStore } from '@ngrx/store';

describe('DispatchAssessmentComponent', () => {
  let component: DispatchAssessmentComponent;
  let fixture: ComponentFixture<DispatchAssessmentComponent>;
  let mockActivatedRoute: ActivatedRoute;
  let mockAssessmentService: jest.Mocked<AssessmentService>;
  let mockToastService: jest.Mocked<ToastService>;
  let mockRouter: jest.Mocked<Router>;

  beforeEach(async () => {
    mockAssessmentService = {
      getAllSelectedProctoring: jest.fn(),
      dispatchAssessmentByEmail: jest
        .fn()
        .mockReturnValue({ subscribe: jest.fn() }),
      getAssessmentLink: jest.fn().mockReturnValue({ subscribe: jest.fn() }),
      getAllAssessment: jest.fn(),
    } as unknown as jest.Mocked<AssessmentService>;

    mockToastService = {
      onShow: jest.fn(),
    } as unknown as jest.Mocked<ToastService>;

    mockRouter = {
      navigate: jest.fn(),
    } as unknown as jest.Mocked<Router>;
    await TestBed.configureTestingModule({
      imports: [DispatchAssessmentComponent, HttpClientModule],
      providers: [
        FormBuilder,
        { provide: AssessmentService, useValue: mockAssessmentService },
        { provide: ToastService, useValue: mockToastService },
        { provide: Router, useValue: mockRouter },
        provideStore({}),
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({
              assessmentId: '123',
              assessmentTitle: 'Test Assessment',
            }),
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(DispatchAssessmentComponent);
    component = fixture.componentInstance;
    mockActivatedRoute = TestBed.inject(ActivatedRoute);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have queryParams', () => {
    mockActivatedRoute.queryParams.subscribe((params) => {
      expect(params).toEqual({
        assessmentId: '123',
        assessmentTitle: 'Test Assessment',
      });
    });
  });

  it('should have queryParams', () => {
    component.ngOnInit();
    expect(component.assessmentId).toBe('123');
    expect(component.assessmentTitle).toBe('Test Assessment');
  });

  it('should have dispatchAssessmentForm', () => {
    expect(component.dispatchAssessmentForm).toBeTruthy();
  });

  it('should have patch form values', () => {
    component.patchForm({}, []);
    expect(component.dispatchAssessmentForm.value.camerashotsInterval).toBe('');
    expect(component.dispatchAssessmentForm.value.screenshotsInterval).toBe('');
  });

  it('should validate email on change', () => {
    component.dispatchAssessmentForm.controls['email'].setValue(
      '<EMAIL>'
    );
    component.validEmailCheckOnChange();
    expect(component.validEmail).toBeTruthy();

    component.dispatchAssessmentForm.controls['email'].setValue(
      'invalid-email'
    );
    component.validEmailCheckOnChange();
    expect(component.validEmail).toBeFalsy();
  });

  it('should patch form values and update configurations array', () => {
    const intervals = { camerashotsInterval: '30', screenshotsInterval: '60' };
    const proctorFeatures = [
      { name: 'Feature1', selected: true, value: 'Feature1', locked: false },
      { name: 'Feature2', selected: false, value: 'Feature2', locked: true },
    ];

    component.patchForm(intervals, proctorFeatures);

    expect(component.dispatchAssessmentForm.value.camerashotsInterval).toBe(
      '30'
    );
    expect(component.dispatchAssessmentForm.value.screenshotsInterval).toBe(
      '60'
    );
    expect(component.configurationsArray.length).toBe(2);
    expect(component.configurationsArray.at(0).getRawValue()).toEqual({
      selected: true,
      name: 'Feature1',
      locked: false,
    });
    expect(component.configurationsArray.at(1).getRawValue()).toEqual({
      selected: false,
      name: 'Feature2',
      locked: true,
    });
  });

  it('should return true if value is "Yes"', () => {
    expect(component.showResults('Yes')).toBeTruthy();
  });

  it('should return false if value is not "Yes"', () => {
    expect(component.showResults('No')).toBeFalsy();
    expect(component.showResults('')).toBeFalsy();
  });

  it('should remove email from the list', () => {
    component.emails = ['<EMAIL>', '<EMAIL>'];
    component.removeEmail(0);
    expect(component.emails).toEqual(['<EMAIL>']);

    component.removeEmail(1);
    expect(component.emails).toEqual(['<EMAIL>']); // Index out of bounds, no change
  });

  it('should remove email on Enter key press', () => {
    component.emails = ['<EMAIL>', '<EMAIL>'];
    const event = new KeyboardEvent('keydown', { code: 'Enter' });
    component.onKeydownRemoveEmail(event, 0);
    expect(component.emails).toEqual(['<EMAIL>']);
  });

  it('should filter emails based on search term', () => {
    component.emails = ['<EMAIL>', '<EMAIL>'];
    component.onSearch('test1');
    expect(component.filteredEmails).toEqual(['<EMAIL>']);
  });

  it('should close cancel modal', () => {
    component.showCancelModal = true;
    component.closeCancelModal();
    expect(component.showCancelModal).toBeFalsy();
  });

  describe('Form Validation', () => {
    it('should initialize with invalid form', () => {
      expect(component.dispatchAssessmentForm.valid).toBeFalsy();
    });

    it('should validate email format', () => {
      const emailControl = component.dispatchAssessmentForm.get('email');

      emailControl?.setValue('invalid-email');
      expect(emailControl?.valid).toBeFalsy();

      emailControl?.setValue('<EMAIL>');
      expect(emailControl?.valid).toBeTruthy();
    });

    it('should validate camera shots interval range', () => {
      const control = component.dispatchAssessmentForm.get(
        'camerashotsInterval'
      );

      control?.setValue(10);
      expect(control?.errors?.['min']).toBeTruthy();

      control?.setValue(130);
      expect(control?.errors?.['max']).toBeTruthy();

      control?.setValue(60);
      expect(control?.valid).toBeTruthy();
    });
  });
  describe('Email Management', () => {
    it('should add valid email to the list', () => {
      const validEmail = '<EMAIL>';
      component.dispatchAssessmentForm.get('email')?.setValue(validEmail);
      component.addEmail();

      expect(component.emails).toContain(validEmail);
      expect(component.dispatchAssessmentForm.get('email')?.value).toBeNull();
    });

    it('should not add invalid email to the list', () => {
      const invalidEmail = 'invalid-email';
      component.dispatchAssessmentForm.get('email')?.setValue(invalidEmail);
      component.addEmail();

      expect(component.emails).not.toContain(invalidEmail);
    });

    it('should not add duplicate email', () => {
      const email = '<EMAIL>';
      component.emails = [email];
      component.dispatchAssessmentForm.get('email')?.setValue(email);
      component.addEmail();

      expect(component.emails.filter((e) => e === email).length).toBe(1);
    });

    it('should remove email from list', () => {
      const email = '<EMAIL>';
      component.emails = [email];
      component.removeEmail(0);

      expect(component.emails).not.toContain(email);
    });
  });

  describe('File Management', () => {
    let getElementByIdSpy: jest.SpyInstance;

    afterEach(() => {
      // Clean up DOM mocks after each test
      if (getElementByIdSpy) {
        getElementByIdSpy.mockRestore();
      }
    });

    it('should remove selected file and clear emails', () => {
      const mockFile = new File(['content'], 'test.csv', { type: 'text/csv' });
      component.selectedFile = mockFile;
      component.emails = ['<EMAIL>', '<EMAIL>'];
      component.filteredEmails = ['<EMAIL>', '<EMAIL>'];

      const mockInput = { value: 'test.csv' } as HTMLInputElement;
      getElementByIdSpy = jest
        .spyOn(document, 'getElementById')
        .mockReturnValue(mockInput);

      component.removeSelectedFile();

      expect(component.selectedFile).toBeNull();
      expect(component.emails).toEqual([]);
      expect(component.filteredEmails).toEqual([]);
      expect(mockInput.value).toBe('');
    });

    it('should handle removeSelectedFile when no DOM element exists', () => {
      const mockFile = new File(['content'], 'test.csv', { type: 'text/csv' });
      component.selectedFile = mockFile;
      component.emails = ['<EMAIL>'];
      getElementByIdSpy = jest
        .spyOn(document, 'getElementById')
        .mockReturnValue(null);

      expect(() => component.removeSelectedFile()).not.toThrow();
      expect(component.selectedFile).toBeNull();
      expect(component.emails).toEqual([]);
    });
  });

  describe('Assessment Dispatch', () => {
    beforeEach(() => {
      component.dispatchAssessmentForm.patchValue({
        expireDate: '2024-12-31',
        expireTime: '23:59',
        showResults: 'Yes',
        camerashotsInterval: 60,
        screenshotsInterval: 60,
      });
      component.emails = ['<EMAIL>'];
    });

    it('should dispatch assessment by email successfully', () => {
      mockAssessmentService.dispatchAssessmentByEmail.mockReturnValue(
        of({
          alreadyInvitedCandidates: [],
        } as unknown as DispatchedAssessmentResponse)
      );

      component.dispatchByEmail({
        proctorFeatures: ['Honour Code'],
        showClock: true,
        expireDate: '2024-12-31T23:59',
        camerashotsInterval: '60',
        conductSurvey: false,
        screenshotsInterval: '60',
        showResults: true,
        emails: ['<EMAIL>'],
      });

      expect(
        mockAssessmentService.dispatchAssessmentByEmail
      ).toHaveBeenCalled();
      expect(mockToastService.onShow).toHaveBeenCalledWith(
        'Assessment Dispatched ',
        expect.any(String),
        true,
        'success'
      );
    });

    it('should handle dispatch error', () => {
      const errorMessage = 'Dispatch failed';
      mockAssessmentService.dispatchAssessmentByEmail.mockReturnValue(
        throwError(() => errorMessage)
      );

      component.dispatchByEmail({
        proctorFeatures: ['Honour Code'],
        showClock: true,
        expireDate: '2024-12-31T23:59',
        camerashotsInterval: '60',
        conductSurvey: false,
        screenshotsInterval: '60',
        showResults: true,
        emails: ['<EMAIL>'],
      });

      expect(mockToastService.onShow).toHaveBeenCalledWith(
        'error',
        errorMessage,
        true,
        'error'
      );
    });
  });

  describe('Navigation', () => {
    it('should show cancel modal', () => {
      component.showCancel();
      expect(component.showCancelModal).toBeTruthy();
    });

    it('should close cancel modal', () => {
      component.showCancelModal = true;
      component.closeCancelModal();
      expect(component.showCancelModal).toBeFalsy();
    });

    it('should navigate to assessments page on cancel confirm', () => {
      component.confirmCancelDispatch();
      expect(mockRouter.navigate).toHaveBeenCalledWith([
        '/dashboard/test-management/assessments',
      ]);
    });
  });

  it('should call getAllSelectedProctoring when assessmentId is present', () => {
    const mockResponse = {
      data: {
        configuration: {
          camerashotsInterval: 5,
          screenshotsInterval: 10,
          conductSurvey: true,
          showClock: false,
          proctorFeatures: [{ name: 'Feature1' }, { name: 'Feature2' }],
        },
      },
    };

    jest
      .spyOn(mockAssessmentService, 'getAllSelectedProctoring')
      .mockReturnValue(
        of(mockResponse as unknown as ProctorFeaturesApiResponse)
      );
    jest.spyOn(component, 'patchForm');
    component.ngOnInit();

    expect(component.assessmentId).toBe('123');
    expect(component.assessmentTitle).toBe('Test Assessment');
    expect(component.configurationSubscription).toBeDefined();
  });

  describe('validForm', () => {
    it('should return true when form is valid and emails list is not empty', () => {
      component.dispatchAssessmentForm = new FormGroup({
        someField: new FormControl('valid value', Validators.required),
      });
      component.emails = ['<EMAIL>'];
      expect(component.validForm()).toBe(true);
    });

    it('should return false when form is valid but emails list is empty', () => {
      component.dispatchAssessmentForm = new FormGroup({
        someField: new FormControl('valid value', Validators.required),
      });
      component.emails = [];
      expect(component.validForm()).toBe(false);
    });
  });

  describe('onDispatch', () => {
    it('should dispatch assessment by email when isDispatchByEmail is true and selectModeOfDispatch is "Dispatch to email"', () => {
      component.isDispatchByEmail = true;
      component.selectModeOfDispatch = 'Dispatch to email';
      component.dispatchAssessmentForm = {
        value: {
          configurations: [],
          expireDate: '2023-12-31',
          expireTime: '23:59',
          camerashotsInterval: '5',
          screenshotsInterval: '10',
          showResults: true,
        },
      } as unknown as FormGroup;
      component.emails = ['<EMAIL>'];
      jest.spyOn(component, 'dispatchByEmail');

      component.onDispatch();

      expect(component.dispatchByEmail).toHaveBeenCalled();
    });

    it('should handle empty configurations array gracefully', () => {
      component.isDispatchByEmail = false;
      component.dispatchAssessmentForm = {
        value: {
          configurations: [],
          expireDate: '2023-12-31',
          expireTime: '23:59',
          camerashotsInterval: '5',
          screenshotsInterval: '10',
          showResults: true,
        },
      } as FormGroup;
      jest.spyOn(component, 'dispatchByAssessmentLink');

      component.onDispatch();

      expect(component.dispatchByAssessmentLink).toHaveBeenCalled();
    });
  });

  describe('uploadEmailFromCSV', () => {
    it('should add valid emails to the emails list when CSV contains valid email addresses', () => {
      const csvContent = '<EMAIL>\<EMAIL>';
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const file = new File([blob], 'emails.csv', { type: 'text/csv' });
      const event = { target: { files: [file] } } as unknown as Event;

      component.uploadEmailFromCSV(event);

      expect(component.emails).toEqual([]);
      expect(component.inValidEmails).toEqual([]);
    });

    it('should not add any emails when CSV is empty', () => {
      const csvContent = '';
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const file = new File([blob], 'empty.csv', { type: 'text/csv' });
      const event = { target: { files: [file] } } as unknown as Event;

      component.uploadEmailFromCSV(event);

      expect(component.emails).toEqual([]);
      expect(component.inValidEmails).toEqual([]);
    });
  });

  describe('previousToRoutePage', () => {
    it('should navigate to the assessments page when called', () => {
      component.previousToRoutePage();
      expect(mockRouter.navigate).toHaveBeenCalledWith([
        'dashboard/test-management/assessments',
      ]);
    });

    it('should handle error when router service is unavailable', () => {
      try {
        component.previousToRoutePage();
      } catch (error) {
        expect(error).toBeDefined();
      }
    });
  });

  describe('closeAlreadyInvitedCandidatesModal', () => {
    it('should set showAlreadyInvitedCandidatesModal to false when called', () => {
      component.showAlreadyInvitedCandidatesModal = true;
      component.closeAlreadyInvitedCandidatesModal();
      expect(component.showAlreadyInvitedCandidatesModal).toBe(false);
    });

    it('should not change state when modal is already closed', () => {
      component.showAlreadyInvitedCandidatesModal = false;
      component.closeAlreadyInvitedCandidatesModal();
      expect(component.showAlreadyInvitedCandidatesModal).toBe(false);
    });
  });

  describe('selectedOption', () => {
    it('should update selectModeOfDispatch with the event value', () => {
      const eventValue = 'email';
      component.selectedOption(eventValue);
      expect(component.selectModeOfDispatch).toBe(eventValue);
    });

    it('should handle empty string event parameter', () => {
      const eventValue = '';
      component.selectedOption(eventValue);
      expect(component.selectModeOfDispatch).toBe(eventValue);
    });
  });

  it('should #isTimeInValid return true when time is invalid', () => {
    component.dispatchAssessmentForm.patchValue({
      expireTime: new Date().toTimeString(),
      expireDate: new Date().toDateString(),
    });
    expect(component.isTimeInValid('expireDate', 'expireTime')).toBe(false);
  });

  describe('Interval Validation Methods', () => {
    beforeEach(() => {
      component.dispatchAssessmentForm.patchValue({
        camerashotsInterval: 20,
        screenshotsInterval: 25,
      });
    });

    it('should return true when camerashots interval is invalid', () => {
      component.dispatchAssessmentForm
        .get('camerashotsInterval')
        ?.setErrors({ min: true });
      component.dispatchAssessmentForm
        .get('camerashotsInterval')
        ?.markAsTouched();
      expect(component.isCameraShotsIntervalInvalid()).toBeTruthy();
    });

    it('should return false when camerashots interval is valid', () => {
      component.dispatchAssessmentForm
        .get('camerashotsInterval')
        ?.setErrors(null);
      expect(component.isCameraShotsIntervalInvalid()).toBeFalsy();
    });

    it('should return true when screenshots interval is invalid', () => {
      component.dispatchAssessmentForm
        .get('screenshotsInterval')
        ?.setErrors({ max: true });
      component.dispatchAssessmentForm
        .get('screenshotsInterval')
        ?.markAsDirty();
      expect(component.isScreenShotsIntervalInvalid()).toBeTruthy();
    });

    it('should return false when screenshots interval is valid', () => {
      component.dispatchAssessmentForm
        .get('screenshotsInterval')
        ?.setErrors(null);
      expect(component.isScreenShotsIntervalInvalid()).toBeFalsy();
    });
  });

  describe('Interval Input Handling', () => {
    it('should set minimum value on invalid input change', () => {
      const mockEvent = {
        target: { value: '10' },
      } as unknown as Event;

      jest.spyOn(component.dispatchAssessmentForm, 'get').mockReturnValue({
        setValue: jest.fn(),
      } as unknown as FormControl);

      component.onIntervalInputChange('camerashotsInterval', mockEvent);

      setTimeout(() => {
        expect(
          component.dispatchAssessmentForm.get('camerashotsInterval')?.setValue
        ).toHaveBeenCalledWith(15);
      }, 1);
    });

    it('should handle empty value on input change', () => {
      const mockEvent = {
        target: { value: '' },
      } as unknown as Event;

      jest.spyOn(component.dispatchAssessmentForm, 'get').mockReturnValue({
        setValue: jest.fn(),
      } as unknown as FormControl);

      component.onIntervalInputChange('screenshotsInterval', mockEvent);

      setTimeout(() => {
        expect(
          component.dispatchAssessmentForm.get('screenshotsInterval')?.setValue
        ).toHaveBeenCalledWith(15);
      }, 1);
    });

    it('should set minimum value on blur when value is too low', () => {
      const mockEvent = {
        target: { value: '5' },
      } as unknown as Event;

      jest.spyOn(component.dispatchAssessmentForm, 'get').mockReturnValue({
        setValue: jest.fn(),
      } as unknown as FormControl);

      component.onIntervalInputBlur('camerashotsInterval', mockEvent);
      expect(
        component.dispatchAssessmentForm.get('camerashotsInterval')?.setValue
      ).toHaveBeenCalledWith(15);
    });

    it('should set maximum value on blur when value is too high', () => {
      const mockEvent = {
        target: { value: '150' },
      } as unknown as Event;

      jest.spyOn(component.dispatchAssessmentForm, 'get').mockReturnValue({
        setValue: jest.fn(),
      } as unknown as FormControl);

      component.onIntervalInputBlur('screenshotsInterval', mockEvent);
      expect(
        component.dispatchAssessmentForm.get('screenshotsInterval')?.setValue
      ).toHaveBeenCalledWith(120);
    });

    it('should not change value on blur when value is valid', () => {
      const mockEvent = {
        target: { value: '60' },
      } as unknown as Event;

      jest.spyOn(component.dispatchAssessmentForm, 'get').mockReturnValue({
        setValue: jest.fn(),
      } as unknown as FormControl);

      component.onIntervalInputBlur('camerashotsInterval', mockEvent);
      expect(
        component.dispatchAssessmentForm.get('camerashotsInterval')?.setValue
      ).not.toHaveBeenCalled();
    });
  });

  describe('Keyboard Event Handling', () => {
    it('should allow numeric keys', () => {
      const mockEvent = {
        key: '5',
        preventDefault: jest.fn(),
        target: { value: '20' },
      } as unknown as KeyboardEvent;

      component.onIntervalKeyDown(mockEvent);
      expect(mockEvent.preventDefault).not.toHaveBeenCalled();
    });

    it('should prevent non-numeric keys', () => {
      const mockEvent = {
        key: 'a',
        preventDefault: jest.fn(),
        target: { value: '20' },
      } as unknown as KeyboardEvent;

      component.onIntervalKeyDown(mockEvent);
      expect(mockEvent.preventDefault).toHaveBeenCalled();
    });

    it('should allow control keys', () => {
      const allowedKeys = [
        'Backspace',
        'Delete',
        'Tab',
        'Escape',
        'Enter',
        'ArrowLeft',
        'ArrowRight',
      ];

      allowedKeys.forEach((key) => {
        const mockEvent = {
          key,
          preventDefault: jest.fn(),
          target: { value: '120' },
        } as unknown as KeyboardEvent;

        component.onIntervalKeyDown(mockEvent);
        expect(mockEvent.preventDefault).not.toHaveBeenCalled();
      });
    });

    it('should allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X', () => {
      const ctrlKeys = ['a', 'c', 'v', 'x'];

      ctrlKeys.forEach((key) => {
        const mockEvent = {
          key,
          ctrlKey: true,
          preventDefault: jest.fn(),
          target: { value: '20' },
        } as unknown as KeyboardEvent;

        component.onIntervalKeyDown(mockEvent);
        expect(mockEvent.preventDefault).not.toHaveBeenCalled();
      });
    });

    it('should prevent backspace/delete when value length is 2 or less', () => {
      const mockEvent = {
        key: 'Backspace',
        preventDefault: jest.fn(),
        target: { value: '15' },
      } as unknown as KeyboardEvent;

      component.onIntervalKeyDown(mockEvent);
      expect(mockEvent.preventDefault).toHaveBeenCalled();
    });
  });

  describe('Drag and Drop Functionality', () => {
    it('should handle drag over event', () => {
      const mockEvent = {
        preventDefault: jest.fn(),
      } as unknown as DragEvent;

      component.onDragOver(mockEvent);
      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(component.isDragging).toBeTruthy();
    });

    it('should handle drag leave event', () => {
      const mockEvent = {
        preventDefault: jest.fn(),
      } as unknown as DragEvent;

      component.onDragLeave(mockEvent);
      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(component.isDragging).toBeFalsy();
    });

    it('should handle file div keydown with Enter key', () => {
      const mockFileInput = {
        click: jest.fn(),
      } as unknown as HTMLInputElement;

      const mockEvent = {
        key: 'Enter',
        preventDefault: jest.fn(),
      } as unknown as KeyboardEvent;

      component.onFileDivKeydown(mockEvent, mockFileInput);
      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(mockFileInput.click).toHaveBeenCalled();
    });

    it('should handle file div keydown with Space key', () => {
      const mockFileInput = {
        click: jest.fn(),
      } as unknown as HTMLInputElement;

      const mockEvent = {
        key: ' ',
        preventDefault: jest.fn(),
      } as unknown as KeyboardEvent;

      component.onFileDivKeydown(mockEvent, mockFileInput);
      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(mockFileInput.click).toHaveBeenCalled();
    });

    it('should not trigger click for other keys', () => {
      const mockFileInput = {
        click: jest.fn(),
      } as unknown as HTMLInputElement;

      const mockEvent = {
        key: 'Tab',
        preventDefault: jest.fn(),
      } as unknown as KeyboardEvent;

      component.onFileDivKeydown(mockEvent, mockFileInput);
      expect(mockEvent.preventDefault).not.toHaveBeenCalled();
      expect(mockFileInput.click).not.toHaveBeenCalled();
    });
  });

  describe('Advanced Email Management', () => {
    it('should not add email when it is null', () => {
      component.dispatchAssessmentForm.patchValue({ email: null });
      const initialLength = component.emails.length;

      component.addEmail();
      expect(component.emails.length).toBe(initialLength);
    });

    it('should not add email when it is empty string', () => {
      component.dispatchAssessmentForm.patchValue({ email: '' });
      const initialLength = component.emails.length;

      component.addEmail();
      expect(component.emails.length).toBe(initialLength);
    });

    it('should trigger change detection after adding email', () => {
      jest.spyOn(component['cdr'], 'detectChanges');
      component.dispatchAssessmentForm.patchValue({
        email: '<EMAIL>',
      });

      component.addEmail();
      expect(component['cdr'].detectChanges).toHaveBeenCalled();
    });
  });

  describe('Assessment Dispatch Advanced Cases', () => {
    beforeEach(() => {
      component.dispatchAssessmentForm.patchValue({
        expireDate: '2024-12-31',
        expireTime: '23:59',
        showResults: 'Yes',
        camerashotsInterval: 60,
        screenshotsInterval: 60,
        configurations: [],
      });
      component.emails = ['<EMAIL>'];
    });

    it('should dispatch assessment with commencement date when provided', () => {
      component.dispatchAssessmentForm.patchValue({
        commencementDate: '2024-12-01',
        commencementTime: '09:00',
      });

      jest.spyOn(component, 'dispatchByEmail');
      component.isDispatchByEmail = true;
      component.selectModeOfDispatch = 'Dispatch to email';

      component.onDispatch();

      expect(component.dispatchByEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          commenceDate: '2024-12-01T09:00',
        })
      );
    });

    it('should dispatch by assessment link when isDispatchByEmail is false', () => {
      jest.spyOn(component, 'dispatchByAssessmentLink');
      component.isDispatchByEmail = false;

      component.onDispatch();

      expect(component.dispatchByAssessmentLink).toHaveBeenCalled();
      expect(component.isLoading).toBeTruthy();
    });

    it('should dispatch by assessment link when selectModeOfDispatch is Copy link', () => {
      jest.spyOn(component, 'dispatchByAssessmentLink');
      component.isDispatchByEmail = true;
      component.selectModeOfDispatch = 'Copy link';

      component.onDispatch();

      expect(component.dispatchByAssessmentLink).toHaveBeenCalled();
    });

    it('should handle dispatch by assessment link success', () => {
      const mockResponse = {
        data: { link: 'https://test-link.com' },
        success: true,
      };

      const mockWriteText = jest.fn().mockResolvedValue(undefined);
      Object.assign(navigator, {
        clipboard: { writeText: mockWriteText },
      });

      mockAssessmentService.getAssessmentLink.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'loadAssessment');

      component.dispatchByAssessmentLink({
        proctorFeatures: ['Honour Code'],
        showClock: true,
        expireDate: '2024-12-31T23:59',
        camerashotsInterval: '60',
        conductSurvey: false,
        screenshotsInterval: '60',
        showResults: true,
      });

      expect(mockAssessmentService.getAssessmentLink).toHaveBeenCalled();
      expect(mockWriteText).toHaveBeenCalledWith('https://test-link.com');
      expect(mockToastService.onShow).toHaveBeenCalledWith(
        'success',
        'Link Generated Successfully',
        true,
        'success'
      );
      expect(mockRouter.navigate).toHaveBeenCalledWith([
        '/dashboard/test-management/assessments',
      ]);
    });

    it('should handle dispatch by assessment link error', () => {
      const errorMessage = 'Link generation failed';
      mockAssessmentService.getAssessmentLink.mockReturnValue(
        throwError(() => errorMessage)
      );

      component.dispatchByAssessmentLink({
        proctorFeatures: ['Honour Code'],
        showClock: true,
        expireDate: '2024-12-31T23:59',
        camerashotsInterval: '60',
        conductSurvey: false,
        screenshotsInterval: '60',
        showResults: true,
      });

      expect(component.isLoading).toBeFalsy();
      expect(mockToastService.onShow).toHaveBeenCalledWith(
        'error',
        errorMessage,
        true,
        'error'
      );
    });
  });

  describe('Utility Methods', () => {
    it('should return correct form control', () => {
      const control = component.getFormControl('email');
      expect(control).toBeTruthy();
    });

    it('should route back to assessments page', () => {
      component.routeBack();
      expect(mockRouter.navigate).toHaveBeenCalledWith([
        '/dashboard/test-management/assessments',
      ]);
    });

    it('should call dispatchAssessmentsViaEmail and set isDispatchByEmail to true', () => {
      jest.spyOn(component, 'onDispatch');
      component.dispatchAssessmentsViaEmail();

      expect(component.isDispatchByEmail).toBeTruthy();
      expect(component.onDispatch).toHaveBeenCalled();
    });

    it('should call generateLink and set isDispatchByEmail to false', () => {
      jest.spyOn(component, 'onDispatch');
      component.generateLink();

      expect(component.isDispatchByEmail).toBeFalsy();
      expect(component.onDispatch).toHaveBeenCalled();
    });

    it('should call loadAssessment and set assessments to empty', () => {
      jest.spyOn(component['assessmentStore'], 'setOnlyAssessmentsToEmpty');
      component.loadAssessment();

      expect(
        component['assessmentStore'].setOnlyAssessmentsToEmpty
      ).toHaveBeenCalled();
    });
  });

  describe('Configuration Toggle', () => {
    beforeEach(() => {
      component.configurationsArray.push(
        component['fb'].group({
          selected: [false],
          name: ['Test Feature'],
          locked: [false],
        })
      );
    });

    it('should toggle configuration when not disabled', () => {
      component.toggleConfiguration(0, false);
      expect(
        component.configurationsArray.at(0).get('selected')?.value
      ).toBeTruthy();
    });

    it('should not toggle configuration when disabled', () => {
      const control = component.configurationsArray.at(0);
      control.disable();

      component.toggleConfiguration(0, false);
      expect(control.get('selected')?.value).toBeFalsy();
    });
  });

  describe('Date Time Validation', () => {
    it('should return false for isTimeInValid when no date control exists', () => {
      const result = component.isTimeInValid('nonexistentDate', 'expireTime');
      expect(result).toBeFalsy();
    });

    it('should return false for isTimeInValid when no time control exists', () => {
      const result = component.isTimeInValid('expireDate', 'nonexistentTime');
      expect(result).toBeFalsy();
    });

    it('should return false for isTimeInValid when date value is empty', () => {
      component.dispatchAssessmentForm.patchValue({
        expireDate: '',
        expireTime: '10:00',
      });

      const result = component.isTimeInValid('expireDate', 'expireTime');
      expect(result).toBeFalsy();
    });

    it('should return false for isTimeInValid when time value is empty', () => {
      component.dispatchAssessmentForm.patchValue({
        expireDate: '2024-12-31',
        expireTime: '',
      });

      const result = component.isTimeInValid('expireDate', 'expireTime');
      expect(result).toBeFalsy();
    });

    it('should validate commence date time correctly when both dates are provided', () => {
      component.dispatchAssessmentForm.patchValue({
        commencementDate: '2024-12-31',
        expireDate: '2024-12-30',
        commencementTime: '10:00',
        expireTime: '09:00',
      });

      const result = component.validateCommenceDateTime();
      expect(result).toBeTruthy();
    });

    it('should return false when commence date time is not provided', () => {
      component.dispatchAssessmentForm.patchValue({
        commencementDate: '',
        expireDate: '2024-12-30',
        commencementTime: '',
        expireTime: '09:00',
      });

      const result = component.validateCommenceDateTime();
      expect(result).toBeFalsy();
    });

    it('should return false for invalid date formats', () => {
      component.dispatchAssessmentForm.patchValue({
        commencementDate: 'invalid-date',
        expireDate: 'invalid-date',
        commencementTime: 'invalid-time',
        expireTime: 'invalid-time',
      });

      const result = component.validateCommenceDateTime();
      expect(result).toBeFalsy();
    });

    it('should return false when commence date is before expire date', () => {
      component.dispatchAssessmentForm.patchValue({
        commencementDate: '2024-12-28',
        expireDate: '2024-12-30',
        commencementTime: '10:00',
        expireTime: '11:00',
      });

      const result = component.validateCommenceDateTime();
      expect(result).toBeFalsy();
    });
  });

  describe('Form Validation Advanced', () => {
    it('should return false when form is invalid', () => {
      component.dispatchAssessmentForm.setErrors({ required: true });
      component.emails = ['<EMAIL>'];

      const result = component.isFormValid;
      expect(result).toBeFalsy();
    });

    it('should return false when emails list is empty', () => {
      component.dispatchAssessmentForm.setErrors(null);
      component.emails = [];

      const result = component.isFormValid;
      expect(result).toBeFalsy();
    });

    it('should return false when commence date time validation fails', () => {
      component.dispatchAssessmentForm.setErrors(null);
      component.emails = ['<EMAIL>'];
      component.dispatchAssessmentForm.patchValue({
        commencementDate: '2024-12-31',
        expireDate: '2024-12-30',
        commencementTime: '10:00',
        expireTime: '09:00',
      });

      const result = component.isFormValid;
      expect(result).toBeFalsy();
    });

    it('should return false when time is invalid', () => {
      component.dispatchAssessmentForm.setErrors(null);
      component.emails = ['<EMAIL>'];
      jest.spyOn(component, 'isTimeInValid').mockReturnValue(true);

      const result = component.isFormValid;
      expect(result).toBeFalsy();
    });
  });

  describe('Custom Validators', () => {
    it('should validate future date correctly - valid future date', () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 1);

      const control = {
        value: futureDate.toISOString().split('T')[0],
      } as AbstractControl;
      const result = component.futureDateValidator(control);

      expect(result).toBeNull();
    });

    it('should validate future date correctly - past date', () => {
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 1);

      const control = {
        value: pastDate.toISOString().split('T')[0],
      } as AbstractControl;
      const result = component.futureDateValidator(control);

      expect(result).toEqual({ pastDate: true });
    });

    it('should validate future date correctly - no value', () => {
      const control = { value: null } as AbstractControl;
      const result = component.futureDateValidator(control);

      expect(result).toBeNull();
    });

    it('should validate future time correctly - valid future time', () => {
      const futureDateTime = new Date();
      futureDateTime.setHours(futureDateTime.getHours() + 1);

      const mockParent = {
        get: jest.fn().mockImplementation((key: string) => {
          if (key === 'expireDate') {
            return { value: futureDateTime.toISOString().split('T')[0] };
          }
          return null;
        }),
      };

      const control = {
        value: futureDateTime.toTimeString().slice(0, 5),
        parent: mockParent,
      } as unknown as AbstractControl;

      const validator = component.futureTimeValidator();
      const result = validator(control);

      expect(result).toBeNull();
    });

    it('should validate future time correctly - past time', () => {
      const pastDateTime = new Date();
      pastDateTime.setHours(pastDateTime.getHours() - 1);

      const mockParent = {
        get: jest.fn().mockImplementation((key: string) => {
          if (key === 'expireDate') {
            return { value: pastDateTime.toISOString().split('T')[0] };
          }
          return null;
        }),
      };

      const control = {
        value: pastDateTime.toTimeString().slice(0, 5),
        parent: mockParent,
      } as unknown as AbstractControl;

      const validator = component.futureTimeValidator();
      const result = validator(control);

      expect(result).toEqual({ pastTime: true });
    });

    it('should validate future time correctly - no parent form', () => {
      const control = {
        value: '10:00',
        parent: null,
      } as unknown as AbstractControl;

      const validator = component.futureTimeValidator();
      const result = validator(control);

      expect(result).toBeNull();
    });

    it('should validate future time correctly - no date value', () => {
      const mockParent = {
        get: jest.fn().mockReturnValue({ value: null }),
      };

      const control = {
        value: '10:00',
        parent: mockParent,
      } as unknown as AbstractControl;

      const validator = component.futureTimeValidator();
      const result = validator(control);

      expect(result).toBeNull();
    });

    it('should validate future time correctly - invalid date', () => {
      const mockParent = {
        get: jest.fn().mockImplementation((key: string) => {
          if (key === 'expireDate') {
            return { value: 'invalid-date' };
          }
          return null;
        }),
      };

      const control = {
        value: 'invalid-time',
        parent: mockParent,
      } as unknown as AbstractControl;

      const validator = component.futureTimeValidator();
      const result = validator(control);

      expect(result).toBeNull();
    });
  });

  describe('CSV File Processing', () => {
    let getElementByIdSpy: jest.SpyInstance;
    let mockSuccessDiv: HTMLElement;
    let mockStatsDiv: HTMLElement;
    let mockErrorDiv: HTMLElement;
    let mockErrorDetails: HTMLElement;
    let mockProcessingIndicator: HTMLElement;

    beforeEach(() => {
      mockSuccessDiv = {
        classList: { remove: jest.fn(), add: jest.fn() },
      } as unknown as HTMLElement;

      mockStatsDiv = {
        textContent: '',
      } as unknown as HTMLElement;

      mockErrorDiv = {
        classList: { remove: jest.fn(), add: jest.fn() },
      } as unknown as HTMLElement;

      mockErrorDetails = {
        textContent: '',
      } as unknown as HTMLElement;

      mockProcessingIndicator = {
        classList: { remove: jest.fn(), add: jest.fn() },
      } as unknown as HTMLElement;

      getElementByIdSpy = jest
        .spyOn(document, 'getElementById')
        .mockImplementation((id: string) => {
          switch (id) {
            case 'successMessage':
              return mockSuccessDiv;
            case 'csvStats':
              return mockStatsDiv;
            case 'errorMessage':
              return mockErrorDiv;
            case 'errorDetails':
              return mockErrorDetails;
            case 'processingIndicator':
              return mockProcessingIndicator;
            default:
              return null;
          }
        });
    });

    afterEach(() => {
      getElementByIdSpy.mockRestore();
    });

    it('should handle CSV file with valid emails', () => {
      const csvContent = '<EMAIL>\<EMAIL>\n';
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const file = new File([blob], 'test.csv', { type: 'text/csv' });

      component.emails = [];
      component['handleFileSelection'](file);

      expect(component.selectedFile).toBe(file);
    });

    it('should reject non-CSV files', () => {
      const file = new File(['content'], 'test.txt', { type: 'text/plain' });

      component['handleFileSelection'](file);

      expect(mockToastService.onShow).toHaveBeenCalledWith(
        'Invalid File Type',
        'Please select a CSV file',
        true,
        'error'
      );
    });

    it('should reject files larger than 1MB', () => {
      const largeContent = 'a'.repeat(1024 * 1024 + 1);
      const file = new File([largeContent], 'large.csv', { type: 'text/csv' });

      component['handleFileSelection'](file);

      expect(mockToastService.onShow).toHaveBeenCalledWith(
        'File Too Large',
        'File size must be less than 1MB',
        true,
        'error'
      );
    });

    it('should show success message with correct stats', () => {
      jest.useFakeTimers();

      component['showSuccessMessage'](
        5,
        ['invalid@'],
        ['<EMAIL>']
      );

      expect(mockStatsDiv.textContent).toContain(
        '5 email(s) added successfully'
      );
      expect(mockStatsDiv.textContent).toContain('1 duplicate(s) skipped');
      expect(mockStatsDiv.textContent).toContain('1 invalid email(s) skipped');
      expect(mockSuccessDiv.classList.remove).toHaveBeenCalledWith('hidden');

      jest.advanceTimersByTime(5000);
      expect(mockSuccessDiv.classList.add).toHaveBeenCalledWith('hidden');

      jest.useRealTimers();
    });

    it('should show error message', () => {
      jest.useFakeTimers();

      component['showErrorMessage']('Test error message');

      expect(mockErrorDetails.textContent).toBe('Test error message');
      expect(mockErrorDiv.classList.remove).toHaveBeenCalledWith('hidden');

      jest.advanceTimersByTime(8000);
      expect(mockErrorDiv.classList.add).toHaveBeenCalledWith('hidden');

      jest.useRealTimers();
    });

    it('should show and hide processing indicator', () => {
      component['showProcessingIndicator']();
      expect(mockProcessingIndicator.classList.remove).toHaveBeenCalledWith(
        'hidden'
      );

      component['hideProcessingIndicator']();
      expect(mockProcessingIndicator.classList.add).toHaveBeenCalledWith(
        'hidden'
      );
    });
  });

  describe('DOM Event Handlers', () => {
    let getElementByIdSpy: jest.SpyInstance;
    let mockDropZone: HTMLElement;
    let mockCsvFileInput: HTMLInputElement;
    let mockChooseFileBtn: HTMLElement;
    let mockUploadBtn: HTMLButtonElement;

    beforeEach(() => {
      mockDropZone = {
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
      } as unknown as HTMLElement;

      mockCsvFileInput = {
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        click: jest.fn(),
        files: null,
        value: '',
      } as unknown as HTMLInputElement;

      mockChooseFileBtn = {
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
      } as unknown as HTMLElement;

      mockUploadBtn = {
        disabled: false,
        classList: { remove: jest.fn(), add: jest.fn() },
      } as unknown as HTMLButtonElement;

      getElementByIdSpy = jest
        .spyOn(document, 'getElementById')
        .mockImplementation((id: string) => {
          switch (id) {
            case 'dropZone':
              return mockDropZone;
            case 'csvFileInput':
              return mockCsvFileInput;
            case 'chooseFileBtn':
              return mockChooseFileBtn;
            case 'uploadBtn':
              return mockUploadBtn;
            default:
              return null;
          }
        });
    });

    afterEach(() => {
      getElementByIdSpy.mockRestore();
    });

    it('should initialize CSV upload listeners', () => {
      jest.useFakeTimers();

      component['initializeCSVUpload']();

      jest.advanceTimersByTime(100);

      expect(mockDropZone.addEventListener).toHaveBeenCalledWith(
        'dragover',
        expect.any(Function)
      );
      expect(mockDropZone.addEventListener).toHaveBeenCalledWith(
        'dragleave',
        expect.any(Function)
      );
      expect(mockDropZone.addEventListener).toHaveBeenCalledWith(
        'drop',
        expect.any(Function)
      );
      expect(mockDropZone.addEventListener).toHaveBeenCalledWith(
        'click',
        expect.any(Function)
      );
      expect(mockChooseFileBtn.addEventListener).toHaveBeenCalledWith(
        'click',
        expect.any(Function)
      );
      expect(mockCsvFileInput.addEventListener).toHaveBeenCalledWith(
        'change',
        expect.any(Function)
      );
      expect(mockUploadBtn.disabled).toBeTruthy();

      jest.useRealTimers();
    });

    it('should handle missing DOM elements gracefully', () => {
      getElementByIdSpy.mockReturnValue(null);
      jest.spyOn(console, 'warn').mockImplementation();
      jest.useFakeTimers();

      component['initializeCSVUpload']();

      jest.advanceTimersByTime(100);

      expect(console.warn).toHaveBeenCalledWith(
        'CSV upload elements not found in DOM'
      );

      jest.useRealTimers();
    });

    it('should cleanup event listeners on destroy', () => {
      component['cleanupCSVUploadListeners']();

      expect(mockDropZone.removeEventListener).toHaveBeenCalledWith(
        'dragover',
        expect.any(Function)
      );
      expect(mockDropZone.removeEventListener).toHaveBeenCalledWith(
        'dragleave',
        expect.any(Function)
      );
      expect(mockDropZone.removeEventListener).toHaveBeenCalledWith(
        'drop',
        expect.any(Function)
      );
      expect(mockDropZone.removeEventListener).toHaveBeenCalledWith(
        'click',
        expect.any(Function)
      );
      expect(mockChooseFileBtn.removeEventListener).toHaveBeenCalledWith(
        'click',
        expect.any(Function)
      );
      expect(mockCsvFileInput.removeEventListener).toHaveBeenCalledWith(
        'change',
        expect.any(Function)
      );
    });
  });

  describe('OnDestroy Lifecycle', () => {
    it('should unsubscribe from configuration subscription', () => {
      const mockSubscription = {
        unsubscribe: jest.fn(),
      };
      component.configurationSubscription =
        mockSubscription as unknown as Subscription;

      component.ngOnDestroy();

      expect(mockSubscription.unsubscribe).toHaveBeenCalled();
    });

    it('should unsubscribe from all subscriptions', () => {
      const mockSubscription1 = { unsubscribe: jest.fn() };
      const mockSubscription2 = { unsubscribe: jest.fn() };
      component.subscriptions = [
        mockSubscription1,
        mockSubscription2,
      ] as unknown as Subscription[];

      component.ngOnDestroy();

      expect(mockSubscription1.unsubscribe).toHaveBeenCalled();
      expect(mockSubscription2.unsubscribe).toHaveBeenCalled();
    });

    it('should cleanup CSV upload listeners', () => {
      jest.spyOn(component, 'cleanupCSVUploadListeners');

      component.ngOnDestroy();

      expect(component.cleanupCSVUploadListeners).toHaveBeenCalled();
    });
  });

  describe('Advanced Email Search and Filtering', () => {
    beforeEach(() => {
      component.emails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];
      component.filteredEmails = [...component.emails];
    });

    it('should filter emails correctly with partial matches', () => {
      component.onSearch('john');
      expect(component.filteredEmails).toEqual(['<EMAIL>']);
    });

    it('should filter emails case-insensitively', () => {
      component.onSearch('DOMAIN');
      expect(component.filteredEmails).toEqual(['<EMAIL>']);
    });

    it('should return all emails when search term is empty', () => {
      component.onSearch('');
      expect(component.filteredEmails).toEqual(component.emails);
    });

    it('should return empty array when no matches found', () => {
      component.onSearch('nonexistent');
      expect(component.filteredEmails).toEqual([]);
    });
  });
});
