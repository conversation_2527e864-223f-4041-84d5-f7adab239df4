import {
  ChangeDetector<PERSON><PERSON>,
  Component,
  inject,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
} from '@angular/core';
import { CustomDropdownComponent } from '../../../components/custom-dropdown/custom-dropdown.component';
import { CustomInputComponent } from '../../../components/custom-input/custom-input.component';
import { CustomButtonComponent } from '../../../components/custombutton/custombutton.component';
import { CustomSearchInputComponent } from '../../../components/custom-search-input/custom-search-input.component';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { AssessmentService } from '../../../services/test-management-service/assessment-service/assessment.service';
import { ToastService } from '../../../services/toast-service/toast.service';
import {
  CreateAssessmentInterface,
  ProctoringConfiguration,
} from '../../../Interfaces/Types/assessmentInterface';
import {
  checkMinDate,
  configurations,
  mapToBackendFormat,
  processConfigurations,
  proctoringDetails,
} from '../../../utils/assessment/assessment';
import { Subscription } from 'rxjs';

import { CustomMiniModalComponent } from '../../../components/custom-mini-modal/custom-mini-modal.component';
import { QuestionTitleComponent } from '../../Questions/main-questions-page/components/question-title/question-title.component';
import { environment } from '../../../../environments/environment';
import { CustomModalComponent } from '../../../components/custom-modal/custom-modal.component';
import { BasicButtonDropdownComponent } from '../../../components/basic-button-dropdown/basic-button-dropdown.component';
import { CommonModule } from '@angular/common';
import { AnalyticsService } from '../../../services/analytics.service';
import { AssessmentStore } from '../../../stores/assessments-store/assessment.store';
import { emailSchema } from '../../../utils/constants';
import { SafeHtmlDirective } from '../../../directives/safe-html.directive';
import { RequiredSvgComponent } from '../../../components/required-svg/required-svg.component';
import { TestStore } from '../../../stores/tests-store/test.store';

@Component({
  selector: 'app-dispatch-assessment',
  standalone: true,
  imports: [
    CustomDropdownComponent,
    CustomInputComponent,
    CustomButtonComponent,
    CustomSearchInputComponent,
    ReactiveFormsModule,
    CustomMiniModalComponent,
    QuestionTitleComponent,
    CustomModalComponent,
    BasicButtonDropdownComponent,
    CommonModule,
    SafeHtmlDirective,
    RequiredSvgComponent,
  ],
  templateUrl: './dispatch-assessment.component.html',
})
export class DispatchAssessmentComponent implements OnInit, OnDestroy {
  title = 'Dispatch Assessment';
  emails: string[] = [];
  filteredEmails: string[] = [];
  inValidEmails: string[] = [];
  searchTerm: string = '';
  enableTestTakerReport = ['Yes', 'No'];
  assessmentId: string = '';
  assessmentTitle: string = '';
  dispatchAssessmentForm: FormGroup;
  subscriptions: Subscription[] = [];
  showCancelModal = false;
  proctors = configurations;
  validEmail = true;
  minDate: string = '';
  isDispatchByEmail = true;
  proctorFeatures: string[] = [];
  configurationSubscription: Subscription | undefined;
  showClock = false;
  conductSurvey = false;
  isDragging = false;
  isLoading = false;
  isSendingEmail = false;
  showAlreadyInvitedCandidatesModal = false;
  alreadyInvitedCandidates: string[] = [];
  selectModeOfDispatch = '';
  isProduction = environment.production;
  proctoringDetails = proctoringDetails;
  private expireDateSubscription?: Subscription;
  selectedFile: File | null = null;
  private readonly assessmentStore = inject(AssessmentStore);

  private readonly handleDrop: (event: DragEvent) => void;
  private readonly handleDropZoneClick: (event: Event) => void;
  private readonly handleChooseFileClick: (event: Event) => void;
  private readonly handleFileInputChange: (event: Event) => void;
  private readonly handleDragOver: (event: DragEvent) => void;
  private readonly handleDragLeave: (event: DragEvent) => void;

  options = [
    {
      label: 'Dispatch to email',
      value: 'Dispatch to email',
    },
    {
      label: 'Copy link',
      value: 'Copy link',
    },
  ];

  securityIcons = [
    'assets/icons/asses-file.svg',
    'assets/icons/id-card.svg',
    'assets/icons/face-capturee.svg',
    'assets/icons/screen-capturee.svg',
    'assets/icons/idle-time.svg',
    'assets/icons/danger.svg',
    'assets/icons/clipboard.svg',
  ];
  constructor(
    private readonly fb: FormBuilder,
    private readonly activatedRoute: ActivatedRoute,
    private readonly assessmentService: AssessmentService,
    private readonly toast: ToastService,
    private readonly router: Router,
    private readonly analyticsService: AnalyticsService,
    private readonly cdr: ChangeDetectorRef,
    private readonly testStore: TestStore
  ) {
    this.dispatchAssessmentForm = this.fb.group({
      expireDate: ['', [Validators.required, this.futureDateValidator]],
      expireTime: ['', [Validators.required, this.futureTimeValidator()]],
      commencementDate: [''],
      commencementTime: [''],
      email: new FormControl('', [Validators.email]),
      showResults: new FormControl('', Validators.required),
      camerashotsInterval: ['', [Validators.min(15), Validators.max(120)]],
      screenshotsInterval: ['', [Validators.min(15), Validators.max(120)]],
      configurations: this.fb.array([]),
    });

    this.handleDragOver = (event: DragEvent) => {
      event.preventDefault();
      this.isDragging = true;
    };

    this.handleDragLeave = (event: DragEvent) => {
      event.preventDefault();
      this.isDragging = false;
    };

    this.handleDrop = (event: DragEvent) => {
      event.preventDefault();
      this.isDragging = false;

      const files = event.dataTransfer?.files;
      if (files && files.length > 0) {
        this.handleFileSelection(files[0]);
        this.processCSVFile(files[0]);
      }
    };

    this.handleDropZoneClick = (event: Event) => {
      const csvFileInput = document.getElementById(
        'csvFileInput'
      ) as HTMLInputElement;
      if (
        event.target === document.getElementById('dropZone') &&
        csvFileInput
      ) {
        csvFileInput.click();
      }
    };

    this.handleChooseFileClick = (event: Event) => {
      event.preventDefault();
      const csvFileInput = document.getElementById(
        'csvFileInput'
      ) as HTMLInputElement;
      if (csvFileInput) {
        csvFileInput.click();
      }
    };

    this.handleFileInputChange = (event: Event) => {
      const target = event.target as HTMLInputElement;
      const file = target.files?.[0];
      if (file) {
        this.handleFileSelection(file);
        this.processCSVFile(file);
        target.value = '';
      }
    };
  }

  ngOnInit(): void {
    this.minDate = checkMinDate();
    this.expireDateSubscription = this.dispatchAssessmentForm
      .get('expireDate')
      ?.valueChanges.subscribe(() => {
        this.dispatchAssessmentForm.get('expireTime')?.updateValueAndValidity();
      });
    this.subscriptions.push(
      this.activatedRoute.queryParams.subscribe((params) => {
        this.assessmentId = params['assessmentId'];
        this.assessmentTitle = params['assessmentTitle'];

        if (this.assessmentId) {
          this.configurationSubscription = this.assessmentService
            .getAllSelectedProctoring(this.assessmentId)
            .subscribe((response) => {
              const intervals = {
                camerashotsInterval:
                  response.data.configuration.camerashotsInterval,
                screenshotsInterval:
                  response.data.configuration.screenshotsInterval,
              };
              const proctorFeatures = this.proctors.map((feature) => {
                if (feature.name === 'Enable Survey After Test') {
                  return {
                    ...feature,
                    selected: response.data.configuration.conductSurvey,
                  };
                }

                if (feature.name === 'Display Clock') {
                  return {
                    ...feature,
                    selected: response.data.configuration.showClock,
                  };
                }
                const isSelected =
                  response.data.configuration.proctorFeatures.some(
                    (item) => item.name === feature.value
                  );
                return { ...feature, selected: isSelected };
              });
              this.patchForm(intervals, proctorFeatures);
            });
        }
      })
    );
    this.initializeCSVUpload();
  }

  private initializeCSVUpload(): void {
    setTimeout(() => {
      const dropZone = document.getElementById('dropZone');
      const csvFileInput = document.getElementById(
        'csvFileInput'
      ) as HTMLInputElement;
      const chooseFileBtn = document.getElementById('chooseFileBtn');
      const uploadBtn = document.getElementById(
        'uploadBtn'
      ) as HTMLButtonElement;

      if (!dropZone || !csvFileInput || !chooseFileBtn) {
        console.warn('CSV upload elements not found in DOM');
        return;
      }

      if (uploadBtn) {
        uploadBtn.disabled = true;
      }

      dropZone.addEventListener('dragover', this.handleDragOver);
      dropZone.addEventListener('dragleave', this.handleDragLeave);
      dropZone.addEventListener('drop', this.handleDrop);

      chooseFileBtn.addEventListener('click', this.handleChooseFileClick);

      dropZone.addEventListener('click', this.handleDropZoneClick);

      csvFileInput.addEventListener('change', this.handleFileInputChange);
    }, 100);
  }
  private processCSVFile(file: File): void {
    this.showProcessingIndicator();

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const csv = e.target?.result as string;
        const lines = csv.split('\n');

        this.hideProcessingIndicator();

        const addedEmails: string[] = [];
        const invalidEmails: string[] = [];
        const duplicateEmails: string[] = [];

        lines.forEach((line) => {
          const email = line.split(',')[0]?.trim();
          if (!email) return;

          if (emailSchema.validate(email).error !== undefined) {
            invalidEmails.push(email);
            return;
          }
          if (this.emails.includes(email)) {
            duplicateEmails.push(email);
            return;
          }
          this.emails.push(email);
          addedEmails.push(email);
        });

        this.filteredEmails = this.emails;
        this.cdr.detectChanges();

        this.showSuccessMessage(
          addedEmails.length,
          invalidEmails,
          duplicateEmails
        );

        if (invalidEmails.length > 0) {
          this.toast.onShow(
            'Invalid Emails Found',
            `${invalidEmails.length} invalid email(s) were skipped from the CSV file`,
            true,
            'warning'
          );
        }
      } catch (error: unknown) {
        this.hideProcessingIndicator();
        this.showErrorMessage(
          `Failed to parse CSV file: ${error instanceof Error ? error.message : 'Unknown error'}`
        );
      }
    };
    reader.readAsText(file);
  }

  private showProcessingIndicator(): void {
    const indicator = document.getElementById('processingIndicator');
    if (indicator) {
      indicator.classList.remove('hidden');
    }
  }

  private hideProcessingIndicator(): void {
    const indicator = document.getElementById('processingIndicator');
    if (indicator) {
      indicator.classList.add('hidden');
    }
  }

  private showSuccessMessage(
    addedCount: number,
    invalidEmails: string[],
    duplicateEmails: string[]
  ): void {
    const successDiv = document.getElementById('successMessage');
    const statsDiv = document.getElementById('csvStats');

    if (successDiv && statsDiv) {
      let statsText = `${addedCount} email(s) added successfully`;

      if (duplicateEmails.length > 0) {
        statsText += `, ${duplicateEmails.length} duplicate(s) skipped`;
      }

      if (invalidEmails.length > 0) {
        statsText += `, ${invalidEmails.length} invalid email(s) skipped`;
      }

      statsDiv.textContent = statsText;
      successDiv.classList.remove('hidden');
      setTimeout(() => {
        successDiv.classList.add('hidden');
      }, 5000);
    }
  }

  private showErrorMessage(message: string): void {
    const errorDiv = document.getElementById('errorMessage');
    const errorDetails = document.getElementById('errorDetails');

    if (errorDiv && errorDetails) {
      errorDetails.textContent = message;
      errorDiv.classList.remove('hidden');
      setTimeout(() => {
        errorDiv.classList.add('hidden');
      }, 8000);
    }
  }

  private handleFileSelection(file: File): void {
    if (!file.name.toLowerCase().endsWith('.csv')) {
      this.toast.onShow(
        'Invalid File Type',
        'Please select a CSV file',
        true,
        'error'
      );
      return;
    }

    const maxSize = 1024 * 1024;
    if (file.size > maxSize) {
      this.toast.onShow(
        'File Too Large',
        'File size must be less than 1MB',
        true,
        'error'
      );
      return;
    }

    this.selectedFile = file;
    this.showFilePreview(file);
  }

  private showFilePreview(file: File): void {
    const filePreview = document.getElementById('filePreview');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    const uploadBtn = document.getElementById('uploadBtn') as HTMLButtonElement;

    if (filePreview && fileName && fileSize && uploadBtn) {
      filePreview.classList.remove('hidden');
      fileName.textContent = file.name;
      fileSize.textContent = `${(file.size / 1024).toFixed(2)} KB`;
      uploadBtn.classList.remove('hidden');
      uploadBtn.disabled = false;
    }
  }

  isCameraShotsIntervalInvalid(): boolean {
    const control = this.getFormControl('camerashotsInterval');
    return control?.invalid && (control.dirty || control.touched);
  }

  isScreenShotsIntervalInvalid(): boolean {
    const control = this.getFormControl('screenshotsInterval');
    return control?.invalid && (control.dirty || control.touched);
  }

  onIntervalInputChange(controlName: string, event: Event) {
    const input = event.target as HTMLInputElement;
    const value = input.value;
    const numValue = parseInt(value);
    if (!value || isNaN(numValue) || numValue < 15) {
      setTimeout(() => {
        this.dispatchAssessmentForm.get(controlName)?.setValue(15);
      }, 0);
    }
  }

  onIntervalInputBlur(controlName: string, event: Event) {
    const input = event.target as HTMLInputElement;
    const value = input.value;
    const numValue = parseInt(value);

    if (!value || isNaN(numValue) || numValue < 15) {
      this.dispatchAssessmentForm.get(controlName)?.setValue(15);
    } else if (numValue > 120) {
      this.dispatchAssessmentForm.get(controlName)?.setValue(120);
    }
  }

  onIntervalKeyDown(event: KeyboardEvent) {
    const input = event.target as HTMLInputElement;
    const value = input.value;

    const allowedKeys = [
      'Backspace',
      'Delete',
      'Tab',
      'Escape',
      'Enter',
      'Decimal',
      'Home',
      'End',
      'ArrowLeft',
      'ArrowRight',
      'ArrowUp',
      'ArrowDown',
    ];
    if (
      allowedKeys.includes(event.key) ||
      (event.key.toLowerCase() === 'a' && event.ctrlKey === true) ||
      (event.key.toLowerCase() === 'c' && event.ctrlKey === true) ||
      (event.key.toLowerCase() === 'v' && event.ctrlKey === true) ||
      (event.key.toLowerCase() === 'x' && event.ctrlKey === true)
    ) {
      if (
        (event.key === 'Backspace' || event.key === 'Delete') &&
        value.length <= 2
      ) {
        event.preventDefault();
        return;
      }
      return;
    }

    if (!/^\d$/.test(event.key)) {
      event.preventDefault();
    }
  }

  public routeBack(): void {
    this.router.navigate(['/dashboard/test-management/assessments']);
  }

  ngOnDestroy(): void {
    if (this.configurationSubscription) {
      this.configurationSubscription.unsubscribe();
    }
    this.expireDateSubscription?.unsubscribe();
    this.subscriptions.forEach((subscription) => subscription.unsubscribe());
    this.cleanupCSVUploadListeners();
  }

  validForm() {
    return this.dispatchAssessmentForm.valid && this.emails.length > 0;
  }

  onDispatch() {
    const settings = {
      showClock: true,
      conductSurvey: this.conductSurvey,
    };
    const formData = this.dispatchAssessmentForm.value;
    const selectedProctors = formData.configurations
      .filter((config: { selected: boolean }) => config.selected)
      .map((config: { name: string }) =>
        mapToBackendFormat(config.name)
      ) as string[];

    const processedConfigurations = processConfigurations(
      selectedProctors,
      settings
    );

    this.proctorFeatures = processedConfigurations.proctorFeatures;
    let assessmentBody: CreateAssessmentInterface;
    const isCommencementDateValid = !!(
      formData.commencementDate && formData.commencementTime
    );

    if (isCommencementDateValid) {
      assessmentBody = {
        proctorFeatures: ['Honour Code', ...this.proctorFeatures],
        showClock: true,
        expireDate: formData.expireDate + 'T' + formData.expireTime,
        commenceDate:
          formData.commencementDate + 'T' + formData.commencementTime,
        camerashotsInterval: formData.camerashotsInterval,
        conductSurvey: settings.conductSurvey,
        screenshotsInterval: formData.screenshotsInterval,
        showResults: this.showResults(formData.showResults),
        emails: this.emails,
      };
    } else {
      assessmentBody = {
        proctorFeatures: ['Honour Code', ...this.proctorFeatures],
        showClock: true,
        expireDate: formData.expireDate + 'T' + formData.expireTime,
        camerashotsInterval: formData.camerashotsInterval,
        conductSurvey: settings.conductSurvey,
        screenshotsInterval: formData.screenshotsInterval,
        showResults: this.showResults(formData.showResults),
        emails: this.emails,
      };
    }
    if (!this.isDispatchByEmail) {
      this.isLoading = true;
      delete assessmentBody.emails;
      this.dispatchByAssessmentLink(assessmentBody);
    }

    if (this.isDispatchByEmail) {
      this.isSendingEmail = true;
      if (this.selectModeOfDispatch === 'Dispatch to email') {
        this.dispatchByEmail(assessmentBody);
      } else {
        this.dispatchByAssessmentLink(assessmentBody);
      }
    }
  }

  onDragOver(event: DragEvent) {
    event.preventDefault();
    this.isDragging = true;
  }

  onDragLeave(event: DragEvent) {
    event.preventDefault();
    this.isDragging = false;
  }

  onFileDivKeydown(event: KeyboardEvent, fileInput: HTMLInputElement) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      fileInput.click();
    }
  }

  addEmail() {
    const email = this.dispatchAssessmentForm.value.email;

    if (
      email !== null &&
      email !== '' &&
      emailSchema.validate(email).error === undefined &&
      !this.emails.includes(email)
    ) {
      this.emails.push(email);
      this.dispatchAssessmentForm.get('email')?.reset();
      this.cdr.detectChanges();
    }

    this.filteredEmails = this.emails;
  }

  dispatchByEmail(assessmentBody: CreateAssessmentInterface) {
    this.assessmentService
      .dispatchAssessmentByEmail(this.assessmentId, assessmentBody)
      .subscribe({
        next: (response) => {
          this.isSendingEmail = false;
          this.analyticsService
            .track('Assessment Dispatched', {
              candidateCount: this.emails.length,
            })
            .then();
          this.toast.onShow(
            'Assessment Dispatched ',
            'Congratulations! You have successfully dispatched assessment ‘' +
              this.assessmentTitle.replaceAll('--', ' ') +
              '’',
            true,
            'success'
          );
          this.testStore.resetTestData();
          this.loadAssessment();
          this.alreadyInvitedCandidates =
            response.data.summary.alreadyInvitedCandidates;
          this.emails = [];
          this.configurationsArray.clear();
          if (this.alreadyInvitedCandidates.length <= 0) {
            this.router.navigate(['/dashboard/test-management/assessments']);
          } else {
            this.showAlreadyInvitedCandidatesModal = true;
          }
        },
        error: (error) => {
          this.isSendingEmail = false;
          this.analyticsService
            .track('Assessment Dispatch Failed', {
              reason: 'API error',
            })
            .then();
          this.toast.onShow('error', error, true, 'error');
        },
      });
  }

  dispatchByAssessmentLink(assessmentBody: CreateAssessmentInterface) {
    this.assessmentService
      .getAssessmentLink(this.assessmentId, assessmentBody)
      .subscribe({
        next: (response) => {
          this.isLoading = false;
          this.loadAssessment();
          navigator.clipboard.writeText(response.data.link);
          this.toast.onShow(
            'success',
            'Link Generated Successfully',
            true,
            'success'
          );
          this.router.navigate(['/dashboard/test-management/assessments']);
          this.testStore.resetTestData();
        },
        error: (error) => {
          this.isLoading = false;
          this.toast.onShow('error', error, true, 'error');
        },
      });
  }

  loadAssessment() {
    this.assessmentStore.setOnlyAssessmentsToEmpty();
  }

  dispatchAssessmentsViaEmail() {
    this.isDispatchByEmail = true;
    this.onDispatch();
  }

  generateLink() {
    this.isDispatchByEmail = false;
    this.onDispatch();
  }

  getFormControl(name: string): FormControl {
    return this.dispatchAssessmentForm.get(name) as FormControl;
  }

  get configurationsArray(): FormArray {
    return this.dispatchAssessmentForm.get('configurations') as FormArray;
  }

  patchForm(
    intervals: { camerashotsInterval?: string; screenshotsInterval?: string },
    proctorFeatures: ProctoringConfiguration[]
  ) {
    this.dispatchAssessmentForm.patchValue({
      camerashotsInterval: intervals.camerashotsInterval ?? '',
      screenshotsInterval: intervals.screenshotsInterval ?? '',
    });
    this.configurationsArray.clear();

    proctorFeatures.forEach((config) => {
      const formGroup = this.fb.group({
        selected: [config.selected],
        name: [config.name],
        locked: [config.locked],
      });

      if (config.locked) {
        formGroup.get('selected')?.disable();
      }

      this.configurationsArray.push(formGroup);
    });
  }

  validEmailCheckOnChange() {
    this.validEmail =
      emailSchema.validate(this.dispatchAssessmentForm.value.email).error ===
      undefined;
  }

  showResults(value: string) {
    if (value == 'Yes') {
      return true;
    }
    return false;
  }

  removeEmail(index: number) {
    if (index > -1) {
      this.emails.splice(index, 1);
    }
  }

  onKeydownRemoveEmail(event: KeyboardEvent, index: number) {
    if (event.code === 'Enter') {
      event.preventDefault();
      this.removeEmail(index);
    }
  }

  uploadEmailFromCSV(event: Event) {
    const target = event.target as HTMLInputElement;
    const file = target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const csv = e.target?.result as string;
        const lines = csv.split('\n');

        lines.forEach((line) => {
          const email = line.split(',')[0]?.trim();
          if (email === '') {
            return;
          } else if (
            emailSchema.validate(email).error === undefined &&
            !this.emails.includes(email)
          ) {
            this.emails.push(email);
          } else {
            this.inValidEmails.push(email);
          }
        });

        if (this.inValidEmails.length > 0) {
          this.toast.onShow(
            'error',
            `Invalid emails, ${[...this.inValidEmails]} found in the CSV file`,
            true,
            'error'
          );
        }
      };
      reader.readAsText(file);
    }
    this.filteredEmails = this.emails;
    target.value = '';
  }

  onSearch($event: string) {
    this.searchTerm = $event;
    this.filteredEmails = this.emails;
    this.filteredEmails = this.filteredEmails.filter((data) => {
      if (data.toLowerCase().includes(this.searchTerm.toLowerCase())) {
        return data;
      }
      return null;
    });
  }

  closeCancelModal() {
    this.showCancelModal = false;
  }

  showCancel() {
    this.showCancelModal = true;
  }

  confirmCancelDispatch() {
    this.showCancelModal = false;
    this.router.navigate(['/dashboard/test-management/assessments']);
  }

  previousToRoutePage() {
    this.router.navigate(['dashboard/test-management/assessments']);
  }

  closeAlreadyInvitedCandidatesModal() {
    this.showAlreadyInvitedCandidatesModal = false;
    this.router.navigate(['/dashboard/test-management/assessments']);
  }

  selectedOption(event: string) {
    this.selectModeOfDispatch = event;
    this.onDispatch();
  }
  toggleConfiguration(index: number, currentValue: boolean) {
    const control = this.configurationsArray.at(index);
    if (!control.disabled) {
      control.patchValue({ selected: !currentValue });
    }
  }

  isTimeInValid(dateInput: string, timeInput: string): boolean {
    const form = this.dispatchAssessmentForm;
    const dateControl = form.get(dateInput);
    const timeControl = form.get(timeInput);

    if (!dateControl || !timeControl) return false;

    if (!dateControl.value || !timeControl.value) return false;

    const selectedDateTime = new Date(
      `${dateControl.value}T${timeControl.value}`
    );
    const currentDateTime = new Date();

    if (this.validateCommenceDateTime()) {
      return false;
    }
    return selectedDateTime <= currentDateTime;
  }

  validateCommenceDateTime(): boolean {
    const { commencementDate, expireDate, commencementTime, expireTime } =
      this.dispatchAssessmentForm.value;

    if (!commencementDate || !expireDate || !commencementTime || !expireTime)
      return false;

    const commence = new Date(`${commencementDate}T${commencementTime}`);
    const expire = new Date(`${expireDate}T${expireTime}`);

    if (isNaN(commence.getTime()) || isNaN(expire.getTime())) {
      return false;
    }

    if (commence >= expire) return true;
    return false;
  }

  get isFormValid(): boolean {
    const hasError =
      this.isTimeInValid('commencementDate', 'commencementTime') ||
      this.isTimeInValid('expireDate', 'expireTime');

    return (
      this.dispatchAssessmentForm.valid &&
      this.emails.length > 0 &&
      !this.validateCommenceDateTime() &&
      !hasError
    );
  }

  cleanupCSVUploadListeners(): void {
    const dropZone = document.getElementById('dropZone');
    const csvFileInput = document.getElementById(
      'csvFileInput'
    ) as HTMLInputElement;
    const chooseFileBtn = document.getElementById('chooseFileBtn');

    if (dropZone) {
      dropZone.removeEventListener('dragover', this.handleDragOver);
      dropZone.removeEventListener('dragleave', this.handleDragLeave);
      dropZone.removeEventListener('drop', this.handleDrop);
      dropZone.removeEventListener('click', this.handleDropZoneClick);
    }

    if (chooseFileBtn) {
      chooseFileBtn.removeEventListener('click', this.handleChooseFileClick);
    }

    if (csvFileInput) {
      csvFileInput.removeEventListener('change', this.handleFileInputChange);
    }
  }

  futureDateValidator(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;

    const selectedDate = new Date(control.value);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    return selectedDate >= today ? null : { pastDate: true };
  }

  futureTimeValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const form = control.parent;
      if (!form) return null;

      const dateControl = form.get('expireDate');
      const timeControl = control;

      if (!dateControl?.value || !timeControl?.value) {
        return null;
      }

      const selectedDateTime = new Date(
        `${dateControl.value}T${timeControl.value}`
      );
      const currentDateTime = new Date();

      if (isNaN(selectedDateTime.getTime())) {
        return null; // Prevent Invalid Date issues
      }

      return selectedDateTime > currentDateTime ? null : { pastTime: true };
    };
  }

  removeSelectedFile(): void {
    this.selectedFile = null;
    this.emails = [];
    this.filteredEmails = [];
    const csvFileInput = document.getElementById(
      'csvFileInput'
    ) as HTMLInputElement;
    if (csvFileInput) {
      csvFileInput.value = '';
    }
    this.cdr.detectChanges();
  }
}
