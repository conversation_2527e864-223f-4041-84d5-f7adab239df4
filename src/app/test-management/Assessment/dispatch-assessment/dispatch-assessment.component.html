<main>
  <section class="flex items-center justify-between gap-x-6">
    <div>
      <app-question-title
        [newDesign]="true"
        [questionType]="title"
        [separateLines]="true"
        (backRoute)="routeBack()"
      ></app-question-title>
      <p class="text-[#0C4767] text-[16px] font-medium mb-5">
        Configure and dispatch your assessments with advanced security controls
      </p>
    </div>
    <div class="flex items-center flex-wrap gap-x-3">
      <div class="max-w-[100px] lg:max-w-[200px]">
        <app-custombutton [variant]="'secondary'" (clicked)="showCancel()"
          >Cancel</app-custombutton
        >
      </div>
      <div class="lg:max-w-[250px] max-w-fit lg:mt-0 mt-2">
        <app-basic-button-dropdown
          [selectedLabel]="'+ Dispatch Assessment'"
          [options]="options"
          (optionSelected)="selectedOption($event)"
          [isDisabled]="!isFormValid"
        ></app-basic-button-dropdown>
      </div>
    </div>
  </section>
  <section
    class="bg-[#fafafa] rounded-lg border-b-[#082230] p-6 max-h-[calc(100vh-350px)] mt-5 overflow-y-auto"
  >
    <form
      class="grid grid-cols-1 md:grid-cols-2 gap-6"
      [formGroup]="dispatchAssessmentForm"
    >
      <div class="col-span-1">
        <label
          for="commencementDate"
          class="text-[#262626] font-medium mb-1 flex items-center gap-x-2"
        >
          Commencement Date
        </label>
        <app-custom-input
          [id]="'commencementDate'"
          [placeholder]="'Enter Commencement date'"
          [formControl]="getFormControl('commencementDate')"
          [type]="'date'"
          [min]="minDate"
        ></app-custom-input>
      </div>
      @if (getFormControl('commencementDate').value !== '') {
        <div class="col-span-1">
          <label
            for="commencementDate"
            class="text-[#262626] font-medium mb-1 flex items-center gap-x-2"
          >
            Commencement Time
            <app-required-svg />
          </label>
          <app-custom-input
            id="commencementTime"
            placeholder="Enter Commencement time"
            [formControl]="getFormControl('commencementTime')"
            type="time"
          ></app-custom-input>
          @if (isTimeInValid('commencementDate', 'commencementTime')) {
            <p class="text-red-500 text-xs">
              Please select a valid future time
            </p>
          }
        </div>
      } @else {
        <div class="col-span-1">
          <label
            for="enableTestTakerReport"
            class="text-[#262626] font-medium mb-1 flex items-center gap-x-2"
          >
            Enable Test Taker Report
            <app-required-svg />
          </label>

          <app-custom-dropdown
            [id]="'enableTestTakerReport'"
            [placeholder]="'Please Select'"
            formControlName="showResults"
            [options]="enableTestTakerReport"
          ></app-custom-dropdown>
        </div>
      }
      <div class="col-span-1">
        <label
          for="commencementDate"
          class="text-[#262626] font-medium mb-1 flex items-center gap-x-2"
        >
          Expiry Date
          <app-required-svg />
        </label>
        <app-custom-input
          [id]="'expireDate'"
          [placeholder]="'Enter Expiry Date'"
          [formControl]="getFormControl('expireDate')"
          [type]="'date'"
          [min]="minDate"
        ></app-custom-input>
      </div>

      <div class="col-span-1">
        <label
          for="commencementDate"
          class="text-[#262626] font-medium mb-1 flex items-center gap-x-2"
        >
          Expiry Time
          <app-required-svg />
        </label>
        <app-custom-input
          id="expireTime"
          placeholder="Enter Expiry Time"
          [formControl]="getFormControl('expireTime')"
          type="time"
        ></app-custom-input>

        @if (isTimeInValid('expireDate', 'expireTime')) {
          <p class="text-red-500 text-xs">Please select a valid future time</p>
        }
        @if (validateCommenceDateTime()) {
          <p class="text-red-500 text-xs">
            Commencement date should be before expiry date
          </p>
        }
      </div>

      @if (getFormControl('commencementDate').value !== '') {
        <div class="col-span-1">
          <label
            for="enableTestTakerReport"
            class="text-[#262626] font-medium mb-1 flex items-center gap-x-2"
          >
            Enable Test Taker Report
            <app-required-svg />
          </label>
          <app-custom-dropdown
            [id]="'enableTestTakerReport'"
            [placeholder]="'Please Select'"
            formControlName="showResults"
            [options]="enableTestTakerReport"
          ></app-custom-dropdown>
        </div>
      }
      <div class="col-span-1 md:col-span-2">
        <label
          for="configurations"
          class="text-[#262626] font-medium mb-1 flex items-center gap-x-2"
        >
          Configurations
        </label>
        <p class="text-[#082230CC] text-sm mb-6">
          Configure security features and monitoring options for your assessment
        </p>
        <section>
          <div formArrayName="configurations" class="grid gap-4">
            @for (
              setting of configurationsArray.controls;
              track setting;
              let i = $index
            ) {
              <div class="flex items-start gap-4">
                <div
                  class="bg-white rounded-lg border border-[#E1E3EA] p-4 flex-1"
                >
                  <div [formGroupName]="i" class="flex items-start gap-4">
                    <label
                      class="inline-flex items-center mt-1"
                      [class.cursor-not-allowed]="setting.getRawValue().locked"
                      [class.cursor-pointer]="!setting.getRawValue().locked"
                    >
                      <input
                        type="checkbox"
                        formControlName="selected"
                        class="hidden"
                        (click)="
                          setting.getRawValue().locked
                            ? $event.preventDefault()
                            : null
                        "
                        (keydown.space)="
                          setting.getRawValue().locked
                            ? $event.preventDefault()
                            : null
                        "
                      />
                      <span
                        class="relative w-5 h-5 border rounded transition-all duration-200 flex-shrink-0"
                        [class]="
                          setting.getRawValue().locked &&
                          setting.getRawValue().selected
                            ? 'bg-gray-200 border-gray-400'
                            : setting.getRawValue().locked
                              ? 'bg-gray-100 border-gray-300'
                              : setting.getRawValue().selected
                                ? 'bg-[#0C4766CC] border-[#0C4766CC]'
                                : 'bg-white border-gray-300'
                        "
                      >
                        <svg
                          *ngIf="setting.getRawValue().selected"
                          class="absolute inset-0 w-full h-full p-0.5"
                          [class.text-white]="!setting.getRawValue().locked"
                          [class.text-gray-400]="setting.getRawValue().locked"
                          viewBox="0 0 20 20"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M16.7071 5.29289C17.0976 5.68342 17.0976 6.31658 16.7071 6.70711L8.70711 14.7071C8.31658 15.0976 7.68342 15.0976 7.29289 14.7071L3.29289 10.7071C2.90237 10.3166 2.90237 9.68342 3.29289 9.29289C3.68342 8.90237 4.31658 8.90237 4.70711 9.29289L8 12.5858L15.2929 5.29289C15.6834 4.90237 16.3166 4.90237 16.7071 5.29289Z"
                            fill="currentColor"
                          />
                        </svg>
                      </span>
                    </label>

                    <div class="flex-1">
                      <div class="flex items-center gap-2">
                        <img
                          [src]="securityIcons[i]"
                          alt=""
                          class="w-8 h-8 mr-1"
                        />
                        <span class="text-[#262626] font-medium">{{
                          setting.getRawValue().name
                        }}</span>
                      </div>
                      <p class="text-[#082230CC] text-sm mt-1">
                        {{ proctoringDetails[i] }}
                      </p>

                      @if (i === 2 && setting.getRawValue().selected) {
                        <div
                          class="transition-all duration-300 ease-in-out overflow-hidden"
                          [ngStyle]="{
                            maxHeight: setting.getRawValue().selected
                              ? '200px'
                              : '0',
                            opacity: setting.getRawValue().selected ? '1' : '0',
                            paddingTop: setting.getRawValue().selected
                              ? '12px'
                              : '0',
                            paddingBottom: setting.getRawValue().selected
                              ? '12px'
                              : '0',
                          }"
                        >
                          <label
                            class="text-[#262626] font-medium block mb-1"
                            for="cameraShotsInterval"
                            >Capture interval (15-120 seconds)</label
                          >
                          <div class="flex items-center gap-2">
                            <div>
                              <app-custom-input
                                id="cameraShotsInterval"
                                type="number"
                                [min]="15"
                                [max]="120"
                                [formControl]="
                                  getFormControl('camerashotsInterval')
                                "
                                [class.error-border]="
                                  isCameraShotsIntervalInvalid()
                                "
                                (input)="
                                  onIntervalInputChange(
                                    'camerashotsInterval',
                                    $event
                                  )
                                "
                                (blur)="
                                  onIntervalInputBlur(
                                    'camerashotsInterval',
                                    $event
                                  )
                                "
                                (keydown)="onIntervalKeyDown($event)"
                              />
                            </div>
                            <span class="-mt-2">sec</span>
                          </div>
                          <span
                            class="text-red-500 text-xs mt-1 block"
                            *ngIf="isCameraShotsIntervalInvalid()"
                          >
                            Interval must be between 15 and 120 seconds
                          </span>
                        </div>
                      }

                      @if (i === 3 && setting.getRawValue().selected) {
                        <div
                          class="transition-all duration-300 ease-in-out overflow-hidden"
                          [ngStyle]="{
                            maxHeight: setting.getRawValue().selected
                              ? '200px'
                              : '0',
                            opacity: setting.getRawValue().selected ? '1' : '0',
                            paddingTop: setting.getRawValue().selected
                              ? '12px'
                              : '0',
                            paddingBottom: setting.getRawValue().selected
                              ? '12px'
                              : '0',
                          }"
                        >
                          <label
                            class="text-[#262626] font-medium block mb-1"
                            for="screenShotsInterval"
                            >Capture interval (15-120 seconds)</label
                          >
                          <div class="flex items-center gap-2">
                            <div>
                              <app-custom-input
                                id="screenShotsInterval"
                                type="number"
                                [min]="15"
                                [max]="120"
                                [formControl]="
                                  getFormControl('screenshotsInterval')
                                "
                                [class.error-border]="
                                  isScreenShotsIntervalInvalid()
                                "
                                (input)="
                                  onIntervalInputChange(
                                    'screenshotsInterval',
                                    $event
                                  )
                                "
                                (blur)="
                                  onIntervalInputBlur(
                                    'screenshotsInterval',
                                    $event
                                  )
                                "
                                (keydown)="onIntervalKeyDown($event)"
                              />
                            </div>
                            <span class="-mt-2">sec</span>
                          </div>
                          <span
                            class="text-red-500 text-xs mt-1 block"
                            *ngIf="isScreenShotsIntervalInvalid()"
                          >
                            Interval must be between 15 and 120 seconds
                          </span>
                        </div>
                      }
                    </div>
                  </div>

                  <div
                    class="w-auto max-w-[20rem] z-20 text-white bg-[#0C4767] rounded-lg px-1.5 hidden group-hover:block"
                    [appSafeHtml]="proctoringDetails[i]"
                  ></div>
                </div>
              </div>
            }
          </div>
        </section>
      </div>

      <div class="col-span-1 md:col-span-2">
        <div class="flex items-center justify-between mb-[32px] mt-9">
          <div>
            <p
              class="text-[#262626] font-medium mb-1 flex items-center gap-x-2"
            >
              Candidate Management ({{ emails.length }})
            </p>
            <p class="text-[#0C4767] text-[16px] font-medium mb-5">
              Add candidate email addresses individually or use CSV upload below
            </p>
          </div>
          @if (!isProduction) {
            <app-custombutton
              [disabled]="
                dispatchAssessmentForm.invalid ||
                emails.length > 0 ||
                validateCommenceDateTime()
              "
              [variant]="'secondary'"
              (clicked)="generateLink()"
              [spinner]="isLoading"
              class="mt-4"
              >{{ isLoading ? '' : 'Click to Generate Link' }}</app-custombutton
            >
          }
        </div>
        <div class="mb-4">
          <div class="flex gap-x-4 items-center w-full max-w-[600px]">
            <div class="w-full mt-2">
              <app-custom-input
                [id]="'email'"
                [formControl]="getFormControl('email')"
                (input)="validEmailCheckOnChange()"
                [placeholder]="'Enter Email'"
              ></app-custom-input>
            </div>
            <app-custombutton [variant]="'secondary'" (clicked)="addEmail()">
              Add email
            </app-custombutton>
          </div>
        </div>

        @if (!this.validEmail) {
          <div class="text-red-500">Please enter valid email</div>
        }

        <div class="mb-6">
          @if (this.emails.length > 10) {
            <app-custom-search-input
              [placeholder]="'Search email'"
              [searchTerm]="searchTerm"
              (searchTermChange)="onSearch($event)"
            ></app-custom-search-input>
          }
          @if (this.emails.length > 0) {
            <div class="max-h-[260px] overflow-y-auto pr-5 mt-5">
              @for (email of filteredEmails; track email; let index = $index) {
                <div class="flex items-center gap-x-3 mb-1">
                  <div class="w-full">
                    <app-custom-input
                      [value]="email"
                      [readonly]="true"
                    ></app-custom-input>
                  </div>
                  <button
                    (click)="removeEmail(index)"
                    (keydown)="onKeydownRemoveEmail($event, index)"
                  >
                    <img
                      src="../../../../assets/icons/delete-icon.svg"
                      alt=""
                    />
                  </button>
                </div>
              }
            </div>
          }
        </div>

        <div class="w-full mx-auto p-6">
          <div class="space-y-6">
            <div>
              <h2
                class="text-[#262626] font-medium mb-1 flex items-center gap-x-2"
              >
                Bulk Import via CSV
              </h2>
              <p class="text-gray-600">
                Upload a CSV file containing candidate email addresses
              </p>
            </div>

            <div class="relative">
              <div
                id="dropZone"
                class="border-2 border-dashed border-gray-300 rounded-lg p-12 text-center transition-all duration-200 hover:border-blue-400 hover:bg-blue-50 cursor-pointer"
                [class.border-blue-400]="isDragging"
                [class.bg-blue-50]="isDragging"
              >
                <div class="flex justify-center mb-4">
                  <svg
                    width="40"
                    height="40"
                    viewBox="0 0 40 40"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M20.0003 21.6659V34.9992M20.0003 34.9992L13.3337 28.3325M20.0003 34.9992L26.667 28.3325M7.32204 25.4476C5.96082 24.2568 4.89508 22.7659 4.20899 21.0925C3.5229 19.4191 3.23524 17.6091 3.36874 15.8055C3.50224 14.0018 4.05324 12.2539 4.97823 10.6998C5.90321 9.14568 7.17685 7.82788 8.69856 6.85047C10.2203 5.87306 11.9484 5.26282 13.7464 5.06793C15.5445 4.87305 17.3632 5.09886 19.059 5.72752C20.7548 6.35619 22.2812 7.3705 23.5177 8.69036C24.7542 10.0102 25.6669 11.5995 26.1837 13.3326H29.167C30.7872 13.3324 32.3638 13.8568 33.661 14.8274C34.9582 15.7979 35.9063 17.1624 36.3633 18.7167C36.8204 20.271 36.7619 21.9316 36.1965 23.4498C35.6312 24.9681 34.5894 26.2625 33.227 27.1393"
                      stroke="#085AC4"
                      stroke-width="2.33333"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </div>

                <p class="text-lg font-medium text-gray-700 mb-2">
                  Drop your CSV file here
                </p>
                <p class="text-gray-500 mb-6">or click to browse files</p>
                <button
                  id="chooseFileBtn"
                  type="button"
                  class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <svg
                    width="25"
                    height="24"
                    viewBox="0 0 25 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M21.5 15V19C21.5 19.5304 21.2893 20.0391 20.9142 20.4142C20.5391 20.7893 20.0304 21 19.5 21H5.5C4.96957 21 4.46086 20.7893 4.08579 20.4142C3.71071 20.0391 3.5 19.5304 3.5 19V15M17.5 8L12.5 3M12.5 3L7.5 8M12.5 3V15"
                      stroke="#082230"
                      stroke-opacity="0.8"
                      stroke-width="1.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                  Choose CSV file
                </button>
              </div>

              <input
                type="file"
                id="csvFileInput"
                accept=".csv"
                class="hidden"
              />
            </div>

            @if (selectedFile) {
              <div class="mt-4 pt-4">
                <p
                  class="text-[#262626] border-b border-gray-200 font-medium mb-3 pb-3"
                >
                  File Added
                </p>
                <div class="flex items-center justify-between py-2">
                  <div class="flex items-center gap-3">
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z"
                        stroke="#4B5563"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M14 2V8H20"
                        stroke="#4B5563"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M16 13H8"
                        stroke="#4B5563"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M16 17H8"
                        stroke="#4B5563"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M10 9H9H8"
                        stroke="#4B5563"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                    <div>
                      <p class="text-[#262626] font-medium">
                        File name {{ selectedFile.name }}
                      </p>
                      <p class="text-[#6B7280] text-sm">
                        {{ (selectedFile.size / 1024).toFixed(2) }}MB
                      </p>
                    </div>
                  </div>
                  <button
                    type="button"
                    (click)="removeSelectedFile()"
                    class="p-1 hover:bg-gray-100 rounded-full transition-colors duration-200"
                    aria-label="Remove file"
                  >
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M3 6H5H21"
                        stroke="#EF4444"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z"
                        stroke="#EF4444"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M10 11V17"
                        stroke="#EF4444"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M14 11V17"
                        stroke="#EF4444"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            }
            <div
              id="successMessage"
              class="hidden bg-green-50 border border-green-200 rounded-lg p-4"
            >
              <div class="flex items-center">
                <svg
                  class="w-5 h-5 text-green-500 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
            </div>

            <div
              id="errorMessage"
              class="hidden bg-red-50 border border-red-200 rounded-lg p-4"
            >
              <div class="flex items-center">
                <svg
                  class="w-5 h-5 text-red-500 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <div class="flex-1">
                  <p class="text-sm font-medium text-red-900">
                    Error processing CSV
                  </p>
                  <p id="errorDetails" class="text-sm text-red-700"></p>
                </div>
              </div>
            </div>

            <div class="bg-[#085AC408] rounded-lg p-4">
              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <svg
                    class="w-5 h-5 text-blue-500 mt-0.5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-blue-900">
                    CSV Format Requirements:
                  </h3>
                  <ul class="mt-2 text-sm text-blue-800 space-y-1">
                    <li>• One email per line or comma-separated</li>
                    <li>• Maximum file size: 1MB</li>
                    <li>• Only valid email addresses will be imported</li>
                    <li>• Duplicate emails will be automatically skipped</li>
                    <li>
                      • Emails will appear automatically below after upload
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div class="flex justify-end">
              <button
                id="uploadBtn"
                type="button"
                class="hidden px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              >
                <span class="flex items-center">
                  <svg
                    class="w-4 h-4 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 17v-2.5M15 17v-2.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  Upload CSV
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </form>
  </section>
</main>

@if (this.showCancelModal) {
  <app-custom-mini-modal
    [headerTitle]="'Confirm Cancellation'"
    [textAlign]="'center'"
    [left]="'No, Continue'"
    [right]="'Yes, Cancel'"
    [visibleModal]="showCancelModal"
    [bodyText]="'Are you sure you want to cancel dispatching this assessment?'"
    (leftClickEvent)="closeCancelModal()"
    (rightClickEvent)="confirmCancelDispatch()"
    (visibleModalChange)="closeCancelModal()"
    [width]="'446px'"
    [height]="'223px'"
  />
}

<app-custom-modal
  [headerTitle]="'Already Invited Candidates'"
  [textAlign]="'center'"
  [visibleModal]="showAlreadyInvitedCandidatesModal"
  (visibleModalChange)="closeAlreadyInvitedCandidatesModal()"
>
  <div class="max-h-[40rem] min-w-[15rem] overflow-y-auto pr-5 mt-5">
    @for (email of alreadyInvitedCandidates; track $index) {
      <p>{{ email }}</p>
    }
  </div>
</app-custom-modal>
