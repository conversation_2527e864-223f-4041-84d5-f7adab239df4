import { Routes } from '@angular/router';
import { MainAssessmentComponent } from './main-assessment.component';
import { LayoutWrapperComponent } from '../Questions/main-questions-page/shared/layout-wrapper/layout-wrapper.component';

export const assessmentRoutes: Routes = [
  {
    path: '',
    component: MainAssessmentComponent,
    data: { breadcrumb: 'All Assessments' },
    children: [
      { path: '', redirectTo: 'all-assessments', pathMatch: 'full' },
      {
        path: 'all-assessments',
        loadComponent: () =>
          import('./assessments/assessments.component').then(
            (c) => c.AssessmentsComponent
          ),
      },
      {
        path: 'create-assessments',
        loadChildren: () =>
          import(
            '../Assessment/create-assessment/createAssessment.routes'
          ).then((r) => r.CreateAssessmentsRoute),
      },
      {
        path: 'edit-assessments',
        loadChildren: () =>
          import('../Assessment/create-assessment/editAssessment.routes').then(
            (r) => r.EditAssessmentsRoute
          ),
      },
      {
        path: '',
        component: LayoutWrapperComponent,
        children: [
          {
            path: 'dispatch',
            data: { breadcrumb: 'Dispatch Assessment' },
            loadComponent: () =>
              import(
                '../Assessment/dispatch-assessment/dispatch-assessment.component'
              ).then((c) => c.DispatchAssessmentComponent),
          },
          {
            path: 'preview/:assessmentId',
            data: {
              breadcrumb: 'Preview Created Assessment',
              hideButtons: true,
            },
            loadComponent: () =>
              import(
                '../Assessment/assessment-preview/assessment-preview.component'
              ).then((c) => c.AssessmentPreviewComponent),
          },
          {
            path: 'history/:assessmentId',
            data: { breadcrumb: 'Assessment History' },
            loadComponent: () =>
              import(
                '../Assessment/assessment-history/assessment-history.component'
              ).then((c) => c.AssessmentHistoryComponent),
          },
        ],
      },
    ],
  },
];
