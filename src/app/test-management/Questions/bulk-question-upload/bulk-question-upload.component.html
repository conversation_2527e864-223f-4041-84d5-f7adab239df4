<main>
  <div class="sm:block md:hidden lg:hidden xl:hidden">
    <div
      class="flex flex-col items-center justify-center p-8 text-center min-h-[50vh]"
    >
      <img
        src="../../../../assets/icons/device-not-supported.svg"
        alt="Device not supported"
        class="mb-4 w-16 h-16"
      />
      <h2 class="text-[#0C4767] text-xl font-bold mb-2">
        Device Not Supported
      </h2>
      <p class="text-[#7F849A] text-base">
        Bulk upload is not supported on mobile devices. Please use a desktop
        computer to access this feature.
      </p>
    </div>
  </div>

  <div class="hidden sm:hidden xs:hidden md:block lg:block">
    <div class="flex justify-between flex-wrap">
      <app-question-title
        [questionType]="'Bulk Question Upload'"
        (backRoute)="routeBack()"
      ></app-question-title>
      <div class="buttons flex gap-x-3 justify-end mb-3">
        <app-custombutton
          [variant]="'secondary'"
          (clicked)="showCancelQuestionUploadModal()"
          >Cancel</app-custombutton
        >
        <app-custombutton
          [variant]="'primary'"
          [disabled]="csvTableData.length === 0 || errorCount > 0"
          (click)="onAddBulkQuestion()"
          (keydown)="onKeyboardAddBulkQuestionEvent($event)"
          >Save question</app-custombutton
        >
      </div>
    </div>

    <section>
      <h3 class="text-[#0C4767] text-[16px] font-medium mb-8">
        Question Type Selection
      </h3>
      <p class="text-[#7F849A] text-[12px]">Select a question type to upload</p>
      <div>
        @for (question of questionTypes; track question; let index = $index) {
          <div class="flex gap-x-3 max-w-[550px] mb-[16px] items-center">
            <input
              type="radio"
              [value]="question.value"
              [name]="'questionGroup'"
              [checked]="selectedQuestion === question.value"
              (keydown)="selectQuestionKeyBoard($event)"
              (click)="selectQuestion(question.value)"
              title="Select {{ question.name }} question type"
              placeholder="Select question type"
            />
            <button
              type="button"
              class="w-full cursor-default flex items-center"
              (keydown)="selectQuestionKeyBoard($event)"
              (click)="selectQuestion(question.value)"
              [attr.aria-pressed]="selectedQuestion === question.value"
            >
              <p
                class="text-[#0C4767] w-[250px] h-[30px] text-[16px] font-medium md:w-[500px] md:h-[40px] flex items-center bg-[#fff] rounded-lg pl-[16px]"
              >
                {{ question.name }}
              </p>
            </button>
          </div>
        }
      </div>
      <div class="button flex gap-x-11 items-center">
        <label
          for="bulk-questions"
          class="cursor-pointer min-w-[174px] min-h-[45px] bg-[#0C4767] rounded-lg text-[#fff] grid place-items-center"
          [ngClass]="{ 'bg-[#B5B5C3]': !selectedQuestion }"
          ><span>Upload CSV File</span>
          <input
            type="file"
            id="bulk-questions"
            class="hidden"
            accept=".csv"
            (change)="onFileUpload($event)"
            [disabled]="!selectedQuestion"
          />
        </label>
        @if (selectedQuestion) {
          <a
            class="text-[#0C4767] text-[16px] font-medium flex items-center gap-x-2 cursor-pointer"
            [href]="sampleFileDownload()"
          >
            <p>Download Sample CSV</p>
            <div>
              <img src="../../../../assets/icons/download.svg" alt="" />
            </div>
          </a>
        }
      </div>
      @if (fileName) {
        <div class="flex items-center gap-x-1 mt-4">
          <img src="../../../../assets/icons/csvIcon.svg" alt="" />
          <p class="text-[#2F55F0] text-[14px] font-normal">
            {{ fileName }}
          </p>
          <img
            (keydown)="removeFileKeyBoard($event)"
            (click)="removeFile()"
            class="cursor-pointer"
            src="../../../../assets/icons/delete-icon.svg"
            alt="Delete File"
            aria-hidden="true"
          />
        </div>
      }
    </section>

    <section class="mr-3">
      @if (csvTableData.length > 0) {
        <div class="flex justify-end items-center mb-3">
          <section class="toggle flex gap-x-3">
            <app-toggle-switch [toggleInfo]="editTableInfo"></app-toggle-switch>

            <app-toggle-switch
              [toggleInfo]="deleteButtonInfo"
            ></app-toggle-switch>
          </section>
        </div>

        <app-editable-table
          [data]="csvTableData"
          [errorMap]="errorMap"
          [displayColumns]="csvColumns"
          [showRowNumbers]="true"
          (dataChange)="onDataChange($event)"
          [enableDeleteButton]="deleteButtonInfo.toggled"
          [enableTableEditing]="editTableInfo.toggled"
          (actionButtonClickedFromTable)="actionButtonClickedFromTable($event)"
          [actionButtonItems]="actionButtonItems"
        >
        </app-editable-table>
      }
      <div>
        <p class="text-[#474D66] text-[16px] font-medium mb-6">
          Total number of issues: {{ errorCount }}
        </p>
        <p class="text-[#474D66] text-[16px] font-medium">
          Total number of rows: {{ csvTableData.length }}
        </p>
      </div>
    </section>
  </div>
</main>

@if (this.showCancelBulkQuestionModal) {
  <app-custom-mini-modal
    [headerTitle]="'Confirm Question Upload Cancellation'"
    [textAlign]="'center'"
    [left]="'Yes, Cancel'"
    [right]="'No, Continue'"
    [visibleModal]="showCancelBulkQuestionModal"
    [bodyText]="'Are you sure you want to cancel?'"
    (leftClickEvent)="confirmCancelQuestionUpload()"
    (rightClickEvent)="closeQuestionUploadCancelModal()"
    (visibleModalChange)="closeQuestionUploadCancelModal()"
    [width]="'446px'"
    [height]="'223px'"
  />
}

@if (showErrors) {
  <app-custom-modal
    [(visibleModal)]="showErrors"
    (visibleModalChange)="closeErrorModal()"
    [width]="'389px'"
    [height]="'350px'"
    [textAlign]="'center'"
    [headerTitle]="'Error Details'"
  >
    <div class="px-6">
      <div class="text-center text-[#ED2020] text-[14px] font-medium">
        Row number {{ rowNumber }}
      </div>
      <ol class="list-decimal">
        @for (item of errorMessage; track item) {
          <li>{{ item }}</li>
        }
      </ol>
    </div>
  </app-custom-modal>
}
