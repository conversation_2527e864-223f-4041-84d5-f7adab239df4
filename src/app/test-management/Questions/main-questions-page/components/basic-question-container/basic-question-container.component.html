<main class="w-full">
  @if (showEditor) {
    <p
      class="text-[#262626] flex gap-2 items-center text-lg sm:text-xl font-medium px-4"
    >
      <span> Question </span>
      <app-required-svg />
    </p>
    <div
      [ngClass]="{
        'p-4': addPadding,
        'p-0': !addPadding,
      }"
    >
      <app-custom-text-editor
        [placeholder]="'Enter Question Here'"
        [formControl]="getFormControl('questionText')"
        (addBlankOptionEmitter)="addBlankOption($event)"
        [insertBlankFillIn]="fillInTheBlank"
        [editorId]="questionEditorId"
      ></app-custom-text-editor>
    </div>
  }
  <ng-content></ng-content>
  <div
    class="max-w-full"
    [ngClass]="{
      'p-4': addPadding,
      'p-0': !addPadding,
    }"
  >
    <div
      class="grid grid-cols-1 gap-4"
      [ngClass]="{
        'md:grid-cols-2': !isGenerateModal,
        'md:grid-cols-1': isGenerateModal,
      }"
    >
      @for (item of constraints; track $index) {
        <div class="flex flex-col">
          <div
            class="text-[#262626] flex gap-2 items-center font-medium text-sm sm:text-base mb-2"
          >
            <span> {{ item.label }} </span>
            <app-required-svg />
          </div>
          <app-custom-dropdown
            [options]="item.data"
            [displayKey]="'name'"
            [placeholder]="item.placeholder"
            [formControl]="getFormControl(item.id)"
            (selectionChange)="onSelectionChange(item.id, $event)"
            [showSearch]="item.label === 'Domain'"
            [isCategoryDropdown]="item.id === 'category'"
            [showNoDomainSelectedMessage]="showNoDomainSelectedMessage"
            [errorMessage]="
              'You will need to select a domain to select a category'
            "
            [searchInputPlaceholder]="'Search Domain...'"
            [center]="false"
            (dropdownToggleEmitter)="onDropdownToggle(item.id)"
          ></app-custom-dropdown>
        </div>
      }

      <div class="flex flex-col">
        <div
          class="text-[#262626] flex gap-2 items-center font-medium text-sm sm:text-base mb-2"
        >
          <span> Score </span>
          <app-required-svg />
        </div>
        <app-custom-input
          [type]="'number'"
          [formControl]="getFormControl('score')"
          [placeholder]="'Type score here'"
          [min]="1"
        ></app-custom-input>
        @if (form.get('score')?.errors?.['negativeNumber']) {
          <div class="text-red-500 text-xs sm:text-sm mt-1">
            Score cannot be negative
          </div>
        }
      </div>
    </div>

    <!--    &lt;!&ndash; Reuse Settings Checkbox &ndash;&gt;-->
    <!--    @if (reuseSettings) {-->
    <!--      <div class="mt-4 pt-4 border-t border-[#0822301A]">-->
    <!--        <div class="flex items-center gap-3">-->
    <!--          <input-->
    <!--            type="checkbox"-->
    <!--            id="reuseSettings"-->
    <!--            [formControl]="getReuseSettingsControl()"-->
    <!--            class="w-4 h-4 text-[#0C4767] bg-gray-100 border-gray-300 rounded focus:ring-[#0C4767] focus:ring-2"-->
    <!--          />-->
    <!--          <span class="text-sm font-medium text-[#262626]">-->
    <!--            Reuse domain, category, difficulty level, and score for next-->
    <!--            question-->
    <!--          </span>-->
    <!--        </div>-->
    <!--        <p class="text-xs text-[#6B7280] mt-1 ml-7">-->
    <!--          When checked, these fields will remain filled after saving the-->
    <!--          question-->
    <!--        </p>-->
    <!--      </div>-->
    <!--    }-->
  </div>
</main>

<app-custom-mini-modal
  [headerTitle]="'Confirm Question Cancellation'"
  [textAlign]="'center'"
  [left]="'Yes, Cancel'"
  [right]="'No, Continue'"
  [visibleModal]="showCancelModal"
  [bodyText]="
    'Are you sure you want to cancel this question? All progress will be lost.'
  "
  (leftClickEvent)="confirmCancelQuestionCreation()"
  (rightClickEvent)="closeCancelModal()"
  (visibleModalChange)="closeCancelModal()"
  [width]="'446px'"
  [height]="'223px'"
/>
