import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Subject, Subscription } from 'rxjs';
import { CustomTextEditorComponent } from '../../../../../components/custom-text-editor/custom-text-editor.component';
import { CustomDropdownComponent } from '../../../../../components/custom-dropdown/custom-dropdown.component';
import { CustomInputComponent } from '../../../../../components/custom-input/custom-input.component';
import { DomainService } from '../../../../../services/domain.service';
import { DomainData } from '../../../../../Interfaces/domainInterface';
import { QuestionsService } from '../../../../../services/test-management-service/questions.service';
import { Router } from '@angular/router';
import { CustomMiniModalComponent } from '../../../../../components/custom-mini-modal/custom-mini-modal.component';
import { NgClass } from '@angular/common';
import { RequiredSvgComponent } from '../../../../../components/required-svg/required-svg.component';

@Component({
  selector: 'app-basic-question-container',
  standalone: true,
  imports: [
    CustomTextEditorComponent,
    CustomDropdownComponent,
    CustomInputComponent,
    CustomMiniModalComponent,
    NgClass,
    RequiredSvgComponent,
    ReactiveFormsModule,
  ],
  templateUrl: './basic-question-container.component.html',
})
export class BasicQuestionContainerComponent implements OnInit, OnDestroy {
  @Input() form: FormGroup = new FormGroup({});
  @Input() showEditor = true;
  @Input() fillInTheBlank = false;
  @Input() questionEditorId = '';
  @Input() addPadding = true;
  showCancelModal: boolean = false;
  @Output() addBlankOptionEmitter = new EventEmitter<string>();
  showCancelModalSubscription: Subscription | undefined;
  @Input() multiSelect: boolean = false;
  @Input() isGenerateModal: boolean = false;
  @Input() isCodingType: boolean = false;
  @Input() showNoDomainSelectedMessage: boolean = false;
  @Input() reuseSettings: boolean = true;
  private readonly destroy$ = new Subject<void>();
  @Input() constraints: {
    id: string;
    label: string;
    placeholder: string;

    data: unknown[];
  }[] = [
    {
      id: 'domain',
      label: 'Domain',
      data: [],
      placeholder: 'Please select',
    },
    {
      id: 'category',
      label: 'Category',
      data: [],
      placeholder: 'Please select',
    },
    {
      id: 'difficultyLevel',
      label: 'Difficulty Level',
      data: ['Beginner', 'Intermediate', 'Advanced'],
      placeholder: 'Please select',
    },
  ];

  constructor(
    private readonly domainService: DomainService,
    private readonly questionService: QuestionsService,
    private readonly router: Router
  ) {}

  ngOnInit(): void {
    if (this.router.url.includes('edit')) {
      this.reuseSettings = false;
    }

    this.showCancelModalSubscription =
      this.questionService.cancelQuestion$.subscribe((value) => {
        this.showCancelModal = value;
      });

    const strictMarkConstraint = {
      id: 'strictMark',
      label: 'Enable Strict Marking',
      data: [
        { name: 'Yes', value: true },
        { name: 'No', value: false },
      ],
      placeholder: 'Please select',
    };

    if (this.multiSelect) {
      this.constraints.push(strictMarkConstraint);
    }

    this.domainService.domainData$.subscribe((data) => {
      if (!data.data.length) {
        this.loadAllDomainData();
      }

      this.constraints[0].data = data.data;

      // 🔽 Call after domain data is ready
      this.initializeEditMode();
    });

    // Add form controls
    this.constraints.forEach((item) => {
      this.form.addControl(item.id, new FormControl('', Validators.required));
    });

    this.form.addControl(
      'score',
      new FormControl('', [Validators.required, Validators.min(1)])
    );
    this.form.addControl(
      'questionText',
      new FormControl('', Validators.required)
    );
    this.form.addControl('reuseSettings', new FormControl(false));
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    this.showCancelModalSubscription?.unsubscribe();
  }
  getFormControl(controlName: string): FormControl {
    return this.form.get(controlName) as FormControl;
  }

  onSelectionChange(itemId: string, selectedOption: DomainData) {
    if (itemId === 'domain') {
      this.getFormControl('category').setValue('');
      this.constraints[1].data = selectedOption.categories;
    }
  }

  onDropdownToggle(itemId: string) {
    if (itemId === 'category') {
      const domainControl = this.getFormControl('domain');
      const isDomainSelected = domainControl?.value;

      if (isDomainSelected) {
        if (
          !this.constraints[1].data ||
          this.constraints[1].data.length === 0
        ) {
          this.populateCategoryData();
        }
        this.showNoDomainSelectedMessage = false;
      } else {
        this.showNoDomainSelectedMessage = true;
      }
    } else {
      this.showNoDomainSelectedMessage = false;
    }
  }

  initializeEditMode() {
    const domainControl = this.getFormControl('domain');
    const domainValue = domainControl?.value;
    if (domainValue) {
      const domainId =
        typeof domainValue === 'object' ? domainValue.id : domainValue;

      const fullDomainData = (this.constraints[0].data as DomainData[]).find(
        (domain: DomainData) => domain.id === domainId
      );

      if (fullDomainData?.categories) {
        this.constraints[1].data = fullDomainData.categories;
      }
    }
  }

  populateCategoryData() {
    const domainControl = this.getFormControl('domain');
    if (domainControl?.value) {
      const selectedDomainId = domainControl.value.id ?? domainControl.value;
      const fullDomainData = (this.constraints[0].data as DomainData[]).find(
        (domain: DomainData) =>
          domain.id === selectedDomainId || domain === selectedDomainId
      );

      if (fullDomainData?.categories) {
        this.constraints[1].data = fullDomainData.categories;
      }
    }
  }

  closeCancelModal() {
    this.showCancelModal = false;
    this.questionService.cancelQuestion.next(false);
  }

  handleKeyCloseCancelModal(event: KeyboardEvent) {
    if (event.code === 'Enter') {
      event.preventDefault();
      this.closeCancelModal();
    }
  }

  handleKeyCancelConfim(event: KeyboardEvent) {
    if (event.code === 'Enter') {
      event.preventDefault();
      this.confirmCancelQuestionCreation();
    }
  }

  confirmCancelQuestionCreation() {
    this.questionService.cancelQuestion.next(false);
    this.form.reset();
    this.router.navigate([
      '/dashboard/test-management/questions/all-questions',
    ]);
  }

  loadAllDomainData() {
    this.domainService.getSortedDomain();
  }

  addBlankOption(event: string) {
    this.addBlankOptionEmitter.emit(event);
  }
}
