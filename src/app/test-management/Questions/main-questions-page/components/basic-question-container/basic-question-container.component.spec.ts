import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BasicQuestionContainerComponent } from './basic-question-container.component';
import { DomainService } from '../../../../../services/domain.service';
import { QuestionsService } from '../../../../../services/test-management-service/questions.service';
import { ToastService } from '../../../../../services/toast-service/toast.service';
import { Router } from '@angular/router';
import { of, Subject } from 'rxjs';
import { ReactiveFormsModule, FormControl } from '@angular/forms';
import { HttpClient, HttpHandler } from '@angular/common/http';

describe('BasicQuestionContainerComponent', () => {
  let component: BasicQuestionContainerComponent;
  let fixture: ComponentFixture<BasicQuestionContainerComponent>;
  let domainServiceMock: jest.Mocked<DomainService>;
  let questionsServiceMock: jest.Mocked<QuestionsService>;
  let toastServiceMock: jest.Mocked<ToastService>;
  let routerMock: jest.Mocked<Router>;

  beforeEach(async () => {
    domainServiceMock = {
      domainData$: of({ data: [] }),
      getSortedDomain: jest.fn(),
    } as unknown as jest.Mocked<DomainService>;

    questionsServiceMock = {
      cancelQuestion$: new Subject(),
      cancelQuestion: new Subject(),
    } as unknown as jest.Mocked<QuestionsService>;

    toastServiceMock = {
      onShow: jest.fn(),
    } as unknown as jest.Mocked<ToastService>;

    routerMock = {
      navigate: jest.fn(),
      url: '/dashboard/test-management/questions/all-questions',
    } as unknown as jest.Mocked<Router>;

    jest.spyOn(questionsServiceMock.cancelQuestion, 'next');

    await TestBed.configureTestingModule({
      imports: [ReactiveFormsModule, BasicQuestionContainerComponent],
      providers: [
        { provide: DomainService, useValue: domainServiceMock },
        { provide: QuestionsService, useValue: questionsServiceMock },
        { provide: ToastService, useValue: toastServiceMock },
        { provide: Router, useValue: routerMock },
        HttpClient,
        HttpHandler,
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(BasicQuestionContainerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form controls and subscribe to cancelQuestion$', () => {
    const formSpy = jest.spyOn(component.form, 'addControl');
    component.ngOnInit();

    expect(formSpy).toHaveBeenCalledTimes(component.constraints.length + 3);
    expect(formSpy).toHaveBeenCalledWith(
      'questionText',
      expect.any(FormControl)
    );
    expect(formSpy).toHaveBeenCalledWith('score', expect.any(FormControl));

    expect(questionsServiceMock.cancelQuestion$.subscribe).toBeTruthy();
  });

  it('should add strictMark constraint when multiSelect is true', () => {
    component.multiSelect = true;
    component.ngOnInit();

    expect(component.constraints).toContainEqual(
      expect.objectContaining({ id: 'strictMark' })
    );
  });

  it('should show no domain message when category dropdown has no data', () => {
    component.constraints[1].data = [];
    component.onDropdownToggle('category');
    expect(component.showNoDomainSelectedMessage).toBe(true);
  });

  it('should unsubscribe and complete destroy$ on ngOnDestroy', () => {
    const nextSpy = jest.spyOn(component['destroy$'], 'next');
    const completeSpy = jest.spyOn(component['destroy$'], 'complete');
    component.ngOnDestroy();

    expect(nextSpy).toHaveBeenCalled();
    expect(completeSpy).toHaveBeenCalled();
  });

  it('should close cancel modal', () => {
    component.showCancelModal = true;
    component.closeCancelModal();

    expect(component.showCancelModal).toBe(false);
    expect(questionsServiceMock.cancelQuestion.next).toHaveBeenCalledWith(
      false
    );
  });

  it('should confirm question cancellation and navigate', () => {
    const resetSpy = jest.spyOn(component.form, 'reset');
    component.confirmCancelQuestionCreation();

    expect(questionsServiceMock.cancelQuestion.next).toHaveBeenCalledWith(
      false
    );
    expect(resetSpy).toHaveBeenCalled();
    expect(routerMock.navigate).toHaveBeenCalledWith([
      '/dashboard/test-management/questions/all-questions',
    ]);
  });

  it('should call closeCancelModal on Enter key press', () => {
    const event = new KeyboardEvent('keydown', { code: 'Enter' });
    const closeModalSpy = jest.spyOn(component, 'closeCancelModal');
    component.handleKeyCloseCancelModal(event);

    expect(closeModalSpy).toHaveBeenCalled();
  });

  it('should call confirmCancelQuestionCreation on Enter key press', () => {
    const event = new KeyboardEvent('keydown', { code: 'Enter' });
    const confirmCancelSpy = jest.spyOn(
      component,
      'confirmCancelQuestionCreation'
    );
    component.handleKeyCancelConfim(event);

    expect(confirmCancelSpy).toHaveBeenCalled();
  });

  describe('onDropdownToggle', () => {
    beforeEach(() => {
      component.form.addControl('domain', new FormControl(''));
      component.form.addControl('category', new FormControl(''));
      component.constraints = [
        {
          id: 'domain',
          label: 'Domain',
          data: [],
          placeholder: 'Please select',
        },
        {
          id: 'category',
          label: 'Category',
          data: [],
          placeholder: 'Please select',
        },
      ];
    });

    it('should set showNoDomainSelectedMessage to true when category dropdown is toggled and no domain is selected', () => {
      component.form.get('domain')?.setValue('');
      component.onDropdownToggle('category');

      expect(component.showNoDomainSelectedMessage).toBe(true);
    });

    it('should set showNoDomainSelectedMessage to false when category dropdown is toggled and domain is selected', () => {
      const mockDomain = {
        id: 1,
        name: 'Test Domain',
        categories: ['Category1', 'Category2'],
      };
      component.form.get('domain')?.setValue(mockDomain);

      component.onDropdownToggle('category');

      expect(component.showNoDomainSelectedMessage).toBe(false);
    });

    it('should call populateCategoryData when category dropdown is toggled, domain is selected, and constraints[1].data is empty', () => {
      const mockDomain = {
        id: 1,
        name: 'Test Domain',
        categories: ['Category1', 'Category2'],
      };
      component.form.get('domain')?.setValue(mockDomain);
      component.constraints[1].data = [];

      const populateCategoryDataSpy = jest.spyOn(
        component,
        'populateCategoryData'
      );

      component.onDropdownToggle('category');

      expect(populateCategoryDataSpy).toHaveBeenCalled();
    });

    it('should not call populateCategoryData when category dropdown is toggled, domain is selected, and constraints[1].data has data', () => {
      const mockDomain = {
        id: 1,
        name: 'Test Domain',
        categories: ['Category1', 'Category2'],
      };
      component.form.get('domain')?.setValue(mockDomain);
      component.constraints[1].data = ['Existing Category'];

      const populateCategoryDataSpy = jest.spyOn(
        component,
        'populateCategoryData'
      );

      component.onDropdownToggle('category');

      expect(populateCategoryDataSpy).not.toHaveBeenCalled();
    });

    it('should set showNoDomainSelectedMessage to false when non-category dropdown is toggled', () => {
      component.showNoDomainSelectedMessage = true;

      component.onDropdownToggle('domain');

      expect(component.showNoDomainSelectedMessage).toBe(false);
    });

    it('should handle null constraints[1].data when category dropdown is toggled', () => {
      const mockDomain = {
        id: 1,
        name: 'Test Domain',
        categories: ['Category1', 'Category2'],
      };
      component.form.get('domain')?.setValue(mockDomain);
      component.constraints[1].data = null as unknown as string[];

      const populateCategoryDataSpy = jest.spyOn(
        component,
        'populateCategoryData'
      );

      component.onDropdownToggle('category');

      expect(populateCategoryDataSpy).toHaveBeenCalled();
    });
  });

  describe('initializeEditMode', () => {
    beforeEach(() => {
      component.form.addControl('domain', new FormControl(''));
      component.constraints = [
        {
          id: 'domain',
          label: 'Domain',
          data: [],
          placeholder: 'Please select',
        },
        {
          id: 'category',
          label: 'Category',
          data: [],
          placeholder: 'Please select',
        },
      ];
    });

    it('should set constraints[1].data to empty array when domain control has value without categories', () => {
      const mockDomain = { id: 1, name: 'Test Domain' };
      component.form.get('domain')?.setValue(mockDomain);

      component.initializeEditMode();

      expect(component.constraints[1].data).toEqual([]);
    });

    it('should not modify constraints[1].data when domain control has no value', () => {
      const originalData = ['Original Category'];
      component.constraints[1].data = originalData;
      component.form.get('domain')?.setValue('');

      component.initializeEditMode();

      expect(component.constraints[1].data).toEqual(originalData);
    });

    it('should not modify constraints[1].data when domain control is null', () => {
      const originalData = ['Original Category'];
      component.constraints[1].data = originalData;
      component.form.get('domain')?.setValue(null);

      component.initializeEditMode();

      expect(component.constraints[1].data).toEqual(originalData);
    });
  });

  describe('populateCategoryData', () => {
    beforeEach(() => {
      component.form.addControl('domain', new FormControl(''));
      component.constraints = [
        {
          id: 'domain',
          label: 'Domain',
          data: [
            { id: 1, name: 'Domain 1', categories: ['Cat1', 'Cat2'] },
            { id: 2, name: 'Domain 2', categories: ['Cat3', 'Cat4'] },
          ],
          placeholder: 'Please select',
        },
        {
          id: 'category',
          label: 'Category',
          data: [],
          placeholder: 'Please select',
        },
      ];
    });

    it('should populate category data when domain control has object value with id', () => {
      const mockDomain = { id: 1, name: 'Domain 1' };
      component.form.get('domain')?.setValue(mockDomain);

      component.populateCategoryData();

      expect(component.constraints[1].data).toEqual(['Cat1', 'Cat2']);
    });

    it('should populate category data when domain control has primitive id value', () => {
      component.form.get('domain')?.setValue(2);

      component.populateCategoryData();

      expect(component.constraints[1].data).toEqual(['Cat3', 'Cat4']);
    });

    it('should not modify category data when domain is not found in constraints data', () => {
      const mockDomain = { id: 999, name: 'Non-existent Domain' };
      component.form.get('domain')?.setValue(mockDomain);
      const originalData = ['Original Category'];
      component.constraints[1].data = originalData;

      component.populateCategoryData();

      expect(component.constraints[1].data).toEqual(originalData);
    });

    it('should not modify category data when domain control has no value', () => {
      component.form.get('domain')?.setValue('');
      const originalData = ['Original Category'];
      component.constraints[1].data = originalData;

      component.populateCategoryData();

      expect(component.constraints[1].data).toEqual(originalData);
    });

    it('should not modify category data when found domain has no categories', () => {
      const domainWithoutCategories = { id: 3, name: 'Domain 3' };
      component.constraints[0].data = [domainWithoutCategories];
      component.form.get('domain')?.setValue(domainWithoutCategories);
      const originalData = ['Original Category'];
      component.constraints[1].data = originalData;

      component.populateCategoryData();

      expect(component.constraints[1].data).toEqual(originalData);
    });

    it('should handle empty constraints[0].data array', () => {
      component.constraints[0].data = [];
      component.form.get('domain')?.setValue({ id: 1, name: 'Domain 1' });
      const originalData = ['Original Category'];
      component.constraints[1].data = originalData;

      component.populateCategoryData();

      expect(component.constraints[1].data).toEqual(originalData);
    });

    it('should handle domain value being the full domain object', () => {
      const fullDomainObject = {
        id: 1,
        name: 'Domain 1',
        categories: ['Cat1', 'Cat2'],
      };
      component.constraints[0].data = [fullDomainObject];
      component.form.get('domain')?.setValue(fullDomainObject);

      component.populateCategoryData();

      expect(component.constraints[1].data).toEqual(['Cat1', 'Cat2']);
    });
  });
});
