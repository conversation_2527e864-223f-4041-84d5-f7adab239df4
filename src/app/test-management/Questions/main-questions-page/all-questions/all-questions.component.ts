import {
  Component,
  Inject,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  PLATFORM_ID,
  signal,
  ViewChild,
} from '@angular/core';
import { NgxPaginationModule } from 'ngx-pagination';
import { CustomPaginationComponent } from '../../../../components/custom-pagination/custom-pagination.component';
import { CustomButtonComponent } from '../../../../components/custombutton/custombutton.component';
import { BasicButtonDropdownComponent } from '../../../../components/basic-button-dropdown/basic-button-dropdown.component';
import {
  FilterList,
  QuestionsData,
} from '../../../../Interfaces/questionInterface';
import {
  ALL_DIFFICULTY_LEVELS,
  ALL_QUESTION_TYPES,
  ALL_STATUS_TYPES,
  FilterType,
  questionTypes,
  transformValue,
} from '../../../../utils/testManagement';
import { calculateRange, getType } from '../../../../utils/questionsConstants';
import { CustomMiniModalComponent } from '../../../../components/custom-mini-modal/custom-mini-modal.component';
import { ActionItemsPopUpComponent } from '../../../../components/action-items-pop-up/action-items-pop-up.component';
import { ToastService } from '../../../../services/toast-service/toast.service';
import { CustomSearchInputComponent } from '../../../../components/custom-search-input/custom-search-input.component';
import { QuestionCreationServiceService } from '../../../../services/test-management-service/question-creation-service.service';
import { LoginUserInfo } from '../../../../services/authServiceInterfaces';
import { AuthService } from '../../../../services/auth.service';
import {
  checkPermission,
  PermissionAccessGroups,
} from '../../../../utils/permissions';
import { NoResultFoundComponent } from '../../../../no-result-found/no-result-found.component';
import { CustomModalComponent } from '../../../../components/custom-modal/custom-modal.component';
import { CustomFilterButtonComponent } from '../../../../reportsManagement/components/custom-filter-button/custom-filter-button.component';
import { CustomDropdownComponent } from '../../../../components/custom-dropdown/custom-dropdown.component';
import { DomainService } from '../../../../services/domain.service';
import { DomainData } from '../../../../Interfaces/domainInterface';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { PreviewDataComponent } from '../../../../components/preview-data/preview-data.component';
import { AsyncPipe, CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { RefreshLoaderComponent } from '../../../../components/refresh-loader/refresh-loader.component';
import { debounceTime, Subject, Subscription } from 'rxjs';
import { QuestionStore } from '../../../../stores/questions-store/question.store';
import { Router } from '@angular/router';
import { CodingStore } from '../../../../stores/coding-store/coding.store';
import { QuestionsCardComponent } from '../../../../components/questions-card/questions-card.component';

@Component({
  selector: 'app-questions',
  standalone: true,
  imports: [
    AsyncPipe,
    NgxPaginationModule,
    CustomPaginationComponent,
    CustomButtonComponent,
    BasicButtonDropdownComponent,
    CustomMiniModalComponent,
    ActionItemsPopUpComponent,
    CustomSearchInputComponent,
    NoResultFoundComponent,
    CustomModalComponent,
    CustomFilterButtonComponent,
    CustomDropdownComponent,
    ReactiveFormsModule,
    PreviewDataComponent,
    CommonModule,
    MatIconModule,
    RefreshLoaderComponent,
    QuestionsCardComponent,
  ],
  templateUrl: './all-questions.component.html',
  styleUrls: ['./all-questions.component.scss'],
})
export class AllQuestionsComponent implements OnInit, OnDestroy {
  private readonly subscription: Subscription = new Subscription();
  allQuestions: QuestionsData[] = [];
  question!: QuestionsData;
  questionText!: string;
  allDomains: DomainData[] = [];
  showDeleteModal = false;
  searchSubject: Subject<string> = new Subject();
  showGlobalizationModal = false;
  selectedValues: { [key: string]: string } = {};
  originalActionButtonItems = [
    {
      name: 'Edit',
      icon: '../../../../../assets/icons/edit.svg',
      onClick: () => {
        this.codingStore.resetAllState();
        const queryParams = this.questionId
          ? {
              questionId: this.questionId,
              questionType: this.questionType,
              index: this.selectedCardIndex,
            }
          : {};
        this.router.navigate([this.editUrl], { queryParams });
      },
    },
    {
      name: 'Add to test',
      icon: '../../../../../assets/icons/plusInCircle.svg',
      onClick: () => {
        this.selectedCardIndex = null;
        this.router.navigate(
          ['dashboard/test-management/tests/add-to-test', this.questionId],
          { queryParams: { domainName: this.domainName.replaceAll(' ', '-') } }
        );
      },
    },

    {
      name: 'Delete',
      icon: '../../../../../assets/icons/delete-icon.svg',
      onClick: () => {
        this.selectedCardIndex = null;
        this.showDeleteModal = true;
      },
    },
  ];
  actionButtonItems: { name: string; icon: string; onClick: () => void }[] = [];
  page = 1;
  pageSize = 25;
  totalNumberOfQuestions = 0;
  searchTerm = '';
  range = '';
  options = questionTypes;
  domainName = '';
  edit = false;
  questionType = '';
  questionId: string = '';
  selectedCardIndex: number | null = null;
  cardIndexTracker: number | null = null;
  baseurl = '/dashboard/test-management/questions/create-question';
  editUrl = '/dashboard/test-management/questions/edit-questions';
  userInfo: LoginUserInfo | null = null;
  showQuestionButtons = false;
  isLoading = this.questionsStore.isQuestionsLoading$;
  showPreviewModal = false;
  value = [];
  selectedFilters: string[] = [];
  isDropdownFilter = false;
  activeDropdownIndex = -1;
  selectedFiltersOptions: { key: string; value: string | boolean }[] = [];
  filterLists: FilterList[] = [];
  isDomain = signal(false);
  selectedDomainId = signal('');
  checkMark = 'assets/icons/check-mark.svg';
  forceFetchQuestions = false;
  hasDomainLoaded = false;
  selectedFilter: string[] = [];
  domainControl = new FormControl('');

  // Enhanced filtering state management
  private cachedCategories: { [domainId: string]: string[] } = {};
  currentCategoryOptions: string[] = [];
  filterState = {
    selectedDomain: '',
    selectedCategory: '',
    preserveCategories: false,
  };

  onDocumentClick() {
    this.selectedCardIndex = null;
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();

    // Clean up mobile event listeners
    if (typeof window !== 'undefined') {
      document.removeEventListener(
        'touchstart',
        this.handleTouchStart.bind(this)
      );
    }

    if (this.searchTerm !== '' || this.selectedFilters.length > 0) {
      this.searchTerm = '';
      this.selectedFilters = [];
      this.selectedFiltersOptions = [];
      this.page = 1;
      this.pageSize = 25;
      this.forceFetchQuestions = true;
      this.loadQuestions();
    }
  }
  constructor(
    private readonly router: Router,
    private readonly toast: ToastService,
    private readonly questionCreationService: QuestionCreationServiceService,
    private readonly authService: AuthService,
    private readonly domainService: DomainService,
    private readonly questionsStore: QuestionStore,
    private readonly codingStore: CodingStore,
    @Inject(PLATFORM_ID) private readonly platformId: object
  ) {
    this.loadQuestions();
  }
  @ViewChild(CustomFilterButtonComponent)
  filterButtonComponent!: CustomFilterButtonComponent;

  ngOnInit(): void {
    this.initializeComponent();
    this.questionsStore.resetSuccessState();
    this.userInfo = this.authService.getLoginUser();

    if (this.userInfo?.permissions) {
      this.showQuestionButtons = checkPermission(
        PermissionAccessGroups.addQuestionsAccessGroup,
        this.userInfo?.permissions
      );
    }
    this.searchSubject.pipe(debounceTime(1200)).subscribe((searchTerm) => {
      if (searchTerm !== this.searchTerm) {
        this.searchTerm = searchTerm;
        this.page = 1;
        this.forceFetchQuestions = true;
        this.loadQuestions();
      }
    });

    const totalQuestionsSub =
      this.questionsStore.totalNumberOfQuestions$.subscribe((data) => {
        if (data) {
          this.totalNumberOfQuestions = data;
        }
      });
    this.subscription.add(totalQuestionsSub);

    const allQuestionsSub = this.questionsStore.allQuestions$.subscribe(
      (data) => {
        if (data) {
          this.allQuestions = data;
          this.range = calculateRange(
            this.page,
            this.pageSize,
            this.totalNumberOfQuestions
          );
        }
      }
    );
    this.subscription.add(allQuestionsSub);

    const domainDataSub = this.domainService.domainData.subscribe((data) => {
      if (data.data.length === 0 && !this.hasDomainLoaded) {
        this.domainService.getSortedDomain();
        this.hasDomainLoaded = true;
      }

      const previousDomainsLength = this.allDomains.length;
      this.allDomains = data.data;

      // Initialize filter lists
      this.initializeFilterLists();

      // If domains were just loaded and we had a selected domain, restore categories
      if (
        previousDomainsLength === 0 &&
        this.allDomains.length > 0 &&
        this.selectedDomainId()
      ) {
        this.loadCategoriesForDomain(this.selectedDomainId());
      }
    });
    this.subscription.add(domainDataSub);
  }
  loadQuestions() {
    const filters = this.selectedFiltersOptions.reduce(
      (
        acc: { [key: string]: string | boolean },
        filter: { key: string; value: string | boolean }
      ) => {
        acc[filter.key] = filter.value;
        return acc;
      },
      {}
    );

    this.questionsStore.fetchQuestions({
      page: this.page,
      size: this.pageSize,
      forceFetch: this.forceFetchQuestions,
      config: {
        keyword: this.searchTerm,
        ...filters,
      },
    });

    this.range = calculateRange(
      this.page,
      this.pageSize,
      this.totalNumberOfQuestions
    );
  }

  onPageChange(pageNumber: number) {
    if (pageNumber < 0 || pageNumber > this.totalNumberOfQuestions) return;
    if (pageNumber === this.page) return;
    this.page = pageNumber;
    this.forceFetchQuestions = true;
    this.loadQuestions();
  }

  onSizeChange(event: number) {
    this.pageSize = event;
    this.page = 1;
    this.forceFetchQuestions = true;
    this.loadQuestions();
  }

  selectedOption(value: string) {
    switch (value) {
      case 'Multiple Choice':
        this.router.navigate([this.baseurl + '/multi-choice']);
        break;
      case 'Multi Select':
        this.router.navigate([this.baseurl + '/multi-select']);
        break;
      case 'True or False':
        this.router.navigate([this.baseurl + '/true-or-false']);
        break;
      case 'Essay':
        this.router.navigate([this.baseurl + '/essay']);
        break;
      case 'Fill In The Blanks':
        this.router.navigate([this.baseurl + '/fill-in-the-blank']);
        break;
      case 'Matrix':
        this.router.navigate([this.baseurl + '/matrix'], {
          queryParams: {
            page: 1,
          },
        });
        break;
      case 'Code':
        this.codingStore.resetAllState();
        this.router.navigate([this.baseurl + '/code'], {
          queryParams: {
            page: 1,
          },
        });
        break;

      default:
        break;
    }
  }

  onActionClick(event: QuestionsData, index: number) {
    this.actionButtonItems = [];
    this.selectedCardIndex = index === this.selectedCardIndex ? null : index;
    this.questionId = event.id;
    this.domainName = event.domain.name;
    this.statusCheck(event);
    this.questionType = event.questionType.replaceAll(' ', '');
  }

  statusCheck(event: QuestionsData) {
    if (
      event.isActive ||
      (event.system && this.userInfo?.role !== 'System Admin')
    ) {
      // Remove "Edit" and "Delete" buttons when the item is active
      this.actionButtonItems = this.originalActionButtonItems.filter(
        (item) => item.name !== 'Edit' && item.name !== 'Delete'
      );
    } else {
      // Show all action buttons when it's not active
      this.actionButtonItems = [...this.originalActionButtonItems];
    }
  }

  closeDeleteModal() {
    this.showDeleteModal = false;
  }

  closeGlobalizationModal() {
    this.showGlobalizationModal = false;
  }

  confirmDeleteQuestionCreation() {
    this.questionCreationService.deleteQuestion(this.questionId).subscribe({
      next: () => {
        this.showDeleteModal = false;
        this.toast.onShow(
          'Success',
          'Question deleted successfully',
          true,
          'success'
        );
        this.searchSubject.next('');
        this.forceFetchQuestions = true;
        this.loadQuestions();
      },
      error: (error) => {
        this.toast.onShow('error', error, true, 'error');
      },
    });
  }

  onSearchChange($event: string) {
    this.searchSubject.next($event);
  }

  onKeydownBulkQuestionUpload(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      this.bulkQuestionUpload();
    }
  }

  bulkQuestionUpload() {
    this.router.navigate(['/dashboard/test-management/questions/bulk-upload']);
  }

  confirmGlobalization() {
    this.questionCreationService.globalizeQuestion(this.questionId).subscribe({
      next: () => {
        this.showGlobalizationModal = false;
        this.toast.onShow(
          'Question Globalization',
          'Question globalization request sent',
          true,
          'success'
        );
      },
      error: (error) => {
        this.toast.onShow('error', error, true, 'error');
      },
    });
  }
  previewQuestion() {
    this.showPreviewModal = !this.showPreviewModal;
  }
  onCardClick(questionData: QuestionsData, index: number) {
    this.cardIndexTracker = index;
    this.question = questionData;
    this.questionText = this.question.questionText;
    this.showPreviewModal = true;
    this.questionType = this.question.questionType;
  }

  toggleDropdownFilterVisibility() {
    this.isDropdownFilter = !this.isDropdownFilter;
  }

  toggleDropdownColumnVisibility(index: number) {
    this.activeDropdownIndex = this.activeDropdownIndex === index ? -1 : index;
  }

  selectFilterItems(item: string) {
    if (item === 'All') {
      if (
        this.selectedFilters.length ===
        this.getDisplayNames().filter((items) => items !== 'All').length
      ) {
        this.selectedFilters = [];
        this.resetAllFilterStates();
      } else {
        this.applyAllFilters();
      }
    } else {
      const isCurrentlySelected = this.selectedFilters.includes(item);

      this.toggleFilterSelection(item);
      if (isCurrentlySelected) {
        this.performFilterCleanup(item);
      }
    }
  }

  private performFilterCleanup(item: string) {
    if (item === 'Domains') {
      this.resetDomainFilters();
    } else {
      this.removeFilterOption(item);
    }
    delete this.selectedValues[item];

    this.filterButtonComponent.removeFilter(item);
    this.forceFetchQuestions = true;
    this.loadQuestions();
  }

  private resetAllFilterStates() {
    this.isDomain.set(false);
    this.selectedDomainId.set('');
    this.domainControl.setValue('');
    this.selectedFiltersOptions = [];

    this.selectedValues = {};
    this.selectedFilters.forEach((filter) => {
      this.filterButtonComponent.removeFilter(filter);
    });
    this.forceFetchQuestions = true;
    this.loadQuestions();
  }

  selectFilteredOption(item: string) {
    const selectedFilter = this.filterLists.find((filter) =>
      filter.value.includes(item)
    );
    if (!selectedFilter) return;

    switch (selectedFilter.key) {
      case 'domainId':
        this.domainControl.setValue(null);
        this.handleDomainSelection(item);
        break;
      case 'category':
        this.handleCategorySelection(item);
        break;
      case 'isActive':
        this.handleStatusSelection(item);
        break;
      default:
        this.updateSelectedFiltersOptions(
          selectedFilter.key,
          transformValue(item)
        );
        break;
    }
    this.selectedValues[selectedFilter.display] = item;
    this.forceFetchQuestions = true;
    this.loadQuestions();
  }

  getOptions(key: string) {
    return this.filterLists.find((item) => item.display === key)?.value ?? [];
  }

  applyAllFilters() {
    this.selectedFilters = this.getDisplayNames().filter(
      (items) => items !== 'All'
    );
  }

  toggleFilterSelection(item: string) {
    const index = this.selectedFilters.indexOf(item);
    if (index === -1) {
      this.selectedFilters.push(item);
    } else {
      this.selectedFilters.splice(index, 1);
    }
  }

  resetDomainFilters() {
    this.isDomain.set(false);
    this.selectedDomainId.set('');

    this.selectedFiltersOptions = this.selectedFiltersOptions.filter(
      (option) => option.key !== 'domainId' && option.key !== 'categoryId'
    );
  }

  removeFilterOption(item: string) {
    const removedFilter = this.filterLists.find((v) => v.display === item);
    if (removedFilter) {
      this.selectedFiltersOptions = this.selectedFiltersOptions.filter(
        (option) => option.key !== removedFilter.key
      );

      // Handle specific filter removals
      if (item === 'Domains') {
        this.resetDomainAndCategoryFilters();
      } else if (item === 'Categories') {
        this.resetCategoryFilter();
      }

      // Clear selected value
      this.selectedValues[item] = '';
    }
  }

  private resetDomainAndCategoryFilters() {
    this.isDomain.set(false);
    this.selectedDomainId.set('');
    this.filterState.selectedDomain = '';
    this.filterState.selectedCategory = '';
    this.currentCategoryOptions = [];
    this.domainControl.setValue('');

    // Remove both domain and category from selected filters
    this.selectedFilters = this.selectedFilters.filter(
      (filter) => filter !== 'Categories'
    );

    this.selectedFiltersOptions = this.selectedFiltersOptions.filter(
      (option) => option.key !== 'domainId' && option.key !== 'categoryId'
    );

    this.selectedValues['Domains'] = '';
    this.selectedValues['Categories'] = '';
  }

  resetCategoryFilter() {
    this.filterState.selectedCategory = '';
    this.domainControl.setValue('');

    this.selectedFiltersOptions = this.selectedFiltersOptions.filter(
      (option) => option.key !== 'categoryId'
    );

    this.selectedValues['Categories'] = '';
  }

  handleDomainSelection(item: string) {
    const domainId = this.getSelectedDomainId(item);
    const previousDomainId = this.selectedDomainId();

    this.selectedDomainId.set(domainId);
    this.filterState.selectedDomain = item;

    // Only clear category selection if domain actually changed
    if (previousDomainId !== domainId) {
      this.selectedFilters = this.selectedFilters.filter(
        (filter) => filter !== 'Categories'
      );

      this.selectedFiltersOptions = this.selectedFiltersOptions.filter(
        (option) => option.key !== 'categoryId'
      );

      this.filterState.selectedCategory = '';
      this.domainControl.setValue('');
      this.selectedValues['Categories'] = '';
    }

    this.updateSelectedFiltersOptions('domainId', domainId);
    this.isDomain.set(true);

    // Load and cache categories for this domain
    this.loadCategoriesForDomain(domainId);
  }

  handleCategorySelection(item: string) {
    const domainId = this.selectedDomainId();
    const domain = this.allDomains.find((d) => d.id === domainId);
    const category = domain?.categories.find((c) => c.name === item);

    if (category) {
      this.filterState.selectedCategory = item;

      // Don't remove domain filter when selecting category
      // Keep both domain and category filters active
      this.updateSelectedFiltersOptions('categoryId', category.id);

      // Update the selected values for UI display
      this.selectedValues['Categories'] = item;
    }
  }

  handleStatusSelection(item: string) {
    const isActiveValue = item === 'Active';
    this.updateSelectedFiltersOptions('isActive', isActiveValue);
  }

  updateSelectedFiltersOptions(key: string, value: string | boolean) {
    const existingOptionIndex = this.selectedFiltersOptions.findIndex(
      (option) => option.key === key
    );

    if (existingOptionIndex !== -1) {
      this.selectedFiltersOptions[existingOptionIndex].value = value;
    } else {
      this.selectedFiltersOptions.push({ key, value });
    }
  }

  getAllDomainNames(domains: DomainData[]): string[] {
    return domains.map((domain) => domain.name);
  }

  getDisplayNames() {
    return this.filterLists
      .filter((filter) => filter.display !== 'Categories')
      .map((filter) => filter.display);
  }

  getCategories() {
    // Return cached categories to prevent unwanted updates
    return this.currentCategoryOptions;
  }

  private loadCategoriesForDomain(domainId: string) {
    // Check cache first
    if (this.cachedCategories[domainId]) {
      this.currentCategoryOptions = this.cachedCategories[domainId];
      this.updateCategoryFilterOptions();
      return;
    }

    // Load from domain data
    const domain = this.allDomains.find((d) => d.id === domainId);
    const categories = domain
      ? domain.categories.map((category) => category.name)
      : [];

    // Cache the categories
    this.cachedCategories[domainId] = categories;
    this.currentCategoryOptions = categories;

    this.updateCategoryFilterOptions();
  }

  private updateCategoryFilterOptions() {
    const categoryFilter = this.filterLists.find(
      (filter) => filter.key === 'category'
    );
    if (categoryFilter) {
      categoryFilter.value = [...this.currentCategoryOptions];
    }
  }

  private initializeFilterLists() {
    this.filterLists = [
      {
        display: 'All',
        key: '',
        value: [],
      },
      {
        display: 'Question Types',
        key: FilterType.QUESTION_TYPE,
        value: ALL_QUESTION_TYPES,
      },
      {
        display: 'Domains',
        key: FilterType.DOMAIN_ID,
        value: this.getAllDomainNames(this.allDomains),
      },
      {
        display: 'Categories',
        key: FilterType.CATEGORY,
        value: [...this.currentCategoryOptions],
      },
      {
        display: 'Difficulty Levels',
        key: FilterType.DIFFICULTY_LEVEL,
        value: ALL_DIFFICULTY_LEVELS,
      },
      {
        display: 'Status',
        key: FilterType.IS_ACTIVE,
        value: ALL_STATUS_TYPES,
      },
    ];
  }

  private initializeComponent(): void {
    // Initialize filter state
    this.filterState = {
      selectedDomain: '',
      selectedCategory: '',
      preserveCategories: false,
    };

    // Initialize selected values if not already initialized
    if (!this.selectedValues) {
      this.selectedValues = {};
    }

    // Clear any cached data
    this.cachedCategories = {};
    this.currentCategoryOptions = [];

    // Add mobile-specific event listeners
    this.setupMobileInteractions();
  }

  private setupMobileInteractions(): void {
    // Prevent zoom on double tap for filter buttons on mobile
    if (typeof window !== 'undefined' && window.innerWidth <= 640) {
      // Add touch event handling for better mobile experience
      document.addEventListener(
        'touchstart',
        this.handleTouchStart.bind(this),
        { passive: true }
      );
    }
  }

  private handleTouchStart(event: TouchEvent): void {
    // Improve touch interactions for filter elements
    const target = event.target as HTMLElement;
    if (target.closest('.filter-remove-btn')) {
      // Add visual feedback for touch
      target.style.transform = 'scale(0.95)';
      setTimeout(() => {
        target.style.transform = '';
      }, 150);
    }
  }

  // Enhanced filter removal with better mobile feedback
  removeFilterItems(item: string): void {
    // Provide haptic feedback on mobile if available
    if ('vibrate' in navigator) {
      navigator.vibrate(50);
    }

    this.selectedFilters = this.selectedFilters.filter(
      (filter) => filter !== item
    );

    if (item === 'Domains') {
      this.resetDomainFilters();
    } else {
      this.removeFilterOption(item);
    }

    // Update filter button component
    if (this.filterButtonComponent) {
      this.filterButtonComponent.removeFilter(item);
    }

    // Clean up selected values
    delete this.selectedValues[item];

    this.forceFetchQuestions = true;
    this.loadQuestions();
  }

  closeModal() {
    this.selectedCardIndex = null;
  }

  getSelectedDomainId(item: string) {
    return this.allDomains.find((domain) => domain.name === item)?.id ?? '';
  }

  onQuestionUpdated(questionType: string) {
    this.questionType = questionType;
  }
  getSelectedValue(item: string): string {
    return this.selectedValues[item] || '';
  }
  getType(middleContent: string) {
    return getType(middleContent);
  }

  protected readonly window = window;
}
