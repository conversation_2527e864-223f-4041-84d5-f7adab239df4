<main class="w-full flex flex-col">
  <!-- Responsive header section -->
  <div
    class="flex flex-col xl:flex-row xl:justify-between xl:items-center mb-4 flex-wrap gap-4"
  >
    <!-- Search Section -->
    <div class="w-full lg:w-auto lg:flex-1 lg:max-w-md xl:max-w-lg">
      <app-custom-search-input
        [placeholder]="'Search question...'"
        [searchTerm]="searchTerm"
        (searchTermChange)="onSearchChange($event)"
        class="w-full"
      ></app-custom-search-input>
    </div>

    <!-- Action Buttons Section -->
    <div
      class="flex flex-col sm:flex-row gap-3 sm:gap-2 md:gap-3 lg:items-center lg:flex-shrink-0 w-full lg:w-auto actions-section"
    >
      <!-- Filter Button -->
      <app-custom-filter-button
        (selectFilterClick)="toggleDropdownFilterVisibility()"
        [dropDownStatus]="isDropdownFilter"
        [filterArray]="getDisplayNames()"
        (selectedFilterItem)="selectFilterItems($event)"
        (closeFilter)="isDropdownFilter = false"
      ></app-custom-filter-button>

      @if (showQuestionButtons) {
        <!-- Mobile: Stacked buttons -->
        <div class="flex flex-col gap-3 sm:hidden w-full">
          <app-custombutton
            [variant]="'secondary'"
            (clicked)="bulkQuestionUpload()"
            (keydown)="onKeydownBulkQuestionUpload($event)"
            class="w-full"
            >Bulk Questions Upload</app-custombutton
          >
          <app-basic-button-dropdown
            [selectedLabel]="'+ Create New Question'"
            [options]="options"
            (optionSelected)="selectedOption($event)"
            class="w-full"
          ></app-basic-button-dropdown>
        </div>

        <!-- Desktop: Horizontal layout -->
        <div class="hidden sm:flex sm:gap-3">
          <app-custombutton
            [variant]="'secondary'"
            (clicked)="bulkQuestionUpload()"
            (keydown)="onKeydownBulkQuestionUpload($event)"
            class="whitespace-nowrap"
            >Bulk Question Upload</app-custombutton
          >
          <app-basic-button-dropdown
            [selectedLabel]="'+ Create New Question'"
            [options]="options"
            (optionSelected)="selectedOption($event)"
          ></app-basic-button-dropdown>
        </div>
      }
    </div>
  </div>

  <!-- Responsive filter section -->
  <div class="filter-section" *ngIf="selectedFilters.length > 0 || isDomain()">
    <!-- Desktop: Improved layout with proper ordering -->
    <div class="desktop-filter-container">
      <div class="desktop-filter-grid">
        @for (item of selectedFilters; track item) {
          <div
            class="desktop-filter-item"
            [class.domain-filter]="item === 'Domains'"
          >
            <span
              class="desktop-filter-label"
              [class]="getSelectedValue(item) ? 'opacity-100' : 'opacity-0'"
            >
              {{ item }}
            </span>
            <div class="relative">
              <button
                class="desktop-filter-close"
                (click)="removeFilterItems(item)"
                (keydown.enter)="removeFilterItems(item)"
                (keydown.space)="removeFilterItems(item)"
                tabindex="0"
                [attr.aria-label]="'Remove ' + item + ' filter'"
              >
                <img
                  src="assets/icons/cancel.svg"
                  alt="Remove"
                  class="w-3 h-3"
                />
              </button>
              <app-custom-dropdown
                [placeholder]="item"
                [options]="getOptions(item)"
                (selectionChange)="selectFilteredOption($event)"
                class="w-full desktop-dropdown"
                height="45px"
                [usePortalMode]="true"
              ></app-custom-dropdown>
            </div>
          </div>

          <!-- Category filter immediately after Domain filter -->
          @if (item === 'Domains' && isDomain()) {
            <div class="desktop-filter-item category-filter">
              <span
                class="desktop-filter-label"
                [class]="domainControl.value ? 'opacity-100' : 'opacity-0'"
              >
                Category
              </span>
              <div class="relative">
                @if (filterState.selectedCategory) {
                  <button
                    class="desktop-filter-close"
                    (click)="
                      resetCategoryFilter();
                      forceFetchQuestions = true;
                      loadQuestions()
                    "
                    (keydown.enter)="
                      resetCategoryFilter();
                      forceFetchQuestions = true;
                      loadQuestions()
                    "
                    (keydown.space)="
                      resetCategoryFilter();
                      forceFetchQuestions = true;
                      loadQuestions()
                    "
                    tabindex="0"
                    aria-label="Remove category filter"
                  >
                    <img
                      src="assets/icons/cancel.svg"
                      alt="Remove"
                      class="w-3 h-3"
                    />
                  </button>
                }
                <app-custom-dropdown
                  [formControl]="domainControl"
                  placeholder="Category"
                  [options]="getCategories()"
                  (selectionChange)="selectFilteredOption($event)"
                  height="45px"
                  class="w-full desktop-dropdown"
                  [isDisabled]="currentCategoryOptions.length === 0"
                  [usePortalMode]="true"
                ></app-custom-dropdown>
              </div>
            </div>
          }
        }
      </div>
    </div>
  </div>

  <div class="flex-1 flex flex-col">
    @if ((isLoading | async) && allQuestions.length === 0) {
      <div class="flex justify-center items-center flex-grow min-h-[40vh]">
        <app-refresh-loader [loadingText]="'Loading...'"></app-refresh-loader>
      </div>
    } @else if (allQuestions.length > 0) {
      <section class="questions-list">
        <div
          class="overflow-y-auto"
          [ngClass]="{
            'h-[calc(100vh-32rem)] md:h-[calc(100vh-28rem)]':
              selectedFilters.length > 0 || isDomain(),
            'h-[calc(100vh-28rem)] md:h-[calc(100vh-24rem)]':
              selectedFilters.length === 0 && !isDomain(),
          }"
        >
          @for (
            item of allQuestions
              | paginate
                : {
                    itemsPerPage: pageSize,
                    currentPage: page,
                    totalItems: totalNumberOfQuestions,
                  };
            track item;
            let i = $index
          ) {
            <div class="w-full mb-3 relative">
              <app-questions-card
                [system]="item.system"
                [title]="item.questionText"
                [showRightSide]="showQuestionButtons"
                [middleContent]="item.questionType"
                [isActive]="item.isActive"
                (actionButtonClicked)="onActionClick(item, i)"
                [isClickable]="true"
                [customRight]="true"
                (cardClicked)="onCardClick(item, i)"
                class="mobile-card"
              >
                <div
                  class="leftSideBody1 flex items-center gap-x-1 md:gap-x-2 text-xs md:text-sm"
                  #leftSideBody1
                >
                  @if (item && item.category && item.category.name) {
                    <div class="flex items-center gap-1">
                      <img
                        src="../../../../assets/icons/code.svg"
                        alt="Category"
                        class="w-3 h-3 md:w-4 md:h-4"
                      />
                      <span class="truncate max-w-[80px] md:max-w-full">{{
                        item.category.name
                      }}</span>
                    </div>
                  }
                  <div class="w-[3px] h-[3px] rounded-full bg-[#0C4767]"></div>
                  <div class="flex items-center gap-1">
                    <img
                      src="../../../../assets/icons/bytesize_book.svg"
                      alt="Difficulty"
                      class="w-3 h-3 md:w-4 md:h-4"
                    />
                    <span>{{ item.difficultyLevel }}</span>
                  </div>
                </div>

                <div
                  class="leftSideBody2 flex items-center gap-1 text-[#474D66] text-[10px] md:text-[12px] mt-1 md:mt-2 lg:mt-0"
                  #leftSideBody2
                >
                  <img
                    src="../../../../assets/icons/solar_medal.svg"
                    alt="Score"
                    class="w-3 h-3 md:w-4 md:h-4"
                  />
                  <span>Score: {{ item.score }}</span>
                </div>
              </app-questions-card>
              @if (selectedCardIndex === i) {
                <div>
                  <app-action-items-pop-up
                    [actionButtonItems]="actionButtonItems"
                    (closeModal)="closeModal()"
                  ></app-action-items-pop-up>
                </div>
              }
            </div>
          }
        </div>
      </section>

      <footer class="mt-6 mb-4 px-2">
        <div
          class="flex flex-col gap-4 md:flex-row md:justify-between md:items-center"
        >
          <div
            class="text-[#474d66] text-sm md:text-base font-medium text-center md:text-left"
          >
            {{ range }}
          </div>

          <div class="flex justify-center">
            <app-custom-pagination
              [total]="totalNumberOfQuestions"
              [size]="pageSize"
              (pagechange)="onPageChange($event)"
              (sizeSelect)="onSizeChange($event)"
            ></app-custom-pagination>
          </div>
        </div>
      </footer>
    } @else {
      <div class="flex-grow flex items-center justify-center">
        <app-no-result-found
          [message]="'No Questions found'"
          [search]="searchTerm"
        ></app-no-result-found>
      </div>
    }
  </div>
</main>

<app-custom-mini-modal
  [headerTitle]="'Confirm Question Deletion'"
  [textAlign]="'center'"
  [left]="'Cancel'"
  [right]="'Delete'"
  [visibleModal]="showDeleteModal"
  [bodyText]="'Are you sure you want to delete this question?'"
  (leftClickEvent)="closeDeleteModal()"
  (rightClickEvent)="confirmDeleteQuestionCreation()"
  (visibleModalChange)="closeDeleteModal()"
  [width]="'446px'"
  [height]="'223px'"
/>

<app-custom-mini-modal
  [headerTitle]="'Confirm Question Globalization'"
  [textAlign]="'center'"
  [left]="'No'"
  [right]="'Yes'"
  [visibleModal]="showGlobalizationModal"
  [bodyText]="'Are you sure you want to globalize this question?'"
  (leftClickEvent)="closeGlobalizationModal()"
  (rightClickEvent)="confirmGlobalization()"
  (visibleModalChange)="closeGlobalizationModal()"
  [width]="'446px'"
  [height]="'223px'"
/>

@if (showPreviewModal && cardIndexTracker !== null) {
  <app-custom-modal
    [headerTitle]="getType(questionType)"
    (visibleModalChange)="previewQuestion()"
  >
    <app-preview-data
      [question]="question"
      [questions]="allQuestions"
      [cardIndex]="cardIndexTracker"
      (questionChange)="onQuestionUpdated($event)"
    ></app-preview-data>
  </app-custom-modal>
}
