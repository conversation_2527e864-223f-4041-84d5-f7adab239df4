/* Filter section */
.filter-section {
  margin-bottom: 1rem;
  position: relative;
  z-index: 1000;
  overflow: visible;
}

/* Ensure filter button has highest priority */
.filter-section app-custom-filter-button {
  z-index: 1500 !important;
  position: relative;
}

/* Force filter dropdown to appear on top */
app-custom-filter-button {
  z-index: 1500 !important;
}

app-custom-filter-button > * {
  z-index: 1500 !important;
}

/* Dropdown hover and border styles */
.mobile-dropdown ::ng-deep .dropdown-panel li:hover,
.mobile-dropdown ::ng-deep .dropdown-content li:hover,
.mobile-dropdown ::ng-deep .dropdown-list li:hover,
.mobile-dropdown ::ng-deep .dropdown-menu li:hover {
  background: #f9fafb !important;
}

.mobile-dropdown ::ng-deep .dropdown-panel li:last-child,
.mobile-dropdown ::ng-deep .dropdown-content li:last-child,
.mobile-dropdown ::ng-deep .dropdown-list li:last-child,
.mobile-dropdown ::ng-deep .dropdown-menu li:last-child {
  border-bottom: none !important;
}

.desktop-dropdown {
  max-width: 200px;
  position: relative;
  z-index: 200;
}

/* Desktop filter layout */
.desktop-filter-container {
  padding: 0;
}

.desktop-filter-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: flex-start;
}

.desktop-filter-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  min-width: 160px;
  max-width: 200px;
  flex: 0 0 auto;
  z-index: 300;
}

/* Filter ordering */
.desktop-filter-item.domain-filter {
  order: 1;
}
.desktop-filter-item.category-filter {
  order: 2;
}
.desktop-filter-item:not(.domain-filter):not(.category-filter) {
  order: 3;
}

.desktop-filter-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #64748b;
  transition: opacity 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.desktop-filter-close {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1300;
  transition: all 0.2s ease;
  border: 1px solid #e2e8f0;
}

.desktop-filter-close:hover {
  background: #f8fafc;
  transform: scale(1.1);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.desktop-filter-close:focus,
.mobile-filter-close:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Layout sections */
.search-section {
  margin-bottom: 1.5rem;
}
.actions-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  position: relative;
  z-index: 1400;
}
.questions-list {
  padding: 0 0.5rem;
}
.mobile-card {
  margin-bottom: 1rem;
  border-radius: 12px;
  overflow: hidden;
}

/* Responsive breakpoints */
@media (min-width: 640px) {
  .actions-section {
    flex-direction: row;
    flex-wrap: wrap;
  }
  .mobile-filter-item {
    min-width: 140px;
  }
}

@media (min-width: 1024px) {
  .search-section {
    max-width: 400px;
  }
  .desktop-filter-item {
    min-width: 180px;
    max-width: 220px;
  }
  .filter-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1280px) {
  .search-section {
    max-width: 500px;
  }
  .desktop-filter-item {
    min-width: 200px;
    max-width: 250px;
  }
  .filter-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}

/* Desktop styles */
@media (min-width: 768px) {
  .search-section {
    margin-bottom: 1rem;
  }
  .questions-list {
    padding: 0 0.25rem;
  }
  .actions-section {
    flex-direction: row;
    gap: 0.75rem;
    align-items: center;
    flex-shrink: 0;
  }
  .actions-section > * {
    flex-shrink: 0;
    white-space: nowrap;
  }
  .mobile-filter-container {
    display: none;
  }
}

/* Mobile optimizations */
.mobile-filter-scroll {
  scroll-behavior: smooth;
}
.mobile-filter-item {
  transition: all 0.2s ease-in-out;
}
.mobile-filter-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.actions-section > * {
  min-height: 44px;
}

@media (max-width: 639px) {
  * {
    max-width: 100%;
    box-sizing: border-box;
  }
}

/* Z-index hierarchy */
::ng-deep {
  .modal,
  .dialog,
  .overlay,
  [role='dialog'],
  .cdk-overlay-container,
  .cdk-overlay-pane {
    z-index: 2000 !important;
  }
  .modal-backdrop,
  .cdk-overlay-backdrop {
    z-index: 1900 !important;
  }
  app-custom-filter-button {
    z-index: 1500 !important;
    position: relative;
  }
  app-custom-filter-button .sort-dropdown {
    z-index: 1600 !important;
    position: absolute;
  }
  app-custom-filter-button .dropdown-panel,
  app-custom-filter-button .dropdown-content,
  app-custom-filter-button .dropdown-menu {
    z-index: 1600 !important;
    position: absolute;
  }
  app-custom-dropdown,
  app-basic-button-dropdown {
    z-index: 400;
    position: relative;
  }
  app-custom-dropdown .dropdown-menu,
  app-basic-button-dropdown .dropdown-content {
    z-index: 500 !important;
    position: absolute;
  }
  .desktop-dropdown .dropdown-panel {
    z-index: 500 !important;
  }
  .desktop-dropdown .sub-dropdown-panel {
    z-index: 400 !important;
  }
  .modal-open app-custom-filter-button .sort-dropdown,
  .dialog-open app-custom-filter-button .sort-dropdown {
    z-index: 100 !important;
  }
}
