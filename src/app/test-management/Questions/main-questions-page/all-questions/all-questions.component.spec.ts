import {
  ComponentFixture,
  fakeAsync,
  TestBed,
  tick,
} from '@angular/core/testing';
import { AllQuestionsComponent } from './all-questions.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { Router } from '@angular/router';
import { ToastService } from '../../../../services/toast-service/toast.service';
import { QuestionsService } from '../../../../services/test-management-service/questions.service';
import { QuestionCreationServiceService } from '../../../../services/test-management-service/question-creation-service.service';
import { AuthService } from '../../../../services/auth.service';
import { DomainService } from '../../../../services/domain.service';
import { of } from 'rxjs';
import { mockQuestionsData } from '../../../../utils/assessment/assessment';
import { FormControl } from '@angular/forms';
import { WritableSignal } from '@angular/core';
import {
  FilterList,
  QuestionsData,
} from '../../../../Interfaces/questionInterface';
import { DomainData } from '../../../../Interfaces/domainInterface';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { QuestionStore } from '../../../../stores/questions-store/question.store';
import { CodingStore } from '../../../../stores/coding-store/coding.store';

describe('AllQuestionsComponent', () => {
  let component: AllQuestionsComponent;
  let fixture: ComponentFixture<AllQuestionsComponent>;
  let mockRouter: jest.Mocked<Router>;
  let mockToastService: jest.Mocked<ToastService>;
  let mockQuestionsService: jest.Mocked<QuestionsService>;
  let mockQuestionCreationService: jest.Mocked<QuestionCreationServiceService>;
  let mockAuthService: jest.Mocked<AuthService>;
  let mockDomainService: jest.Mocked<DomainService>;

  beforeEach(async () => {
    mockRouter = {
      navigate: jest.fn(),
    } as unknown as jest.Mocked<Router>;

    mockToastService = {
      onShow: jest.fn(),
    } as unknown as jest.Mocked<ToastService>;

    mockQuestionsService = {
      getAllQuestions: jest.fn(),
      isQuestionLoaded$: of(true),
      allQuestions$: of({ data: { data: [], totalItems: 0 } }),
    } as unknown as jest.Mocked<QuestionsService>;

    mockQuestionCreationService = {
      deleteQuestion: jest.fn(),
      globalizeQuestion: jest.fn().mockReturnValue({
        subscribe: jest.fn().mockImplementation((callbacks) => {
          callbacks.next();
        }),
      }),
    } as unknown as jest.Mocked<QuestionCreationServiceService>;

    mockAuthService = {
      getLoginUser: jest.fn().mockReturnValue({ permissions: ['admin'] }),
    } as unknown as jest.Mocked<AuthService>;

    mockDomainService = {
      getSortedDomain: jest.fn(),
      domainData: of({ data: [] }),
    } as unknown as jest.Mocked<DomainService>;

    await TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule,
        NoopAnimationsModule,
        AllQuestionsComponent,
      ],
      providers: [
        { provide: Router, useValue: mockRouter },
        { provide: ToastService, useValue: mockToastService },
        { provide: QuestionsService, useValue: mockQuestionsService },
        {
          provide: QuestionCreationServiceService,
          useValue: mockQuestionCreationService,
        },
        { provide: AuthService, useValue: mockAuthService },
        { provide: DomainService, useValue: mockDomainService },
        QuestionStore,
        CodingStore,
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(AllQuestionsComponent);
    component = fixture.componentInstance;
    jest.clearAllMocks();
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should navigate to correct URL when selecting question type', () => {
    component.selectedOption('Multiple Choice');
    expect(mockRouter.navigate).toHaveBeenCalledWith([
      '/dashboard/test-management/questions/create-question/multi-choice',
    ]);
  });

  it('should update page and load questions when changing page', () => {
    const spy = jest.spyOn(component, 'loadQuestions');
    component.totalNumberOfQuestions = 300;
    component.onPageChange(2);
    expect(component.page).toBe(2);
    expect(spy).toHaveBeenCalled();
  });

  it('should update page size and load questions when changing size', () => {
    const spy = jest.spyOn(component, 'loadQuestions');
    component.onSizeChange(50);
    expect(component.pageSize).toBe(50);
    expect(component.page).toBe(1);
    expect(spy).toHaveBeenCalled();
  });

  it('should delete question and show success toast', () => {
    mockQuestionCreationService.deleteQuestion.mockReturnValue(of({}));
    component.questionId = '123';
    component.confirmDeleteQuestionCreation();
    expect(mockQuestionCreationService.deleteQuestion).toHaveBeenCalledWith(
      '123'
    );
    expect(mockToastService.onShow).toHaveBeenCalledWith(
      'Success',
      'Question deleted successfully',
      true,
      'success'
    );
  });

  it('should handle search term change', fakeAsync(() => {
    const spy = jest.spyOn(component, 'loadQuestions');
    component.onSearchChange('test');
    tick(1200);
    expect(component.searchTerm).toBe('test');
    expect(component.page).toBe(1);
    expect(spy).toHaveBeenCalled();
  }));

  it('should toggle preview modal', () => {
    component.showPreviewModal = false;
    component.previewQuestion();
    expect(component.showPreviewModal).toBe(true);
    component.previewQuestion();
    expect(component.showPreviewModal).toBe(false);
  });

  it('should handle filter selection', () => {
    component.selectFilterItems('Question Types');
    expect(component.selectedFilters).toContain('Question Types');
    component.selectFilterItems('Question Types');
    expect(component.selectedFilters).not.toContain('Question Types');
  });

  it('should remove filter items', () => {
    component.selectedFilters = ['Domains', 'Question Types'];
    component.removeFilterItems('Domains');
    expect(component.selectedFilters).not.toContain('Domains');
    expect(component.selectedFilters).toContain('Question Types');
  });

  it('should set selectedCardIndex to null when document is clicked', () => {
    component.selectedCardIndex = 5;
    component.onDocumentClick();
    expect(component.selectedCardIndex).toBeNull();
  });

  it('should navigate to correct route based on the selected option', () => {
    component.selectedOption('Multi Select');
    expect(mockRouter.navigate).toHaveBeenCalled();
    component.selectedOption('True or False');
    expect(mockRouter.navigate).toHaveBeenCalled();
    component.selectedOption('Essay');
    expect(mockRouter.navigate).toHaveBeenCalled();
    component.selectedOption('Fill In The Blanks');
    expect(mockRouter.navigate).toHaveBeenCalled();
    component.selectedOption('Matrix');
    expect(mockRouter.navigate).toHaveBeenCalled();
  });

  it('should not navigate when default case is selected', () => {
    component.baseurl = '/base-url';

    component.selectedOption('Default Case');

    expect(mockRouter.navigate).not.toHaveBeenCalled();
  });
  it('should toggle selectedCardIndex when index matches or differs from current selectedCardIndex', () => {
    component.selectedCardIndex = 1;

    component.onActionClick(mockQuestionsData, 1);
    expect(component.selectedCardIndex).toBeNull();

    component.onActionClick(mockQuestionsData, 2);
    expect(component.selectedCardIndex).toBe(2);
  });

  it('should remove "Edit" and "Delete" buttons from actionButtonItems when event.isActive is true', () => {
    // Setup mock data for originalActionButtonItems
    component.originalActionButtonItems = [
      {
        name: 'Edit',
        icon: 'edit-icon',
        onClick: () => {},
      },
      {
        name: 'Add to test',
        icon: 'add-icon',
        onClick: () => {},
      },
      {
        name: 'Delete',
        icon: 'delete-icon',
        onClick: () => {},
      },
    ];

    // Setup the mock event data
    const mockEvent = { isActive: true } as unknown as QuestionsData;

    // Call the statusCheck method
    component.statusCheck(mockEvent);

    // Expect actionButtonItems to have 'Edit' and 'Delete' removed
    expect(component.actionButtonItems).toEqual([
      {
        name: 'Add to test',
        icon: 'add-icon',
        onClick: expect.any(Function),
      },
    ]);
  });

  it('should load all actions button when question in not active', () => {
    component.originalActionButtonItems = [
      'item1',
      'item2',
      'item3',
    ] as unknown as typeof component.originalActionButtonItems;

    component.statusCheck({ ...mockQuestionsData, isActive: false });

    expect(component.actionButtonItems).toEqual(['item1', 'item2', 'item3']);
  });
  it('should set showDeleteModal to false when called', () => {
    component.showDeleteModal = true;
    component.closeDeleteModal();
    expect(component.showDeleteModal).toBe(false);
  });

  it('should set showGlobalizationModal to false when called', () => {
    component.showGlobalizationModal = true;
    component.closeGlobalizationModal();
    expect(component.showGlobalizationModal).toBe(false);
  });

  it('should trigger bulkQuestionUpload when Enter key is pressed', () => {
    jest.spyOn(component, 'bulkQuestionUpload');
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    component.onKeydownBulkQuestionUpload(event);
    expect(component.bulkQuestionUpload).toHaveBeenCalled();
  });

  it('should close globalization modal and show success toast when service call is successful', () => {
    component.questionId = '123';
    component.confirmGlobalization();
    expect(component.showGlobalizationModal).toBe(false);
    expect(mockQuestionCreationService.globalizeQuestion).toHaveBeenCalledWith(
      '123'
    );
  });
  it('should update cardIndexTracker with the provided index', () => {
    const index = 2;
    component.onCardClick(mockQuestionsData, index);
    expect(component.cardIndexTracker).toBe(index);
  });

  it('should toggle isDropdownFilter from false to true', () => {
    component.isDropdownFilter = false;
    component.toggleDropdownFilterVisibility();
    expect(component.isDropdownFilter).toBe(true);
  });

  it('should set activeDropdownIndex to the given index when it is different from the current active index', () => {
    component.activeDropdownIndex = 1;
    component.toggleDropdownColumnVisibility(2);
    expect(component.activeDropdownIndex).toBe(2);
  });

  it('should select all filters when "All" is chosen and not all filters are selected', () => {
    component.selectedFilters = ['Filter1', 'Filter2'];
    jest
      .spyOn(component, 'getDisplayNames')
      .mockReturnValue(['All', 'Filter1', 'Filter2', 'Filter3']);
    jest.spyOn(component, 'applyAllFilters');

    component.selectFilterItems('All');

    expect(component.applyAllFilters).toHaveBeenCalled();
  });

  it('should process the filter correctly when item matches a filter value', () => {
    component.filterLists = [
      { key: 'category', value: ['item1'], display: 'Category' },
    ];
    jest.spyOn(component, 'handleCategorySelection');
    jest.spyOn(component, 'loadQuestions');

    component.selectFilteredOption('item1');

    expect(component.handleCategorySelection).toHaveBeenCalledWith('item1');
    expect(component.loadQuestions).toHaveBeenCalled();
    expect(component.selectedValues['Category']).toBe('item1');
  });

  it('should set domain control to null and handle domain selection when the filter key is "domainId"', () => {
    component.filterLists = [
      { key: 'domainId', value: ['item1'], display: 'Domain' },
    ];
    jest.spyOn(component, 'handleDomainSelection');
    jest.spyOn(component, 'loadQuestions');
    component.domainControl = { setValue: jest.fn() } as unknown as FormControl;

    component.selectFilteredOption('item1');

    expect(component.domainControl.setValue).toHaveBeenCalledWith(null);
    expect(component.handleDomainSelection).toHaveBeenCalledWith('item1');
    expect(component.loadQuestions).toHaveBeenCalled();
    expect(component.selectedValues['Domain']).toBe('item1');
  });

  it('should update selected filter options using transformValue for non-domain key', () => {
    component.filterLists = [
      { key: 'key1', value: ['item one'], display: 'Display1' },
    ];
    component.selectedValues = {};
    component.loadQuestions = jest.fn();
    component.updateSelectedFiltersOptions = jest.fn();
    const item = 'item one';

    component.selectFilteredOption(item);

    expect(component.updateSelectedFiltersOptions).toHaveBeenCalledWith(
      'key1',
      'item_one'
    );
    expect(component.selectedValues['Display1']).toBe('item one');
    expect(component.loadQuestions).toHaveBeenCalled();
  });

  it('should return an empty array when no matching item is found', () => {
    component.filterLists = [
      { key: 'option one', display: 'Option1', value: ['Value1'] },
      { key: 'option two', display: 'Option2', value: ['Value2'] },
    ];
    const result = component.getOptions('NonExistentOption');
    expect(result).toEqual([]);
  });
  it('should remove domainId and categoryId from selectedFiltersOptions when resetDomainFilters is called', () => {
    component.isDomain = {
      set: jest.fn(),
    } as unknown as WritableSignal<boolean>;
    component.selectedDomainId = {
      set: jest.fn(),
    } as unknown as WritableSignal<string>;
    component.selectedFiltersOptions = [
      { key: 'domainId', value: 'domain1' },
      { key: 'categoryId', value: 'category1' },
    ];
    component.resetDomainFilters();
    expect(component.isDomain.set).toHaveBeenCalledWith(false);
    expect(component.selectedFiltersOptions).toEqual([]);
  });

  it('should remove the filter option when it exists in filterLists', () => {
    component.filterLists = [
      { display: 'option1', key: 'key1' },
    ] as FilterList[];
    component.selectedFiltersOptions = [
      { key: 'key1', value: 'key one' },
      { key: 'key2', value: 'key two' },
    ];

    component.removeFilterOption('option1');

    expect(component.selectedFiltersOptions).toEqual([
      { key: 'key2', value: 'key two' },
    ]);
  });
  it('should filter out "Categories" when a domain is selected', () => {
    component.selectedFilters = ['Categories', 'difficulty'];
    component.selectedFiltersOptions = [
      { key: 'categoryId', value: 'category1' },
      { key: 'difficultyLevel', value: 'advance' },
    ];

    jest.spyOn(component, 'getSelectedDomainId').mockReturnValue('domain123');
    jest.spyOn(component.selectedDomainId, 'set');
    jest.spyOn(component, 'updateSelectedFiltersOptions');

    // Set the current domain ID to be different to trigger the filter clearing
    component.selectedDomainId.set('different-domain');

    component.handleDomainSelection('selectedDomain');

    expect(component.selectedFilters).not.toContain('Categories');
    expect(component.selectedFilters).toContain('difficulty');

    expect(component.selectedFiltersOptions).not.toContainEqual({
      key: 'categoryId',
    });
    expect(component.selectedFiltersOptions).toEqual([
      { key: 'difficultyLevel', value: 'advance' },
      { key: 'domainId', value: 'domain123' },
    ]);

    expect(component.updateSelectedFiltersOptions).toHaveBeenCalledWith(
      'domainId',
      'domain123'
    );
    expect(component.selectedDomainId.set).toHaveBeenCalledWith('domain123');
  });

  it('should update selectedFiltersOptions with correct categoryId when category is found', () => {
    component.allDomains = [
      { id: '1', categories: [{ name: 'Math', id: '101' }] },
      { id: '2', categories: [{ name: 'Science', id: '102' }] },
    ] as DomainData[];
    component.selectedDomainId = jest
      .fn()
      .mockReturnValue('1') as unknown as WritableSignal<string>;
    component.selectedFiltersOptions = [{ key: 'domainId', value: '1' }];

    component.updateSelectedFiltersOptions = jest.fn();

    component.handleCategorySelection('Math');

    expect(component.updateSelectedFiltersOptions).toHaveBeenCalledWith(
      'categoryId',
      '101'
    );
  });

  it('should update the value of an existing filter option when the key exists', () => {
    component.selectedFiltersOptions = [{ key: 'difficulty', value: 'easy' }];
    component.updateSelectedFiltersOptions('difficulty', 'hard');
    expect(component.selectedFiltersOptions).toEqual([
      { key: 'difficulty', value: 'hard' },
    ]);
  });

  it('should return categories when selectedDomainId matches a domain', () => {
    // Set up currentCategoryOptions which is what getCategories() returns
    component.currentCategoryOptions = ['Math', 'Science'];

    const result = component.getCategories();

    expect(result).toEqual(['Math', 'Science']);
  });

  it('should update questionText when called with a non-empty string', () => {
    const newQuestionText = 'What is the capital of France?';
    component.onQuestionUpdated(newQuestionText);
    expect(component.questionType).toBe(newQuestionText);
  });

  it('should return the correct value when the key exists in selectedValues', () => {
    component.selectedValues = { key1: 'value1', key2: 'value2' };
    const result = component.getSelectedValue('key1');
    expect(result).toBe('value1');
  });
});
