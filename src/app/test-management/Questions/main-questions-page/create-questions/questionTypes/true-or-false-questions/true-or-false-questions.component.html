<div class="flex align-center cursor-pointer mb-3">
  <app-question-title
    [newDesign]="true"
    [questionType]="previousPageText"
    (backRoute)="previousPage()"
  ></app-question-title>
</div>
<div class="mb-4">
  <h3 class="text-xl text-[#0C4767] font-medium">{{ title }}</h3>
</div>
<main class="bg-[#fafafa] rounded-lg border-[#0822301A] p-6">
  <form [formGroup]="form" (ngSubmit)="onSubmit($event)">
    <section class="h-[calc(100vh-320px)] overflow-y-auto">
      <app-basic-question-container [form]="form">
        <section class="w-full">
          <app-answer-options
            [optionsArray]="optionsArray"
            [form]="form"
            [isEdit]="isEdit"
            [selectedAnswerIndex]="selectedAnswerIndex"
            [questionType]="'true-or-false'"
            (radioChange)="onRadioChange($event)"
            (deleteOption)="deleteOption($event)"
          ></app-answer-options>
        </section>
      </app-basic-question-container>
    </section>
    <div class="flex gap-x-4 justify-end mb-3">
      @if (form) {
        <app-custombutton
          variant="secondary"
          (click)="onCancel()"
          (keydown)="handleKeyCancel($event)"
          >Cancel</app-custombutton
        >
        <app-custombutton
          [disabled]="buttonState || disableButton()"
          [type]="'submit'"
          [spinner]="isLoading"
          [disabled]="isLoading"
          variant="testSave"
          >{{
            this.isEdit ? 'Save Changes' : 'Save Question'
          }}</app-custombutton
        >
      }
    </div>
  </form>
</main>
