import {
  After<PERSON>iewInit,
  Component,
  EventEmitter,
  HostListener,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { BasicQuestionContainerComponent } from '../../../components/basic-question-container/basic-question-container.component';
import { CustomButtonComponent } from '../../../../../../components/custombutton/custombutton.component';
import { QuestionTitleComponent } from '../../../components/question-title/question-title.component';
import { Router } from '@angular/router';
import {
  FormArray,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  BasicQuestion,
  MessageStatus,
  QuestionMessage,
  QuestionsData,
} from '../../../../../../Interfaces/questionInterface';
import { ToastService } from '../../../../../../services/toast-service/toast.service';
import { QuestionsService } from '../../../../../../services/test-management-service/questions.service';
import { hasDuplicateAnswers } from '../../../../../../utils/questionsConstants';
import { QuestionStore } from '../../../../../../stores/questions-store/question.store';
import { Subscription } from 'rxjs';
import { AnswerOptionsComponent } from '../../../../../../components/answer-options/answer-options.component';
import { FormFactoryService } from '../../../../../../services/form-factory-service/form-factory.service';

@Component({
  selector: 'app-true-or-false-questions',
  standalone: true,
  imports: [
    BasicQuestionContainerComponent,
    CustomButtonComponent,
    QuestionTitleComponent,
    ReactiveFormsModule,
    AnswerOptionsComponent,
  ],
  templateUrl: './true-or-false-questions.component.html',
})
export class TrueOrFalseQuestionsComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  @Input() title = 'Create New Question(True or False)';
  @Input() previousPageText = 'Back';
  @Input() editTrueOrFalseData: QuestionsData | undefined;
  @Output() submitEditedQuestionEvent = new EventEmitter();
  form: FormGroup;
  isEdit: boolean = false;
  selectedAnswerIndex: number | undefined;
  buttonState: boolean = true;
  @Input() isLoading = false;
  subscriptions: Subscription[] = [];

  constructor(
    private readonly router: Router,
    private readonly fb: FormBuilder,
    private readonly toast: ToastService,
    private readonly questionService: QuestionsService,
    private readonly questionsStore: QuestionStore,
    private readonly formFactory: FormFactoryService
  ) {
    this.form = this.formFactory.createQuestionForm();
  }

  @HostListener('window:beforeunload', ['$event'])
  saveFormData() {
    localStorage.setItem(
      'trueOrFalseFormData',
      JSON.stringify(this.form.value)
    );
    localStorage.setItem(
      'selectedAnswerIndex',
      JSON.stringify(this.selectedAnswerIndex)
    );
    localStorage.setItem('isEdit', JSON.stringify(this.isEdit));
    localStorage.setItem('buttonState', JSON.stringify(this.buttonState));
  }

  ngAfterViewInit(): void {
    this.form.valueChanges.subscribe(() => {
      this.buttonState = false;
    });
  }

  ngOnInit(): void {
    const existingQuestionData = {
      ...this.editTrueOrFalseData,
    };

    const savedFormData = localStorage.getItem('trueOrFalseFormData');
    const savedSelectedAnswerIndex = localStorage.getItem(
      'selectedAnswerIndex'
    );
    const savedIsEdit = localStorage.getItem('isEdit');
    const savedButtonState = localStorage.getItem('buttonState');

    this.isEdit = this.router.url.includes('edit');

    const title = this.isEdit ? MessageStatus.Edited : MessageStatus.Created;
    const message = this.isEdit
      ? QuestionMessage.EditSuccess
      : QuestionMessage.CreateSuccess;

    this.subscriptions.push(
      this.questionsStore.isQuestionsSuccess$.pipe().subscribe((isSuccess) => {
        if (isSuccess) {
          this.toast.onShow(title, message, true, 'success');
          this.isLoading = false;
          this.selectedAnswerIndex = undefined;
          this.radioButtonCheck(this.selectedAnswerIndex as unknown as number);

          this.form.reset();
          this.optionsArray.clear();
          this.addOption('True');
          this.addOption('False');

          this.questionsStore.resetSuccessState();
          if (this.isEdit) {
            this.router.navigateByUrl(
              `/dashboard/test-management/questions/all-questions`
            );
          }
        }
      })
    );

    this.subscriptions.push(
      this.questionsStore.questionsError$.subscribe((error) => {
        if (error) {
          this.isLoading = false;
        }
      })
    );

    if (
      savedFormData &&
      savedSelectedAnswerIndex &&
      savedIsEdit &&
      savedButtonState
    ) {
      localStorage.removeItem('trueOrFalseFormData');
      localStorage.removeItem('selectedAnswerIndex');
      localStorage.removeItem('isEdit');
      localStorage.removeItem('buttonState');

      this.isEdit = JSON.parse(savedIsEdit);
      this.buttonState = JSON.parse(savedButtonState);
      let parsedFormData = JSON.parse(savedFormData);

      if (this.isEdit) {
        parsedFormData = { ...existingQuestionData, ...parsedFormData };
      }
      this.populateForm(parsedFormData);
      this.selectedAnswerIndex =
        savedSelectedAnswerIndex === 'undefined'
          ? this.selectedAnswerIndex
          : JSON.parse(savedSelectedAnswerIndex);
    } else if (existingQuestionData && this.isEdit) {
      this.populateForm(existingQuestionData as unknown as QuestionsData);
    } else {
      this.addOption('True');
      this.addOption('False');
    }
  }

  populateForm(data: QuestionsData) {
    this.form.patchValue({
      domain: data.domain,
      category: data.category,
      score: data.score,
      difficultyLevel: data.difficultyLevel,
      questionText: data.questionText,
    });

    if (this.isEdit) {
      const answerOptions = this.form.get('answerOptions') as FormArray;
      data.trueOrFalseAnswer?.options!.forEach((option: string) => {
        answerOptions.push(this.fb.control(option, Validators.required));
      });
      const correctAnswer = data.trueOrFalseAnswer?.answer as Array<string>;
      if (correctAnswer?.[0]) {
        this.selectedAnswerIndex = this.optionsArray.value.indexOf(
          correctAnswer[0]
        );
      }
    } else {
      this.addOption('True');
      this.addOption('False');
    }
  }

  addOption(option: string) {
    this.optionsArray.push(this.fb.control(option, Validators.required));
  }

  get optionsArray(): FormArray {
    return this.form.get('answerOptions') as FormArray;
  }

  previousPage() {
    this.router.navigateByUrl(
      `/dashboard/test-management/questions/all-questions`
    );
  }

  onRadioChange(index: number) {
    this.form.get('answerOptions')?.markAsTouched();
    this.form.get('answerOptions')?.updateValueAndValidity();
    this.optionsArray.controls.forEach((control, i) => {
      if (i !== index) {
        this.radioButtonCheck(i);
      }
    });

    this.selectedAnswerIndex = index;
    this.buttonState = false;
  }

  radioButtonCheck(i: number) {
    const radioInput = document.getElementById(
      `radio-${i}`
    ) as HTMLInputElement;
    if (radioInput) {
      radioInput.checked = false;
    }
  }

  onSubmit(event: Event) {
    this.isLoading = true;
    event.preventDefault();
    const selectedAnswer = this.optionsArray.at(
      Number(this.selectedAnswerIndex)
    )?.value;

    if (hasDuplicateAnswers(this.optionsArray.value)) {
      this.toast.onShow('Answers', `Duplicate answer options`, true, 'error');
      this.isLoading = false;
      return;
    }

    const trueOrFalseFormData = {
      answerOptions: this.optionsArray.value,
      domainId: this.form.get('domain')?.value?.id,
      categoryId: this.form.get('category')?.value?.id,
      score: this.form.get('score')?.value,
      difficultyLevel: this.form.get('difficultyLevel')?.value,
      correctAnswers: [selectedAnswer],
      questionText: this.form.get('questionText')?.value,
      questionType: 'True_or_false',
    };

    if (this.isEdit) {
      this.editFormSubmission(trueOrFalseFormData);
      return;
    }

    this.submitQuestionForm(trueOrFalseFormData);
  }

  editFormSubmission(form: BasicQuestion) {
    this.submitEditedQuestionEvent.emit(form);
  }

  submitQuestionForm(form: BasicQuestion) {
    this.questionsStore.createQuestion({ question: form });
  }

  onCancel() {
    this.questionService.cancelQuestion.next(true);
  }

  deleteOption(index: number) {
    this.optionsArray.removeAt(index);
  }

  handleKeyCancel(event: KeyboardEvent) {
    if (event.code === 'Enter') {
      event.preventDefault();
      this.onCancel();
    }
  }

  disableButton() {
    const formInvalid = this.form.invalid;
    const noOptions = this.optionsArray.length === 0;
    const noAnswerSelected =
      this.selectedAnswerIndex === undefined ||
      this.selectedAnswerIndex === null;

    return formInvalid || noOptions || noAnswerSelected;
  }

  loadQuestions() {
    this.questionService.getAllQuestions(1, 10, {});
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => subscription.unsubscribe());
    // Clear reuse settings when navigating away from the page
  }
}
