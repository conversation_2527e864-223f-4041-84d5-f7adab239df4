import {
  Component,
  Input,
  OnInit,
  HostListener,
  EventEmitter,
  Output,
  OnDestroy,
  ChangeDetectionStrategy,
} from '@angular/core';
import { CustomButtonComponent } from '../../../../../../../components/custombutton/custombutton.component';
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { CommonModule } from '@angular/common';

import { noWhitespaceTextareaValidator } from '../../../../../../../utils/constants';
import {
  BasicQuestion,
  MessageStatus,
  QuestionMessage,
  RowAndOption,
} from '../../../../../../../Interfaces/questionInterface';
import { ToastService } from '../../../../../../../services/toast-service/toast.service';
import { Router } from '@angular/router';
import { MatrixTableComponent } from '../matrix-table/matrix-table.component';
import { MatrixOptionsComponent } from '../../../../../../../components/questions_options/matrix-options/matrix-options.component';
import { debounceTime, distinctUntilChanged, Subscription } from 'rxjs';
import { QuestionStore } from '../../../../../../../stores/questions-store/question.store';

@Component({
  selector: 'app-add-column-and-row-information',
  standalone: true,
  imports: [
    CustomButtonComponent,
    FormsModule,
    CommonModule,
    ReactiveFormsModule,
    MatrixTableComponent,
    MatrixOptionsComponent,
  ],
  templateUrl: './add-column-and-row-information.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AddColumnAndRowInformationComponent implements OnInit, OnDestroy {
  matrixForm: FormGroup;
  @Input() form!: FormGroup;
  @Input() editData: RowAndOption | undefined;
  @Output() submitEdittedRowandRowEvent = new EventEmitter();
  @Output() nextPage = new EventEmitter<number>();
  @Output() matrixDataChange = new EventEmitter<RowAndOption>();
  displayQuestionControl!: FormControl;
  isEdit = false;
  private readonly LOCAL_STORAGE_KEY = 'matrixQuestionData';
  title = 'Add options and subquestions';
  subscriptions: Subscription[] = [];
  previousOptions: string[] = [];

  constructor(
    private readonly fb: FormBuilder,
    private readonly toast: ToastService,
    private readonly router: Router,
    private readonly questionsStore: QuestionStore
  ) {
    this.matrixForm = this.fb.group({
      answerOptions: this.fb.array([]),
      subquestions: this.fb.array([]),
    });
  }

  ngOnInit(): void {
    if (this.router.url.includes('edit')) {
      this.isEdit = true;
    }
    const questionText = this.form.get('questionText')?.value;
    this.displayQuestionControl = this.fb.control({
      value: questionText,
      disabled: true,
    });

    this.restoreFormState();
    this.initializeSubscriptions();
  }

  private initializeSubscriptions(): void {
    this.isEdit = this.router.url.includes('edit');
    this.restoreFormState();
    this.subscriptions.push(
      this.questionsStore.isQuestionsSuccess$
        .pipe(distinctUntilChanged())
        .subscribe((isSuccess) => {
          if (isSuccess) {
            const title = this.isEdit
              ? MessageStatus.Edited
              : MessageStatus.Created;
            const message = this.isEdit
              ? QuestionMessage.EditSuccess
              : QuestionMessage.CreateSuccess;

            this.toast.onShow(title, message, true, 'success');

            // Reset or navigate
            this.form.reset();
            this.matrixForm.reset();
            this.clearFormArrays();
            localStorage.removeItem(this.LOCAL_STORAGE_KEY);
            this.questionsStore.resetSuccessState();
          }
        })
    );

    this.subscriptions.push(
      this.matrixForm.valueChanges.pipe(debounceTime(300)).subscribe(() => {
        this.saveFormState();
        this.emitMatrixDataToParent();
      })
    );
  }

  previousPage() {
    this.router.navigateByUrl(
      '/dashboard/test-management/questions/all-questions'
    );
  }
  private emitMatrixDataToParent(): void {
    if (this.matrixForm.valid && this.hasValidMatrixData()) {
      const matrixData: RowAndOption = {
        options: this.optionsArray.value,
        questions: this.subquestionsArray.value.map(
          (row: { subquestion: string; answer: string }) => ({
            subquestion: row.subquestion,
            answer: [row.answer],
          })
        ),
      };
      this.matrixDataChange.emit(matrixData);
    }
  }

  private hasValidMatrixData(): boolean {
    const hasMinimumOptions = this.optionsArray.length >= 2;
    const hasMinimumQuestions = this.subquestionsArray.length >= 2;
    const allOptionsValid = this.optionsArray.controls.every(
      (control) => control.value && control.value.trim() !== ''
    );
    const allQuestionsValid = this.subquestionsArray.controls.every(
      (control) =>
        control.get('subquestion')?.value &&
        control.get('subquestion')?.value.trim() !== '' &&
        control.get('answer')?.value &&
        control.get('answer')?.value.trim() !== ''
    );

    return (
      hasMinimumOptions &&
      hasMinimumQuestions &&
      allOptionsValid &&
      allQuestionsValid
    );
  }

  @HostListener('window:beforeunload', ['$event'])
  saveFormState() {
    const formData = {
      answerOptions: this.optionsArray.value,
      subquestions: this.subquestionsArray.value.map(
        (row: { subquestion: string; answer: string }) => ({
          subquestion: row.subquestion,
          answer: row.answer,
        })
      ),
      isEdit: this.isEdit,
      editData: this.editData,
    };
    localStorage.setItem(this.LOCAL_STORAGE_KEY, JSON.stringify(formData));
  }

  restoreFormState() {
    const savedData = localStorage.getItem(this.LOCAL_STORAGE_KEY);

    if (savedData) {
      const parsedData = JSON.parse(savedData);

      this.clearFormArrays();

      if (parsedData.answerOptions?.length) {
        parsedData.answerOptions.forEach((option: string) => {
          this.optionsArray.push(
            this.fb.control(option, [
              Validators.required,
              noWhitespaceTextareaValidator,
            ])
          );
        });
      }

      if (parsedData.subquestions?.length) {
        parsedData.subquestions.forEach(
          (row: { subquestion: string; answer: string }) => {
            this.subquestionsArray.push(
              this.fb.group({
                subquestion: [
                  row.subquestion,
                  [Validators.required, noWhitespaceTextareaValidator],
                ],
                answer: [
                  row.answer,
                  [Validators.required, noWhitespaceTextareaValidator],
                ],
              })
            );
          }
        );
      }
    } else if (
      this.editData &&
      (this.router.url.includes('edit') || this.isEdit)
    ) {
      this.isEdit = true;

      this.clearFormArrays();

      if (this.editData.options?.length) {
        this.editData.options.forEach((option: string) => {
          this.optionsArray.push(
            this.fb.control(option, [
              Validators.required,
              noWhitespaceTextareaValidator,
            ])
          );
        });
      }

      if (this.editData.questions?.length) {
        this.editData.questions.forEach(
          (q: { subquestion: string; answer: string[] }) => {
            this.subquestionsArray.push(
              this.fb.group({
                subquestion: [
                  q.subquestion,
                  [Validators.required, noWhitespaceTextareaValidator],
                ],
                answer: [
                  q.answer[0],
                  [Validators.required, noWhitespaceTextareaValidator],
                ],
              })
            );
          }
        );
      }
    }

    setTimeout(() => {
      this.emitMatrixDataToParent();
    }, 0);
  }

  private clearFormArrays() {
    while (this.optionsArray.length !== 0) {
      this.optionsArray.removeAt(0);
    }
    while (this.subquestionsArray.length !== 0) {
      this.subquestionsArray.removeAt(0);
    }
  }

  get optionsArray(): FormArray {
    return this.matrixForm.get('answerOptions') as FormArray;
  }

  get subquestionsArray(): FormArray {
    return this.matrixForm.get('subquestions') as FormArray;
  }

  addOption() {
    this.optionsArray.push(
      this.fb.control('', [Validators.required, noWhitespaceTextareaValidator])
    );
    this.emitMatrixDataToParent();
  }

  removeOption(index: number) {
    this.optionsArray.removeAt(index);
    this.emitMatrixDataToParent();
  }

  addRow() {
    this.subquestionsArray.push(
      this.fb.group({
        subquestion: ['', [Validators.required, noWhitespaceTextareaValidator]],
        answer: ['', [Validators.required, noWhitespaceTextareaValidator]],
      })
    );
    this.emitMatrixDataToParent();
  }

  removeRow(id: number) {
    this.subquestionsArray.removeAt(id);
    this.emitMatrixDataToParent();
  }

  onRadioChange(event: { rowIndex: number; option: string }) {
    const row = this.subquestionsArray.at(event.rowIndex);
    row.patchValue({ answer: event.option });
    this.saveFormState();
    this.emitMatrixDataToParent();
  }

  submitQuestion(matrixData: BasicQuestion) {
    this.questionsStore.createQuestion({ question: matrixData });
  }

  back() {
    history.back();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => subscription.unsubscribe());
  }
}
