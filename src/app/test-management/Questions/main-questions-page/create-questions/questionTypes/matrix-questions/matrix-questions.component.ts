import {
  Component,
  OnInit,
  HostListener,
  OnDestroy,
  Input,
  EventEmitter,
  Output,
} from '@angular/core';
import { BasicQuestionContainerComponent } from '../../../components/basic-question-container/basic-question-container.component';
import { QuestionTitleComponent } from '../../../components/question-title/question-title.component';
import { ActivatedRoute, Router } from '@angular/router';
import { CustomButtonComponent } from '../../../../../../components/custombutton/custombutton.component';
import { AddColumnAndRowInformationComponent } from './add-column-and-row-information/add-column-and-row-information.component';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { noWhitespaceTextareaValidator } from '../../../../../../utils/constants';

import {
  BasicQuestion,
  MessageStatus,
  QuestionMessage,
  QuestionsData,
  RowAndOption,
} from '../../../../../../Interfaces/questionInterface';
import { ToastService } from '../../../../../../services/toast-service/toast.service';
import { QuestionStore } from '../../../../../../stores/questions-store/question.store';
import { Subscription } from 'rxjs';
import { ExpandableInstructionCardComponent } from '../../../../../../components/expandable-instruction-card/expandable-instruction-card.component';

@Component({
  selector: 'app-matrix-questions',
  standalone: true,
  imports: [
    BasicQuestionContainerComponent,
    QuestionTitleComponent,
    CustomButtonComponent,
    AddColumnAndRowInformationComponent,
    ReactiveFormsModule,
    ExpandableInstructionCardComponent,
  ],
  templateUrl: './matrix-questions.component.html',
})
export class MatrixQuestionsComponent implements OnInit, OnDestroy {
  @Input() title = 'Create New Question(Matrix)';
  @Input() previousPageText = 'Back';
  @Input() editMatrixData: QuestionsData | undefined;
  @Output() submitEdittedQuestionEvent = new EventEmitter();
  subscriptions: Subscription[] = [];
  rowAndColumnData!: RowAndOption;
  page = 1;
  form: FormGroup;
  isEdit: boolean = false;
  isReady = false;
  showColumnRowInfo = false;
  isButtonDisabled: boolean = true;

  private readonly LOCAL_STORAGE_KEY = 'matrixQuestionFormData';
  matrixInstructions = [
    'Type your question text',
    'Select the appropriate constraints for your question',
    'Click on add row to have content appear horizontally',
    'Click on add column to have your content presented vertically',
    'Check the preview section to see how your content is being presented',
  ];

  constructor(
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly fb: FormBuilder,
    private readonly toast: ToastService,
    private readonly questionsStore: QuestionStore
  ) {
    this.form = this.fb.group({
      questionText: ['', [Validators.required, noWhitespaceTextareaValidator]],
      domain: ['', Validators.required],
      score: ['', [Validators.required, this.nonNegativeNumberValidator()]],
      difficultyLevel: ['', Validators.required],
      answerOptions: [null],
      category: ['', Validators.required],
    });
  }

  onMatrixDataChange(matrixData: RowAndOption) {
    const formattedData = {
      0: matrixData.options,
      1: matrixData.questions.map((q) => ({
        subquestion: q.subquestion,
        answer: Array.isArray(q.answer) ? q.answer[0] : q.answer,
      })),
    };

    this.form.patchValue({ answerOptions: formattedData });
    this.rowAndColumnData = matrixData;
  }

  submitEdit(event: RowAndOption) {
    const data = {
      questionText: this.form.value.questionText,
      questionType: this.editMatrixData!.questionType,
      difficultyLevel: this.form.value.difficultyLevel,
      score: this.form.value.score,
      answerOptions: event.options,
      subquestions: event.questions,
      categoryId: this.form.value.category.id,
      domainId: this.form.value.domain.id,
    };

    this.submitEdittedQuestionEvent.emit(data);
  }

  ngOnInit() {
    this.restorePageState();
    this.isEdit = this.router.url.includes('edit');
    this.restoreFormState();
    const title = this.isEdit ? MessageStatus.Edited : MessageStatus.Created;
    const message = this.isEdit
      ? QuestionMessage.EditSuccess
      : QuestionMessage.CreateSuccess;

    this.subscriptions.push(
      this.questionsStore.isQuestionsSuccess$.pipe().subscribe((isSuccess) => {
        if (isSuccess) {
          this.toast.onShow(title, message, true, 'success');

          this.questionsStore.resetSuccessState();
          if (this.isEdit) {
            this.previousPage();
          }
        }
      })
    );

    this.subscriptions.push(
      this.form.valueChanges.subscribe(() => {
        this.saveState();
      })
    );

    this.route.queryParams.subscribe((params) => {
      const pageParam = params['page'];
      if (pageParam) {
        this.page = parseInt(pageParam, 10);
      }
    });
  }

  @HostListener('window:beforeunload', ['$event'])
  saveState() {
    const formData = {
      questionText: this.form.get('questionText')?.value,
      domain: this.form.get('domain')?.value,
      score: this.form.get('score')?.value,
      difficultyLevel: this.form.get('difficultyLevel')?.value,
      category: this.form.get('category')?.value,
      answerOptions: this.form.get('answerOptions')?.value,
      page: this.page,
      isEdit: this.isEdit,
      editData: this.editMatrixData,
    };

    localStorage.setItem(this.LOCAL_STORAGE_KEY, JSON.stringify(formData));
  }

  toggleColumnRowInfo() {
    this.showColumnRowInfo = false;
    this.saveState();
  }

  private restoreFormState() {
    const savedData = localStorage.getItem(this.LOCAL_STORAGE_KEY);
    if (savedData) {
      const parsedData = JSON.parse(savedData);
      this.isEdit = parsedData.isEdit;

      if (this.isEdit) {
        this.editMatrixData = parsedData.editData;
        if (this.editMatrixData?.matchMatrixAnswer) {
          this.rowAndColumnData = {
            options: this.editMatrixData.matchMatrixAnswer.options,
            questions: this.editMatrixData.matchMatrixAnswer.questions,
          };
          this.showColumnRowInfo = true;
        }
      }

      Object.keys(parsedData).forEach((key) => {
        if (key !== 'page' && this.form.get(key)) {
          this.form.get(key)?.setValue(parsedData[key]);
        }
      });
    } else if (this.editMatrixData && this.isEdit) {
      const { matchMatrixAnswer } = this.editMatrixData;
      this.rowAndColumnData = matchMatrixAnswer as unknown as RowAndOption;
      this.form.patchValue({
        questionText: this.editMatrixData.questionText,
        domain: this.editMatrixData.domain,
        score: this.editMatrixData.score,
        difficultyLevel: this.editMatrixData.difficultyLevel,
        category: this.editMatrixData.category,
      });
    }
  }

  private restorePageState() {
    const savedData = localStorage.getItem(this.LOCAL_STORAGE_KEY);
    if (savedData) {
      const parsedData = JSON.parse(savedData);

      if (parsedData.page) {
        this.page = parsedData.page;

        this.router.navigate([], {
          queryParams: { page: this.page },
          queryParamsHandling: 'merge',
          replaceUrl: true,
        });
      }
    }
  }

  previousPage() {
    this.router.navigate([
      '/dashboard/test-management/questions/all-questions',
    ]);

    localStorage.removeItem(this.LOCAL_STORAGE_KEY);
  }

  addColumnAndRowInformation() {
    this.showColumnRowInfo = true;
  }

  ngOnDestroy(): void {
    localStorage.removeItem(this.LOCAL_STORAGE_KEY);
    localStorage.removeItem('matrixQuestionData');
  }

  nonNegativeNumberValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value;
      if (value === null || value === '') {
        return null;
      }
      return value < 0 ? { negativeNumber: true } : null;
    };
  }

  public hasEditMatrixDataChanged(currentAnswerOptions: {
    [key: number]:
      | Array<string>
      | Array<{ subquestion: string; answer: string }>;
  }): boolean {
    if (!this.editMatrixData?.matchMatrixAnswer) {
      return false;
    }

    const matchMatrixAnswer = this.editMatrixData
      .matchMatrixAnswer as unknown as RowAndOption;
    const originalAnswerOptions = {
      0: matchMatrixAnswer.options,
      1: matchMatrixAnswer.questions.map((q) => ({
        subquestion: q.subquestion,
        answer: q.answer[0],
      })),
    };

    return (
      JSON.stringify(currentAnswerOptions) !==
      JSON.stringify(originalAnswerOptions)
    );
  }

  disableSave(): boolean {
    if (this.form.invalid) {
      return true;
    }

    const answerOptions = this.form.get('answerOptions')?.value;

    if (!answerOptions || typeof answerOptions !== 'object') {
      return true;
    }

    const options = answerOptions[0];
    const questions = answerOptions[1];

    if (!Array.isArray(options) || !Array.isArray(questions)) {
      return true;
    }

    const hasMinimumOptions = options.length >= 2;
    const hasMinimumQuestions = questions.length >= 2;
    const allOptionsValid = options.every(
      (opt: string) => opt && opt.trim() !== ''
    );
    const allQuestionsValid = questions.every(
      (q: { subquestion: string; answer: string }) =>
        q.subquestion &&
        q.subquestion.trim() !== '' &&
        q.answer &&
        q.answer.trim() !== ''
    );

    if (this.isEdit && this.editMatrixData) {
      const hasBasicFormChanged =
        this.form.get('questionText')?.value !==
          this.editMatrixData.questionText ||
        this.form.get('domain')?.value?.id !== this.editMatrixData.domain.id ||
        this.form.get('category')?.value?.id !==
          this.editMatrixData.category.id ||
        this.form.get('difficultyLevel')?.value !==
          this.editMatrixData.difficultyLevel ||
        this.form.get('score')?.value !== this.editMatrixData.score;

      const hasMatrixDataChanged = this.hasEditMatrixDataChanged(answerOptions);

      return !(
        hasMinimumOptions &&
        hasMinimumQuestions &&
        allOptionsValid &&
        allQuestionsValid &&
        (hasBasicFormChanged || hasMatrixDataChanged)
      );
    }

    return !(
      hasMinimumOptions &&
      hasMinimumQuestions &&
      allOptionsValid &&
      allQuestionsValid
    );
  }

  submitQuestion(matrixData: BasicQuestion) {
    this.questionsStore.createQuestion({ question: matrixData });
  }

  saveQuestion() {
    if (this.disableSave()) {
      return;
    }

    const answerOptions = this.form.get('answerOptions')?.value;

    if (this.isEdit && this.editMatrixData) {
      let options: string[];
      let questions: Array<{ subquestion: string; answer: string[] }>;

      if (answerOptions && Object.keys(answerOptions).length > 0) {
        options = answerOptions[0] as string[];
        questions = (
          answerOptions[1] as Array<{
            subquestion: string;
            answer: string;
          }>
        ).map((row) => ({
          subquestion: row.subquestion,
          answer: [row.answer],
        }));
      } else {
        const { matchMatrixAnswer } = this.editMatrixData;
        options = matchMatrixAnswer?.options || [];
        questions =
          matchMatrixAnswer?.questions?.map((row) => ({
            subquestion: row.subquestion,
            answer: row.answer,
          })) || [];
      }

      this.submitEdit({ options, questions });
    } else {
      const payload = {
        difficultyLevel: this.form.get('difficultyLevel')?.value,
        questionType: 'Matrix',
        score: this.form.get('score')?.value,
        questionText: this.form.get('questionText')?.value,
        answerOptions: answerOptions[0] as string[],
        subquestions: (
          answerOptions[1] as Array<{
            subquestion: string;
            answer: string;
          }>
        ).map((row) => ({
          subquestion: row.subquestion,
          answer: [row.answer],
        })),
        domainId: this.form.get('domain')?.value.id,
        categoryId: this.form.get('category')?.value.id,
      };

      this.submitQuestion(payload);
    }

    localStorage.removeItem(this.LOCAL_STORAGE_KEY);
  }
}
