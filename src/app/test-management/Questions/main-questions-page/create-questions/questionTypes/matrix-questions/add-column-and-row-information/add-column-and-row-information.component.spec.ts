import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, FormBuilder } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';

import { AddColumnAndRowInformationComponent } from './add-column-and-row-information.component';
import { ToastService } from '../../../../../../../services/toast-service/toast.service';
import { QuestionStore } from '../../../../../../../stores/questions-store/question.store';
import { RowAndOption } from '../../../../../../../Interfaces/questionInterface';
import { HttpClientTestingModule } from '@angular/common/http/testing';

describe('AddColumnAndRowInformationComponent', () => {
  let component: AddColumnAndRowInformationComponent;
  let fixture: ComponentFixture<AddColumnAndRowInformationComponent>;
  let mockToastService: jest.Mocked<ToastService>;
  let mockRouter: jest.Mocked<Router>;
  let mockQuestionStore: jest.Mocked<QuestionStore>;
  let formBuilder: FormBuilder;
  let isQuestionsSuccessSubject: Subject<boolean>;

  beforeEach(async () => {
    isQuestionsSuccessSubject = new Subject<boolean>();

    mockToastService = {
      onShow: jest.fn(),
    } as unknown as jest.Mocked<ToastService>;

    mockRouter = {
      url: '/edit',
      navigateByUrl: jest.fn(),
    } as unknown as jest.Mocked<Router>;

    mockQuestionStore = {
      isQuestionsSuccess$: isQuestionsSuccessSubject.asObservable(),
      createQuestion: jest.fn(),
      resetSuccessState: jest.fn(),
    } as unknown as jest.Mocked<QuestionStore>;

    await TestBed.configureTestingModule({
      imports: [
        ReactiveFormsModule,
        HttpClientTestingModule,
        AddColumnAndRowInformationComponent,
      ],
      providers: [
        FormBuilder,
        { provide: ToastService, useValue: mockToastService },
        { provide: Router, useValue: mockRouter },
        { provide: QuestionStore, useValue: mockQuestionStore },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(AddColumnAndRowInformationComponent);
    component = fixture.componentInstance;
    formBuilder = TestBed.inject(FormBuilder);

    component.form = formBuilder.group({
      questionText: ['Test Question'],
      difficultyLevel: ['Medium'],
      score: [10],
      domain: [{ id: 1 }],
      category: [{ id: 2 }],
    });

    fixture.detectChanges();
  });

  afterEach(() => {
    localStorage.clear();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Form Initialization', () => {
    it('should set isEdit to true when url includes edit', () => {
      expect(component.isEdit).toBeTruthy();
    });

    it('should disable question text control', () => {
      expect(component.displayQuestionControl.disabled).toBeTruthy();
    });

    it('should initialize with empty form arrays', () => {
      expect(component.optionsArray.length).toBe(0);
      expect(component.subquestionsArray.length).toBe(0);
    });
  });

  describe('Option and Row Management', () => {
    it('should add an option', () => {
      const initialLength = component.optionsArray.length;
      component.addOption();
      expect(component.optionsArray.length).toBe(initialLength + 1);
    });

    it('should remove an option', () => {
      component.addOption();
      const initialLength = component.optionsArray.length;
      component.removeOption(0);
      expect(component.optionsArray.length).toBe(initialLength - 1);
    });

    it('should add a row', () => {
      const initialLength = component.subquestionsArray.length;
      component.addRow();
      expect(component.subquestionsArray.length).toBe(initialLength + 1);
    });

    it('should remove a row', () => {
      component.addRow();
      const initialLength = component.subquestionsArray.length;
      component.removeRow(0);
      expect(component.subquestionsArray.length).toBe(initialLength - 1);
    });
  });

  describe('Radio Change Handling', () => {
    it('should update answer when radio changes', () => {
      component.addRow();
      const event = {
        rowIndex: 0,
        option: 'Test Option',
      };

      component.onRadioChange(event);

      const row = component.subquestionsArray.at(0);
      expect(row.value.answer).toBe('Test Option');
    });
  });

  describe('Matrix Data Validation', () => {
    it('should return false when insufficient options', () => {
      component.addOption();
      component.addRow();
      component.addRow();

      expect(component['hasValidMatrixData']()).toBeFalsy();
    });

    it('should return false when insufficient questions', () => {
      component.addOption();
      component.addOption();
      component.addRow();

      expect(component['hasValidMatrixData']()).toBeFalsy();
    });

    it('should return true when valid matrix data exists', () => {
      // Add valid options
      component.addOption();
      component.optionsArray.at(0).setValue('Option 1');
      component.addOption();
      component.optionsArray.at(1).setValue('Option 2');

      // Add valid rows
      component.addRow();
      const row1 = component.subquestionsArray.at(0);
      row1.patchValue({ subquestion: 'Question 1', answer: 'Answer 1' });

      component.addRow();
      const row2 = component.subquestionsArray.at(1);
      row2.patchValue({ subquestion: 'Question 2', answer: 'Answer 2' });

      expect(component['hasValidMatrixData']()).toBeTruthy();
    });
  });

  describe('Form State Management', () => {
    beforeEach(() => {
      // Mock localStorage
      const localStorageMock = (() => {
        let store: { [key: string]: string } = {};
        return {
          getItem: jest.fn((key) => store[key] || null),
          setItem: jest.fn((key, value) => {
            store[key] = value.toString();
          }),
          removeItem: jest.fn((key) => {
            delete store[key];
          }),
          clear: jest.fn(() => {
            store = {};
          }),
        };
      })();

      Object.defineProperty(window, 'localStorage', {
        value: localStorageMock,
      });
    });

    it('should save form state to local storage', () => {
      component.addRow();
      component.addOption();

      component.saveFormState();

      expect(localStorage.setItem).toHaveBeenCalledWith(
        'matrixQuestionData',
        expect.any(String)
      );
    });

    it('should restore form state from local storage', () => {
      const mockData = {
        answerOptions: ['Option 1', 'Option 2'],
        subquestions: [
          { subquestion: 'Question 1', answer: 'Answer 1' },
          { subquestion: 'Question 2', answer: 'Answer 2' },
        ],
        isEdit: false,
        editData: null,
      };

      (localStorage.getItem as jest.Mock).mockReturnValue(
        JSON.stringify(mockData)
      );

      component.restoreFormState();

      expect(component.optionsArray.length).toBe(2);
      expect(component.subquestionsArray.length).toBe(2);
      expect(component.optionsArray.at(0).value).toBe('Option 1');
      expect(component.subquestionsArray.at(0).value.subquestion).toBe(
        'Question 1'
      );
    });

    it('should restore form state from editData when in edit mode', () => {
      const editData: RowAndOption = {
        options: ['Edit Option 1', 'Edit Option 2'],
        questions: [
          { subquestion: 'Edit Question 1', answer: ['Edit Answer 1'] },
          { subquestion: 'Edit Question 2', answer: ['Edit Answer 2'] },
        ],
      };

      component.editData = editData;
      component.isEdit = true;
      (localStorage.getItem as jest.Mock).mockReturnValue(null);

      component.restoreFormState();

      expect(component.optionsArray.length).toBe(2);
      expect(component.subquestionsArray.length).toBe(2);
      expect(component.optionsArray.at(0).value).toBe('Edit Option 1');
      expect(component.subquestionsArray.at(0).value.subquestion).toBe(
        'Edit Question 1'
      );
    });
  });

  describe('Navigation', () => {
    it('should navigate to previous page', () => {
      component.previousPage();
      expect(mockRouter.navigateByUrl).toHaveBeenCalledWith(
        '/dashboard/test-management/questions/all-questions'
      );
    });

    it('should go back in history', () => {
      const backSpy = jest.spyOn(history, 'back').mockImplementation();
      component.back();
      expect(backSpy).toHaveBeenCalled();
      backSpy.mockRestore();
    });
  });

  describe('Question Store Integration', () => {
    it('should handle successful question creation', () => {
      const resetSpy = jest.spyOn(component.form, 'reset');
      const matrixResetSpy = jest.spyOn(component.matrixForm, 'reset');
      component.isEdit = false;
      isQuestionsSuccessSubject.next(true);

      expect(mockToastService.onShow).toHaveBeenCalledWith(
        'Question Created',
        'Question created successfully',
        true,
        'success'
      );
      expect(resetSpy).toHaveBeenCalled();
      expect(matrixResetSpy).toHaveBeenCalled();
      expect(mockQuestionStore.resetSuccessState).toHaveBeenCalled();
    });

    it('should submit question to store', () => {
      const mockQuestion = {
        id: 1,
        questionText: 'Test Question',
        questionType: 'matrix',
        score: 10,
        difficultyLevel: 'Medium',
      };
      component.submitQuestion(mockQuestion);

      expect(mockQuestionStore.createQuestion).toHaveBeenCalledWith({
        question: mockQuestion,
      });
    });

    it('should handle empty localStorage during restore', () => {
      localStorage.clear();
      component.restoreFormState();
      expect(component.optionsArray.length).toBe(0);
      expect(component.subquestionsArray.length).toBe(0);
    });

    it('should not emit matrix data when options are invalid', () => {
      const emitSpy = jest.spyOn(component.matrixDataChange, 'emit');

      component.addOption();
      component.optionsArray.at(0).setValue('');

      component.addRow();
      component.subquestionsArray.at(0).patchValue({
        subquestion: 'Valid Question',
        answer: 'Valid Answer',
      });
      component.addRow();
      component.subquestionsArray.at(1).patchValue({
        subquestion: 'Valid Question 2',
        answer: 'Valid Answer 2',
      });

      component['emitMatrixDataToParent']();
      expect(emitSpy).not.toHaveBeenCalled();
    });

    it('should not emit matrix data when questions are invalid', () => {
      const emitSpy = jest.spyOn(component.matrixDataChange, 'emit');

      component.addOption();
      component.optionsArray.at(0).setValue('Valid Option');
      component.addOption();
      component.optionsArray.at(1).setValue('Valid Option 2');

      component.addRow();
      component.subquestionsArray.at(0).patchValue({
        subquestion: 'Valid Question',
        answer: '',
      });

      component['emitMatrixDataToParent']();
      expect(emitSpy).not.toHaveBeenCalled();
    });

    it('should handle edit mode without editData', () => {
      component.isEdit = true;
      component.editData = undefined;
      component.restoreFormState();
      expect(component.optionsArray.length).toBe(0);
      expect(component.subquestionsArray.length).toBe(0);
    });

    it('should clear form arrays properly', () => {
      component.addOption();
      component.addRow();
      component['clearFormArrays']();
      expect(component.optionsArray.length).toBe(0);
      expect(component.subquestionsArray.length).toBe(0);
    });

    describe('when not in edit mode', () => {
      let nonEditRouter: jest.Mocked<Router>;

      beforeEach(async () => {
        nonEditRouter = {
          get url() {
            return '/create';
          },
          navigateByUrl: jest.fn(),
        } as unknown as jest.Mocked<Router>;

        TestBed.resetTestingModule();
        await TestBed.configureTestingModule({
          imports: [
            ReactiveFormsModule,
            HttpClientTestingModule,
            AddColumnAndRowInformationComponent,
          ],
          providers: [
            FormBuilder,
            { provide: ToastService, useValue: mockToastService },
            { provide: Router, useValue: nonEditRouter },
            { provide: QuestionStore, useValue: mockQuestionStore },
          ],
        }).compileComponents();

        fixture = TestBed.createComponent(AddColumnAndRowInformationComponent);
        component = fixture.componentInstance;
        formBuilder = TestBed.inject(FormBuilder);

        component.form = formBuilder.group({
          questionText: ['Test Question'],
          difficultyLevel: ['Medium'],
          score: [10],
          domain: [{ id: 1 }],
          category: [{ id: 2 }],
        });

        component.ngOnInit();
      });

      it('should initialize with empty form when not in edit mode', () => {
        expect(component.isEdit).toBeFalsy();
        expect(component.optionsArray.length).toBe(0);
        expect(component.subquestionsArray.length).toBe(0);
      });
    });

    it('should handle beforeunload event', () => {
      const saveSpy = jest.spyOn(component, 'saveFormState');
      window.dispatchEvent(new Event('beforeunload'));
      expect(saveSpy).toHaveBeenCalled();
    });
  });

  describe('Matrix Data Emission', () => {
    it('should emit matrix data when form is valid', () => {
      const emitSpy = jest.spyOn(component.matrixDataChange, 'emit');

      component.addOption();
      component.optionsArray.at(0).setValue('Option 1');
      component.addOption();
      component.optionsArray.at(1).setValue('Option 2');

      component.addRow();
      const row1 = component.subquestionsArray.at(0);
      row1.patchValue({ subquestion: 'Question 1', answer: 'Answer 1' });

      component.addRow();
      const row2 = component.subquestionsArray.at(1);
      row2.patchValue({ subquestion: 'Question 2', answer: 'Answer 2' });

      component['emitMatrixDataToParent']();

      expect(emitSpy).toHaveBeenCalledWith({
        options: ['Option 1', 'Option 2'],
        questions: [
          { subquestion: 'Question 1', answer: ['Answer 1'] },
          { subquestion: 'Question 2', answer: ['Answer 2'] },
        ],
      });
    });

    it('should not emit matrix data when form is invalid', () => {
      const emitSpy = jest.spyOn(component.matrixDataChange, 'emit');

      component.addOption();
      component.addRow();

      component['emitMatrixDataToParent']();

      expect(emitSpy).not.toHaveBeenCalled();
    });
  });

  describe('Component Cleanup', () => {
    it('should unsubscribe from all subscriptions on destroy', () => {
      const mockSubscription = {
        unsubscribe: jest.fn(),
      };

      component.subscriptions.push(
        mockSubscription as unknown as import('rxjs').Subscription
      );

      component.ngOnDestroy();

      expect(mockSubscription.unsubscribe).toHaveBeenCalled();
    });
  });
});
