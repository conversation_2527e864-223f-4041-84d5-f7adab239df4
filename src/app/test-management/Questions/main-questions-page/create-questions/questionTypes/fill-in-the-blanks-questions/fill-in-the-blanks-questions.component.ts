import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { BasicQuestionContainerComponent } from '../../../components/basic-question-container/basic-question-container.component';
import { QuestionTitleComponent } from '../../../components/question-title/question-title.component';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { CustomButtonComponent } from '../../../../../../components/custombutton/custombutton.component';
import {
  generateUUID,
  getRandomIndex,
  noWhitespaceTextareaValidator,
} from '../../../../../../utils/constants';
import { ToastService } from '../../../../../../services/toast-service/toast.service';
import { QuestionsService } from '../../../../../../services/test-management-service/questions.service';
import { Router } from '@angular/router';
import { EditorService } from '../../../../../../services/editor-service.service';
import { CustomModalComponent } from '../../../../../../components/custom-modal/custom-modal.component';
import {
  FillInOption,
  FillInTheBlankData,
  MessageStatus,
  QuestionMessage,
  QuestionsData,
} from '../../../../../../Interfaces/questionInterface';
import { CustomInputForObjectComponent } from '../../../../../../components/custom-input-for-object/custom-input-for-object.component';
import { QuestionStore } from '../../../../../../stores/questions-store/question.store';
import { Subscription } from 'rxjs';
import { ExpandableInstructionCardComponent } from '../../../../../../components/expandable-instruction-card/expandable-instruction-card.component';
import { SafeHtmlDirective } from '../../../../../../directives/safe-html.directive';

@Component({
  selector: 'app-fill-in-the-blanks-questions',
  standalone: true,
  imports: [
    BasicQuestionContainerComponent,
    QuestionTitleComponent,
    ReactiveFormsModule,
    CustomButtonComponent,
    CustomModalComponent,
    CustomInputForObjectComponent,
    ExpandableInstructionCardComponent,
    SafeHtmlDirective,
  ],
  templateUrl: './fill-in-the-blanks-questions.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FillInTheBlanksQuestionsComponent
  implements OnInit, AfterViewInit, OnChanges, OnDestroy
{
  buttonState = false;
  isEdit = false;
  showCode = true;
  checkMark = 'assets/icons/check-mark.svg';
  fillInTheBlanksForm: FormGroup;
  questionEditorId = generateUUID();
  placeholderForWrongAnswers: string[] = [];
  placeholderForRightAnswers: string[] = [];
  @Input() editFillInTheBlanksData: QuestionsData | undefined;
  @Input() title = 'Create New Question (Fill-In)';
  @Input() previousPageText = 'Back';
  @Output() submitEdittedQuestionEvent = new EventEmitter();

  blankCounter = 0;
  @Input() isShowModal = false;
  headerTitle = 'Preview Question with Options ';
  previewtext = '';
  filledText = '';
  completeQuestionText = '';
  correctAnswers: string[] = [];
  rightOrderForCorrectOptions: string[] = [];
  fillInTheBlanksData: FillInTheBlankData | undefined;
  subscriptions: Subscription[] = [];

  constructor(
    private readonly fb: FormBuilder,
    private readonly toast: ToastService,
    private readonly questionService: QuestionsService,
    private readonly router: Router,
    private readonly editorService: EditorService,
    private readonly cdr: ChangeDetectorRef,
    private readonly questionsStore: QuestionStore
  ) {
    this.fillInTheBlanksForm = this.fb.group({
      questionText: ['', [Validators.required, noWhitespaceTextareaValidator]],
      domain: ['', Validators.required],
      score: ['', [Validators.required, Validators.min(1)]],
      difficultyLevel: ['', Validators.required],
      correctAnswerOptions: this.fb.array([], Validators.required),
      wrongAnswerOptions: this.fb.array([]),
      category: ['', Validators.required],
    });
  }

  fillInInstructions = [
    'Type your question text',
    'Place your cursor where you want a blank to appear',
    'Click "Insert Blank" button',
    'Provide the correct answer for each blank below',
    'Optionally add incorrect answers to create distractors',
  ];

  ngAfterViewInit(): void {
    if (this.isEdit) {
      this.populateForm(this.editFillInTheBlanksData as QuestionsData);
      this.cdr.detectChanges();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['editFillInTheBlanksData'] && this.editFillInTheBlanksData) {
      this.populateForm(this.editFillInTheBlanksData);
    }
  }

  ngOnInit(): void {
    this.isEdit = this.router.url.includes('edit');

    this.editorService
      .getEditorQuestion(this.questionEditorId)
      .subscribe((question) => {
        if (question) {
          this.completeQuestionText = question;
        }
      });

    const title = this.isEdit ? MessageStatus.Edited : MessageStatus.Created;
    const message = this.isEdit
      ? QuestionMessage.EditSuccess
      : QuestionMessage.CreateSuccess;

    this.subscriptions.push(
      this.questionsStore.isQuestionsSuccess$.pipe().subscribe((isSuccess) => {
        if (isSuccess) {
          this.toast.onShow(title, message, true, 'success');

          this.isShowModal = false;

          // Clear answer options arrays
          this.rightAnswersOptionsArray.clear();
          this.wrongAnswerOptionsArray.clear();

          this.questionsStore.resetSuccessState();

          if (this.isEdit) {
            this.router.navigateByUrl(
              `/dashboard/test-management/questions/all-questions`
            );
          }
        }
      })
    );

    this.editorService
      .getCorrectAnswersFillInTheBlanksOptions(this.questionEditorId)
      .subscribe((options) => {
        this.rightOrderForCorrectOptions = options.map(
          (option) => option.blank
        );
        const indicesToRemove: number[] = [];

        // Collect indices of controls to be removed
        for (let i = 0; i < this.rightAnswersOptionsArray.length; i++) {
          const control = this.rightAnswersOptionsArray.at(i);
          if (!this.rightOrderForCorrectOptions.includes(control.value.blank)) {
            indicesToRemove.push(i); // Use index for removal
          }
        }

        // Remove controls in reverse order to maintain the array order
        for (let i = indicesToRemove.length - 1; i >= 0; i--) {
          this.rightAnswersOptionsArray.removeAt(indicesToRemove[i]);
        }
      });
  }

  previousPage() {
    this.router.navigateByUrl(
      `/dashboard/test-management/questions/all-questions`
    );
  }

  disableButton() {
    const isRightAnswerValid: boolean =
      this.rightAnswersOptionsArray.value.some(
        ({ value }: { value: string }) => !value.trim()
      );
    const isWrongAnswerValid: boolean = this.wrongAnswerOptionsArray.value.some(
      ({ value }: { value: string }) => !value.trim()
    );

    return (
      this.fillInTheBlanksForm.invalid ||
      isRightAnswerValid ||
      isWrongAnswerValid
    );
  }

  deleteWrongAnswerOption(index: number) {
    this.wrongAnswerOptionsArray.removeAt(index);
  }

  onRemoveBlankButtonClick(
    value: AbstractControl<unknown, unknown>,
    i: number,
    answersOptions: FormArray
  ) {
    answersOptions.removeAt(i);
    const newValue = value.value as FillInOption;
    this.editorService.triggerRemoveBlank(newValue.blank); // Trigger removal
  }

  onCancel() {
    this.questionService.cancelQuestion.next(true);
  }

  get questionText() {
    return this.fillInTheBlanksForm.get('questionText');
  }

  get wrongAnswerOptionsArray(): FormArray {
    return this.fillInTheBlanksForm.get('wrongAnswerOptions') as FormArray;
  }

  get rightAnswersOptionsArray(): FormArray {
    return this.fillInTheBlanksForm.get('correctAnswerOptions') as FormArray;
  }

  addWrongOption() {
    this.placeholderForWrongAnswers.push('wrong');
    const newControl = this.fb.group({
      value: ['', Validators.required],
    });
    this.wrongAnswerOptionsArray.push(newControl);
  }

  addBlankOption(uuid: string) {
    this.blankCounter++; // Increment the counter
    const placeholder = 'blank' + this.blankCounter;
    this.placeholderForRightAnswers.push(placeholder);

    const initialValue = { blank: uuid, value: '' };
    const newControl = this.fb.control(initialValue, [Validators.required]);

    const insertIndex = this.rightOrderForCorrectOptions.indexOf(uuid);

    if (insertIndex === -1) {
      this.rightAnswersOptionsArray.push(newControl);
    } else {
      this.rightAnswersOptionsArray.insert(insertIndex, newControl);
    }
  }

  updateControlValue(
    index: number,
    inputValue: string,
    answersOptions: FormArray
  ): void {
    const control = answersOptions.at(index);
    if (control) {
      const currentValue = control.value;
      currentValue.value = inputValue;
      control.setValue(currentValue);
    }
  }

  previewModal() {
    const text = this.replacePlaceholders(
      this.completeQuestionText || this.questionText?.value,
      this.rightAnswersOptionsArray.value
    );

    this.filledText = text;

    const objectMap = new Map(
      this.rightAnswersOptionsArray.value.map(
        (obj: { blank: string; value: string }) => [obj.blank, obj.value]
      )
    );
    if (this.completeQuestionText) {
      this.correctAnswers = this.rightOrderForCorrectOptions.map((id) =>
        objectMap.get(id)
      ) as unknown as string[];
    } else {
      this.correctAnswers = this.rightAnswersOptionsArray.value.map(
        (obj: { blank: string; value: string }) => obj.value
      );
    }

    const hasAnswer = this.wrongAnswerOptionsArray.value.some(
      (wrongAnswer: { value: string }) => {
        return this.correctAnswers.includes(wrongAnswer.value);
      }
    );

    if (hasAnswer) {
      const answerOption =
        this.correctAnswers.length === 1 ? 'option' : 'options'; // Determine singular/plural based on length
      const wrongOption =
        this.wrongAnswerOptionsArray.value === 1 ? 'option' : 'options';

      this.toast.onShow(
        'error',
        `answer in correct answer ${answerOption} cannot be included in the wrong answer ${wrongOption}`,
        true,
        'error'
      );
      return;
    }

    this.isShowModal = true;
    const fillInOptions = [
      ...this.rightAnswersOptionsArray.value,
      ...this.wrongAnswerOptionsArray.value,
    ];

    const customShuffle = (arr: FillInOption[]) => {
      const shuffledArr = [...arr];
      const n = shuffledArr.length;
      for (let i = 0; i < n; i++) {
        const randomIndex = getRandomIndex(n);
        [shuffledArr[i], shuffledArr[randomIndex]] = [
          shuffledArr[randomIndex],
          shuffledArr[i],
        ];
      }
      return shuffledArr;
    };

    const shuffledOptions = customShuffle(fillInOptions);

    this.fillInTheBlanksData = {
      questionText: this.completeQuestionText || this.questionText?.value,
      questionType: 'Fill_in',
      difficultyLevel: this.fillInTheBlanksForm.get('difficultyLevel')?.value,
      score: this.fillInTheBlanksForm.get('score')?.value,
      categoryId: this.fillInTheBlanksForm.get('category')?.value.id,
      domainId: this.fillInTheBlanksForm.get('domain')?.value.id,
      correctAnswers: this.correctAnswers,
      fillInOptions: shuffledOptions,
    };
  }

  saveQuestion() {
    if (this.isEdit) {
      this.submitEdittedQuestionEvent.emit(this.fillInTheBlanksData);
      return;
    }
    this.questionsStore.createQuestion({
      question: this.fillInTheBlanksData as FillInTheBlankData,
    });
  }

  closeModal() {
    this.isShowModal = false;
  }
  replacePlaceholders(
    text: string,
    replacements: { blank: string; value: string }[]
  ) {
    replacements.forEach((replacement) => {
      // Regular expression to match the entire nested span structure for the placeholder
      const placeholder = new RegExp(
        `<span class="ql-insert-blank blank-placeholder" data-uuid="${replacement.blank}">\\s*<span contenteditable="false">\\s*<span contenteditable="false">_______</span>\\s*</span>\\s*</span>`,
        'g'
      );

      // Escape special HTML characters to prevent XSS vulnerabilities
      const safeValue = replacement.value
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;');

      // Create the new HTML to replace the placeholder with
      const replacementHtml = `<span style="border-bottom: 2px solid; font-weight: bold; padding-bottom: 2px;">${safeValue}</span>`;

      // Replace the placeholder with the new HTML in the text
      text = text.replace(placeholder, replacementHtml);
    });

    return text;
  }

  populateForm(data: QuestionsData) {
    if (!data) return;

    this.rightAnswersOptionsArray.clear();
    this.wrongAnswerOptionsArray.clear();

    if (data.fillInAnswer) {
      data.fillInAnswer.options.forEach((option) => {
        if (option.blank) {
          const initialValue = { blank: option.blank, value: option.value };
          const newControl = this.fb.control(initialValue, [
            Validators.required,
          ]);
          this.rightAnswersOptionsArray.push(newControl);
        } else {
          const newControl = this.fb.group({
            value: [option.value, Validators.required],
          });
          this.wrongAnswerOptionsArray.push(newControl);
        }
      });
    }

    this.fillInTheBlanksForm.patchValue({
      domain: data.domain,
      category: data.category,
      score: data.score,
      difficultyLevel: data.difficultyLevel,
      questionText: data.questionText,
    });
  }

  loadQuestions() {
    this.questionService.getAllQuestions(1, 10, {});
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => subscription.unsubscribe());
  }
}
