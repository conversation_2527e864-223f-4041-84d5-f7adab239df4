import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  HostListener,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { BasicQuestionContainerComponent } from '../../../components/basic-question-container/basic-question-container.component';
import {
  FormArray,
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { CustomButtonComponent } from '../../../../../../components/custombutton/custombutton.component';
import {
  BasicQuestion,
  MessageStatus,
  QuestionMessage,
  QuestionsData,
} from '../../../../../../Interfaces/questionInterface';
import { QuestionsService } from '../../../../../../services/test-management-service/questions.service';
import { QuestionTitleComponent } from '../../../components/question-title/question-title.component';
import { Router } from '@angular/router';
import { ToastService } from '../../../../../../services/toast-service/toast.service';
import { hasDuplicateAnswers } from '../../../../../../utils/questionsConstants';
import {
  noWhitespaceTextareaValidator,
  noWhitespaceValidator,
} from '../../../../../../utils/constants';
import { QuestionStore } from '../../../../../../stores/questions-store/question.store';
import { Subscription } from 'rxjs';
import { AnswerOptionsComponent } from '../../../../../../components/answer-options/answer-options.component';
import { FormFactoryService } from '../../../../../../services/form-factory-service/form-factory.service';

@Component({
  selector: 'app-multi-choice-questions',
  standalone: true,
  imports: [
    BasicQuestionContainerComponent,
    ReactiveFormsModule,
    CustomButtonComponent,
    QuestionTitleComponent,
    AnswerOptionsComponent,
  ],
  templateUrl: './multi-choice-questions.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MultiChoiceQuestionsComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  form: FormGroup;
  selectedAnswerIndex: number | undefined;
  isEdit: boolean = false;
  @Input() title = 'Create New Question(Multiple-Choice)';
  @Input() previousPageText = 'Back';
  @Input() editMultiChoiceData: QuestionsData | undefined;
  @Output() submitEdittedQuestionEvent = new EventEmitter();
  subscriptions: Subscription[] = [];
  buttonState: boolean = true;
  STORAGE_KEY = 'multiChoiceFormData';

  @HostListener('window:beforeunload')
  saveFormData() {
    const formData = {
      ...this.form.value,
      selectedAnswerIndex: this.selectedAnswerIndex,
      isEdit: this.isEdit,
      editData: this.editMultiChoiceData,
    };
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(formData));
  }

  constructor(
    private readonly fb: FormBuilder,
    private readonly questionService: QuestionsService,
    private readonly router: Router,
    private readonly toast: ToastService,
    private readonly questionsStore: QuestionStore,
    private readonly formFactory: FormFactoryService,
    private readonly cdr: ChangeDetectorRef // Added ChangeDetectorRef
  ) {
    this.form = this.formFactory.createQuestionForm();
  }

  ngAfterViewInit(): void {
    this.form.valueChanges.subscribe(() => {
      this.buttonState = false;
    });
  }

  ngOnInit(): void {
    const savedData = localStorage.getItem(this.STORAGE_KEY);

    if (savedData) {
      const parsedData = JSON.parse(savedData);
      this.isEdit = parsedData.isEdit;

      if (this.isEdit) {
        this.editMultiChoiceData = parsedData.editData;
      }

      this.populateForm(parsedData);
      localStorage.removeItem(this.STORAGE_KEY);
    } else if (this.editMultiChoiceData && this.router.url.includes('edit')) {
      this.isEdit = true;
      this.populateForm(this.editMultiChoiceData);
    }
    setTimeout(() => {
      this.checkFormValidity();
    });

    const title = this.isEdit ? MessageStatus.Edited : MessageStatus.Created;
    const message = this.isEdit
      ? QuestionMessage.EditSuccess
      : QuestionMessage.CreateSuccess;

    this.subscriptions.push(
      this.questionsStore.isQuestionsSuccess$.pipe().subscribe((isSuccess) => {
        if (isSuccess) {
          this.toast.onShow(title, message, true, 'success');

          // Complete form reset for successful creation
          this.resetFormCompletely();

          this.questionsStore.resetSuccessState();
          if (this.isEdit) {
            this.router.navigateByUrl(
              `/dashboard/test-management/questions/all-questions`
            );
          }
        }
      })
    );
  }

  private resetFormCompletely(): void {
    this.optionsArray.clear();
    this.selectedAnswerIndex = undefined;
    this.form.reset();

    this.buttonState = true;
    this.cdr.detectChanges();

    setTimeout(() => {
      this.checkFormValidity();
      this.cdr.detectChanges();
    });
  }

  populateForm(data: QuestionsData) {
    if (!data) return;

    const answerOptions = this.form.get('answerOptions') as FormArray;
    while (answerOptions.length) {
      answerOptions.removeAt(0);
    }

    const options =
      data.multipleChoiceAnswer?.options ?? data.answerOptions ?? [];
    options.forEach((option: string) => {
      answerOptions.push(
        this.fb.control(option, [Validators.required, noWhitespaceValidator])
      );
    });

    this.form.patchValue({
      domain: data.domain,
      category: data.category,
      score: data.score,
      difficultyLevel: data.difficultyLevel,
      questionText: data.questionText,
    });

    if (data.multipleChoiceAnswer?.answer) {
      const correctAnswer = data.multipleChoiceAnswer.answer[0];
      this.selectedAnswerIndex = options.findIndex(
        (option: string) => option === correctAnswer
      );
    } else if (data.selectedAnswerIndex !== undefined) {
      this.selectedAnswerIndex = data.selectedAnswerIndex;
    }

    this.form.updateValueAndValidity();
    this.checkFormValidity();
    this.cdr.detectChanges();
  }

  get optionsArray(): FormArray {
    return this.form.get('answerOptions') as FormArray;
  }

  addOption() {
    this.optionsArray.push(
      this.fb.control('', [Validators.required, noWhitespaceTextareaValidator])
    );
    this.checkFormValidity();
    this.cdr.detectChanges();
  }

  checkFormValidity() {
    this.buttonState = this.disableButton();
  }

  onSubmit(event: Event) {
    event.preventDefault();

    if (hasDuplicateAnswers(this.optionsArray.value)) {
      this.toast.onShow('Answers', `Duplicate answer options`, true, 'error');
      return;
    }

    const selectedAnswer = this.optionsArray.at(
      Number(this.selectedAnswerIndex)
    )?.value;

    const MultiChoiceData = {
      answerOptions: this.optionsArray.value,
      domainId: this.form.get('domain')?.value?.id,
      categoryId: this.form.get('category')?.value?.id,
      score: this.form.get('score')?.value,
      difficultyLevel: this.form.get('difficultyLevel')?.value,
      correctAnswers: [selectedAnswer],
      questionText: this.form.get('questionText')?.value,
      questionType: 'Multiple_choice',
    };

    if (this.isEdit) {
      this.submitEdittedQuestionEvent.emit({
        ...MultiChoiceData,
      });
      return;
    }

    this.submitQuestion(MultiChoiceData);
  }

  onCancel() {
    this.questionService.cancelQuestion.next(true);
  }

  submitQuestion(MultiChoiceData: BasicQuestion) {
    this.questionsStore.createQuestion({ question: MultiChoiceData });
  }

  onRadioChange(index: number) {
    this.selectedAnswerIndex = index;
    this.checkFormValidity();
    this.cdr.detectChanges();
  }

  handleKeydown(event: KeyboardEvent) {
    if (event.code === 'Space') {
      event.preventDefault();
      this.addOption();
    }
  }

  handleKeyCancel(event: KeyboardEvent) {
    if (event.code === 'enter') {
      event.preventDefault();
      this.onCancel();
    }
  }

  deleteOption(index: number) {
    if (index === this.selectedAnswerIndex) {
      this.selectedAnswerIndex = undefined;
    } else if (
      this.selectedAnswerIndex !== undefined &&
      index < this.selectedAnswerIndex
    ) {
      this.selectedAnswerIndex = this.selectedAnswerIndex - 1;
    }

    this.optionsArray.removeAt(index);
    this.checkFormValidity();
    this.cdr.detectChanges();
  }

  handleKeyDelete(event: KeyboardEvent, index: number) {
    if (event.code === 'delete') {
      event.preventDefault();
      this.deleteOption(index);
    }
  }

  previousPage() {
    this.router.navigateByUrl(
      `/dashboard/test-management/questions/all-questions`
    );
  }

  disableButton() {
    return (
      this.form.invalid ||
      this.optionsArray.length <= 1 ||
      this.selectedAnswerIndex === undefined
    );
  }

  loadQuestions() {
    this.questionService.getAllQuestions(1, 10, {});
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => subscription.unsubscribe());
    localStorage.removeItem(this.STORAGE_KEY);
  }
}
