import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MultiChoiceQuestionsComponent } from './multi-choice-questions.component';
import { HttpClient, HttpHandler } from '@angular/common/http';
import { FormArray, FormControl, FormGroup, Validators } from '@angular/forms';
import { QuestionsData } from '../../../../../../Interfaces/questionInterface';
import { ToastService } from '../../../../../../services/toast-service/toast.service';
import { EventEmitter } from '@angular/core';
import { Router } from '@angular/router';
import { QuestionsService } from '../../../../../../services/test-management-service/questions.service';
import { QuestionCreationServiceService } from '../../../../../../services/test-management-service/question-creation-service.service';
import { fillInTypeDataMock } from '../../../../../../../../__mocks__/fillInTheBlanksMock';
import { of } from 'rxjs';
import { QuestionStore } from '../../../../../../stores/questions-store/question.store';

const mockToastService = { onShow: jest.fn() };
const mockQuestionsStore = {
  isQuestionsSuccess$: of(true),
  resetSuccessState: jest.fn(),
} as unknown as QuestionStore;
const mockRouter = { url: '/edit', navigateByUrl: jest.fn() };
const mockQuestionService = {
  cancelQuestion: { next: jest.fn() },
  cancelQuestion$: {
    subscribe: jest.fn(),
  },
  getAllQuestions: jest.fn(),
};
const mockQuestionCreationService = {
  createQuestion: jest.fn(),
};
describe('MultiChoiceQuestionsComponent', () => {
  let component: MultiChoiceQuestionsComponent;
  let fixture: ComponentFixture<MultiChoiceQuestionsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [MultiChoiceQuestionsComponent],
      providers: [
        HttpClient,
        HttpHandler,
        {
          provide: ToastService,
          useValue: mockToastService,
        },
        {
          provide: Router,
          useValue: mockRouter,
        },
        {
          provide: QuestionsService,
          useValue: mockQuestionService,
        },
        {
          provide: QuestionCreationServiceService,
          useValue: mockQuestionCreationService,
        },
        { provide: QuestionStore, useValue: mockQuestionsStore },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(MultiChoiceQuestionsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with saved data from localStorage', () => {
    const savedData = {
      isEdit: true,
      editData: { someKey: 'someValue' },
    };

    const mockLocalStorage = jest.spyOn(Storage.prototype, 'getItem');

    mockLocalStorage.mockReturnValueOnce(JSON.stringify(savedData));

    component.ngOnInit();

    expect(mockLocalStorage).toHaveBeenCalledWith(component.STORAGE_KEY);
    expect(component.isEdit).toBe(true);
    expect(component.editMultiChoiceData).toEqual(savedData.editData);
  });

  it('should handle case when no saved data is in localStorage', () => {
    const mockLocalStorage = jest.spyOn(Storage.prototype, 'getItem');

    mockLocalStorage.mockReturnValueOnce(null); // Simulate no data in localStorage

    component.ngOnInit();

    expect(component.isEdit).toBeFalsy();
    expect(component.editMultiChoiceData).toBeUndefined();
  });

  it('should call checkFormValidity after initialization', () => {
    const checkFormValiditySpy = jest.spyOn(component, 'checkFormValidity');

    component.ngOnInit();

    setTimeout(() => {
      expect(checkFormValiditySpy).toHaveBeenCalled();
    }, 0);
  });

  it('should save form data to localStorage when window is about to unload', () => {
    component.form = {
      value: { question: 'Sample Question' },
    } as never as FormGroup;
    component.selectedAnswerIndex = 1;
    component.isEdit = false;
    component.editMultiChoiceData = undefined;
    const spySetItem = jest.spyOn(Storage.prototype, 'setItem');

    component.saveFormData();

    expect(spySetItem).toHaveBeenCalledWith(
      component.STORAGE_KEY,
      JSON.stringify({
        question: 'Sample Question',
        selectedAnswerIndex: 1,
        isEdit: false,
      })
    );
  });

  it('should include form values, selected answer index, edit status, and edit data in saved data', () => {
    component.form = {
      value: { question: 'Sample Question' },
    } as never as FormGroup;
    component.selectedAnswerIndex = 2;
    component.isEdit = true;
    component.editMultiChoiceData = {
      someData: 'data',
    } as unknown as QuestionsData;
    const spySetItem = jest.spyOn(localStorage, 'setItem');

    component.saveFormData();

    expect(spySetItem).toHaveBeenCalledWith(
      component.STORAGE_KEY,
      JSON.stringify({
        question: 'Sample Question',
        selectedAnswerIndex: 2,
        isEdit: true,
        editData: { someData: 'data' },
      })
    );
  });

  it('should subscribe to form value changes in ngAfterViewInit', () => {
    const spy = jest.spyOn(component.form.valueChanges, 'subscribe');
    component.ngAfterViewInit();
    expect(spy).toHaveBeenCalled();
  });

  it('should set buttonState to false when form values change', () => {
    component.form = new FormGroup({
      testField: new FormControl(['', Validators.required]),
    });
    component.ngAfterViewInit();
    component.form.setValue({ testField: 'newValue' });
    expect(component.buttonState).toBe(false);
  });

  it('should handle empty form value changes', () => {
    component.form = new FormGroup({
      testField: new FormControl(['', Validators.required]),
    });
    component.ngAfterViewInit();
    component.form.reset();
    expect(component.buttonState).toBe(false);
  });

  it('should identify edit mode from router URL correctly', () => {
    component.editMultiChoiceData = {
      domain: 'Science',
    } as unknown as QuestionsData;
    jest.spyOn(component, 'populateForm');

    component.ngOnInit();

    expect(component.isEdit).toBe(true);
    expect(component.populateForm).toHaveBeenCalledWith(
      component.editMultiChoiceData
    );
  });

  it('should handle absence of data in localStorage gracefully', () => {
    jest.spyOn(component, 'populateForm');

    component.ngOnInit();

    expect(component.populateForm).not.toHaveBeenCalled();
  });

  it('should populate form fields with valid QuestionsData', () => {
    component.form = new FormGroup({
      domain: new FormControl(''),
      category: new FormControl(''),
      score: new FormControl(''),
      difficultyLevel: new FormControl(''),
      questionText: new FormControl(''),
      answerOptions: new FormArray([]),
    });

    const data: QuestionsData = {
      id: '1',
      questionText: 'Sample Question',
      questionType: 'multiple-choice',
      score: 5,
      system: false,
      timeLimit: '1 min',
      difficultyLevel: 'easy',
      essayAnswer: { rubrics: '' },
      fillInAnswer: fillInTypeDataMock,
      strictMark: false,
      isActive: true,
      category: { id: 'cat1', name: 'Category 1' },
      domain: { id: 'dom1', name: 'Domain 1' },
      answerOptions: ['Option 1', 'Option 2'],
    };

    const patchValueSpy = jest.spyOn(component.form, 'patchValue');
    const updateValiditySpy = jest.spyOn(
      component.form,
      'updateValueAndValidity'
    );

    component.populateForm(data);

    expect(patchValueSpy).toHaveBeenCalledWith({
      domain: data.domain,
      category: data.category,
      score: data.score,
      difficultyLevel: data.difficultyLevel,
      questionText: data.questionText,
    });

    expect(updateValiditySpy).toHaveBeenCalled();
    const answerOptions = component.form.get('answerOptions') as FormArray;
    expect(answerOptions.length).toBe(2);
    expect(answerOptions.at(0).value).toBe('Option 1');
    expect(answerOptions.at(1).value).toBe('Option 2');
  });

  it('should populate form fields with valid QuestionsData (second test)', () => {
    component.form = new FormGroup({
      domain: new FormControl(''),
      category: new FormControl(''),
      score: new FormControl(''),
      difficultyLevel: new FormControl(''),
      questionText: new FormControl(''),
      answerOptions: new FormArray([]),
    });

    const data: QuestionsData = {
      id: '1',
      questionText: 'Sample Question',
      questionType: 'multiple-choice',
      score: 5,
      system: false,
      timeLimit: '1 min',
      difficultyLevel: 'easy',
      essayAnswer: { rubrics: '' },
      fillInAnswer: fillInTypeDataMock,
      strictMark: false,
      isActive: true,
      category: { id: 'cat1', name: 'Category 1' },
      domain: { id: 'dom1', name: 'Domain 1' },
      answerOptions: [],
    };

    const patchValueSpy = jest.spyOn(component.form, 'patchValue');

    component.populateForm(data);

    expect(patchValueSpy).toHaveBeenCalledWith({
      domain: data.domain,
      category: data.category,
      score: data.score,
      difficultyLevel: data.difficultyLevel,
      questionText: data.questionText,
    });
  });

  it('should not set selectedAnswerIndex if multipleChoiceAnswer is missing', () => {
    component.form = new FormGroup({
      domain: new FormControl(''),
      category: new FormControl(''),
      score: new FormControl(''),
      difficultyLevel: new FormControl(''),
      questionText: new FormControl(''),
      answerOptions: new FormArray([]),
    });

    const dataWithoutMultipleChoiceAnswer = {
      id: '1',
      questionText: 'Sample Question',
      questionType: 'multiple-choice',
      score: 5,
      system: false,
      timeLimit: '1 min',
      difficultyLevel: 'easy',
      essayAnswer: { rubrics: '' },
      fillInAnswer: fillInTypeDataMock,
      strictMark: false,
      isActive: true,
      category: { id: 'cat1', name: 'Category 1' },
      domain: { id: 'dom1', name: 'Domain 1' },
      answerOptions: ['Option 1', 'Option 2'],
    };

    component.populateForm(dataWithoutMultipleChoiceAnswer);

    expect(component.selectedAnswerIndex).toBeUndefined();
  });
  it('should populate form fields with valid QuestionsData', () => {
    component.form = new FormGroup({
      domain: new FormControl(''),
      category: new FormControl(''),
      score: new FormControl(''),
      difficultyLevel: new FormControl(''),
      questionText: new FormControl(''),
      answerOptions: new FormArray([]),
    });

    const data: QuestionsData = {
      id: '1',
      questionText: 'Sample Question',
      questionType: 'multiple-choice',
      score: 5,
      system: false,
      timeLimit: '1 min',
      difficultyLevel: 'easy',
      essayAnswer: { rubrics: '' },
      fillInAnswer: fillInTypeDataMock,
      strictMark: false,
      isActive: true,
      category: { id: 'cat1', name: 'Category 1' },
      domain: { id: 'dom1', name: 'Domain 1' },
      answerOptions: [],
    };

    const patchValueSpy = jest.spyOn(component.form, 'patchValue');
    const updateValiditySpy = jest.spyOn(
      component.form,
      'updateValueAndValidity'
    );

    component.populateForm(data);

    expect(patchValueSpy).toHaveBeenCalledWith({
      domain: data.domain,
      category: data.category,
      score: data.score,
      difficultyLevel: data.difficultyLevel,
      questionText: data.questionText,
    });

    expect(updateValiditySpy).toHaveBeenCalled();
  });

  it('should add answer options to the form', () => {
    component.form = new FormGroup({
      domain: new FormControl(''),
      category: new FormControl(''),
      score: new FormControl(''),
      difficultyLevel: new FormControl(''),
      questionText: new FormControl(''),
      answerOptions: new FormArray([]),
    });

    const data: QuestionsData = {
      id: '1',
      questionText: 'Sample Question',
      questionType: 'multiple-choice',
      score: 5,
      system: false,
      timeLimit: '1 min',
      difficultyLevel: 'easy',
      essayAnswer: { rubrics: '' },
      fillInAnswer: fillInTypeDataMock,
      strictMark: false,
      isActive: true,
      category: { id: 'cat1', name: 'Category 1' },
      domain: { id: 'dom1', name: 'Domain 1' },
      answerOptions: ['Option 1', 'Option 2'],
    };

    component.populateForm(data);

    const answerOptions = component.form.get('answerOptions') as FormArray;
    expect(answerOptions.length).toBe(2);
    expect(answerOptions.at(0).value).toBe('Option 1');
    expect(answerOptions.at(1).value).toBe('Option 2');
  });

  it('should populate form fields with valid QuestionsData (alternative approach)', () => {
    const mockFormArray = {
      length: 0,
      removeAt: jest.fn(),
      push: jest.fn(),
      clear: jest.fn(),
    } as never as FormArray;

    const mockForm = {
      get: jest.fn().mockReturnValue(mockFormArray),
      patchValue: jest.fn(),
      updateValueAndValidity: jest.fn(),
      _updateTreeValidity: jest.fn(),
    } as never as FormGroup;

    component.form = mockForm;

    jest.spyOn(component['cdr'], 'detectChanges').mockImplementation(() => {});

    const data: QuestionsData = {
      id: '1',
      questionText: 'Sample Question',
      questionType: 'multiple-choice',
      score: 5,
      system: false,
      timeLimit: '1 min',
      difficultyLevel: 'easy',
      essayAnswer: { rubrics: '' },
      fillInAnswer: fillInTypeDataMock,
      strictMark: false,
      isActive: true,
      category: { id: 'cat1', name: 'Category 1' },
      domain: { id: 'dom1', name: 'Domain 1' },
      answerOptions: ['Option 1', 'Option 2'],
    };

    component.populateForm(data);

    expect(mockForm.patchValue).toHaveBeenCalledWith({
      domain: data.domain,
      category: data.category,
      score: data.score,
      difficultyLevel: data.difficultyLevel,
      questionText: data.questionText,
    });
  });

  it('should add a new option to the options array', () => {
    const initialLength = component.optionsArray.length;
    component.addOption();
    expect(component.optionsArray.length).toBe(initialLength + 1);
  });

  it('should validate the new option with required and no whitespace validators', () => {
    component.addOption();
    const control = component.optionsArray.at(0);
    control.setValue('');
    expect(control.errors).toEqual({ required: true, whitespace: true });
  });

  it('should add an option when the form is already valid', () => {
    component.optionsArray.push(new FormControl('Valid Option'));
    expect(component.optionsArray.valid).toBe(true);
    component.addOption();
    expect(component.optionsArray.length).toBe(2);
  });

  it('should add an option when the options array is empty', () => {
    expect(component.optionsArray.length).toBe(0);
    component.addOption();
    expect(component.optionsArray.length).toBe(1);
  });

  it('should add an option when the form is in an invalid state', () => {
    component.optionsArray.push(new FormControl('', Validators.required));
    expect(component.optionsArray.valid).toBe(false);
    component.addOption();
    expect(component.optionsArray.length).toBe(2);
  });

  it('should set buttonState to false when form is valid', () => {
    jest.spyOn(component, 'disableButton').mockReturnValue(false);
    component.checkFormValidity();
    expect(component.buttonState).toBe(false);
  });

  it('should set buttonState to true when form is invalid', () => {
    jest.spyOn(component, 'disableButton').mockReturnValue(true);
    component.checkFormValidity();
    expect(component.buttonState).toBe(true);
  });

  it('should set buttonState to true when form is empty', () => {
    component.optionsArray.push(new FormControl('', Validators.required));
    jest.spyOn(component, 'disableButton').mockReturnValue(true);
    component.checkFormValidity();
    expect(component.buttonState).toBe(true);
  });

  it('should prevent form submission when duplicate answers are present', () => {
    component.optionsArray.push(new FormControl('', Validators.required));
    component.optionsArray.push(new FormControl('', Validators.required));
    const event = { preventDefault: jest.fn() } as unknown as Event;
    component.optionsArray.setValue(['A', 'A']);

    component.onSubmit(event);

    expect(event.preventDefault).toHaveBeenCalled();
    expect(mockToastService.onShow).toHaveBeenCalledWith(
      'Answers',
      'Duplicate answer options',
      true,
      'error'
    );
  });

  it('should emit event with question data when editing a question', () => {
    const event = { preventDefault: jest.fn() } as unknown as Event;
    component.isEdit = true;
    component.submitEdittedQuestionEvent = {
      emit: jest.fn(),
    } as never as EventEmitter<unknown>;

    component.optionsArray.push(new FormControl('', Validators.required));
    component.optionsArray.push(new FormControl('', Validators.required));
    component.optionsArray.setValue(['A', 'B']);
    component.selectedAnswerIndex = 0;

    component.onSubmit(event);

    component.onSubmit(event);

    expect(component.submitEdittedQuestionEvent.emit).toHaveBeenCalledWith(
      expect.objectContaining({
        answerOptions: ['A', 'B'],
        correctAnswers: ['A'],
        questionType: 'Multiple_choice',
      })
    );
  });

  it('should trigger cancelQuestion event in questionService', () => {
    component.onCancel();
    expect(mockQuestionService.cancelQuestion.next).toHaveBeenCalledWith(true);
  });

  it('should set cancelQuestion event value to true', () => {
    component.onCancel();
    expect(mockQuestionService.cancelQuestion.next).toHaveBeenCalledWith(true);
  });

  it('should uncheck other radio buttons when one is selected', () => {
    component.optionsArray.push(new FormControl('', Validators.required));
    component.optionsArray.push(new FormControl('', Validators.required));
    component.optionsArray.push(new FormControl('', Validators.required));
    component.optionsArray.setValue(['A', 'B', 'C']);
    component.onRadioChange(1);
  });

  it('should add an option when "Space" key is pressed', () => {
    jest.spyOn(component, 'addOption');
    const event = new KeyboardEvent('keydown', { code: 'Space' });
    component.handleKeydown(event);
    expect(component.addOption).toHaveBeenCalled();
  });

  it('should call preventDefault when "Space" key is pressed', () => {
    const event = new KeyboardEvent('keydown', { code: 'Space' });
    jest.spyOn(event, 'preventDefault');
    component.handleKeydown(event);
    expect(event.preventDefault).toHaveBeenCalled();
  });

  it('should not add an option when non-"Space" key is pressed', () => {
    jest.spyOn(component, 'addOption');
    const event = new KeyboardEvent('keydown', { code: 'Enter' });
    component.handleKeydown(event);
    expect(component.addOption).not.toHaveBeenCalled();
  });

  it('should delete the option at the specified index', () => {
    component.optionsArray.push(new FormControl('', Validators.required));
    component.optionsArray.push(new FormControl('', Validators.required));
    component.optionsArray.setValue(['A', 'B']);
    component.deleteOption(0);
    expect(component.optionsArray.length).toBe(1);
  });

  it('should delete an option when the "delete" key is pressed', () => {
    const deleteOptionSpy = jest.spyOn(component, 'deleteOption');
    const event = new KeyboardEvent('keydown', { code: 'delete' });
    component.handleKeyDelete(event, 0);
    expect(deleteOptionSpy).toHaveBeenCalledWith(0);
  });

  it('should prevent default behavior when "delete" key is pressed', () => {
    const event = new KeyboardEvent('keydown', { code: 'delete' });
    const preventDefaultSpy = jest.spyOn(event, 'preventDefault');
    component.handleKeyDelete(event, 0);
    expect(preventDefaultSpy).toHaveBeenCalled();
  });

  it('should not throw error for non-"delete" key presses', () => {
    const event = new KeyboardEvent('keydown', { code: 'Enter' });
    expect(() => component.handleKeyDelete(event, 0)).not.toThrow();
  });

  it('should navigate to the correct URL when previousPage is called', () => {
    component.previousPage();
    expect(mockRouter.navigateByUrl).toHaveBeenCalledWith(
      '/dashboard/test-management/questions/all-questions'
    );
  });

  it('should trigger navigation without errors', () => {
    expect(() => component.previousPage()).not.toThrow();
  });

  it('should disable button when form is invalid', () => {
    component.form = {
      get: jest.fn().mockReturnValue({ length: 3 }),
      invalid: true,
    } as never as FormGroup;
    component.selectedAnswerIndex = 0;
    expect(component.disableButton()).toBe(true);
  });

  it('should disable button when form is invalid', () => {
    component.form = {
      get: jest.fn().mockReturnValue({ length: 3 }),
      invalid: false,
    } as never as FormGroup;
    component.selectedAnswerIndex = undefined;
    expect(component.disableButton()).toBe(true);
  });

  it('should enable button when conditions are met ', () => {
    component.form = {
      get: jest.fn().mockReturnValue({ length: 3 }),
      invalid: false,
    } as never as FormGroup;
    component.selectedAnswerIndex = 0;
    expect(component.disableButton()).toBe(false);
  });

  it('should retrieve a list of questions when service returns data', async () => {
    const mockQuestions = [
      { id: 1, text: 'Question 1' },
      { id: 2, text: 'Question 2' },
    ];

    mockQuestionService.getAllQuestions.mockResolvedValue(mockQuestions);
    component.loadQuestions();

    expect(mockQuestionService.getAllQuestions).toHaveBeenCalledWith(1, 10, {});
  });
});
