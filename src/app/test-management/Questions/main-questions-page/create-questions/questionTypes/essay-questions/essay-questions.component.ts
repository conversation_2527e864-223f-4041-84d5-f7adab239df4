import {
  AfterViewInit,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { BasicQuestionContainerComponent } from '../../../components/basic-question-container/basic-question-container.component';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { QuestionTitleComponent } from '../../../components/question-title/question-title.component';
import { CustomButtonComponent } from '../../../../../../components/custombutton/custombutton.component';
import { AddRubricEssayQuestionComponent } from './add-rubric-essay-question/add-rubric-essay-question.component';
import { QuestionsService } from '../../../../../../services/test-management-service/questions.service';
import { Router } from '@angular/router';
import {
  BasicQuestion,
  MessageStatus,
  QuestionMessage,
  QuestionsData,
  Rubrics,
} from '../../../../../../Interfaces/questionInterface';
import { Subscription } from 'rxjs';
import { ToastService } from '../../../../../../services/toast-service/toast.service';
import { PreviewDataComponent } from '../../../../../../components/preview-data/preview-data.component';
import { CustomModalComponent } from '../../../../../../components/custom-modal/custom-modal.component';
import { noWhitespaceTextareaValidator } from '../../../../../../utils/constants';
import { QuestionStore } from '../../../../../../stores/questions-store/question.store';
import { CustomDropdownComponent } from '../../../../../../components/custom-dropdown/custom-dropdown.component';

@Component({
  selector: 'app-essay-questions',
  standalone: true,
  imports: [
    BasicQuestionContainerComponent,
    QuestionTitleComponent,
    CustomButtonComponent,
    AddRubricEssayQuestionComponent,
    PreviewDataComponent,
    CustomModalComponent,
    CustomDropdownComponent,
  ],
  templateUrl: './essay-questions.component.html',
})
export class EssayQuestionsComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  showPreview = false;
  questionData!: QuestionsData;
  @Input() editEssayData: QuestionsData | undefined;
  @Output() submitEdittedEssayQuestion = new EventEmitter();
  form: FormGroup;
  @Input() title = 'Create New Question (Essay)';
  @Input() previousPageText = 'Back';
  page = 1;
  allRubricsArray: Rubrics[] = [];
  rubricsSubscription: Subscription | undefined;
  edit: boolean = false;
  buttonState: boolean = true;
  saveButton = false;
  subscription = new Subscription();
  isEditUrl = false;

  constructor(
    private readonly questionService: QuestionsService,
    private readonly router: Router,
    private readonly fb: FormBuilder,
    private readonly toast: ToastService,
    private readonly questionsStore: QuestionStore
  ) {
    this.form = this.fb.group({
      questionText: ['', [Validators.required, noWhitespaceTextareaValidator]],
      domain: ['', Validators.required],
      score: ['', [Validators.required, Validators.min(1)]],
      difficultyLevel: ['', Validators.required],
      category: ['', Validators.required],
      markingType: ['', Validators.required],
    });
  }

  ngAfterViewInit(): void {
    this.form.valueChanges.subscribe(() => {
      this.buttonState = false;
    });
  }

  ngOnInit(): void {
    this.isEditUrl = this.router.url.includes('edit');
    if (this.editEssayData && this.isEditUrl) {
      this.edit = true;
      this.populateForm(this.editEssayData as unknown as QuestionsData);
      this.allRubricsArray = this.questionService.convertRubricsStringToArray(
        this.editEssayData?.essayAnswer?.rubrics || ''
      ) as unknown as Rubrics[];

      this.questionService.rubricsData(this.allRubricsArray);
      this.questionService.totalRubricsScore(
        this.questionService.checkTotalScore(this.allRubricsArray)
      );
    }

    this.rubricsSubscription = this.questionService.rubricsData$.subscribe(
      (data) => {
        this.allRubricsArray = data;
      }
    );

    const title = this.edit ? MessageStatus.Edited : MessageStatus.Created;
    const message = this.edit
      ? QuestionMessage.EditSuccess
      : QuestionMessage.CreateSuccess;

    this.subscription.add(
      this.questionsStore.isQuestionsSuccess$.pipe().subscribe((isSuccess) => {
        if (isSuccess) {
          this.toast.onShow(title, message, true, 'success');
          this.saveButton = false;
          this.form.reset();
          this.questionsStore.resetSuccessState();
          if (this.edit) {
            this.router.navigateByUrl(
              `/dashboard/test-management/questions/all-questions`
            );
          }
        } else {
          this.saveButton = false;
        }
      })
    );
  }

  get totalQuestionScore() {
    return this.form.get('score')?.value;
  }
  populateForm(data: QuestionsData) {
    this.form.patchValue({
      domain: data.domain,
      category: data.category,
      score: data.score,
      difficultyLevel: data.difficultyLevel,
      questionText: data.questionText,
      markingType:
        data.essayAnswer && data.essayAnswer.rubrics !== ''
          ? 'Rubric-based AI Scoring'
          : 'AI-based Scoring',
    });
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
    if (this.rubricsSubscription) {
      this.rubricsSubscription.unsubscribe();
      this.questionService.rubricsData([]);
      this.questionService.totalRubricsScore(0);
    }
  }
  nextPage() {
    this.page = 2;
  }

  prevPage() {
    this.page = 1;
  }

  closePreview() {
    this.showPreview = false;
  }

  handleKeydownNextPage(event: KeyboardEvent) {
    if (event.code === 'enter') {
      event.preventDefault();
      this.nextPage();
    }
  }

  onCancel() {
    this.questionService.cancelQuestion.next(true);
  }

  previousPage() {
    this.router.navigate([
      '/dashboard/test-management/questions/all-questions',
    ]);
  }

  disableSubmit() {
    return this.form.invalid || this.allRubricsArray.length === 0;
  }

  editForm(data: QuestionsData) {
    this.submitEdittedEssayQuestion.emit(data);
  }

  onSaveCreatOrEditQuestion() {
    const essayQuestion = {
      domainId: this.form.get('domain')?.value.id,
      categoryId: this.form.get('category')?.value.id,
      score: this.form.get('score')?.value,
      difficultyLevel: this.form.get('difficultyLevel')?.value,
      questionText: this.form.get('questionText')?.value,
      questionType: 'Essay',
      rubrics:
        this.form.get('markingType')?.value === 'AI-based Scoring'
          ? ''
          : this.questionService.converArrayToRubricsString(
              this.allRubricsArray
            ),
    };

    if (
      this.questionService.checkTotalScore(this.allRubricsArray) !==
        Number(essayQuestion.score) &&
      this.getFormControl('markingType').value === 'Rubric-based AI Scoring'
    ) {
      this.toast.onShow(
        'Error',
        'Total rubrics score does not match the question score ',
        true,
        'error'
      );
      return;
    }

    if (this.edit) {
      this.questionService.essayQuestionData(essayQuestion);
      this.submitEdittedEssayQuestion.emit(essayQuestion);
    } else if (
      this.getFormControl('markingType').value === 'AI-based Scoring'
    ) {
      this.onSave(essayQuestion);
    }
  }

  onKeyDownSaveEssayQuestion(event: KeyboardEvent) {
    if (event.code === 'enter') {
      event.preventDefault();
      this.onSaveCreatOrEditQuestion();
    }
  }

  addRubricInfo() {
    this.page = 2;
    const essayQuestion = {
      domainId: this.form.get('domain')?.value.id,
      categoryId: this.form.get('category')?.value.id,
      score: this.form.get('score')?.value,
      difficultyLevel: this.form.get('difficultyLevel')?.value,
      questionText: this.form.get('questionText')?.value,
      questionType: 'Essay',
      rubrics: this.questionService.converArrayToRubricsString(
        this.allRubricsArray
      ),
    };
    this.questionService.essayQuestionData(essayQuestion);
  }

  onSave(essayQuestion: BasicQuestion) {
    if (
      this.questionService.totalRubricsScore$.value !==
        Number(essayQuestion.score) &&
      this.getFormControl('markingType').value === 'Rubric-based AI Scoring'
    ) {
      this.toast.onShow(
        'Error',
        'Total rubrics score does not match the question score ',
        true,
        'error'
      );
      return;
    }
    this.saveButton = true;

    this.subscription.add(
      this.questionsStore.createQuestion({ question: essayQuestion })
    );
  }

  preview() {
    this.showPreview = true;

    this.subscription.add(
      this.questionService.preview(this.allRubricsArray).subscribe((data) => {
        this.questionData = data;
      })
    );
  }

  getFormControl(controlName: string): FormControl {
    return this.form.get(controlName) as FormControl;
  }
}
