import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  HostListener,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { BasicQuestionContainerComponent } from '../../../components/basic-question-container/basic-question-container.component';

import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { QuestionCreationServiceService } from '../../../../../../services/test-management-service/question-creation-service.service';
import {
  BasicQuestion,
  MessageStatus,
  QuestionMessage,
  QuestionsData,
} from '../../../../../../Interfaces/questionInterface';
import { ToastService } from '../../../../../../services/toast-service/toast.service';
import { QuestionsService } from '../../../../../../services/test-management-service/questions.service';
import { QuestionTitleComponent } from '../../../components/question-title/question-title.component';
import { Router } from '@angular/router';
import { hasDuplicateAnswers } from '../../../../../../utils/questionsConstants';
import {
  noWhitespaceTextareaValidator,
  noWhitespaceValidator,
} from '../../../../../../utils/constants';
import { CustomButtonComponent } from '../../../../../../components/custombutton/custombutton.component';
import { QuestionStore } from '../../../../../../stores/questions-store/question.store';
import { Subscription } from 'rxjs';
import { AnswerOptionsComponent } from '../../../../../../components/answer-options/answer-options.component';
import { FormFactoryService } from '../../../../../../services/form-factory-service/form-factory.service';

@Component({
  selector: 'app-multi-select-questions',
  standalone: true,
  imports: [
    BasicQuestionContainerComponent,
    ReactiveFormsModule,
    QuestionTitleComponent,
    CustomButtonComponent,
    AnswerOptionsComponent,
  ],
  templateUrl: './multi-select-questions.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MultiSelectQuestionsComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  form: FormGroup;
  isEdit: boolean = false;
  @Input() title = 'Create New Question(Multiple-Select)';
  @Input() previousPageText = 'Back';
  @Input() editMultiSelectData: QuestionsData | undefined;
  @Output() submitEditedQuestionEvent = new EventEmitter();
  selectedAnswerIndex: number | undefined;
  optionBlockArray: {
    check: FormControl<string | null> | boolean;
    value: FormControl<string | null>;
  }[] = [];
  buttonState: boolean = true;
  STORAGE_KEY = 'multiSelectQuestionForm';
  subscriptions: Subscription[] = [];

  constructor(
    private readonly fb: FormBuilder,
    private readonly questionCreationService: QuestionCreationServiceService,
    private readonly questionService: QuestionsService,
    private readonly toast: ToastService,
    private readonly router: Router,
    private readonly questionsStore: QuestionStore,
    private readonly formFactory: FormFactoryService,
    private readonly cdr: ChangeDetectorRef
  ) {
    this.form = this.formFactory.createQuestionForm({
      strictMark: new FormControl('', Validators.required),
    });
  }

  ngAfterViewInit(): void {
    this.form.valueChanges.subscribe(() => {
      this.buttonState = false;
    });
  }

  STORAGE_KEY_EDIT = 'multiSelectQuestionFormEdit';

  ngOnInit(): void {
    this.isEdit = this.router.url.includes('edit');
    const title = this.isEdit ? MessageStatus.Edited : MessageStatus.Created;
    const message = this.isEdit
      ? QuestionMessage.EditSuccess
      : QuestionMessage.CreateSuccess;

    this.subscriptions.push(
      this.questionsStore.isQuestionsSuccess$.pipe().subscribe((isSuccess) => {
        if (isSuccess) {
          this.toast.onShow(title, message, true, 'success');
          this.resetFormCompletely();

          this.questionsStore.resetSuccessState();
          if (this.isEdit) {
            this.router.navigateByUrl(
              `/dashboard/test-management/questions/all-questions`
            );
          }
        }
      })
    );

    if (this.isEdit) {
      const savedEditData = localStorage.getItem(this.STORAGE_KEY_EDIT);
      if (savedEditData) {
        const parsedEditData = JSON.parse(savedEditData);
        this.populateForm(parsedEditData);
      } else if (this.editMultiSelectData) {
        const existingQuestionData = this.prepareEditData(
          this.editMultiSelectData
        );
        this.populateForm(existingQuestionData);
      }
    } else {
      const savedData = localStorage.getItem(this.STORAGE_KEY);
      if (savedData) {
        const formData = JSON.parse(savedData);
        this.populateForm(formData);
      }
    }

    localStorage.removeItem(this.STORAGE_KEY);
    localStorage.removeItem(this.STORAGE_KEY_EDIT);

    setTimeout(() => {
      this.checkFormValidity();
    });
  }

  private resetFormCompletely(): void {
    const answerOptions = this.form.get('answerOptions') as FormArray;
    while (answerOptions.length !== 0) {
      answerOptions.removeAt(0);
    }
    this.optionBlockArray = [];
    this.form.reset();
    this.selectedAnswerIndex = undefined;
    this.buttonState = true;
    this.cdr.detectChanges();
    this.form.updateValueAndValidity();
    this.checkFormValidity();
  }

  populateForm(data: QuestionsData) {
    if (!data) return;

    const answerOptions = this.form.get('answerOptions') as FormArray;
    answerOptions.clear();
    this.optionBlockArray = [];

    const formStrictMark =
      typeof data.strictMark === 'boolean'
        ? {
            name: data.strictMark ? 'Yes' : 'No',
            value: data.strictMark ? 'true' : 'false',
          }
        : data.strictMark;

    this.form.patchValue({
      domain: data.domain,
      category: data.category,
      score: data.score,
      difficultyLevel: data.difficultyLevel,
      questionText: data.questionText,
      strictMark: formStrictMark,
    });

    const options = data.answerOptions ?? [];
    const correctAnswers = data.correctAnswers ?? [];

    options.forEach((option: string) => {
      const isAnswer = correctAnswers.includes(option);

      const optionGroup = this.fb.group({
        check: [isAnswer],
        inputValue: [option, [Validators.required, noWhitespaceValidator]],
      });

      answerOptions.push(optionGroup);

      this.optionBlockArray.push({
        check: optionGroup.get('check') as FormControl,
        value: optionGroup.get('inputValue') as FormControl,
      });
    });

    this.form.updateValueAndValidity();
    this.checkFormValidity();

    this.cdr.detectChanges();
  }

  checkFormValidity() {
    this.buttonState = this.disableButton();
  }

  prepareEditData(data: QuestionsData): QuestionsData {
    const strictMarkValue =
      typeof data.strictMark === 'boolean'
        ? data.strictMark
        : data.strictMark?.value === 'true';

    const preparedData: QuestionsData = {
      ...data,
      strictMark: {
        name: strictMarkValue ? 'Yes' : 'No',
        value: strictMarkValue ? 'true' : 'false',
      },
      answerOptions: this.isEdit
        ? (data.multipleSelectAnswer?.options ?? [])
        : data.answerOptions || [],
      correctAnswers: this.isEdit
        ? (data.multipleSelectAnswer?.answer ?? [])
        : (data.correctAnswers ?? []),
    };

    return preparedData;
  }

  isChecked(index: number) {
    const optionValue = this.optionsArray.at(index).value;
    const actualSelectedAnswers = this.optionBlockArray
      .filter(
        (optionBlock) =>
          optionBlock.check === true && optionBlock.value.value !== null
      )
      .map((optionBlock) => optionBlock.value.value);

    return actualSelectedAnswers.includes(optionValue);
  }

  addOption() {
    const currentOptions = this.form.get('answerOptions') as FormArray;

    const newOption = this.fb.group({
      check: [false, Validators.required],
      inputValue: ['', [Validators.required, noWhitespaceTextareaValidator]],
    });

    const updatedControls = [...currentOptions.controls, newOption];

    this.form.setControl('answerOptions', this.fb.array(updatedControls));

    // Update optionBlockArray to match
    this.optionBlockArray.push({
      check: newOption.get('check') as FormControl,
      value: newOption.get('inputValue') as FormControl,
    });

    // Trigger change detection
    this.cdr.detectChanges();
  }

  get optionsArray(): FormArray {
    return this.form.get('answerOptions') as FormArray;
  }

  onSubmit(event: Event) {
    event.preventDefault();
    if (
      hasDuplicateAnswers(
        this.optionsArray.controls.map((control) => control.value.inputValue)
      )
    ) {
      this.toast.onShow('Answers', `Duplicate answer options`, true, 'error');
      return;
    }
    const multiSelectQuestionForm = {
      domainId: this.form.get('domain')?.value.id,
      categoryId: this.form.get('category')?.value.id,
      score: this.form.get('score')?.value,
      difficultyLevel: this.form.get('difficultyLevel')?.value,
      questionText: this.form.get('questionText')?.value,
      strictMark: this.form.get('strictMark')?.value.value,
      questionType: 'Multi_select',
      answerOptions: this.getAnswerOption(),
      correctAnswers: this.getCorrectAnswer(),
    };
    if (this.isEdit) {
      this.submitFormForEditMultiSelect(multiSelectQuestionForm);
    } else {
      this.submitMultiSelectQuestionForm(multiSelectQuestionForm);
    }
  }

  submitFormForEditMultiSelect(editedData: BasicQuestion) {
    this.submitEditedQuestionEvent.emit(editedData);
  }

  getAnswerOption(): string[] {
    return this.optionsArray.value.map(
      (optionBlock: { inputValue: string }) => optionBlock.inputValue
    );
  }

  getCorrectAnswer(): string[] {
    return this.optionsArray.value
      .filter(
        (optionBlock: { check: boolean; inputValue: string }) =>
          optionBlock.check && optionBlock.inputValue
      )
      .map((optionBlock: { inputValue: string }) => optionBlock.inputValue);
  }

  submitMultiSelectQuestionForm(multiSelectQuestionForm: BasicQuestion) {
    this.questionsStore.createQuestion({ question: multiSelectQuestionForm });
  }

  onCancel() {
    this.questionService.cancelQuestion.next(true);
  }

  handleKeyCancel(event: KeyboardEvent) {
    if (event.code === 'enter') {
      event.preventDefault();
      this.onCancel();
    }
  }

  handleKeydownAddOption(event: KeyboardEvent) {
    if (event.code === 'Space') {
      event.preventDefault();
      this.addOption();
    }
  }

  deleteOption(index: number) {
    this.optionsArray.removeAt(index);
    this.optionBlockArray.splice(index, 1);

    this.cdr.detectChanges();
    this.checkFormValidity();
  }

  previousPage() {
    this.router.navigateByUrl(
      `/dashboard/test-management/questions/all-questions`
    );
  }

  disableButton() {
    return (
      this.form.invalid ||
      this.optionsArray.length === 0 ||
      this.getCorrectAnswer().length === 0
    );
  }

  loadQuestions() {
    this.questionService.getAllQuestions(1, 10, {});
  }

  @HostListener('window:beforeunload')
  saveFormToLocalStorage() {
    const formValue = this.form.getRawValue();
    const answerOptions = this.getAnswerOption();
    const correctAnswers = this.getCorrectAnswer();

    const formData: QuestionsData = {
      ...this.editMultiSelectData,
      ...formValue,
      answerOptions,
      correctAnswers,
      strictMark: formValue.strictMark?.value === 'true',
    };

    if (this.isEdit) {
      localStorage.setItem(this.STORAGE_KEY_EDIT, JSON.stringify(formData));
    } else {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(formData));
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => subscription.unsubscribe());
    localStorage.removeItem(this.STORAGE_KEY);
  }
}
