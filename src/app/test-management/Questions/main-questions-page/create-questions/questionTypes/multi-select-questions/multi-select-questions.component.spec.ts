import { ComponentFixture, TestBed } from '@angular/core/testing';

import { MultiSelectQuestionsComponent } from './multi-select-questions.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { CustomButtonComponent } from '../../../../../../components/custombutton/custombutton.component';
import { BasicQuestionContainerComponent } from '../../../components/basic-question-container/basic-question-container.component';
import { QuestionsData } from '../../../../../../Interfaces/questionInterface';
import { QuestionCreationServiceService } from '../../../../../../services/test-management-service/question-creation-service.service';
import { ToastService } from '../../../../../../services/toast-service/toast.service';
import { Router } from '@angular/router';
import { FormArray, FormControl } from '@angular/forms';
import { fillInTypeDataMock } from '../../../../../../../../__mocks__/fillInTheBlanksMock';

describe('MultiSelectQuestionsComponent', () => {
  let component: MultiSelectQuestionsComponent;
  let fixture: ComponentFixture<MultiSelectQuestionsComponent>;

  const mockQuestionCreationService = {
    createQuestion: jest.fn().mockReturnValue({
      subscribe: jest.fn().mockImplementation((callbacks) => {
        callbacks.next();
      }),
    }),
  };
  const mockToast = {
    onShow: jest.fn(),
  };

  const mockRouter = {
    url: '/edit',
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        MultiSelectQuestionsComponent,
        HttpClientTestingModule,
        CustomButtonComponent,
        BasicQuestionContainerComponent,
      ],
      providers: [
        {
          provide: QuestionCreationServiceService,
          useValue: mockQuestionCreationService,
        },
        { provide: ToastService, useValue: mockToast },
        { provide: Router, useValue: mockRouter },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(MultiSelectQuestionsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  describe('Html layout', () => {
    it('should display three buttons', () => {
      const pageButtons =
        fixture.debugElement.nativeElement.querySelectorAll('app-custombutton');
      expect(pageButtons).toBeTruthy();
      expect(pageButtons.length).toBe(3);
    });

    it('should display the basic container for question creation', () => {
      const basicQuestionContainer =
        fixture.debugElement.nativeElement.querySelectorAll(
          'app-basic-question-container'
        );
      expect(basicQuestionContainer).toBeTruthy();
      expect(basicQuestionContainer.length).toBe(1);
    });
  });

  describe('page methods', () => {
    it('should set buttonState to true when disableButton returns true', () => {
      jest.spyOn(component, 'disableButton').mockReturnValue(true);
      component.checkFormValidity();
      expect(component.buttonState).toBe(true);
    });
    it('should convert boolean strictMark to object with name and value', () => {
      const data: QuestionsData = {
        id: '1',
        questionText: 'Sample Question',
        questionType: 'multi-select',
        score: 5,
        system: false,
        timeLimit: '60s',
        difficultyLevel: 'easy',
        essayAnswer: { rubrics: 'Sample Rubric' },
        fillInAnswer: fillInTypeDataMock,
        strictMark: true,
        isActive: true,
        category: { id: 'cat1', name: 'Category 1' },
        domain: { id: 'dom1', name: 'Domain 1' },
        answerOptions: [],
      };
      const result = component.prepareEditData(data);
      expect(result.strictMark).toEqual({ name: 'Yes', value: 'true' });
    });
    it('should prevent default form submission when onSubmit is called', () => {
      const event = { preventDefault: jest.fn() } as unknown as Event;
      component.onSubmit(event);
      expect(event.preventDefault).toHaveBeenCalled();
    });

    it('should successfully submit a valid multi-select question form', () => {
      const mockData = {
        domainId: '1',
        categoryId: '2',
        score: 10,
        difficultyLevel: 'Medium',
        questionText: 'What is the capital of France?',
        strictMark: true,
        questionType: 'Multi_select',
        answerOptions: ['Paris', 'London', 'Berlin', 'Rome'],
        correctAnswers: ['1', '2'],
      };

      component.submitMultiSelectQuestionForm(mockData);
      expect(mockQuestionCreationService.createQuestion).toHaveBeenCalled();
    });

    it('should set isEdit to true when URL contains "edit"', () => {
      component.ngOnInit();
      expect(component.isEdit).toBe(true);
    });

    it('should populate the form correctly with provided data', () => {
      const data: QuestionsData = {
        domain: { id: '1', name: 'Math' },
        category: { id: '1', name: 'Algebra' },
        score: 5,
        difficultyLevel: 'Medium',
        questionText: 'What is 2 + 2?',
        strictMark: true,
        answerOptions: ['2', '3', '4', '5'],
        correctAnswers: ['4'],
        id: '',
        questionType: '',
        system: false,
        timeLimit: '',
        essayAnswer: {
          rubrics: '',
        },
        fillInAnswer: fillInTypeDataMock,
        isActive: false,
      };

      component.populateForm(data);

      const formValue = component.form.value;
      expect(formValue.domain).toEqual(data.domain);
      expect(formValue.category).toEqual(data.category);
      expect(formValue.score).toEqual(data.score);
      expect(formValue.difficultyLevel).toEqual(data.difficultyLevel);
      expect(formValue.questionText).toEqual(data.questionText);
      expect(formValue.strictMark).toEqual({
        name: 'Yes',
        value: 'true',
      });

      const answerOptions = component.form.get('answerOptions') as FormArray;
      expect(answerOptions.length).toBe(4);

      data.answerOptions?.forEach((option, index) => {
        const optionGroup = answerOptions.at(index);
        const expectedIsAnswer = data.correctAnswers?.includes(option);

        expect(optionGroup.get('check')?.value).toBe(expectedIsAnswer);
        expect(optionGroup.get('inputValue')?.value).toBe(option);
      });

      expect(component.optionBlockArray.length).toBe(4);
      component.optionBlockArray.forEach((block, index) => {
        const expectedIsAnswer = data.correctAnswers?.includes(
          data.answerOptions[index]
        );
        const checkControl = block.check as FormControl;

        expect(checkControl.value).toBe(expectedIsAnswer);
        expect(block.value.value).toBe(data.answerOptions[index]);
      });
    });

    describe('prepareEditData method', () => {
      it('should prepare data correctly when in edit mode', () => {
        component.isEdit = true;

        const data: QuestionsData = {
          domain: { id: '1', name: 'Math' },
          category: { id: '1', name: 'Algebra' },
          score: 5,
          difficultyLevel: 'Medium',
          questionText: 'What is 2 + 2?',
          strictMark: { name: 'Yes', value: 'true' },
          multipleSelectAnswer: {
            id: '1',
            questionId: 'q1',
            options: ['2', '3', '4', '5'],
            answer: ['4'],
          },
          id: '',
          answerOptions: ['2', '3', '4', '5'],
          questionType: '',
          system: false,
          timeLimit: '',
          essayAnswer: {
            rubrics: '',
          },
          fillInAnswer: fillInTypeDataMock,
          isActive: false,
        };

        const result = component.prepareEditData(data);
        expect(result.strictMark).toEqual({
          name: 'Yes',
          value: 'true',
        });
        expect(result.answerOptions).toEqual(['2', '3', '4', '5']);
        expect(result.correctAnswers).toEqual(['4']);
      });

      it('should prepare data correctly when not in edit mode', () => {
        component.isEdit = false;

        const data: QuestionsData = {
          domain: { id: '1', name: 'Math' },
          category: { id: '1', name: 'Algebra' },
          score: 5,
          difficultyLevel: 'Medium',
          questionText: 'What is 2 + 2?',
          strictMark: true,
          answerOptions: ['1', '2', '4', '5'],
          correctAnswers: ['4'],
          id: '',
          questionType: '',
          system: false,
          timeLimit: '',
          essayAnswer: {
            rubrics: '',
          },
          fillInAnswer: fillInTypeDataMock,
          isActive: false,
        };

        const result = component.prepareEditData(data);
        expect(result.strictMark).toEqual({
          name: 'Yes',
          value: 'true',
        });
        expect(result.answerOptions).toEqual(['1', '2', '4', '5']);
        expect(result.correctAnswers).toEqual(['4']);
      });
    });
  });
});
