import {
  ComponentFixture,
  fakeAsync,
  TestBed,
  tick,
} from '@angular/core/testing';
import { CodingQuestionsComponent } from './coding-questions.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ActivatedRoute, Router } from '@angular/router';
import { of } from 'rxjs';
import { CodeExecutionService } from '../../../../../../services/code-execution.service';
import { ToastService } from '../../../../../../services/toast-service/toast.service';
import { CodingStore } from '../../../../../../stores/coding-store/coding.store';
import { CodeEditorComponent } from '../../../../../../components/code-editor/code-editor.component';
import { ElementRef } from '@angular/core';
import { MonacoLoaderService } from '../../../../../../services/monaco-loader.service';

describe('CodingQuestionsComponent', () => {
  let component: CodingQuestionsComponent;
  let fixture: ComponentFixture<CodingQuestionsComponent>;

  const mockRouter = {
    url: '/dashboard/test-management/questions/edit',
    navigate: jest.fn(),
    navigateByUrl: jest.fn(),
  };

  const mockCodingStore = {
    questionState$: of({
      basicInfo: {
        questionText: 'Test Question',
        score: 5,
        questionType: 'Code',
        difficultyLevel: 'Easy',
        domain: 'Math',
        category: 'General',
        questionTitle: 'Title',
      },
      language: {
        languageName: 'Javascript',
        judge0Id: 63,
        monacoValue: 'javascript',
      },
      referenceSolution: 'function test() {}',
      codeTemplate: 'function template() {}',
      testCases: [],
      codeConstraints: {
        timeComplexity: 'O(n)',
        spaceComplexity: 'O(1)',
        timeLimit: '1000',
        memoryLimit: '256',
      },
    }),
    updateAllState: jest.fn(),
  };

  const mockExecutionService = {
    runCode: jest.fn(() => of({ data: { output: '42' } })),
  };

  const mockToastService = {
    showErrorToast: jest.fn(),
  };

  const mockActivatedRoute = {
    queryParams: of({ questionId: '123' }),
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CodingQuestionsComponent, HttpClientTestingModule],
      providers: [
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: Router, useValue: mockRouter },
        { provide: CodingStore, useValue: mockCodingStore },
        { provide: CodeExecutionService, useValue: mockExecutionService },
        { provide: ToastService, useValue: mockToastService },
        {
          provide: MonacoLoaderService,
          useValue: {
            loadMonaco: jest.fn(() => Promise.resolve({})),
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(CodingQuestionsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize the form with values from the store', () => {
    expect(component.form.value.questionText).toBe('Test Question');
    expect(component.solutionCode).toBe('function test() {}');
  });

  it('should add a new test case', () => {
    const initialLength = component.testCases.length;
    component.addTestCase();
    expect(component.testCases.length).toBe(initialLength + 1);
  });

  it('should switch code views', fakeAsync(() => {
    component.codeEditor = {
      getValue: jest.fn(() => 'current code'),
      setValue: jest.fn(),
    } as unknown as CodeEditorComponent;

    fixture.detectChanges();

    component.switchCodeView('boilerplate');
    tick();

    fixture.detectChanges();
    expect(component.currentEditorView).toBe('boilerplate');
    expect(component.codeEditor.setValue).toHaveBeenCalled();
  }));

  it('should run a test case and update output', fakeAsync(() => {
    component.solutionCode = 'function sum(a, b) { return a + b; }';
    component.testCases = [
      { input: '1,2', output: '', visibility: 'private', weight: 1 },
    ];
    component.getFormControl('language').setValue({ judge0Id: 63 });

    component.runTestCase(0);
    tick();

    expect(component.testCases[0].output).toBe('42');
  }));

  it('should add a test case with default values', () => {
    component.testCases = [];
    component.addTestCase();

    expect(component.testCases[0]).toEqual({
      input: '',
      output: '',
      visibility: 'private',
      weight: 1,
    });
  });

  it('should trigger preview with valid test cases', () => {
    component.testCases = [
      { input: '1', output: '2', visibility: 'public', weight: 1 },
    ];
    component.form.patchValue({
      questionText: 'What is 1+1?',
      score: 5,
      difficultyLevel: 'Easy',
      domain: 'Math',
      category: 'Logic',
      questionTitle: 'Simple Math',
      language: {
        languageName: 'Javascript',
        judge0Id: 63,
        monacoValue: 'javascript',
      },
    });

    component.preview();

    expect(mockCodingStore.updateAllState).toHaveBeenCalled();
    expect(mockRouter.navigate).toHaveBeenCalled();
  });
  it('should remove a test case', fakeAsync(() => {
    component.testCases = [
      { input: '1', output: '2', visibility: 'private', weight: 1 },
      { input: '2', output: '4', visibility: 'private', weight: 1 },
    ];
    component.selectedTab = 1;

    component.removeTestCase(1);
    tick();

    expect(component.testCases.length).toBe(1);
    expect(component.selectedTab).toBe(0);
  }));
  it('should update a test case at a specific index', () => {
    component.testCases = [
      { input: '1', output: '2', visibility: 'private', weight: 1 },
    ];

    component.updateTestCase(0, {
      input: '3',
      output: '6',
      visibility: 'public',
      weight: 2,
    });

    expect(component.testCases[0]).toEqual({
      input: '3',
      output: '6',
      visibility: 'public',
      weight: 2,
    });
  });
  it('should warn if code editor is not initialized in loadSelectedCode', () => {
    const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
    component.codeEditor = null as unknown as CodeEditorComponent;
    component['loadSelectedCode']();
    expect(consoleSpy).toHaveBeenCalledWith('Code editor not initialized yet.');
    consoleSpy.mockRestore();
  });

  // utils/extract-values.spec.ts

  describe('extractValuesFromDeclarations', () => {
    it('should handle array and object destructuring', () => {
      const input = 'let [a, b] = [1, 2], {x, y} = {x: 10, y: 20}';
      expect(component.extractValuesFromDeclarations(input)).toBe(
        '[1, 2], {x: 10, y: 20}'
      );
    });

    it('should ignore missing values', () => {
      const input = 'var a = 5, b';
      expect(component.extractValuesFromDeclarations(input)).toBe('5, b');
    });

    it('should handle extra spaces and newlines', () => {
      const input = `const   a = 42 ,
      b = 'test' , c= { foo: "bar" }`;
      expect(component.extractValuesFromDeclarations(input)).toBe(
        '42, \'test\', { foo: "bar" }'
      );
    });

    it('should work even without let/const/var', () => {
      const input = 'x = 1, y = 2';
      expect(component.extractValuesFromDeclarations(input)).toBe('1, 2');
    });
  });

  // utils/stringify-unquoted.spec.ts

  describe('stringifyUnquotedWords', () => {
    it('should quote unquoted words', () => {
      const input = `apple banana cherry`;
      expect(component.stringifyUnquotedWords(input)).toBe(
        `"apple" "banana" "cherry"`
      );
    });

    it('should not quote JS literals', () => {
      const input = `true false null undefined`;
      expect(component.stringifyUnquotedWords(input)).toBe(
        `true false null undefined`
      );
    });

    it('should trim leading and trailing whitespace', () => {
      const input = `   key: value   `;
      expect(component.stringifyUnquotedWords(input)).toBe(`"key": "value"`);
    });
  });

  it('should reset scrollOffset if scroll buttons are not needed', () => {
    component.testCases = [
      { input: 'x', output: '1', visibility: 'private', weight: 1 },
      { input: 'y', output: '2', visibility: 'private', weight: 1 },
    ];
    component.selectedTab = 1;
    component.scrollOffset = 100;

    // mock shouldShowScrollButtons
    component.shouldShowScrollButtons = jest.fn(() => false);

    component.removeTestCase(1);

    expect(component.scrollOffset).toBe(0);
  });

  describe('scrollToMakeTabVisible', () => {
    beforeEach(() => {
      // Mock container element
      component.testCasesContainer = {
        nativeElement: {
          clientWidth: 300, // 3 tabs visible at a time if tabWidth = 100
        },
      } as unknown as ElementRef;

      component.tabWidth = 100;
      component.testCases = [
        { input: '', output: '', visibility: 'private', weight: 1 },
        { input: '', output: '', visibility: 'private', weight: 1 },
        { input: '', output: '', visibility: 'private', weight: 1 },
        { input: '', output: '', visibility: 'private', weight: 1 },
        { input: '', output: '', visibility: 'private', weight: 1 },
      ];

      component.getMaxScrollOffset = jest.fn(() => 200); // max scroll range
    });

    it('should not scroll if the tab is already fully visible', () => {
      component.scrollOffset = 0; // starting from the beginning
      component.scrollToMakeTabVisible(2); // tab index 2, within 0-300px
      expect(component.scrollOffset).toBe(0);
    });

    it('should scroll left if tab is before visible area', () => {
      component.scrollOffset = -200; // scrolled far right
      component.scrollToMakeTabVisible(0); // tab 0 not visible
      expect(component.scrollOffset).toBe(-0); // scrolls to beginning
    });

    it('should scroll right if tab is beyond visible area', () => {
      component.scrollOffset = 0;
      component.scrollToMakeTabVisible(4); // tab 4 is at 400-500px
      // should scroll to bring tab 4 into view
      expect(component.scrollOffset).toBe(-200);
    });

    it('should clamp scroll offset to max scroll range', () => {
      component.scrollOffset = 0;
      component.getMaxScrollOffset = jest.fn(() => 150);
      component.scrollToMakeTabVisible(4); // tab 4 is at 400-500

      component.scrollToMakeTabVisible(4);
      expect(component.scrollOffset).toBe(-150); // clamped
    });

    it('should do nothing if testCasesContainer or testCases is empty', () => {
      component.testCasesContainer = null as unknown as ElementRef;
      component.scrollOffset = -50;
      component.testCases = [];
      component.scrollToMakeTabVisible(2);
      expect(component.scrollOffset).toBe(-50);
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
    fixture.destroy();
    component.ngOnDestroy();
    fixture.destroy();
  });
});
