<app-question-title
  [questionType]="'Back'"
  (backRoute)="previousPage()"
  [newDesign]="true"
></app-question-title>

<div class="flex justify-between flex-wrap gap-4 mb-6">
  <div class="flex flex-col gap-2">
    <h3 class="flex text-xl text-[#0C4767] font-medium">
      {{ title }}&nbsp;<span
        class="flex justify-center w-[67px] h-[30px] border border-[#FFA500] rounded-[99px]"
        ><span class="text-[#FFA500] text-[16px] font-semibold">
          Beta
        </span></span
      >
    </h3>
    <p class="text-sm text-[#082230CC]">
      {{ titleDescription }}
    </p>
  </div>
  <div class="flex items-center gap-2">
    <app-custombutton
      [variant]="'secondary'"
      (clicked)="displayGenerateModal()"
    >
      <span class="flex items-center gap-2">
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M8 1L7.48415 2.39405C6.80774 4.222 6.46953 5.136 5.80278 5.8028C5.13603 6.4695 4.22204 6.8077 2.39405 7.4842L1 8L2.39405 8.5158C4.22204 9.1923 5.13603 9.5305 5.80278 10.1972C6.46953 10.864 6.80774 11.778 7.48415 13.6059L8 15L8.5158 13.6059C9.1923 11.778 9.5305 10.864 10.1972 10.1972C10.864 9.5305 11.778 9.1923 13.6059 8.5158L15 8L13.6059 7.4842C11.778 6.8077 10.864 6.4695 10.1972 5.8028C9.5305 5.136 9.1923 4.222 8.5158 2.39405L8 1Z"
            stroke="currentColor"
            stroke-width="1.75"
            stroke-linejoin="round"
          />
        </svg>

        <span>Generate with AI</span>
      </span>
    </app-custombutton>

    <app-custombutton (clicked)="preview()" [disabled]="form.invalid"
      >Preview and Save</app-custombutton
    >
  </div>
</div>

<app-dashboard-navbar
  [showLine]="false"
  [navbarData]="codingView"
  [isTestMode]="true"
  [isCodingMode]="true"
></app-dashboard-navbar>

<div class="xl:grid xl:grid-cols-2 gap-6 my-6">
  <span class="font-semibold text-[#082230CC] text-[1.12rem]"
    >Question Details</span
  >
  <span class="font-semibold text-[#082230CC] text-[1.12rem]"
    >Solution Details</span
  >
</div>
<main class="xl:grid xl:grid-cols-2 gap-6">
  <section class="flex-1 bg-white border-2 border-[#0822301A] rounded-lg p-4">
    <span class="font-medium text-[#262626]"> Question Title </span>
    <div class="my-2">
      <app-custom-input
        [placeholder]="'Please enter a question title'"
        [formControl]="getFormControl('questionTitle')"
      ></app-custom-input>
    </div>
    <app-basic-question-container
      [form]="form"
      [showEditor]="false"
      [addPadding]="false"
    ></app-basic-question-container>
    <div class="mt-5">
      <p class="question-details flex items-center gap-2 mb-5">
        <span class="font-medium text-[#262626]">Question</span>
        <app-required-svg />
      </p>
      <app-custom-text-editor
        [placeholder]="'Enter question here'"
        [formControl]="getFormControl('questionText')"
        [maxHeight]="'18rem'"
      ></app-custom-text-editor>
    </div>
  </section>

  <section
    class="flex-1 text-[1.125rem] border-2 bg-white border-[#0822301A] rounded-lg"
  >
    <div class="bg-[#F3F4F6] w-full p-4 rounded-t-lg">
      <div class="flex justify-between gap-2 lg:gap-10 flex-wrap items-center">
        <app-dashboard-navbar
          [navBgColor]="'transparent'"
          [showLine]="false"
          [navbarData]="codingNav"
          [isCodingMode]="false"
        ></app-dashboard-navbar>

        <app-theme-toggle
          [currentTheme$]="themeService.theme$"
          (themeToggled)="toggleTheme()"
        ></app-theme-toggle>
      </div>
    </div>
    <div class="p-4">
      <div class="flex justify-end items-center gap-2 mb-4">
        <app-custombutton
          (clicked)="generateSolutionOrBoilerplate()"
          [disabled]="isGeneratingSolutionOrBoilerplate"
          [spinner]="isGeneratingSolutionOrBoilerplate"
          >{{ isGeneratingSolutionOrBoilerplate ? 'Generating' : 'Generate' }}
          {{ currentEditorView }} Code</app-custombutton
        >
        <app-custom-dropdown
          [placeholder]="'Select Language'"
          [options]="languageOptions"
          [displayKey]="'languageName'"
          [formControl]="getFormControl('language')"
        ></app-custom-dropdown>
      </div>

      <div
        class="border border-gray-300 rounded-lg p-1 mt-4 bg-white transition mx-auto h-[34.3rem]"
      >
        <app-code-editor
          [language]="getFormControl('language').value.monacoValue"
          (codeChange)="onCodeChanged($event)"
          #codeEditor
        ></app-code-editor>
      </div>
    </div>
  </section>
</main>

<section class="xl:min-w-[45%] mt-6">
  <div class="flex justify-between items-center">
    <h2 class="text-[#082230E5] text-lg mb-3 mt-7 font-medium">Test Cases</h2>
    <!-- Add Test Case Button -->
    <div class="mt-4 flex justify-start gap-3 mb-2">
      <button
        class="bg-white text-[#082230CC] font-semibold px-4 py-2 rounded-md"
        (click)="addTestCase()"
      >
        + Add Test Case
      </button>
      <app-custombutton
        [variant]="'secondary'"
        (clicked)="openGenerateTestCasesModal()"
        >Generate Test Cases</app-custombutton
      >
    </div>
  </div>

  <section class="bg-white rounded-xl shadow-sm" *ngIf="testCases.length > 0">
    @if (testCases.length > 0) {
      <div class="rounded-t-xl mt-4 bg-[#0822300A] flex items-center">
        <div class="rounded-t-xl flex-1 overflow-hidden">
          <div
            #testCasesContainer
            class="flex space-x-2 transition-transform duration-300 ease-in-out"
            [style.transform]="'translateX(' + scrollOffset + 'px)'"
          >
            <button
              *ngFor="let testCase of testCases; let i = index"
              class="px-4 py-3 font-semibold text-sm border-b-2 min-w-[88px] whitespace-nowrap flex-shrink-0 transition-colors"
              [ngClass]="{
                'bg-white': selectedTab === i,
                'border-transparent text-gray-600': selectedTab !== i,
              }"
              (click)="onTabSwitch(i)"
            >
              Test Case {{ i + 1 }}
            </button>
          </div>
        </div>

        <div class="flex gap-2 mr-3">
          @if (shouldShowScrollButtons()) {
            <button
              (click)="scrollTestCases(-1)"
              [disabled]="scrollOffset >= 0"
              class="p-2 text-gray-600 disabled:text-gray-300 hover:text-gray-800 transition-colors"
              aria-label="Scroll left"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                fill="currentColor"
                viewBox="0 0 16 16"
              >
                <path
                  fill-rule="evenodd"
                  d="M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z"
                />
              </svg>
            </button>

            <button
              (click)="scrollTestCases(1)"
              [disabled]="selectedTab === testCases.length - 1"
              class="bg-[#0822300A] p-2 text-gray-600 disabled:text-gray-300"
              aria-label="Next test case"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                fill="currentColor"
                viewBox="0 0 16 16"
              >
                <path
                  fill-rule="evenodd"
                  d="M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z"
                />
              </svg>
            </button>
          }
        </div>
      </div>
    }
    <app-test-case-row
      [isLoading]="loading"
      [testCase]="testCases[selectedTab]"
      [shouldGenerateOutput]="solutionCode !== ''"
      (update)="updateTestCase(selectedTab, $event)"
      (delete)="removeTestCase(selectedTab)"
      (run)="runTestCase(selectedTab)"
    ></app-test-case-row>
  </section>
</section>

<app-custom-modal
  [visibleModal]="errorMessage !== ''"
  (visibleModalChange)="closeErrorModal()"
  [headerTitle]="'Error'"
  [headerColor]="' #111827'"
  [textColor]="'#fff'"
  [backgroundColor]="'#111827'"
>
  <div
    class="bg-gray-900 text-gray-200 font-mono overflow-auto max-h-96 shadow-lg"
  >
    <pre class="p-4 whitespace-pre-wrap break-words">{{ errorMessage }}</pre>
  </div>
</app-custom-modal>

<app-custom-modal
  [visibleModal]="showGenerateQuestionModal"
  (visibleModalChange)="!isGeneratingQuestions && closeGenerateModal()"
  [headerTitle]="
    currentAiView === 'full' ? 'Generate Question' : 'Generate Test Cases'
  "
  [width]="'45%'"
  [textColor]="'#082230E5'"
>
  <div
    class="bg-[#1E709C08] text-[#1E709C] h-[36px] w-[36px] p-1 flex items-center justify-center"
    headerImage
  >
    <app-ai-sparkle-svg />
  </div>
  <span class="text-[#082230E5] text-sm" description>
    {{
      currentAiView === 'full'
        ? 'Generate coding questions with AI'
        : 'Automatically generate test cases for your coding question.'
    }}</span
  >

  <div class="w-full overflow-auto max-h-[calc(100vh-200px)] p-4 shadow-lg">
    <div class="border-b border-b-[#0822301A] w-full mb-5"></div>

    <div class="mb-5">
      <div
        class="space-y-4 bg-gray-50 p-6 rounded-2xl shadow-sm border border-gray-200 w-full break-all"
      >
        @if (currentAiView === 'full') {
          <h3 class="text-xl font-bold text-gray-800">Full Generation</h3>
        }
        <ul class="list-disc pl-5 space-y-2 text-base text-gray-700">
          @if (currentAiView === 'full') {
            <li>
              Use AI to generates a question, solution code and a boilerplate
              code.
            </li>
            <li>Automatically generate 10 test cases.</li>
          } @else {
            <li>
              <strong>Required:</strong> Provide a coding question, solution
              code and a programming language.
            </li>
            <li>
              <strong>Test Cases:</strong> Specify the number of test cases to
              generate (choose between <strong>1</strong> and
              <strong>15</strong>).
              <br />
              A maximum of <strong>15</strong> test cases is allowed.
            </li>
            <li>
              <strong> Hidden Test Cases:</strong>
              Hidden test cases will be hidden from users during the test
              taking.
            </li>
            <li>
              Generate comprehensive test cases that cover edge cases and common
              scenarios.
            </li>
          }
        </ul>
      </div>
    </div>
    <!-- full Generation -->
    @if (currentAiView === 'full') {
      <div>
        <div class="flex gap-2 items-center mb-3">
          <label
            for="questionDescription"
            class="text-[#082230E5] font-medium text-lg"
            >Describe what you want to generate</label
          >
          <app-required-svg />
        </div>
        <textarea
          class="w-full rounded-lg outline-none border border-[#0822301A] h-[120px] px-3 py-2 resize-none text-[#000] text-base bg-[#fff] mb-4"
          placeholder="Enter prompt to generate a question"
          [formControl]="questionDescription"
        ></textarea>
      </div>
      <div class="mb-4">
        <p
          class="text-[#262626] text-sm flex gap-2 items-center sm:text-base font-medium mb-2"
        >
          <span> Programming Language</span>
          <app-required-svg />
        </p>
        <app-custom-dropdown
          [placeholder]="'Select Language'"
          [options]="languageOptions"
          [displayKey]="'languageName'"
          [formControl]="getFormControl('language')"
        ></app-custom-dropdown>
      </div>

      <app-basic-question-container
        [form]="form"
        [reuseSettings]="false"
        [showEditor]="false"
        [addPadding]="false"
        [isGenerateModal]="true"
      ></app-basic-question-container>
    }
    <!-- Ai Test Case -->
    @if (currentAiView === 'testCases') {
      <section class="flex flex-col gap-6">
        <div class="grid md:grid-cols-2 gap-5 grid-cols-1">
          <div class="flex flex-col">
            <div
              class="text-[#262626] flex gap-2 items-center font-medium text-sm sm:text-base mb-2"
            >
              <span> Number of Public Test Cases</span>
              <app-required-svg />
            </div>
            <app-custom-input
              [type]="'number'"
              [formControl]="getFormControl('numberOfTestCases')"
              [placeholder]="'Type number of public test cases here'"
            ></app-custom-input>
            @if (form.get('numberOfTestCases')?.errors?.['negativeNumber']) {
              <div class="text-red-500 text-xs sm:text-sm mt-1">
                Test cases cannot be negative
              </div>
            } @else if (form.get('numberOfTestCases')?.errors?.['max']) {
              <div class="text-red-500 text-xs sm:text-sm mt-1">
                Maximum number of test cases allowed is {{ MAX_TEST_CASES }}
              </div>
            } @else {
              <span class="text-[#082230CC] text-sm"
                >Maximum of 15 test cases allowed</span
              >
            }
          </div>
          <div class="flex flex-col">
            <div
              class="text-[#262626] flex gap-2 items-center font-medium text-sm sm:text-base mb-2"
            >
              <span> Number of Hidden Test Cases </span>
            </div>
            <app-custom-input
              [type]="'number'"
              [formControl]="getFormControl('numberOfHiddenTestCases')"
              [placeholder]="'Type number of hidden test cases here'"
            ></app-custom-input>
            @if (
              form.get('numberOfHiddenTestCases')?.errors?.['negativeNumber']
            ) {
              <div class="text-red-500 text-xs sm:text-sm mt-1">
                Hidden test cases cannot be negative
              </div>
            } @else if (
              form.get('numberOfHiddenTestCases')?.errors?.['max'] ||
              (getTotalTestCases() > MAX_TEST_CASES &&
                MAX_TEST_CASES - getPublicTestCases() >= 0)
            ) {
              <div class="text-red-500 text-xs sm:text-sm mt-1">
                Maximum number of hidden test cases allowed is
                {{ MAX_TEST_CASES - getPublicTestCases() }}
              </div>
            } @else {
              <span class="text-[#082230CC] text-sm"
                >Hidden from users during testing</span
              >
            }
          </div>
        </div>

        <div class="flex flex-col gap-5">
          @if (form.get('questionText')?.value) {
            <div>
              <div class="flex items-center justify-between gap-4 mb-[14px]">
                <label
                  class="text-[#082230E5] text-[1.125rem] font-medium"
                  for="existingSolution"
                  >Question</label
                >
                <button (click)="toggleQuestionExpansion()">
                  <img
                    class="w-4 h-4 transition-transform duration-300 ease-in-out"
                    [class.rotate-90]="isQuestionExpanded"
                    src="../../../../../../../assets/icons/chevron-right.svg"
                    alt="cheveron-right"
                  />
                </button>
              </div>

              <div
                class="w-full border border-[#0822301A] rounded transition-all duration-300 ease-in-out overflow-hidden"
                [class]="
                  isQuestionExpanded
                    ? 'max-h-full opacity-100'
                    : 'max-h-0 opacity-0'
                "
              >
                <div
                  [appSafeHtml]="form.get('questionText')?.value"
                  class="px-3 py-2 text-sm text-[#000] break-all max-w-[54rem]"
                ></div>
              </div>
            </div>

            @if (!isQuestionExpanded) {
              <span class="w-full border border-[#0822301A]"></span>
            }
          }
          @if (solutionCode && getFormControl('language').value.languageName) {
            <div>
              <div class="flex items-center justify-between gap-4 mb-[14px]">
                <label
                  class="text-[#082230E5] text-[1.125rem] font-medium"
                  for="existingSolution"
                  >Solution Code</label
                >
                <button (click)="toggleSolutionExpansion()">
                  <img
                    class="w-4 h-4 transition-transform duration-300 ease-in-out"
                    [class.rotate-90]="isSolutionExpanded"
                    src="../../../../../../../assets/icons/chevron-right.svg"
                    alt="cheveron-right"
                  />
                </button>
              </div>

              <pre
                class="w-full mb-2 border rounded transition-all duration-300 ease-in-out overflow-hidden"
                [class]="
                  isSolutionExpanded
                    ? 'max-h-full opacity-100'
                    : 'max-h-0 opacity-0'
                "
              >
               @if (isSolutionLoading) {
                 <span
                class="flex flex-col items-center justify-center  p-3"
              >
                <div
                  class="w-8 h-8 border-4 border-gray-300 border-t-blue-500 rounded-full animate-spin"
                ></div>
                <span class="mt-2 text-sm text-gray-600 dark:text-gray-300"
                  >Solution code loading...</span
                >
              </span>
              }@else {
              <code
             [class]="
             (themeService.theme$ | async) === 'dark'
              ? 'bg-[#303030] text-gray-100 border-gray-700'
              : 'bg-white text-gray-900 border-gray-300'
              "
             class="p-4  text-sm text-[#082230E5] rounded  break-all max-w-[54rem]" [highlight]="solutionCode"  [language]="getFormControl('language').value.languageName">{{  solutionCode }}</code>}</pre>
            </div>
          }
        </div>

        <!-- Test Case Configuration Summary -->
        @if (
          getFormControl('numberOfTestCases').value ||
          getFormControl('numberOfHiddenTestCases').value
        ) {
          <div
            class="mt-4 p-3 bg-[#F8F9FA] border-[#0C476633] border rounded-md"
          >
            <div class="text-base text-[#1E709C] flex flex-col gap-[15px]">
              <div class="flex items-center gap-3">
                <img src="../../../../assets/icons/info.svg" alt="" />
                <strong class="text-[#1.125]">Configuration Summary:</strong>
              </div>
              <div>
                The total generated test cases may be lower than requested due
                to system constraints or configuration limits.
              </div>
              <ul class="space-y-1">
                <li>Public Test Cases: {{ getPublicTestCases() }}</li>
                <li>Hidden Test Cases: {{ getHiddenTestCases() }}</li>
                <li>Total Test Cases: {{ getTotalTestCases() }}</li>
              </ul>
              @if (!isValidTestCaseConfiguration()) {
                <div class="mt-2 text-red-600 font-medium">
                  ⚠️ Requirements: At least 1 public test case, maximum 15 total
                  test cases
                </div>
              }
            </div>
          </div>
        }
      </section>
    }

    <div class="flex mt-14 gap-[18px] justify-end">
      <app-custombutton [variant]="'secondary'" (clicked)="closeGenerateModal()"
        >Cancel</app-custombutton
      >

      <app-custombutton
        (clicked)="
          currentAiView === 'full' ? generateQuestion() : generateTestCases()
        "
        [spinner]="isGeneratingQuestions || isGenerateTestCases"
        [disabled]="
          (currentAiView !== 'full' && !isValidTestCaseConfiguration()) ||
          isGeneratingQuestions ||
          isGenerateTestCases
        "
      >
        <span class="flex items-center gap-2 test-generation-wrapper">
          <app-ai-sparkle-svg />
          <span>{{
            currentAiView === 'full' ? 'Generate with AI' : 'Generate Test Case'
          }}</span>
        </span>
      </app-custombutton>
    </div>
  </div>
</app-custom-modal>
