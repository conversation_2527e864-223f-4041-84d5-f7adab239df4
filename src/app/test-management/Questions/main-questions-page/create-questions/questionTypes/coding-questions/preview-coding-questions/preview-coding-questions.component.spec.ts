import { HttpClientTestingModule } from '@angular/common/http/testing';
import {
  ComponentFixture,
  fakeAsync,
  TestBed,
  tick,
} from '@angular/core/testing';
import { ActivatedRoute } from '@angular/router';
import { HIGHLIGHT_OPTIONS } from 'ngx-highlightjs';
import { of, Subscription } from 'rxjs';
import { QuestionCreationServiceService } from '../../../../../../../services/test-management-service/question-creation-service.service';
import { ToastService } from '../../../../../../../services/toast-service/toast.service';
import {
  CodingState,
  CodingStore,
} from '../../../../../../../stores/coding-store/coding.store';
import { PreviewCodingQuestionsComponent } from './preview-coding-questions.component';
import {
  Category,
  DomainData,
} from '../../../../../../../Interfaces/domainInterface';

describe('PreviewCodingQuestionsComponent', () => {
  let component: PreviewCodingQuestionsComponent;
  let fixture: ComponentFixture<PreviewCodingQuestionsComponent>;
  let mockToastService: ToastService;
  let mockQuestionService: QuestionCreationServiceService;

  const mockActivatedRoute = {
    queryParams: of({ questionId: '123' }),
  };

  const mockCodingData = {
    basicInfo: {
      domain: { id: '1', name: 'Math' },
      category: { id: '2', name: 'Algebra' },
      score: 100,
      difficultyLevel: 'Medium',
      questionText: 'Solve equation',
      questionType: 'code',
    },
    language: { languageName: 'Python', judge0Id: 71 },
    referenceSolution: 'def solve(): pass',
    codeTemplate: 'def solve():',
    testCases: [],
    codeConstraints: {
      timeComplexity: 'O(n)',
      spaceComplexity: 'O(1)',
      timeLimit: 1,
      memoryLimit: 128,
    },
  };

  const mockCodingStore = {
    questionState$: of(mockCodingData),
    updateAllState: jest.fn(),
  };

  beforeEach(async () => {
    mockToastService = {
      onShow: jest.fn(),
    } as unknown as ToastService;

    mockQuestionService = {
      createCodeQuestion: jest.fn().mockReturnValue(of({ success: true })),
      updateCodeQuestion: jest.fn().mockReturnValue(of({ success: true })),
    } as unknown as QuestionCreationServiceService;

    await TestBed.configureTestingModule({
      imports: [PreviewCodingQuestionsComponent, HttpClientTestingModule],
      providers: [
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: CodingStore, useValue: mockCodingStore },
        { provide: ToastService, useValue: mockToastService },
        {
          provide: QuestionCreationServiceService,
          useValue: mockQuestionService,
        },
        {
          provide: HIGHLIGHT_OPTIONS,
          useValue: {
            fullLibraryLoader: () => import('highlight.js'),
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(PreviewCodingQuestionsComponent);
    component = fixture.componentInstance;

    // Mock localStorage
    jest
      .spyOn(Storage.prototype, 'getItem')
      .mockImplementation((key: string) => {
        if (key === 'coding-data') {
          try {
            return JSON.stringify(mockCodingData);
          } catch (error) {
            console.error('Error parsing JSON from localStorage:', error);
            return null;
          }
        } else if (key === 'notificationSoundState') {
          return 'true';
        }
        return null;
      });

    jest.spyOn(Storage.prototype, 'removeItem').mockImplementation(() => {});
    jest.spyOn(Storage.prototype, 'setItem').mockImplementation(() => {});

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load data from localStorage and populate codingStore', () => {
    expect(mockCodingStore.updateAllState).toHaveBeenCalledWith(mockCodingData);
  });

  it('should read query params and set questionId', () => {
    expect(component.questionId).toBe('123');
  });

  it('should update preview details link based on edit mode', () => {
    component.isEditMode = true;
    component.updatePreviewDetails();
    expect(component.codingView[0].link).toContain('edit-question');

    component.isEditMode = false;
    component.updatePreviewDetails();
    expect(component.codingView[0].link).toContain('create-question');
  });

  it('should set isSolutionLoading to false after timeout', fakeAsync(() => {
    component.isSolutionLoading = true;
    component.ngOnInit();
    tick(1000);
    expect(component.isSolutionLoading).toBe(false);
  }));

  it('should populate questionConstraints correctly', () => {
    expect(component.questionConstraints).toEqual([
      { name: 'Domain', value: 'Math', id: '1' },
      { name: 'Category', value: 'Algebra', id: '2' },
      { name: 'Score', value: 100 },
      { name: 'Difficulty Level', value: 'Medium' },
      { name: 'Time Complexity', value: 'O(n)' },
      { name: 'Space Complexity', value: 'O(1)' },
      { name: 'Time Limit (ms)', value: 1 },
      { name: 'Memory Limit (KB)', value: 128 },
    ]);
  });

  it('should trigger beforeunloadHandler on window:beforeunload', () => {
    const beforeunloadSpy = jest.spyOn(component, 'beforeunloadHandler');
    window.dispatchEvent(new Event('beforeunload'));
    expect(beforeunloadSpy).toHaveBeenCalled();
  });

  it('should call createQuestion when not in edit mode', () => {
    component.isEditMode = false;
    const createSpy = jest.spyOn(component, 'createQuestion');
    component.saveQuestion();
    expect(createSpy).toHaveBeenCalled();
  });

  it('should not show toast if all test cases have output', () => {
    const toastSpy = jest.spyOn(mockToastService, 'onShow');

    component.backendData = {
      ...component.backendData,
      testCases: [
        { input: '1', output: 'output1', visibility: 'public', weight: 1 },
      ],
    };

    component.createQuestion();

    expect(toastSpy).not.toHaveBeenCalledWith(
      expect.stringContaining('Missing Output'),
      expect.anything(),
      expect.anything(),
      'error'
    );
  });

  it('should call updateQuestion when in edit mode', () => {
    component.isEditMode = true;
    const updateSpy = jest.spyOn(component, 'updateQuestion');
    component.saveQuestion();
    expect(updateSpy).toHaveBeenCalled();
  });

  it('should create question and show success toast', () => {
    component.createQuestion();
    expect(mockQuestionService.createCodeQuestion).toHaveBeenCalled();
    expect(mockToastService.onShow).toHaveBeenCalledWith(
      'success',
      'Question created successfully',
      true,
      'success'
    );
  });

  it('should update question and show success toast', () => {
    component.updateQuestion();
    expect(mockQuestionService.updateCodeQuestion).toHaveBeenCalledWith(
      component.backendData,
      '123'
    );
    expect(mockToastService.onShow).toHaveBeenCalledWith(
      'success',
      'Question updated successfully',
      true,
      'success'
    );
  });

  it('should go to previous page when previousPage is called', () => {
    const backSpy = jest.spyOn(history, 'back').mockImplementation(() => {});
    component.previousPage();
    expect(backSpy).toHaveBeenCalled();
  });

  it('should unsubscribe from all subscriptions on destroy', () => {
    const subscription = { unsubscribe: jest.fn() } as unknown as Subscription;
    component.subscriptions.push(subscription);
    component.ngOnDestroy();
    expect(subscription.unsubscribe).toHaveBeenCalled();
  });

  it('should handle JSON parse error from localStorage gracefully', () => {
    jest
      .spyOn(Storage.prototype, 'getItem')
      .mockImplementation((key: string) => {
        if (key === 'coding-data') {
          return 'malformed_json';
        }
        return null;
      });
    const errorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    component.ngOnInit();
    expect(errorSpy).toHaveBeenCalled();
  });
  it('should store coding data in localStorage before unload', () => {
    component.codingData = {
      basicInfo: {
        domain: {} as DomainData,
        category: {} as Category,
        score: '',
        difficultyLevel: '',
        questionType: '',
        questionText: '',
      },
    } as CodingState;
    const setItemSpy = jest.spyOn(Storage.prototype, 'setItem');
    try {
      component.beforeunloadHandler();
      expect(setItemSpy).toHaveBeenCalledWith(
        'coding-data',
        JSON.stringify({ some: 'data' })
      );
    } catch (error) {
      console.error('Error handling before unload:', error);
    }
  });
});
