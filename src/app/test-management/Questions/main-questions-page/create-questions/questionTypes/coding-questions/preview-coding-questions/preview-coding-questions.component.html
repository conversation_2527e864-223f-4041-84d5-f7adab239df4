<main>
  <div class="flex justify-between gap-3 w-full mb-1 flex-wrap">
    <div>
      <app-question-title
        [questionType]="title"
        (backRoute)="previousPage()"
      ></app-question-title>
      <p class="ml-7 text-sm text-[#082230CC]">
        {{ titleDescription }}
      </p>
    </div>
  </div>
  <div class="space-y-1 my-6">
    <app-dashboard-navbar
      [showLine]="false"
      [navbarData]="codingView"
      [isTestMode]="true"
      [isCodingMode]="true"
    ></app-dashboard-navbar>
  </div>
  <div class="flex flex-col gap-6">
    @if (backendData) {
      <div>
        <h3 class="text-[#262626] text-lg font-medium">Question Title</h3>
        <div
          class="text-[#082230CC] ml-3"
          [appSafeHtml]="backendData.questionTitle ?? 'NA'"
        ></div>
      </div>
      <div class="flex flex-wrap md:flex-row gap-16">
        <div>
          <h3 class="text-[#262626] text-lg font-medium">
            Programming Language
          </h3>
          <div class="text-[#082230CC]">
            {{ backendData.language.languageName || 'NA' }}
          </div>
        </div>
        <div>
          <h3 class="text-[#262626] text-lg font-medium">Domain</h3>
          <div class="text-[#082230CC]">
            {{ this.codingData.basicInfo.domain.name }}
          </div>
        </div>
        <div>
          <h3 class="text-[#262626] text-lg font-medium">Category</h3>
          <div class="text-[#082230CC]">
            {{ this.codingData.basicInfo.category.name }}
          </div>
        </div>

        <div>
          <h3 class="text-[#262626] text-lg font-medium">Difficulty Level</h3>
          <div class="text-[#082230CC]">
            {{ backendData.difficultyLevel || 'NA' }}
          </div>
        </div>

        <div>
          <h3 class="text-[#262626] text-lg font-medium">Score</h3>

          <div class="text-[#082230CC]">
            {{ backendData.score || 'NA' }}
          </div>
        </div>
      </div>

      <main class="flex flex-col gap-6">
        <div class="flex flex-col gap-5">
          <p class="text-[#262626] text-lg font-medium">Question Details</p>
          <div
            class="text-sm border border-gray-300 rounded text-gray-700 p-4 bg-white break-words"
            [appSafeHtml]="backendData.questionText"
          ></div>
        </div>

        @if (backendData.referenceSolution) {
          <div class="flex flex-col gap-5">
            <p
              class="text-[#0C4767] border-b-4 border-b-[#0C4767] text-lg font-medium w-[135px] whitespace-pre-wrap break-words"
            >
              Solution Code
            </p>
            @if (isSolutionLoading) {
              <span
                class="flex flex-col items-center justify-center border bg-white rounded p-3"
              >
                <div
                  class="w-8 h-8 border-4 border-gray-300 border-t-blue-500 rounded-full animate-spin"
                ></div>
                <span class="mt-2 text-sm text-gray-600 dark:text-gray-300"
                  >Solution code loading...</span
                >
              </span>
            } @else {
              <pre class="mb-2"><code
             [class]="(themeService.theme$ | async) === 'dark'
          ? 'bg-[#303030] text-gray-100 border-gray-700'
          : 'bg-white text-gray-900 border-gray-300'"
            class="p-4 border rounded text-sm  block " [highlight]="backendData.referenceSolution" [language]="backendData.language.languageName "> {{ backendData.referenceSolution.length ? backendData.referenceSolution : 'Loading solution code...'}}</code></pre>
            }
          </div>
        }
        @if (backendData.codeTemplate) {
          <div class="flex flex-col gap-5">
            <p class="text-[#262626] text-lg font-medium">Boilerplate</p>
            @if (isSolutionLoading) {
              <span
                class="flex flex-col items-center justify-center border bg-white rounded p-3"
              >
                <div
                  class="w-8 h-8 border-4 border-gray-300 border-t-blue-500 rounded-full animate-spin"
                ></div>
                <span class="mt-2 text-sm text-gray-600 dark:text-gray-300"
                  >Boilerplate code loading...</span
                >
              </span>
            } @else {
              <pre class="mb-2"><code
             [class]="(themeService.theme$ | async) === 'dark'
          ? 'bg-[#303030] text-gray-100 border-gray-700'
          : 'bg-white text-gray-900 border-gray-300'"
            class="w-full h-full border  p-3 rounded text-sm  block" [highlight]="backendData.codeTemplate" [language]="'javascript'">{{  backendData.codeTemplate }}</code></pre>
            }
          </div>
        }
      </main>
    }
  </div>

  <div class="mt-6">
    @for (testCase of backendData.testCases; track testCase; let i = $index) {
      <div class="mb-2 bg-white rounded-md p-4 flex flex-wrap justify-between">
        <h3 class="text-[#082230CC] text-[1.125rem] font-semibold">
          Test Case {{ i + 1 }}
        </h3>

        <div class="grid grid-cols-2 gap-6 w-full mb-5">
          @for (
            item of [
              { title: 'Input', value: testCase.input },
              { title: 'Output', value: testCase.output },
              {
                title: 'Visibility',
                value:
                  testCase.visibility === 'private'
                    ? 'hidden'
                    : testCase.visibility,
              },
              { title: 'Weight Score', value: testCase.weight },
            ];
            track item;
            let j = $index
          ) {
            <div class="flex-1 pr-2 mb-2">
              <h4 class="text-[#082230E5] text-sm font-semibold">
                {{ item.title }}
              </h4>
              <pre
                class="bg-gray-100 p-4 rounded-md text-sm text-[#082230E5] whitespace-pre-wrap break-words"
                >{{ item.value }}</pre
              >
            </div>
          }
        </div>
      </div>
    }
  </div>
  <div class="flex justify-end mb-2">
    <app-custombutton
      [spinner]="isLoading"
      [disabled]="isLoading"
      (clicked)="saveQuestion()"
      >{{ isEditMode ? 'Save Changes' : 'Save' }}</app-custombutton
    >
  </div>
</main>
