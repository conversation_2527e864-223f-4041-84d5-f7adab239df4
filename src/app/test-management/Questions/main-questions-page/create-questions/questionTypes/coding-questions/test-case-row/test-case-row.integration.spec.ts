import { TestBed } from '@angular/core/testing';
import { TestCaseRowComponent } from './test-case-row.component';
import { ComponentFixture } from '@angular/core/testing';
import { TestCase } from '../../../../../../../Interfaces/codingTypeQuestionsInterface';

describe('TestCaseRowComponent Integration Tests', () => {
  let component: TestCaseRowComponent;
  let fixture: ComponentFixture<TestCaseRowComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TestCaseRowComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(TestCaseRowComponent);
    component = fixture.componentInstance;
  });

  describe('Complex Content Scenarios', () => {
    it('should handle complex JSON objects in optimal rows calculation', () => {
      const complexObject = {
        data: {
          nested: {
            array: [1, 2, 3, 4, 5],
            object: { key1: 'value1', key2: 'value2' },
          },
          string:
            'This is a long string that should take multiple lines when displayed',
        },
      };

      const rows = component.getOptimalRows(complexObject);
      expect(rows).toBeGreaterThan(2);
    });

    it('should handle very deeply nested objects', () => {
      const deepObject: Record<string, unknown> = {};
      let current = deepObject;
      for (let i = 0; i < 10; i++) {
        current['level' + i] = {};
        current = current['level' + i] as Record<string, unknown>;
      }
      current['value'] = 'deeply nested value';

      const rows = component.getOptimalRows(deepObject);
      expect(rows).toBeGreaterThan(2);
    });

    it('should handle arrays with mixed content types', () => {
      const mixedArray = [
        'string',
        123,
        { object: 'value' },
        [1, 2, 3],
        null,
        undefined,
        true,
      ];

      const rows = component.getOptimalRows(mixedArray);
      expect(rows).toBeGreaterThanOrEqual(2);
    });
  });

  describe('Edge Case Scenarios', () => {
    it('should handle special characters in input/output', () => {
      const specialChars = '!@#$%^&*()_+{}|:<>?[];,./~`';
      component.localInput = specialChars;

      const data = component.getCurrentData();
      expect(data.input).toBe(specialChars);
    });

    it('should handle unicode characters', () => {
      const unicodeString = '🚀 Hello 世界 🌍 مرحبا';
      component.localOutput = unicodeString;

      const data = component.getCurrentData();
      expect(data.output).toBe(unicodeString);
    });

    it('should handle very large numbers', () => {
      const largeNumber = Number.MAX_SAFE_INTEGER;
      component.localWeight = largeNumber;

      const data = component.getCurrentData();
      expect(data.weight).toBe(largeNumber);
    });

    it('should handle negative weight values', () => {
      component.localWeight = -5;

      const data = component.getCurrentData();
      expect(data.weight).toBe(-5);
    });
  });

  describe('State Management', () => {
    it('should maintain expansion state independently for input and output', () => {
      component.isInputExpanded = true;
      component.isOutputExpanded = false;

      component.toggleInputExpansion();
      expect(component.isInputExpanded).toBe(false);
      expect(component.isOutputExpanded).toBe(false);

      component.toggleOutputExpansion();
      expect(component.isInputExpanded).toBe(false);
      expect(component.isOutputExpanded).toBe(true);
    });

    it('should preserve local values when toggling expansion', () => {
      component.localInput = 'test input';
      component.localOutput = 'test output';

      component.toggleInputExpansion();
      component.toggleOutputExpansion();

      expect(component.localInput).toBe('test input');
      expect(component.localOutput).toBe('test output');
    });
  });

  describe('Auto-expansion Logic', () => {
    it('should not auto-expand for content that fits in default rows', () => {
      const shortTestCase: TestCase = {
        input: 'short',
        output: 'ok',
        visibility: 'public',
        weight: 1,
      };

      component.testCase = shortTestCase;
      component.ngOnChanges({
        testCase: {
          currentValue: shortTestCase,
          previousValue: null,
          firstChange: true,
          isFirstChange: () => true,
        },
      });

      expect(component.isInputExpanded).toBe(false);
      expect(component.isOutputExpanded).toBe(false);
    });

    it('should handle mixed content lengths (one short, one long)', () => {
      const mixedTestCase: TestCase = {
        input: 'short',
        output: 'a'.repeat(500), // Very long output to trigger auto-expansion
        visibility: 'public',
        weight: 1,
      };

      component.testCase = mixedTestCase;
      component.ngOnChanges({
        testCase: {
          currentValue: mixedTestCase,
          previousValue: null,
          firstChange: true,
          isFirstChange: () => true,
        },
      });

      expect(component.isInputExpanded).toBe(false);
      // The output should be long enough to trigger auto-expansion
      // Let's check if it would show expand button instead
      expect(component.shouldShowExpandButton(component.localOutput)).toBe(
        true
      );
    });
  });

  describe('Data Consistency', () => {
    it('should maintain data consistency between updates and runs', () => {
      component.localInput = 'test input';
      component.localOutput = 'test output';
      component.localWeight = 5;
      component.localHidden = true;

      const updateSpy = jest.spyOn(component.update, 'emit');
      const runSpy = jest.spyOn(component.run, 'emit');

      component.emitRun();

      expect(updateSpy).toHaveBeenCalledWith({
        input: 'test input',
        output: 'test output',
        visibility: 'private',
        weight: 5,
      });
      expect(runSpy).toHaveBeenCalled();
    });

    it('should handle null weight values gracefully', () => {
      component.localWeight = null;

      const data = component.getCurrentData();
      expect(data.weight).toBeNull();
    });
  });
});
