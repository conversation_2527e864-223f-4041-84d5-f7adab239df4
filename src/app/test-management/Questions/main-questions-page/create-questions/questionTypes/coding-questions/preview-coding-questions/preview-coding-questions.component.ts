import {
  Component,
  HostListener,
  inject,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
} from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { HighlightModule } from 'ngx-highlightjs';
import { Subscription } from 'rxjs';
import { CustomButtonComponent } from '../../../../../../../components/custombutton/custombutton.component';
import { SafeHtmlDirective } from '../../../../../../../directives/safe-html.directive';
import { CreateCodeQuestionInterface } from '../../../../../../../Interfaces/codingTypeQuestionsInterface';
import { QuestionsData } from '../../../../../../../Interfaces/questionInterface';
import { DashboardNav } from '../../../../../../../Interfaces/Types/dashboardInterface';
import { DashboardNavbarComponent } from '../../../../../../../main-app/components/dashboard-navbar/dashboard-navbar.component';
import { QuestionCreationServiceService } from '../../../../../../../services/test-management-service/question-creation-service.service';
import { ToastService } from '../../../../../../../services/toast-service/toast.service';
import {
  CodingState,
  CodingStore,
} from '../../../../../../../stores/coding-store/coding.store';
import { QuestionStore } from '../../../../../../../stores/questions-store/question.store';
import { secondsToMilliseconds } from '../../../../../../../utils/constants';
import { QuestionTitleComponent } from '../../../../components/question-title/question-title.component';
import { ThemeService } from '../../../../../../../services/theme.service';
import { CommonModule } from '@angular/common';
import { getMissingOutputIndexes } from '../../../../../../../utils/codingTypeUtils';

@Component({
  selector: 'app-preview-coding-questions',
  standalone: true,
  imports: [
    CustomButtonComponent,
    QuestionTitleComponent,
    HighlightModule,
    SafeHtmlDirective,
    DashboardNavbarComponent,
    CommonModule,
  ],
  templateUrl: './preview-coding-questions.component.html',
})
export class PreviewCodingQuestionsComponent implements OnInit, OnDestroy {
  codingStore = inject(CodingStore);
  private readonly questionsStore = inject(QuestionStore);
  router = inject(Router);
  route = inject(ActivatedRoute);
  questionId = '';
  backendData: CreateCodeQuestionInterface = {} as CreateCodeQuestionInterface;
  form!: FormGroup;
  isEditMode = false;
  questionConstraints: {
    name: string;
    value: string;
    id?: string;
  }[] = [];
  questionCreation = inject(QuestionCreationServiceService);
  codingData: CodingState = {} as CodingState;
  toastService = inject(ToastService);
  themeService = inject(ThemeService);
  subscriptions: Subscription[] = [];
  isSolutionLoading = true;
  title = 'Create Question';
  titleDescription =
    'Previews details, sample solution and test cases before proceeding to create ';
  isLoading = false;

  codingView: DashboardNav[] = [
    {
      title: 'Details',
    },
    {
      title: 'Preview ',
      onClick: () => {
        this.router.navigate([
          this.isEditMode
            ? '/dashboard/test-management/questions/edit-question/code/preview'
            : '/dashboard/test-management/questions/create-question/code/preview',
        ]);
      },
    },
  ];

  @HostListener('window:beforeunload', ['$event'])
  beforeunloadHandler(): void {
    localStorage.setItem('coding-data', JSON.stringify(this.codingData));
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => subscription.unsubscribe());
  }

  ngOnInit(): void {
    this.form = new FormGroup({
      questionText: new FormControl(''),
    });
    const localStorageData = localStorage.getItem('coding-data');
    this.isEditMode = this.router.url.includes('edit');
    this.subscriptions.push(
      this.route.queryParams.subscribe((params) => {
        this.questionId = params['questionId'];
      })
    );

    if (localStorageData) {
      try {
        const parsedData = JSON.parse(localStorageData);

        if (parsedData) {
          this.codingStore.updateAllState(parsedData);
        } else {
          console.error('Invalid data structure in localStorage');
        }
      } catch (error) {
        console.error('Error parsing localStorage data:', error);
      }

      localStorage.removeItem('coding-data');
    }

    this.codingStore.questionState$.subscribe({
      next: (response) => {
        this.codingData = response;
        this.questionConstraints = [
          {
            name: 'Domain',
            value: response.basicInfo?.domain?.name,
            id: response.basicInfo?.domain?.id,
          },
          {
            name: 'Category',
            value: response.basicInfo?.category?.name,
            id: response.basicInfo?.category?.id,
          },
          { name: 'Score', value: response.basicInfo?.score },
          {
            name: 'Difficulty Level',
            value: response.basicInfo?.difficultyLevel,
          },
          {
            name: 'Time Complexity',
            value: response.codeConstraints.timeComplexity,
          },
          {
            name: 'Space Complexity',
            value: response.codeConstraints.spaceComplexity,
          },
          {
            name: 'Time Limit (ms)',
            value: response.codeConstraints.timeLimit,
          },
          {
            name: 'Memory Limit (KB)',
            value: response.codeConstraints.memoryLimit,
          },
        ];

        this.getDataForBackend(response);
      },
      error: (error) => {
        console.error('Error fetching data from coding store:', error);
      },
    });
    this.updatePreviewDetails();
  }

  updatePreviewDetails() {
    this.codingView[0].link = this.isEditMode
      ? '/dashboard/test-management/questions/edit-question/code/create-code-question'
      : '/dashboard/test-management/questions/create-question/code/create-code-question';
  }

  getFormControl(controlName: string): FormControl {
    return this.form.get(controlName) as FormControl;
  }

  getDataForBackend(response: CodingState) {
    const rawConstraints = response.codeConstraints ?? {};

    const codeConstraints = {
      ...(rawConstraints.timeComplexity?.trim()
        ? { timeComplexity: rawConstraints.timeComplexity }
        : {}),
      ...(rawConstraints.spaceComplexity?.trim()
        ? { spaceComplexity: rawConstraints.spaceComplexity }
        : {}),
      ...(rawConstraints.timeLimit !== undefined &&
      rawConstraints.timeLimit !== ''
        ? {
            timeLimit: Math.min(
              secondsToMilliseconds(Number(rawConstraints.timeLimit)),
              128000
            ),
          }
        : {}),
      ...(rawConstraints.memoryLimit !== undefined &&
      rawConstraints.memoryLimit !== ''
        ? {
            memoryLimit:
              Number(rawConstraints.memoryLimit) > 5000
                ? 5000
                : Number(rawConstraints.memoryLimit),
          }
        : {}),
    };
    this.backendData = {
      language: {
        languageName: response.language?.languageName,
        judge0Id: response.language?.judge0Id,
      },
      ...(response.basicInfo?.questionTitle?.trim()
        ? { questionTitle: response.basicInfo.questionTitle }
        : {}),
      referenceSolution: response.referenceSolution,
      ...(response.codeTemplate ? { codeTemplate: response.codeTemplate } : {}),
      testCases: response.testCases,
      domainId: response.basicInfo?.domain?.id,
      categoryId: response.basicInfo?.category?.id,
      score: response.basicInfo?.score,
      difficultyLevel: response.basicInfo?.difficultyLevel,
      questionText: response.basicInfo?.questionText,
      questionType: response.basicInfo?.questionType,
      ...(Object.keys(codeConstraints).length ? { codeConstraints } : {}),
    };
    this.form.patchValue({
      questionText: this.backendData.questionText,
      language: this.backendData.language.languageName,
    });

    setTimeout(() => {
      this.isSolutionLoading = false;
    }, 1000);
  }

  saveQuestion() {
    this.isLoading = true;
    this.isEditMode ? this.updateQuestion() : this.createQuestion();
  }

  previousPage() {
    history.back();
  }

  private hasMissingOutputs(): boolean {
    const missingOutputIndexes = getMissingOutputIndexes(
      this.backendData.testCases
    );

    if (missingOutputIndexes.length > 0) {
      const plural = missingOutputIndexes.length > 1;
      this.toastService.onShow(
        'Missing Output',
        `Test case${plural ? 's' : ''} ${missingOutputIndexes.join(', ')} ${plural ? 'are' : 'is'} missing output.`,
        true,
        'error'
      );
      this.isLoading = false;
      return true;
    }

    return false;
  }

  updateQuestion() {
    if (!this.questionId) {
      this.questionId = this.codingData.basicInfo?.questionId as string;
    }
    if (this.hasMissingOutputs()) return;
    this.subscriptions.push(
      this.questionCreation
        .updateCodeQuestion(this.backendData, this.questionId)
        .subscribe({
          next: (response) => {
            if (response) {
              this.isLoading = false;
              this.toastService.onShow(
                'success',
                'Question updated successfully',
                true,
                'success'
              );

              this.router.navigate([
                '/dashboard/test-management/questions/all-questions',
              ]);
            }
          },
          error: (error) => {
            this.isLoading = false;
            this.toastService.onShow('error', error, true, 'error');
          },
        })
    );
  }

  createQuestion() {
    if (this.hasMissingOutputs()) return;
    this.subscriptions.push(
      this.questionCreation.createCodeQuestion(this.backendData).subscribe({
        next: (response) => {
          if (response) {
            this.isLoading = false;
            this.toastService.onShow(
              'success',
              'Question created successfully',
              true,
              'success'
            );

            const newQuestion: QuestionsData = {
              ...response.data,
              answerOptions: [],
              essayAnswer: { rubrics: '' },
              fillInAnswer: {
                options: [],
                answer: [],
              },
              timeLimit: response.data.timeLimit?.toString(),
              referenceSolution: response.data.referenceSolution.map(
                (solution) => ({
                  ...solution,
                  codeType: solution.codeType as
                    | 'referenceSolution'
                    | 'template',
                })
              ),
            };
            this.questionsStore.addQuestion(newQuestion);

            this.router.navigate([
              '/dashboard/test-management/questions/all-questions',
            ]);
          }
        },
        error: (error) => {
          this.isLoading = false;
          this.toastService.onShow('error', error, true, 'error');
        },
      })
    );
  }
}
