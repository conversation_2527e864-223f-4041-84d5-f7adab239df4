import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TestCaseRowComponent } from './test-case-row.component';
import { TestCase } from '../../../../../../../Interfaces/codingTypeQuestionsInterface';

describe('TestCaseRowComponent', () => {
  let component: TestCaseRowComponent;
  let fixture: ComponentFixture<TestCaseRowComponent>;

  const mockTestCase: TestCase = {
    input: '1 2',
    output: '3',
    visibility: 'public',
    weight: 2,
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TestCaseRowComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(TestCaseRowComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize local values from testCase on ngOnChanges', () => {
    component.testCase = mockTestCase;
    component.ngOnChanges({
      testCase: {
        currentValue: mockTestCase,
        previousValue: null,
        firstChange: true,
        isFirstChange: () => true,
      },
    });

    expect(component.localInput).toBe('1 2');
    expect(component.localOutput).toBe('3');
    expect(component.localWeight).toBe(2);
    expect(component.localHidden).toBe(false);
  });

  it('should emit update with correct data', () => {
    const emitSpy = jest.spyOn(component.update, 'emit');

    component.localInput = 'input';
    component.localOutput = 'output';
    component.localHidden = true;
    component.localWeight = 1;

    component.emitUpdate();

    expect(emitSpy).toHaveBeenCalledWith({
      input: 'input',
      output: 'output',
      visibility: 'private',
      weight: 1,
    });
  });

  it('should call emitUpdate and emit run on emitRun', () => {
    const updateSpy = jest.spyOn(component, 'emitUpdate');
    const runSpy = jest.spyOn(component.run, 'emit');

    component.emitRun();

    expect(updateSpy).toHaveBeenCalled();
    expect(runSpy).toHaveBeenCalled();
  });

  it('should return correct current data', () => {
    component.localInput = '5 6';
    component.localOutput = '11';
    component.localWeight = 3;
    component.localHidden = false;

    const result = component.getCurrentData();

    expect(result).toEqual({
      input: '5 6',
      output: '11',
      visibility: 'public',
      weight: 3,
    });
  });

  describe('Expansion functionality', () => {
    it('should toggle input expansion state', () => {
      expect(component.isInputExpanded).toBe(false);

      component.toggleInputExpansion();
      expect(component.isInputExpanded).toBe(true);

      component.toggleInputExpansion();
      expect(component.isInputExpanded).toBe(false);
    });

    it('should toggle output expansion state', () => {
      expect(component.isOutputExpanded).toBe(false);

      component.toggleOutputExpansion();
      expect(component.isOutputExpanded).toBe(true);

      component.toggleOutputExpansion();
      expect(component.isOutputExpanded).toBe(false);
    });
  });

  describe('getOptimalRows', () => {
    it('should return 2 for empty content', () => {
      expect(component.getOptimalRows('')).toBe(2);
      expect(component.getOptimalRows(null)).toBe(2);
      expect(component.getOptimalRows(undefined)).toBe(2);
    });

    it('should return correct rows for string content', () => {
      const shortString = 'test';
      expect(component.getOptimalRows(shortString)).toBe(2);

      const longString = 'a'.repeat(200);
      const result = component.getOptimalRows(longString);
      expect(result).toBeGreaterThan(2);
      expect(result).toBeLessThanOrEqual(8);
    });

    it('should handle multiline content', () => {
      const multilineContent = 'line1\nline2\nline3\nline4\nline5';
      const result = component.getOptimalRows(multilineContent);
      expect(result).toBeGreaterThanOrEqual(5);
    });

    it('should handle JSON objects', () => {
      const complexObject = { key1: 'value1', key2: { nested: 'value2' } };
      const result = component.getOptimalRows(complexObject);
      expect(result).toBeGreaterThanOrEqual(2);
    });

    it('should handle numbers and booleans', () => {
      expect(component.getOptimalRows(123)).toBe(2);
      expect(component.getOptimalRows(true)).toBe(2);
      expect(component.getOptimalRows(false)).toBe(2);
    });

    it('should respect max rows limit', () => {
      const veryLongContent = 'a'.repeat(1000);
      const result = component.getOptimalRows(veryLongContent, 5);
      expect(result).toBeLessThanOrEqual(5);
    });

    it('should handle unparseable objects gracefully', () => {
      const circularObject: Record<string, unknown> = {};
      circularObject['self'] = circularObject;

      const result = component.getOptimalRows(circularObject);
      expect(result).toBe(2); // Should fallback to showing "[Complex Object]"
    });
  });

  describe('shouldShowExpandButton', () => {
    it('should return false for empty content', () => {
      expect(component.shouldShowExpandButton('')).toBe(false);
      expect(component.shouldShowExpandButton(null)).toBe(false);
      expect(component.shouldShowExpandButton(undefined)).toBe(false);
    });

    it('should return false for short content', () => {
      expect(component.shouldShowExpandButton('short')).toBe(false);
    });

    it('should return true for long content', () => {
      const longContent = 'a'.repeat(200);
      expect(component.shouldShowExpandButton(longContent)).toBe(true);
    });

    it('should return true for multiline content with more than 2 lines', () => {
      const multilineContent = 'line1\nline2\nline3\nline4';
      expect(component.shouldShowExpandButton(multilineContent)).toBe(true);
    });

    it('should handle JSON objects', () => {
      const smallObject = { key: 'value' };
      // JSON.stringify with pretty print makes this 3 lines: {\n  "key": "value"\n}
      // Since lines > 2, expand button should show
      expect(component.shouldShowExpandButton(smallObject)).toBe(true);

      const largeObject = {
        key1: 'value1'.repeat(50),
        key2: 'value2'.repeat(50),
        key3: 'value3'.repeat(50),
      };
      expect(component.shouldShowExpandButton(largeObject)).toBe(true);
    });

    it('should return false for unparseable objects', () => {
      const circularObject: Record<string, unknown> = {};
      circularObject['self'] = circularObject;

      expect(component.shouldShowExpandButton(circularObject)).toBe(false);
    });

    it('should handle numbers and booleans correctly', () => {
      expect(component.shouldShowExpandButton(123)).toBe(false);
      expect(component.shouldShowExpandButton(true)).toBe(false);
    });
  });

  describe('auto-expansion on ngOnChanges', () => {
    it('should auto-expand input when content is very long', () => {
      // Create content that will definitely exceed 3 rows (240+ chars = 3+ rows at 80 chars/row)
      const longInput = 'a'.repeat(320); // 4 rows worth of content
      const testCaseWithLongInput: TestCase = {
        ...mockTestCase,
        input: longInput,
      };

      component.testCase = testCaseWithLongInput;
      component.ngOnChanges({
        testCase: {
          currentValue: testCaseWithLongInput,
          previousValue: null,
          firstChange: true,
          isFirstChange: () => true,
        },
      });

      // Should auto-expand because content needs more than 3 rows
      expect(component.isInputExpanded).toBe(true);
    });

    it('should auto-expand output when content is very long', () => {
      // Create content that will definitely exceed 3 rows (240+ chars = 3+ rows at 80 chars/row)
      const longOutput = 'b'.repeat(320); // 4 rows worth of content
      const testCaseWithLongOutput: TestCase = {
        ...mockTestCase,
        output: longOutput,
      };

      component.testCase = testCaseWithLongOutput;
      component.ngOnChanges({
        testCase: {
          currentValue: testCaseWithLongOutput,
          previousValue: null,
          firstChange: true,
          isFirstChange: () => true,
        },
      });

      // Should auto-expand because content needs more than 3 rows
      expect(component.isOutputExpanded).toBe(true);
    });

    it('should not auto-expand for moderately long content', () => {
      const moderateInput = 'a'.repeat(100);
      const testCaseWithModerateInput: TestCase = {
        ...mockTestCase,
        input: moderateInput,
      };

      component.testCase = testCaseWithModerateInput;
      component.ngOnChanges({
        testCase: {
          currentValue: testCaseWithModerateInput,
          previousValue: null,
          firstChange: true,
          isFirstChange: () => true,
        },
      });

      expect(component.isInputExpanded).toBe(false);
      expect(component.isOutputExpanded).toBe(false);
    });
  });

  describe('Branch Coverage Tests', () => {
    it('should handle falsy values in shouldShowExpandButton', () => {
      expect(component.shouldShowExpandButton(0)).toBe(false);
      expect(component.shouldShowExpandButton(false)).toBe(false);
      expect(component.shouldShowExpandButton('')).toBe(false);
      expect(component.shouldShowExpandButton(null)).toBe(false);
      expect(component.shouldShowExpandButton(undefined)).toBe(false);
    });

    it('should handle different visibility values', () => {
      component.localHidden = true;
      let result = component.getCurrentData();
      expect(result.visibility).toBe('private');

      component.localHidden = false;
      result = component.getCurrentData();
      expect(result.visibility).toBe('public');
    });

    it('should handle testCase changes vs no changes in ngOnChanges', () => {
      // Test with no testCase change
      component.ngOnChanges({});
      expect(component.localInput).toBe('');

      // Test with testCase change
      const newTestCase: TestCase = {
        input: 'new input',
        output: 'new output',
        visibility: 'private',
        weight: 5,
      };

      component.testCase = newTestCase;
      component.ngOnChanges({
        testCase: {
          currentValue: newTestCase,
          previousValue: null,
          firstChange: true,
          isFirstChange: () => true,
        },
      });

      expect(component.localInput).toBe('new input');
      expect(component.localOutput).toBe('new output');
      expect(component.localHidden).toBe(true);
      expect(component.localWeight).toBe(5);
    });

    it('should handle string vs non-string content in getOptimalRows', () => {
      // String content
      const stringContent = 'test string';
      let rows = component.getOptimalRows(stringContent);
      expect(rows).toBeGreaterThanOrEqual(2);

      // Number content
      const numberContent = 123456;
      rows = component.getOptimalRows(numberContent);
      expect(rows).toBe(2);

      // Boolean content
      rows = component.getOptimalRows(true);
      expect(rows).toBe(2);

      // Object content that can be stringified
      const objectContent = { key: 'value' };
      rows = component.getOptimalRows(objectContent);
      expect(rows).toBeGreaterThanOrEqual(2);
    });

    it('should handle different maxRows limits in getOptimalRows', () => {
      const longContent = 'a'.repeat(1000);

      // With default maxRows (8)
      let rows = component.getOptimalRows(longContent);
      expect(rows).toBeLessThanOrEqual(8);

      // With custom maxRows (5)
      rows = component.getOptimalRows(longContent, 5);
      expect(rows).toBeLessThanOrEqual(5);

      // With very low maxRows (3)
      rows = component.getOptimalRows(longContent, 3);
      expect(rows).toBeLessThanOrEqual(3);
    });

    it('should handle minimum rows constraint in getOptimalRows', () => {
      // Very short content should still return minimum 2 rows
      expect(component.getOptimalRows('a')).toBe(2);
      expect(component.getOptimalRows('ab')).toBe(2);
      expect(component.getOptimalRows('abc')).toBe(2);
    });
  });
});
