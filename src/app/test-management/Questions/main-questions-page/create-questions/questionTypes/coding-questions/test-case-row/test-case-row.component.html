<div class="p-6 rounded-xl bg-white space-y-6">
  <div class="grid grid-cols-1 gap-6">
    <!-- Top section: Input and Output, -->
    <div class="space-y-4">
      <!-- Input Section with Expand/Collapse -->
      <div class="space-y-2">
        <div class="flex items-center justify-between">
          <label for="input" class="text-sm font-semibold text-gray-800"
            >Input</label
          >
          <div class="flex items-center gap-2">
            @if (shouldShowExpandButton(localInput) || isInputExpanded) {
              <button
                type="button"
                (click)="toggleInputExpansion()"
                class="text-xs text-indigo-600 hover:text-indigo-800 transition-colors flex items-center gap-1"
              >
                <svg
                  class="w-4 h-4 transition-transform duration-200"
                  [class.rotate-180]="isInputExpanded"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 9l-7 7-7-7"
                  ></path>
                </svg>
                {{ isInputExpanded ? 'Collapse' : 'Expand' }}
              </button>
            }
          </div>
        </div>
        <textarea
          [(ngModel)]="localInput"
          (ngModelChange)="emitUpdate()"
          [rows]="isInputExpanded ? getOptimalRows(localInput, 8) : 2"
          [style.max-height]="isInputExpanded ? '20rem' : 'auto'"
          class="w-full p-3 border resize-none rounded-md bg-gray-50 shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-all duration-300"
          [class.overflow-y-auto]="isInputExpanded"
          placeholder="Enter input data here..."
        ></textarea>
        <p class="text-xs text-gray-500 italic">
          Arguments should be comma-separated (e.g., value1, value2, value3)
        </p>
      </div>

      <!-- Output Section with Expand/Collapse -->
      <div class="space-y-2">
        <div class="flex items-center justify-between">
          <label for="output" class="text-sm font-semibold text-gray-800"
            >Output</label
          >
          <div class="flex items-center gap-2">
            @if (shouldShowExpandButton(localOutput) || isOutputExpanded) {
              <button
                type="button"
                (click)="toggleOutputExpansion()"
                class="text-xs text-indigo-600 hover:text-indigo-800 transition-colors flex items-center gap-1"
              >
                <svg
                  class="w-4 h-4 transition-transform duration-200"
                  [class.rotate-180]="isOutputExpanded"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 9l-7 7-7-7"
                  ></path>
                </svg>
                {{ isOutputExpanded ? 'Collapse' : 'Expand' }}
              </button>
            }
          </div>
        </div>
        <textarea
          [(ngModel)]="localOutput"
          (ngModelChange)="emitUpdate()"
          [rows]="isOutputExpanded ? getOptimalRows(localOutput, 8) : 2"
          [style.max-height]="isOutputExpanded ? '20rem' : 'auto'"
          class="w-full p-3 border resize-none rounded-md bg-gray-50 shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-all duration-300"
          [class.overflow-y-auto]="isOutputExpanded"
          placeholder="Enter expected output here..."
        ></textarea>
      </div>

      <!-- Bottom section: Hidden checkbox, and Delete button and generate output button-->
      <div class="flex flex-wrap justify-between gap-3 items-center">
        <!-- score and hide from candiadte wrapper -->
        <div class="flex items-center gap-6">
          <div class="flex items-center gap-2">
            <input
              type="checkbox"
              [(ngModel)]="localHidden"
              (ngModelChange)="emitUpdate()"
              class="h-5 w-5 text-indigo-600 focus:ring-indigo-500"
            />
            <span class="text-sm font-semibold text-gray-800"
              >Hide Test Case</span
            >
          </div>

          <div class="flex items-center gap-3">
            <label for="weight" class="text-sm font-semibold text-gray-800"
              >Score:</label
            >
            <input
              type="number"
              [(ngModel)]="localWeight"
              (ngModelChange)="emitUpdate()"
              class="max-w-full p-3 border rounded-md bg-gray-50 shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
              min="0"
            />
          </div>
        </div>

        <div class="flex flex-col sm:flex-row justify-end gap-3">
          <button
            (click)="delete.emit()"
            (keydown.enter)="delete.emit()"
            class="w-full sm:w-auto flex gap-2 items-center bg-[#C735320A] text-[#C73A3A] font-medium py-2 px-4 rounded-lg"
          >
            <svg
              width="25"
              height="24"
              viewBox="0 0 25 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M3.5 6H21.5M19.5 6V20C19.5 21 18.5 22 17.5 22H7.5C6.5 22 5.5 21 5.5 20V6M8.5 6V4C8.5 3 9.5 2 10.5 2H14.5C15.5 2 16.5 3 16.5 4V6M10.5 11V17M14.5 11V17"
                stroke="#C73A3A"
                stroke-width="1.4"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>

            <span> Delete</span>
          </button>

          @if (shouldGenerateOutput) {
            <app-custombutton
              [variant]="'primaryWithGreenBg'"
              (clicked)="emitRun()"
              [spinner]="isLoading"
              [disabled]="isLoading"
              class="w-full sm:w-auto"
            >
              Generate Output
            </app-custombutton>
          }
        </div>
      </div>
    </div>
  </div>
</div>
