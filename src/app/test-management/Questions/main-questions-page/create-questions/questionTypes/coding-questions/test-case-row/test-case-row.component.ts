import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
} from '@angular/core';
import { CustomButtonComponent } from '../../../../../../../components/custombutton/custombutton.component';
import { FormsModule } from '@angular/forms';
import { TestCase } from '../../../../../../../Interfaces/codingTypeQuestionsInterface';

@Component({
  selector: 'app-test-case-row',
  standalone: true,
  imports: [CustomButtonComponent, FormsModule],
  templateUrl: './test-case-row.component.html',
})
export class TestCaseRowComponent implements OnChanges {
  @Input() isLoading = false;
  @Input() shouldGenerateOutput = false;
  @Input() testCase?: TestCase;

  @Output() update = new EventEmitter<{
    input: string;
    output: unknown;
    visibility: 'private' | 'public';
    weight: number;
  }>();
  @Output() delete = new EventEmitter<void>();
  @Output() run = new EventEmitter<void>();

  localInput = '';
  localOutput: unknown;
  localHidden = false;
  localWeight: number | null = null;

  // Expansion states for better UX
  isInputExpanded = false;
  isOutputExpanded = false;

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['testCase'] && this.testCase) {
      this.localInput = this.testCase.input;
      this.localOutput = this.testCase.output as string;
      this.localWeight = this.testCase.weight;
      this.localHidden = this.testCase.visibility === 'private';

      // Auto-expand if content is long
      this.autoExpandIfNeeded();
    }
  }

  emitUpdate(): void {
    this.update.emit({
      input: this.localInput,
      output: this.localOutput,
      visibility: this.localHidden ? 'private' : 'public',
      weight: this.localWeight as number,
    });
  }

  emitRun(): void {
    this.emitUpdate();
    this.run.emit();
  }

  getCurrentData(): {
    input: string;
    output: unknown;
    visibility: 'private' | 'public';
    weight: number;
  } {
    return {
      input: this.localInput,
      output: this.localOutput,
      visibility: this.localHidden ? 'private' : 'public',
      weight: this.localWeight as number,
    };
  }

  /**
   * Toggle expansion state for input textarea
   */
  toggleInputExpansion(): void {
    this.isInputExpanded = !this.isInputExpanded;
  }

  /**
   * Toggle expansion state for output textarea
   */
  toggleOutputExpansion(): void {
    this.isOutputExpanded = !this.isOutputExpanded;
  }

  /**
   * Calculate optimal number of rows based on content length
   * @param content - The text content
   * @param maxRows - Maximum number of rows
   * @returns Optimal number of rows
   */
  getOptimalRows(content: unknown, maxRows: number = 8): number {
    if (!content) return 2;

    // Safely convert content to string
    let textContent: string;
    if (typeof content === 'string') {
      textContent = content;
    } else if (typeof content === 'object' && content !== null) {
      try {
        textContent = JSON.stringify(content, null, 2);
      } catch {
        textContent = '[Complex Object]';
      }
    } else if (typeof content === 'number' || typeof content === 'boolean') {
      textContent = content.toString();
    } else {
      textContent = (content as string) || '';
    }

    const lines = textContent.split('\n').length;
    const estimatedLinesFromLength = Math.ceil(textContent.length / 80); // Assume ~80 chars per line

    const calculatedRows = Math.max(lines, estimatedLinesFromLength);
    return Math.min(Math.max(calculatedRows, 2), maxRows);
  }

  /**
   * Auto-expand textareas if content is too long to fit in 2 rows
   */
  private autoExpandIfNeeded(): void {
    // Only auto-expand for significantly long content to avoid unnecessary expansions
    const inputRows = this.getOptimalRows(this.localInput); // Use default maxRows (8)
    const outputRows = this.getOptimalRows(this.localOutput); // Use default maxRows (8)

    // Auto-expand only if content needs more than 3 rows (more conservative)
    if (inputRows > 3) {
      this.isInputExpanded = true;
    }
    if (outputRows > 3) {
      this.isOutputExpanded = true;
    }
  }

  /**
   * Check if the expand button should be shown for a given content
   * Only show if content exists and would overflow the basic 2-row height
   * @param content - The content to check
   * @returns True if expand button should be visible
   */
  shouldShowExpandButton(content: unknown): boolean {
    if (!content) return false;

    let textContent: string;
    if (typeof content === 'string') {
      textContent = content;
    } else if (typeof content === 'object' && content !== null) {
      try {
        textContent = JSON.stringify(content, null, 2);
      } catch {
        return false; // Don't show expand for unparseable objects
      }
    } else if (typeof content === 'number' || typeof content === 'boolean') {
      textContent = content.toString();
    } else {
      return false;
    }

    // Check if content would need more than 2 rows
    const lines = textContent.split('\n').length;
    const estimatedLinesFromLength = Math.ceil(textContent.length / 80); // Assume ~80 chars per line
    const neededRows = Math.max(lines, estimatedLinesFromLength);

    return neededRows > 2;
  }
}
