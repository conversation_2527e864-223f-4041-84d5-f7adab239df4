import {
  After<PERSON><PERSON>w<PERSON>hecked,
  ChangeDete<PERSON><PERSON><PERSON>,
  <PERSON>mpo<PERSON>,
  <PERSON>ementRef,
  HostListener,
  inject,
  <PERSON><PERSON><PERSON><PERSON>,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';

import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { HighlightModule } from 'ngx-highlightjs';
import { distinctUntilChanged, Subject, takeUntil } from 'rxjs';
import { AiSparkleSvgComponent } from '../../../../../../components/ai-sparkle-svg/ai-sparkle-svg.component';
import { CodeEditorComponent } from '../../../../../../components/code-editor/code-editor.component';
import { CustomDropdownComponent } from '../../../../../../components/custom-dropdown/custom-dropdown.component';
import { CustomInputComponent } from '../../../../../../components/custom-input/custom-input.component';
import { CustomModalComponent } from '../../../../../../components/custom-modal/custom-modal.component';
import { CustomTextEditorComponent } from '../../../../../../components/custom-text-editor/custom-text-editor.component';
import { CustomButtonComponent } from '../../../../../../components/custombutton/custombutton.component';
import { ThemeToggleComponent } from '../../../../../../components/theme-toggle/theme-toggle.component';
import { SafeHtmlDirective } from '../../../../../../directives/safe-html.directive';
import { FeatureFlagDirective } from '../../../../../../directives/feature-flag.directive';
import {
  AIToJudge0Interface,
  APIAutoGenerateTestCaseResponseInterface,
  TestCase,
} from '../../../../../../Interfaces/codingTypeQuestionsInterface';
import { DashboardNavbarComponent } from '../../../../../../main-app/components/dashboard-navbar/dashboard-navbar.component';
import { CodeExecutionService } from '../../../../../../services/code-execution.service';
import { QuestionsService } from '../../../../../../services/test-management-service/questions.service';
import { ThemeService } from '../../../../../../services/theme.service';
import { ToastService } from '../../../../../../services/toast-service/toast.service';
import { CodingStore } from '../../../../../../stores/coding-store/coding.store';
import {
  isValidTestCases,
  MAX_TEST_CASES,
  parsedHtml,
  validateCodingQuestionTestCases,
  CODING_LANGUAGE_CONFIG,
  getLanguageProcessor,
} from '../../../../../../utils/codingTypeUtils';
import { noWhitespaceTextareaValidator } from '../../../../../../utils/constants';
import { BasicQuestionContainerComponent } from '../../../components/basic-question-container/basic-question-container.component';
import { QuestionTitleComponent } from '../../../components/question-title/question-title.component';
import { TestCaseRowComponent } from './test-case-row/test-case-row.component';
import { RequiredSvgComponent } from '../../../../../../components/required-svg/required-svg.component';

@Component({
  selector: 'app-coding-questions',
  standalone: true,
  imports: [
    BasicQuestionContainerComponent,
    CodeEditorComponent,
    DashboardNavbarComponent,
    CustomButtonComponent,
    QuestionTitleComponent,
    CustomDropdownComponent,
    TestCaseRowComponent,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    CustomInputComponent,
    CustomModalComponent,
    ThemeToggleComponent,
    AiSparkleSvgComponent,
    CustomTextEditorComponent,
    SafeHtmlDirective,
    FeatureFlagDirective,
    HighlightModule,
    RequiredSvgComponent,
  ],
  templateUrl: './coding-questions.component.html',
  styleUrls: [
    '../../../../../../components/questions_options/multi-choice-options/multi-choice-options.component.css',
  ],
})
export class CodingQuestionsComponent implements OnDestroy, AfterViewChecked {
  title = 'Create New Question (Coding) -';
  form: FormGroup;
  fb = inject(FormBuilder);
  private readonly destroy$ = new Subject<void>();

  boilerplateCode = '';
  solutionCode = '';
  currentEditorView: 'boilerplate' | 'solution' = 'solution';
  currentAiView: 'full' | 'testCases' = 'full';
  isEditMode = false;
  router = inject(Router);
  codingStore = inject(CodingStore);
  route = inject(ActivatedRoute);
  codeExecutionService = inject(CodeExecutionService);
  toastService = inject(ToastService);
  cdr = inject(ChangeDetectorRef);
  questionService = inject(QuestionsService);
  themeService = inject(ThemeService);
  selectedTab = 0;
  previousTab: number = 0;
  functionCode = '';
  challengeTitle = '';
  loading = false;
  questionId = '';
  errorMessage = '';
  showGenerateQuestionModal = false;
  isGeneratingQuestions = false;
  questionDescription = new FormControl('');
  isGenerateTestCases = false;
  isGeneratingSolutionOrBoilerplate = false;
  isPreviewMode = false;
  isQuestionExpanded = false;
  isSolutionExpanded = false;
  isSolutionLoading = true;

  @ViewChild(CodeEditorComponent) codeEditor!: CodeEditorComponent;
  @ViewChild(TestCaseRowComponent) testCaseComponent!: TestCaseRowComponent;
  @ViewChild('testCasesContainer', { static: false })
  testCasesContainer!: ElementRef<HTMLDivElement>;
  scrollOffset = 0;
  tabWidth = 120;
  visibleTabsCount = 5;
  titleDescription = 'Add a new coding question to your assessment';
  complexityOptions = [
    'O(1)',
    'O(log n)',
    'O(n)',
    'O(n log n)',
    'O(n²)',
    'O(n³)',
    'O(2ⁿ)',
    'O(n!)',
  ];
  codeConstraints = [
    {
      label: 'Time Limit (ms)',
      control: 'timeLimit',
      placeholder: 'e.g. 1000 ',
      min: 2000,
      max: 10000,
    },
    {
      label: 'Memory Limit (MB)',
      control: 'memoryLimit',
      placeholder: 'e.g. 256',
      min: 128,
      max: 1024,
    },
    {
      label: 'Time Complexity',
      control: 'timeComplexity',
      placeholder: 'e.g. O(n)',
    },

    {
      label: 'Space Complexity',
      control: 'spaceComplexity',
      placeholder: 'e.g. O(n)',
    },
  ];
  constraintsForm!: FormGroup;
  languageOptions = CODING_LANGUAGE_CONFIG;

  codingNav = [
    {
      title: 'Solution Code',
      id: 'solution',
      onClick: () => this.switchCodeView('solution'),
    },
    {
      title: 'Boilerplate Code',
      id: 'boilerplate',
      onClick: () => this.switchCodeView('boilerplate'),
    },
  ];

  codingView = [
    {
      title: 'Details',
      id: 'details',
      onClick: () => this.handleCodingView('details'),
    },
    {
      title: 'Preview ',
      id: 'preview',
      disabled: !this.isPreviewMode,
      onClick: () => this.handleCodingView('preview'),
    },
  ];

  testCases: TestCase[] = [];
  difficultyLevels = ['Easy', 'Medium', 'Hard'];
  allTags = ['Arrays', 'Recursion', 'Math', 'Strings', 'Graphs', 'DP'];
  selectedDifficulty = '';
  selectedTags: string[] = [];
  constraints = '';
  editorial = '';
  score = 0;
  visibilityOptions = ['Public', 'Private', 'Draft'];
  visibility = 'Public';
  sampleInputsOutputs = [{ input: '', output: '' }];
  private hasLoadedCode = false;
  storeStateLoaded = false;
  latestEditorCode = '';

  @HostListener('window:beforeunload', ['$event'])
  beforeunloadHandler(): void {
    localStorage.setItem(
      'coding-data',
      JSON.stringify({
        basicInfo: {
          ...this.form.value,
          questionId: this.questionId,
        },
        language: this.getFormControl('language').value,
        referenceSolution: this.solutionCode,
        codeTemplate: this.boilerplateCode,
        testCases: this.testCases,
        codeConstraints: {
          ...this.constraintsForm.value,
        },
      })
    );
  }

  ngAfterViewChecked(): void {
    if (!this.hasLoadedCode && this.codeEditor && this.storeStateLoaded) {
      this.currentEditorView = 'solution';

      this.loadSelectedCode();
      this.cdr.detectChanges();

      setTimeout(() => {
        this.hasLoadedCode = true;
      });
    }
  }

  constructor() {
    const localStorageData = localStorage.getItem('coding-data');
    this.isEditMode = this.router.url.includes('edit');
    this.form = this.fb.group({
      questionText: ['', [Validators.required, noWhitespaceTextareaValidator]],
      questionTitle: [''],
      domain: ['', Validators.required],
      score: ['', [Validators.required, Validators.min(1)]],
      difficultyLevel: ['', Validators.required],
      answerOptions: this.fb.array([]),
      category: ['', Validators.required],
      language: ['', Validators.required],
      numberOfHiddenTestCases: ['', [Validators.min(0), Validators.max(14)]],
      numberOfTestCases: [
        '',
        [Validators.min(1), Validators.max(MAX_TEST_CASES)],
      ],
    });
    this.setInitialValues();

    if (localStorageData) {
      try {
        const parsedData = JSON.parse(localStorageData);

        if (parsedData) {
          this.codingStore.updateAllState(parsedData);
        } else {
          console.error('Invalid data structure in localStorage');
        }
      } catch (error) {
        console.error('Error parsing localStorage data:', error);
      }

      localStorage.removeItem('coding-data');
    }

    this.codingStore.questionState$.pipe(takeUntil(this.destroy$)).subscribe({
      next: (state) => {
        this.form.patchValue({ ...state.basicInfo });
        this.questionId = state.basicInfo.questionId ?? '';
        this.getFormControl('language').setValue(state.language);
        this.solutionCode = state.referenceSolution;
        this.boilerplateCode = state.codeTemplate;

        setTimeout(() => {
          this.testCases = state.testCases;
          this.constraintsForm.patchValue({
            ...state.codeConstraints,
          });
          this.storeStateLoaded = true;
          this.isPreviewMode =
            this.form.valid && isValidTestCases(this.testCases);
          this.updateCodingView();
        });
      },
    });

    this.form.statusChanges.pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.isPreviewMode = isValidTestCases(this.testCases);
      this.updateCodingView();
    });

    this.form
      .get('numberOfHiddenTestCases')
      ?.valueChanges.pipe(takeUntil(this.destroy$), distinctUntilChanged())
      .subscribe((value: number) => {
        const userInput = Number(value) || 0;
        let maxAllowedHidden = this.MAX_TEST_CASES - this.getPublicTestCases();
        maxAllowedHidden =
          maxAllowedHidden === this.MAX_TEST_CASES
            ? maxAllowedHidden - 1
            : maxAllowedHidden;
        if (userInput > maxAllowedHidden) {
          setTimeout(() => {
            this.form
              .get('numberOfHiddenTestCases')
              ?.setValue(maxAllowedHidden >= 0 ? maxAllowedHidden : 0, {
                emitEvent: false,
              });
          });
        }
      });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  updateCodingView() {
    this.codingView[1].disabled =
      !this.form.valid ||
      !this.isPreviewMode ||
      !this.getFormControl('language').value?.languageName;
  }

  getFormControl(controlName: string): FormControl {
    return this.form.get(controlName) as FormControl;
  }

  getFormControlConstraints(controlName: string): FormControl {
    return this.constraintsForm.get(controlName) as FormControl;
  }

  setInitialValues() {
    const group: { [key: string]: FormControl } = {};
    this.codeConstraints.forEach((field) => {
      group[field.control] = new FormControl('');
    });
    this.constraintsForm = this.fb.group(group);
  }

  previousPage() {
    this.router.navigateByUrl(
      'dashboard/test-management/questions/all-questions'
    );
  }

  onCancel() {
    this.questionService.cancelQuestion.next(true);
  }

  handleCodingView(view: 'details' | 'preview') {
    if (view === 'details') {
      this.router.navigateByUrl(
        this.isEditMode
          ? '/dashboard/test-management/questions/edit-question/code'
          : '/dashboard/test-management/questions/create-question/code'
      );
    } else {
      this.preview();
    }
  }

  handleAiView(view: 'full' | 'testCases') {
    this.currentAiView = view;
  }

  switchCodeView(view: 'boilerplate' | 'solution') {
    if (!this.codeEditor) {
      return;
    }

    this.saveCurrentCode();
    this.currentEditorView = view;
    this.loadSelectedCode();
  }

  private saveCurrentCode() {
    if (!this.codeEditor) return;

    const currentCode = this.codeEditor.getValue(); // always pull from editor

    if (this.currentEditorView === 'boilerplate') {
      this.boilerplateCode = currentCode;
    } else {
      this.solutionCode = currentCode;
    }

    this.latestEditorCode = currentCode;
  }

  private loadSelectedCode() {
    if (!this.codeEditor) {
      console.warn('Code editor not initialized yet.');
      return;
    }

    const codeToLoad =
      this.currentEditorView === 'boilerplate'
        ? this.boilerplateCode
        : this.solutionCode;

    if (this.codeEditor.getValue() !== codeToLoad) {
      this.codeEditor.setValue(codeToLoad);
    }
  }

  onCodeChanged(code: string) {
    this.latestEditorCode = code;
    this.saveCurrentCode();
  }

  shouldShowScrollButtons(): boolean {
    if (
      !this.testCasesContainer?.nativeElement ||
      this.testCases.length === 0
    ) {
      return false;
    }
    const containerWidth = this.testCasesContainer.nativeElement.clientWidth;
    const totalTabsWidth = this.testCases.length * this.tabWidth;
    return totalTabsWidth > containerWidth - 20;
  }

  addTestCase() {
    if (this.testCases.length > 0 && this.testCaseComponent) {
      const updated = this.testCaseComponent.getCurrentData();
      this.updateTestCase(this.selectedTab, updated);
    }

    this.testCases = [
      ...this.testCases,
      {
        input: '',
        output: '',
        visibility: 'private',
        weight: 1,
      },
    ];

    this.selectedTab = this.testCases.length - 1;

    setTimeout(() => {
      this.scrollToMakeTabVisible(this.selectedTab);
      this.cdr.detectChanges();
    }, 0);
  }

  updateTestCase(
    index: number,
    updated: {
      input: string;
      output: unknown;
      visibility: 'private' | 'public';
      weight: number;
    }
  ) {
    this.testCases[index] = { ...updated };
    this.isPreviewMode = isValidTestCases(this.testCases);
    this.updateCodingView();
  }

  removeTestCase(index: number) {
    if (this.testCases.length === 0) {
      return;
    }

    const newTestCases = [...this.testCases];
    newTestCases.splice(index, 1);

    this.testCases = newTestCases;
    let newSelectedTab = this.selectedTab;

    if (this.testCases.length === 0) {
      this.selectedTab = 0;
    } else if (this.selectedTab >= this.testCases.length) {
      newSelectedTab = Math.max(0, this.testCases.length - 1);
      this.selectedTab = newSelectedTab;
    } else if (this.selectedTab > index) {
      newSelectedTab = this.selectedTab - 1;
      this.selectedTab = newSelectedTab;
    }

    if (!this.shouldShowScrollButtons()) {
      this.scrollOffset = 0;
    } else if (this.testCases.length > 0) {
      this.scrollToMakeTabVisible(this.selectedTab);
    }
    this.cdr.detectChanges();
  }

  runTestCase(index: number): void {
    this.loading = true;

    const rawInput = this.testCases[index].input.trim();

    const currentLanguage =
      this.getFormControl('language').value?.languageName ?? 'JavaScript';
    const processor = getLanguageProcessor(currentLanguage);

    let parsedInputArray: unknown[] = [];

    try {
      // Try parsing as array of arguments
      parsedInputArray = JSON.parse(`[${rawInput}]`);
    } catch (err) {
      console.error(
        'Failed to parse input as array, falling back to string:',
        err
      );

      parsedInputArray = [rawInput];
    }

    // Process each argument individually
    const processedInput = parsedInputArray
      .map((arg: unknown) => processor.processTestCaseInput(arg))
      .join(',');

    this.testCases[index].output = 'Running...';

    const languageId = this.getFormControl('language').value.judge0Id;

    const payload = {
      function_code: this.solutionCode,
      test_case: processedInput,
      language_id: languageId,
    };

    this.codeExecutionService
      .runCode(payload.function_code, payload.test_case, payload.language_id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          const formattedOutput = processor.formatOutput(response.data.output);

          if (response) {
            this.testCases[index] = {
              ...this.testCases[index],
              input: processedInput,
              output: formattedOutput,
            };
            this.testCases = [...this.testCases];
          }
        },
        error: (error) => {
          this.errorMessage = error;
          this.testCases[index] = {
            ...this.testCases[index],
            output: 'No output generated',
          };
          this.testCases = [...this.testCases];
          this.loading = false;
        },
        complete: () => {
          this.loading = false;
        },
      });
  }

  /**
   * Fix unquoted string values inside object literals like {key: value}
   */
  fixObjectLiterals(input: string): string {
    let result = '';
    let braceDepth = 0;
    let buffer = '';
    let insideObject = false;

    for (const char of input) {
      if (char === '{') {
        braceDepth++;
        insideObject = true;
        buffer += char;
      } else if (char === '}') {
        braceDepth--;
        buffer += char;

        if (braceDepth === 0) {
          result += this.processObjectString(buffer);
          buffer = '';
          insideObject = false;
        }
      } else {
        if (insideObject) {
          buffer += char;
        }
        if (!insideObject) {
          result += char;
        }
      }
    }

    // Append any leftover (non-closed object)
    if (buffer) {
      result += buffer;
    }

    return result;
  }

  /**
   * Process a single object literal string like {key: value, ...}
   * Quotes unquoted string values.
   */
  processObjectString(objStr: string): string {
    // Remove outer braces
    const inner = objStr.slice(1, -1).trim();

    // Split by commas at depth 0 (ignore commas inside nested objects/arrays)
    const parts: string[] = [];
    let current = '';
    let depth = 0;

    for (const char of inner) {
      if (char === '{' || char === '[' || char === '(') depth++;
      if (char === '}' || char === ']' || char === ')') depth--;

      if (char === ',' && depth === 0) {
        parts.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }

    if (current.trim()) parts.push(current.trim());

    // Process each key:value pair
    const processedParts = parts.map((pair) => {
      const [key, ...rest] = pair.split(':');
      if (rest.length === 0) {
        // no colon found, return as is
        return pair;
      }

      const value = rest.join(':').trim();

      // Check if value needs quoting: unquoted word, not boolean/null/undefined/number, and not quoted
      const needsQuoting =
        /^[a-zA-Z_]\w*$/.test(value) && // looks like a word
        !['true', 'false', 'null', 'undefined'].includes(value) &&
        !/^['"]/.test(value) && // not already quoted
        isNaN(Number(value)); // not a number

      if (needsQuoting) {
        return `${key.trim()}: "${value}"`;
      }

      // else leave value as is
      return pair;
    });

    return `{${processedParts.join(', ')}}`;
  }

  /**
   * Your existing extractValuesFromDeclarations and stringifyUnquotedWords here...
   * (Copy your previous versions)
   */

  extractValuesFromDeclarations(input: string): string {
    const cleaned = input.replace(/^\s*(const|let|var)\s+/, '');

    const parts: string[] = [];
    let current = '';
    const depth = { curly: 0, square: 0, paren: 0 };

    for (const char of cleaned) {
      if (
        char === ',' &&
        depth.curly === 0 &&
        depth.square === 0 &&
        depth.paren === 0
      ) {
        parts.push(current.trim());
        current = '';
      } else {
        current += char;

        if (char === '{') depth.curly++;
        else if (char === '}') depth.curly--;
        else if (char === '[') depth.square++;
        else if (char === ']') depth.square--;
        else if (char === '(') depth.paren++;
        else if (char === ')') depth.paren--;
      }
    }

    if (current.trim()) {
      parts.push(current.trim());
    }

    const values = parts
      .map((part) => {
        const split = part.split('=');
        const value =
          split.length > 1 ? split.slice(1).join('=').trim() : part.trim();
        return value || null;
      })
      .filter((val): val is string => val !== null);

    return values.join(', ');
  }

  stringifyUnquotedWords(input: string): string {
    const trimmedInput = input.trim();

    function isInsideQuotes(str: string, index: number): boolean {
      let inSingle = false;
      let inDouble = false;

      for (let i = 0; i < index; i++) {
        const char = str[i];
        if (char === '"' && !inSingle) inDouble = !inDouble;
        else if (char === "'" && !inDouble) inSingle = !inSingle;
      }

      return inSingle || inDouble;
    }

    const keywords = new Set([
      'true',
      'false',
      'null',
      'undefined',
      'function',
      'return',
      'if',
      'else',
      'for',
      'while',
      'const',
      'let',
      'var',
    ]);

    return trimmedInput.replace(/([a-zA-Z_]\w*)/g, (_, word, offset, str) => {
      const lower = word.toLowerCase();

      if (keywords.has(lower)) {
        return word;
      }

      if (isInsideQuotes(str, offset)) {
        return word;
      }

      return `"${word}"`;
    });
  }

  runBulkTestCases(data: AIToJudge0Interface[]) {
    this.isGenerateTestCases = true;
    const payload = {
      code: this.solutionCode,
      language_id: this.getFormControl('language').value.judge0Id as number,
      testCases: data,
    };
    this.codeExecutionService
      .runBulkTestCases(payload)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          const currentLanguage =
            this.getFormControl('language').value?.languageName ?? 'JavaScript';
          const processor = getLanguageProcessor(currentLanguage);

          response.data.validatedTestCases.forEach(
            (testCase: AIToJudge0Interface) => {
              // Use language-specific output formatting
              const formattedOutput = processor.formatOutput(
                testCase.expected_output
              );

              this.testCases.push({
                input: testCase.input,
                output: formattedOutput,
                visibility: testCase.hidden ? 'private' : 'public',
                weight: 1,
              });
            }
          );
          const generated = response.data.validatedTestCases.length;
          if (this.currentAiView === 'testCases') {
            const successMessage = `Out of ${this.getTotalTestCases()} test cases, ${generated} were generated successfully.`;
            this.toastService.onShow(
              'success',
              successMessage,
              true,
              'success'
            );
          }
          this.showGenerateQuestionModal = false;
          this.isGenerateTestCases = false;
        },
        error: (error) => {
          this.errorMessage = error;
          this.isGenerateTestCases = false;
          this.showGenerateQuestionModal = false;
        },
        complete: () => {
          this.isGenerateTestCases = false;
        },
      });
  }

  toggleTheme(): void {
    this.themeService.toggleTheme();
  }

  onTabSwitch(index: number) {
    if (index === this.selectedTab) {
      return;
    }

    if (
      this.testCases.length > 0 &&
      this.testCaseComponent &&
      this.selectedTab < this.testCases.length
    ) {
      const updated = this.testCaseComponent.getCurrentData();
      this.updateTestCase(this.selectedTab, updated);
    }

    this.previousTab = this.selectedTab;
    this.selectedTab = index;

    this.scrollToMakeTabVisible(index);
    this.cdr.detectChanges();
  }

  preview() {
    this.isPreviewMode = isValidTestCases(this.testCases, this.toastService);

    if (!this.isPreviewMode) {
      return;
    }

    const formValues = this.form.value;
    const payload = {
      basicInfo: {
        questionText: formValues.questionText,
        score: formValues.score,
        questionType: 'Code',
        difficultyLevel: formValues.difficultyLevel,
        domain: formValues.domain,
        category: formValues.category,
        questionTitle: formValues.questionTitle,
        questionId: this.questionId,
      },
      language: {
        languageName: this.getFormControl('language').value
          .languageName as string,
        judge0Id: this.getFormControl('language').value.judge0Id as number,
        monacoValue: this.getFormControl('language').value
          .monacoValue as string,
      },
      referenceSolution: this.solutionCode,
      codeTemplate: this.boilerplateCode,
      testCases: this.testCases,
      codeConstraints: {
        timeComplexity: this.constraintsForm.value.timeComplexity,
        spaceComplexity: this.constraintsForm.value.spaceComplexity,
        timeLimit: this.constraintsForm.value.timeLimit,
        memoryLimit: this.constraintsForm.value.memoryLimit,
      },
    };

    if (payload.language.languageName === '') {
      this.toastService.onShow(
        'error',
        'Please select a programming language',
        true,
        'error'
      );
      return;
    }

    if (payload.referenceSolution === '') {
      this.toastService.onShow(
        'error',
        'Please enter a reference solution',
        true,
        'error'
      );
      return;
    }

    this.codingStore.updateAllState(payload);

    const baseUrl = 'dashboard/test-management/questions';
    const routePath = this.isEditMode
      ? [`${baseUrl}/edit-question/code/preview`]
      : [`${baseUrl}/create-question/code/preview`];
    const queryParams = this.isEditMode
      ? { questionId: this.questionId }
      : undefined;

    this.router.navigate(routePath, { queryParams });
  }

  closeErrorModal() {
    this.errorMessage = '';
  }

  hasInvalidConstraints(): boolean {
    const timeLimit = this.constraintsForm.get('timeLimit')?.value;
    const memoryLimit = this.constraintsForm.get('memoryLimit')?.value;

    const isTimeInvalid =
      timeLimit !== null && timeLimit !== '' && +timeLimit < 2000;
    const isMemoryInvalid =
      memoryLimit !== null && memoryLimit !== '' && +memoryLimit < 128;

    return isTimeInvalid || isMemoryInvalid;
  }

  scrollTestCases(direction: number) {
    if (
      !this.testCasesContainer?.nativeElement ||
      this.testCases.length === 0
    ) {
      return;
    }

    const containerWidth = this.testCasesContainer.nativeElement.clientWidth;
    const totalWidth = this.testCases.length * this.tabWidth;
    const maxScroll = Math.max(0, totalWidth - containerWidth);

    const scrollStep = Math.max(this.tabWidth, containerWidth / 2);

    if (direction === -1) {
      this.scrollOffset = Math.min(0, this.scrollOffset + scrollStep);
    } else if (direction === 1) {
      this.scrollOffset = Math.max(-maxScroll, this.scrollOffset - scrollStep);
    }
  }

  openGenerateTestCasesModal() {
    this.showGenerateQuestionModal = true;
    this.handleAiView('testCases');
  }

  toggleQuestionExpansion() {
    this.isQuestionExpanded = !this.isQuestionExpanded;
  }

  toggleSolutionExpansion() {
    this.isSolutionExpanded = !this.isSolutionExpanded;
    setTimeout(() => {
      this.isSolutionLoading = false;
    }, 1000);
  }

  /**
   * Gets the number of public test cases as a number
   */
  getPublicTestCases(): number {
    return Number(this.form.get('numberOfTestCases')?.value) || 0;
  }

  /**
   * Gets the number of hidden test cases as a number
   */
  getHiddenTestCases(): number {
    return Number(this.form.get('numberOfHiddenTestCases')?.value) || 0;
  }

  /**
   * Gets the total number of test cases (public + hidden) as a number
   */
  getTotalTestCases(): number {
    return this.getPublicTestCases() + this.getHiddenTestCases();
  }

  /**
   * Validates the test case configuration for the generate button
   * Ensures at least 1 public test case and total doesn't exceed 15
   */
  isValidTestCaseConfiguration(): boolean {
    const publicTestCases = this.getPublicTestCases();
    const hiddenTestCases = this.getHiddenTestCases();
    const totalTestCases = this.getTotalTestCases();

    // At least 1 public test case is required
    if (publicTestCases < 1) {
      return false;
    }

    // Total cannot exceed 15
    if (totalTestCases > 15) {
      return false;
    }

    // Hidden test cases cannot be greater than or equal to total
    // This ensures there's always at least 1 public test case
    if (hiddenTestCases >= totalTestCases) {
      return false;
    }

    return true;
  }

  generateTestCases() {
    this.isGenerateTestCases = true;
    const payload = {
      language: this.getFormControl('language').value.monacoValue,
      problem_definition: this.form.value.questionText,
      reference_solution: this.solutionCode,
      ...(this.form.get('numberOfTestCases')?.value
        ? { num_test_cases: this.getTotalTestCases() }
        : {}),
      ...(this.form.get('numberOfHiddenTestCases')?.value
        ? {
            num_hidden_cases: Number(
              this.form.get('numberOfHiddenTestCases')?.value
            ),
          }
        : {}),
    };

    const validation = validateCodingQuestionTestCases(payload);
    if (!validation.isValid) {
      this.toastService.onShow('error', validation.message, true, 'error');
      this.isGenerateTestCases = false;
      return;
    }

    this.codeExecutionService.generateTestCases(payload).subscribe({
      next: (response) => {
        const judge0Results = this.cleanTestCases(response);
        this.runBulkTestCases(judge0Results);
        this.isGeneratingQuestions = false;
      },
      error: (error) => {
        this.isGenerateTestCases = false;
        this.isGeneratingQuestions = false;
        this.showGenerateQuestionModal = false;
        this.toastService.onShow('error', error, true, 'error');
      },
    });
  }

  cleanTestCases(data: APIAutoGenerateTestCaseResponseInterface[]) {
    const currentLanguage =
      this.getFormControl('language').value?.languageName ?? 'JavaScript';
    const processor = getLanguageProcessor(currentLanguage);

    return data.map((testCase) => {
      let parsedInputArray: unknown[] = [];

      // Make sure testCase.input is string or array, else fallback
      const input = testCase.input;

      try {
        if (typeof input === 'string') {
          const raw = input.trim();

          const looksLikeArgs =
            raw.startsWith('function') &&
            (raw.includes('},') || raw.endsWith('null'));

          if (looksLikeArgs) {
            parsedInputArray = JSON.parse(`[${raw}]`);
          } else {
            parsedInputArray = [raw];
          }
        } else if (Array.isArray(input)) {
          parsedInputArray = input;
        } else {
          parsedInputArray = [input];
        }
      } catch {
        parsedInputArray = [input];
      }

      const processedInput = parsedInputArray
        .map((arg: unknown) => {
          if (arg === null || arg === undefined) {
            return String(arg);
          }
          if (typeof arg === 'object') {
            return JSON.stringify(arg, null, 4);
          }
          return processor.processTestCaseInput(arg);
        })
        .join(', ');

      return {
        ...testCase,
        input: processedInput,
      };
    });
  }

  generateSolutionOrBoilerplate() {
    if (!this.getFormControl('language').value.monacoValue) {
      this.toastService.onShow(
        'error',
        'Please Select a language',
        true,
        'error'
      );
      return;
    }

    if (!this.form.value.questionText) {
      this.toastService.onShow(
        'error',
        'Please enter a question ',
        true,
        'error'
      );
      return;
    }

    this.isGeneratingSolutionOrBoilerplate = true;
    const payload = {
      language: this.getFormControl('language').value.monacoValue,
      problem_definition: this.form.value.questionText,
    };
    if (this.currentEditorView === 'solution') {
      this.codeExecutionService.generateSolutionCode(payload).subscribe({
        next: (response) => {
          let code = response.solution;

          code = code.replace(/^```[\w+-]*\n/, '');

          code = code.replace(/```$/, '');

          this.codeEditor.setValue(code);
          this.toastService.onShow('success', 'Solution Generated', true);
          this.isGeneratingSolutionOrBoilerplate = false;
        },
        error: (error) => {
          this.isGeneratingSolutionOrBoilerplate = false;
          this.toastService.onShow('error', error, true, 'error');
        },
      });
    } else {
      this.codeExecutionService.generateBoilerplateCode(payload).subscribe({
        next: (response) => {
          this.isGeneratingSolutionOrBoilerplate = false;
          this.codeEditor.setValue(response.boilerplate);
          this.toastService.onShow('success', 'Boilerplate Generated', true);
        },
        error: (error) => {
          this.isGeneratingSolutionOrBoilerplate = false;
          this.toastService.onShow('error', error, true, 'error');
        },
      });
    }
  }

  displayGenerateModal() {
    this.showGenerateQuestionModal = true;
    this.handleAiView('full');
  }

  generateQuestion() {
    if (!this.questionDescription.value) {
      this.toastService.onShow(
        'error',
        'Please enter a prompt ',
        true,
        'error'
      );
      return;
    }
    if (!this.getFormControl('language').value.monacoValue) {
      this.toastService.onShow(
        'error',
        'Please Select a language',
        true,
        'error'
      );
      return;
    }

    if (!this.form.value.difficultyLevel) {
      this.toastService.onShow(
        'error',
        'Please select a difficulty level',
        true,
        'error'
      );
      return;
    }

    if (
      !this.form.value.domain ||
      !this.form.value.category ||
      !this.form.value.score
    ) {
      this.toastService.onShow(
        'error',
        'All fields are required',
        true,
        'error'
      );
      return;
    }

    this.isGeneratingQuestions = true;
    const payload = {
      simplified_definition: this.questionDescription.value,
      language: this.getFormControl('language').value.monacoValue,
      difficulty_level: this.form.value.difficultyLevel,
    };
    this.codeExecutionService.generateQuestion(payload).subscribe({
      next: (response) => {
        parsedHtml(response.question_definition).then((html) => {
          this.form.get('questionText')?.setValue(html);
        });

        const judge0Results = this.cleanTestCases(response.test_cases);

        this.solutionCode = response.reference_solution;
        this.boilerplateCode = response.boilerplate;
        this.runBulkTestCases(judge0Results);
        if (this.currentEditorView === 'solution') {
          let code = this.solutionCode;

          code = code.replace(/^```[\w+-]*\n/, '');

          code = code.replace(/```$/, '');

          this.codeEditor.setValue(code);
        } else {
          this.codeEditor.setValue(this.boilerplateCode);
        }
        this.toastService.onShow(
          'success',
          'Question generated successfully',
          true,
          'success'
        );

        this.isGeneratingQuestions = false;
        this.closeGenerateModal();
      },
      error: () => {
        this.toastService.onShow(
          'error',
          'Failed to generate question, please try again',
          true,
          'error'
        );
        this.isGeneratingQuestions = false;
      },
    });
  }

  getMaxScrollOffset(): number {
    if (
      !this.testCasesContainer?.nativeElement ||
      this.testCases.length === 0
    ) {
      return 0;
    }

    const containerWidth = this.testCasesContainer.nativeElement.clientWidth;
    const totalWidth = this.testCases.length * this.tabWidth;

    return Math.max(0, totalWidth - containerWidth);
  }

  scrollToMakeTabVisible(tabIndex: number) {
    if (
      !this.testCasesContainer?.nativeElement ||
      this.testCases.length === 0
    ) {
      return;
    }

    const containerWidth = this.testCasesContainer.nativeElement.clientWidth;
    const tabPosition = tabIndex * this.tabWidth;
    const tabEndPosition = tabPosition + this.tabWidth;

    const visibleStart = -this.scrollOffset;
    const visibleEnd = visibleStart + containerWidth;

    if (tabPosition >= visibleStart && tabEndPosition <= visibleEnd) {
      return;
    }

    let newScroll: number;
    if (tabPosition < visibleStart) {
      newScroll = -tabPosition;
    } else {
      newScroll = -(tabEndPosition - containerWidth);
    }

    const maxScroll = this.getMaxScrollOffset();
    this.scrollOffset = Math.max(-maxScroll, Math.min(0, newScroll));
  }

  closeGenerateModal() {
    this.showGenerateQuestionModal = false;
  }

  protected readonly MAX_TEST_CASES = MAX_TEST_CASES;
}
