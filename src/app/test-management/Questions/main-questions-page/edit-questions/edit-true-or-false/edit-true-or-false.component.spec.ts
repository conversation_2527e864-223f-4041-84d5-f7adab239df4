import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ToastService } from '../../../../../services/toast-service/toast.service';
import { QuestionStore } from '../../../../../stores/questions-store/question.store';
import { EditTrueOrFalseComponent } from './edit-true-or-false.component';
import {
  BasicQuestion,
  QuestionsData,
} from '../../../../../Interfaces/questionInterface';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { BehaviorSubject } from 'rxjs';

const mockIsQuestionsSuccess$ = new BehaviorSubject<boolean>(false);
const mockIsQuestionError$ = new BehaviorSubject<boolean>(false);
const questionStore = {
  editQuestion: jest.fn(),
  isQuestionsSuccess$: mockIsQuestionsSuccess$.asObservable(),
  questionsError$: mockIsQuestionError$.asObservable(),
  setQuestionsSuccess: jest.fn(),
};

describe('EditTrueOrFalseComponent', () => {
  let component: EditTrueOrFalseComponent;
  let fixture: ComponentFixture<EditTrueOrFalseComponent>;
  let toastService: { onShow: jest.Mock };

  beforeEach(async () => {
    toastService = { onShow: jest.fn() };

    await TestBed.configureTestingModule({
      imports: [EditTrueOrFalseComponent, HttpClientTestingModule],
      providers: [
        { provide: ToastService, useValue: toastService },
        { provide: QuestionStore, useValue: questionStore },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(EditTrueOrFalseComponent);
    component = fixture.componentInstance;
    component.questionId = '1';
    component.index = 0;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with input data', () => {
    component.editTrueOrFalseData = {
      id: '1',
      system: true,
      timeLimit: '0',
      essayAnswer: { rubrics: '' },
      questionText: 'Is this true?',
      questionType: 'true-or-false',
      score: 1,
      difficultyLevel: 'easy',
      answerOptions: ['True', 'False'],
      correctAnswers: [],
      selectedAnswerIndex: undefined,
      fillInAnswer: { options: [], answer: [] },
      strictMark: false,
      isActive: true,
      category: { id: '1', name: 'General' },
      domain: { id: '1', name: 'General Knowledge' },
    } as QuestionsData;
    fixture.detectChanges();
    expect(component.editTrueOrFalseData).toEqual({
      id: '1',
      system: true,
      timeLimit: '0',
      essayAnswer: { rubrics: '' },
      questionText: 'Is this true?',
      questionType: 'true-or-false',
      score: 1,
      difficultyLevel: 'easy',
      answerOptions: ['True', 'False'],
      correctAnswers: [],
      selectedAnswerIndex: undefined,
      fillInAnswer: { options: [], answer: [] },
      strictMark: false,
      isActive: true,
      category: { id: '1', name: 'General' },
      domain: { id: '1', name: 'General Knowledge' },
    });
  });

  it('should call editQuestion on valid submission', () => {
    const validData: BasicQuestion = {
      questionText: 'Is this true?',
      questionType: 'true-or-false',
      score: 1,
      difficultyLevel: 'easy',
      answerOptions: ['True', 'False'],
    };

    component.onSubmitEditedQuestion(validData);
    expect(questionStore.editQuestion).toHaveBeenCalledWith({
      id: '1',
      question: validData,
      index: 0,
    });
  });

  it('should set loading state on submission', () => {
    const validData: BasicQuestion = {
      questionText: 'Is this true?',
      questionType: 'true-or-false',
      score: 1,
      difficultyLevel: 'easy',
      answerOptions: ['True', 'False'],
    };

    component.onSubmitEditedQuestion(validData);
    expect(component.isLoading).toBe(true);
  });

  it('should show error toast on duplicate answers', async () => {
    const duplicateData: BasicQuestion = {
      questionText: 'Is this true?',
      questionType: 'true-or-false',
      score: 1,
      difficultyLevel: 'easy',
      answerOptions: ['True', 'True'], // Duplicate answers
    };

    jest.spyOn(toastService, 'onShow');

    component.onSubmitEditedQuestion(duplicateData);

    await fixture.whenStable();

    expect(toastService.onShow).toHaveBeenCalledWith(
      'Answers',
      'Duplicate answer options',
      true,
      'error'
    );
  });
});
