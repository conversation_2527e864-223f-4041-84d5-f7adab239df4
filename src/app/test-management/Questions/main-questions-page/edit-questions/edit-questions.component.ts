import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { EditMultiChoiceQuestionComponent } from './edit-multi-choice-question/edit-multi-choice-question.component';
import { EditMultiSelectQuestionComponent } from './edit-multi-select-question/edit-multi-select-question.component';
import { EditEssayQuestionComponent } from './edit-essay-question/edit-essay-question.component';
import { EditTrueOrFalseComponent } from './edit-true-or-false/edit-true-or-false.component';
import { QuestionCreationServiceService } from '../../../../services/test-management-service/question-creation-service.service';
import { Subscription } from 'rxjs';
import { QuestionsData } from '../../../../Interfaces/questionInterface';
import { EditFillInTheBlanksComponent } from './edit-fill-in-the-blanks/edit-fill-in-the-blanks.component';
import { EditMatrixQuestionComponent } from './edit-matrix-question/edit-matrix-question.component';
import { CodingStore } from '../../../../stores/coding-store/coding.store';
import { Category, DomainData } from '../../../../Interfaces/domainInterface';
import { languagesForMonacoAndJudge } from '../../../../utils/codingTypeUtils';
import { RefreshLoaderComponent } from '../../../../components/refresh-loader/refresh-loader.component';

@Component({
  selector: 'app-edit-questions',
  standalone: true,
  imports: [
    EditMultiChoiceQuestionComponent,
    EditMultiSelectQuestionComponent,
    EditEssayQuestionComponent,
    EditTrueOrFalseComponent,
    EditFillInTheBlanksComponent,
    EditMatrixQuestionComponent,
    RefreshLoaderComponent,
  ],
  templateUrl: './edit-questions.component.html',
})
export class EditQuestionsComponent implements OnInit, OnDestroy {
  private readonly subscription: Subscription = new Subscription();
  constructor(
    private readonly route: ActivatedRoute,
    private readonly questionCreationService: QuestionCreationServiceService,
    private readonly codingStore: CodingStore,
    private readonly router: Router
  ) {}
  questionId: string = '';
  questionType: string = '';
  subscriptions: Subscription[] = [];
  singleQuestionSubscription: Subscription | undefined;
  singleQuestion: QuestionsData | undefined;
  index: number | undefined;

  ngOnInit(): void {
    const queryParamsSub = this.route.queryParams.subscribe((params) => {
      this.questionId = params['questionId'];
      this.questionType = params['questionType'];
      this.index = params['index'];
    });
    this.subscription.add(queryParamsSub);
    this.singleQuestionSubscription = this.questionCreationService
      .getSingleQuestion(this.questionId)
      .subscribe((response) => {
        this.singleQuestion = response.data;

        if (this.questionType === 'Code' && this.singleQuestion) {
          this.handleCodeQuestionData();
        }
      });
    this.subscription.add(this.singleQuestionSubscription);
  }

  private handleCodeQuestionData(): void {
    if (!this.singleQuestion) return;

    this.updateBasicInfo();
    this.updateLanguage();
    this.updateCodeTemplate();
    this.updateReferenceSolution();
    this.updateCodeConstraints();
    this.updateTestCases();

    this.router.navigate(
      ['dashboard/test-management/questions/edit-question/code'],
      {
        queryParams: { questionId: this.questionId },
        replaceUrl: true,
      }
    );
  }

  private updateBasicInfo(): void {
    if (this.singleQuestion) {
      this.codingStore.updateBasicInfo({
        domain: this.singleQuestion.domain as DomainData,
        category: this.singleQuestion.category as unknown as Category,
        score: this.singleQuestion.score as unknown as string,
        difficultyLevel: this.singleQuestion
          .difficultyLevel as unknown as string,
        questionText: this.singleQuestion.questionText,
        questionType: this.singleQuestion.questionType,
        questionTitle: this.singleQuestion?.questionTitle,
        questionId: this.singleQuestion.id,
      });
    }
  }

  private updateLanguage(): void {
    const reference_solution = this.singleQuestion?.referenceSolution?.[0];
    if (
      reference_solution?.Language?.name &&
      reference_solution.Language.judgeLanguageId !== undefined
    ) {
      const langName = reference_solution.Language
        .name as keyof typeof languagesForMonacoAndJudge;
      const monacoConfig = languagesForMonacoAndJudge[langName];

      if (monacoConfig) {
        this.codingStore.updateLanguage({
          languageName: langName,
          judge0Id: reference_solution.Language.judgeLanguageId,
          monacoValue: monacoConfig.monacoValue,
        });
      } else {
        console.warn(`Monaco config not found for language: ${langName}`);
      }
    }
  }

  private updateCodeTemplate(): void {
    const codeTemplate = this.singleQuestion?.codeTemplates?.[0];
    if (codeTemplate?.body) {
      this.codingStore.updateCodeTemplate(codeTemplate.body);
    }
  }

  private updateReferenceSolution(): void {
    const solution = this.singleQuestion?.referenceSolution?.[0]?.body;
    if (solution) {
      this.codingStore.updateReferenceSolution(solution);
    }
  }

  private updateCodeConstraints(): void {
    const constraints = this.singleQuestion?.CodeConstraint;
    if (constraints) {
      this.codingStore.updateCodeConstraints({
        timeComplexity: constraints.timeComplexity,
        spaceComplexity: constraints.spaceComplexity,
        timeLimit: constraints.timeLimit as unknown as string,
        memoryLimit: constraints.memoryLimit as unknown as string,
      });
    }
  }

  private updateTestCases(): void {
    if (this.singleQuestion?.testCases) {
      const testCases = this.singleQuestion.testCases.map((tc) => ({
        input: tc.input_data,
        output: tc.output_data,
        visibility: tc.visibility,
        weight: tc.weight,
      }));
      this.codingStore.updateTestCases(testCases);
    }
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
