import { ComponentFixture, TestBed } from '@angular/core/testing';
import { EditMatrixQuestionComponent } from './edit-matrix-question.component';
import { QuestionsService } from '../../../../../services/test-management-service/questions.service';
import { QuestionCreationServiceService } from '../../../../../services/test-management-service/question-creation-service.service';
import { ToastService } from '../../../../../services/toast-service/toast.service';
import { ActivatedRoute, Router } from '@angular/router';
import { of, throwError } from 'rxjs';
import { BasicQuestion } from '../../../../../Interfaces/questionInterface';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { hasDuplicateAnswers } from '../../../../../utils/questionsConstants';
import { provideLottieOptions } from 'ngx-lottie';
import player from 'lottie-web';

jest.mock('../../../../../utils/questionsConstants', () => ({
  hasDuplicateAnswers: jest.fn(),
}));

describe('EditMatrixQuestionComponent', () => {
  let component: EditMatrixQuestionComponent;
  let fixture: ComponentFixture<EditMatrixQuestionComponent>;

  // Mock form-factory-service
  const mockQuestionsService = {
    getAllQuestions: jest.fn(),
    cancelQuestion$: of(true),
  };

  const mockQuestionCreationService = {
    editQuestion: jest.fn(),
  };

  const mockToastService = {
    onShow: jest.fn(),
  };

  const mockRouter = {
    navigate: jest.fn(),
    url: '', // Set default URL as '/create', can be changed during tests
  } as unknown as jest.Mocked<Router>;

  const mockActivatedRoute = {
    queryParams: of({
      questionId: 'test-question-id',
      questionType: 'matrix',
      index: 0,
    }),
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        provideLottieOptions({
          player: () => player,
        }),
        { provide: QuestionsService, useValue: mockQuestionsService },
        {
          provide: QuestionCreationServiceService,
          useValue: mockQuestionCreationService,
        },
        { provide: ToastService, useValue: mockToastService },
        { provide: Router, useValue: mockRouter },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(EditMatrixQuestionComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set questionId from route query params', () => {
    expect(component.questionId).toBe('');
  });

  describe('onSubmitEditedQuestion', () => {
    it('should show toast error for duplicate answers', () => {
      const mockData = {
        answerOptions: ['Option1', 'Option1'],
      };
      (hasDuplicateAnswers as jest.Mock).mockReturnValue(true);

      component.onSubmitEditedQuestion(mockData as BasicQuestion);

      expect(mockToastService.onShow).toHaveBeenCalledWith(
        'Answers',
        'Duplicate answer options',
        true,
        'error'
      );
    });

    it('should show error toast on edit failure', () => {
      const mockData = {
        answerOptions: ['Option1', 'Option2'],
      };
      const mockError = 'Edit failed';

      mockQuestionCreationService.editQuestion.mockReturnValue(
        throwError(() => new Error(mockError))
      );

      component.onSubmitEditedQuestion(mockData as BasicQuestion);

      expect(mockToastService.onShow).toHaveBeenCalledTimes(2);
    });
  });

  it('should successfully edit question and navigate on successful edit', () => {
    const mockData = {
      answerOptions: ['Option1', 'Option2'],
    };

    mockQuestionCreationService.editQuestion.mockReturnValue(of({}));
    mockQuestionsService.getAllQuestions.mockReturnValue(of({}));

    (hasDuplicateAnswers as jest.Mock).mockReturnValue(false);

    component.onSubmitEditedQuestion(mockData as BasicQuestion);

    expect(mockQuestionCreationService.editQuestion).toHaveBeenCalled();
  });
});
