import { Routes } from '@angular/router';
import { TestsComponent } from './tests.component';
import { ComprehensionQuestionPreviewComponent } from './comprehension-test/comprehension-question-preview/comprehension-question-preview.component';
import { LayoutWrapperComponent } from '../../Questions/main-questions-page/shared/layout-wrapper/layout-wrapper.component';

export const testsRoutes: Routes = [
  {
    path: '',
    component: TestsComponent,
    data: { breadcrumb: 'All Tests' },
    children: [
      { path: '', redirectTo: 'all-tests', pathMatch: 'full' },
      {
        path: 'all-tests',
        loadComponent: () =>
          import('./all-tests/all-tests.component').then(
            (c) => c.AllTestsComponent
          ),
      },
      {
        path: '',
        component: LayoutWrapperComponent,
        children: [
          {
            path: 'create-test/regular',
            loadChildren: () =>
              import('./regular-test/regular-test.routes').then(
                (r) => r.regularTestRoutes
              ),
          },
          {
            path: 'edit-test/regular',
            loadChildren: () =>
              import('./regular-test/regular-test.routes').then(
                (r) => r.regularTestRoutes
              ),
          },
          {
            path: 'create-test/comprehension',
            data: { breadcrumb: 'Create Comprehension Test' },
            loadComponent: () =>
              import('./comprehension-test/comprehension-test.component').then(
                (c) => c.ComprehensionTestComponent
              ),
          },
          {
            path: 'preview-test',
            data: { breadcrumb: 'Preview Test' },
            loadComponent: () =>
              import('./preview-test-page/preview-test-page.component').then(
                (c) => c.PreviewTestPageComponent
              ),
          },
          {
            path: 'create-test/regular-test-preview',
            data: { breadcrumb: 'Create Regular Test Preview' },
            loadComponent: () =>
              import(
                './regular-test/regular-test-preview/regular-test-preview.component'
              ).then((c) => c.RegularTestPreviewComponent),
          },
          {
            path: 'edit-test/regular-test-preview',
            data: { breadcrumb: 'Edit Regular Test Preview' },
            loadComponent: () =>
              import(
                './regular-test/regular-test-preview/regular-test-preview.component'
              ).then((c) => c.RegularTestPreviewComponent),
          },
          {
            path: 'edit-test/comprehension',
            data: { breadcrumb: 'Edit Comprehension Test' },
            loadComponent: () =>
              import('./comprehension-test/comprehension-test.component').then(
                (c) => c.ComprehensionTestComponent
              ),
          },
          {
            path: 'create-test/comprehension/preview',
            component: ComprehensionQuestionPreviewComponent,
          },
          {
            path: 'add-to-test/:id',
            data: { breadcrumb: 'Add to Test' },
            loadComponent: () =>
              import('../add-to-test/add-to-test.component').then(
                (c) => c.AddToTestComponent
              ),
          },
        ],
      },
    ],
  },
];
