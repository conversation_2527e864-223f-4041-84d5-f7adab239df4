<div
  class="bg-[#fafafa] rounded-lg border-b-[#082230] p-4 md:p-6 mt-6 justify-between mb-3"
>
  <div
    class="border-b border-[#E5E7EB] pb-3 mb-3 font-medium text-[1.1rem] md:text-[1.25rem]"
  >
    <h1>Select Questions</h1>
  </div>

  <div class="flex flex-col gap-4">
    <div
      class="flex flex-col md:flex-row md:justify-between md:items-center gap-4"
    >
      <div class="flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6">
        @if (!AllDomainQuestionCheck) {
          <div class="flex items-center gap-3">
            <div class="relative input-grey flex items-center">
              <input
                type="checkbox"
                class="w-5 h-5 accent-transparent checked:accent-[#0c4767] cursor-pointer flex-shrink-0"
                (change)="selectAllPageQuestions(page)"
                [checked]="checkAll[page]"
                [disabled]="AllDomainQuestionCheck"
                aria-label="Select all questions on this page"
              />
              @if (
                selectedQuestion.length > 0 &&
                (partialCheck || !checkAll[page]) &&
                !AllDomainQuestionCheck
              ) {
                <button
                  class="absolute h-3 w-3 bg-[#0c4767] flex items-center justify-center top-1 left-1"
                  (click)="selectAllPageQuestions(page)"
                  aria-label="Select all questions on this page"
                >
                  <span></span>
                </button>
              }
            </div>
            <p class="text-[#082230CC] text-[0.875rem] leading-5">
              @if (selectedQuestion.length === 0) {
                <ng-container>
                  Select all <strong>{{ questions.length }}</strong> questions
                  on this page
                </ng-container>
              }

              @if (
                selectedQuestion.length === questions.length &&
                selectedQuestion.length !== 0
              ) {
                <ng-container>
                  <strong>{{ questions.length }}</strong> questions selected
                </ng-container>
              } @else if (selectedQuestion.length > 0) {
                <ng-container>
                  <strong
                    >{{ selectedQuestion.length }} questions selected</strong
                  >
                </ng-container>
              }
            </p>
          </div>
        }

        <div class="flex items-center gap-3">
          <input
            type="checkbox"
            class="h-5 w-5"
            [class.cursor-not-allowed]="checkAll[page] || hasActiveFilters()"
            (change)="selectAllDomainQuestions(page)"
            [checked]="AllDomainQuestionCheck"
            [disabled]="checkAll[page] || hasActiveFilters()"
            aria-label="Select all questions in the domain"
          />
          <p class="text-[#474D66] text-sm font-normal">
            Select all
            <strong>{{ totalNumberOfQuestions }} questions</strong> in this
            domain
            <strong>
              {{ domainName ? ' (' + domainName + ')' : '' }}
            </strong>
          </p>
        </div>

        @if (this.selectedQuestion.length > 0) {
          <button
            (click)="clearSelection()"
            class="text-sm text-blue-600 hover:underline hover:text-blue-800"
            title="This will remove all selected questions across all pages"
          >
            Clear selection
          </button>
        }
      </div>

      <div class="flex items-center justify-end sm:justify-start">
        <div class="w-full sm:w-auto">
          <app-custom-filter-button
            (selectFilterClick)="toggleDropdownFilterVisibility()"
            [dropDownStatus]="isDropdownFilter"
            [disabledFilter]="AllDomainQuestionCheck"
            [filterArray]="getDisplayNames()"
            [selectedFilters]="selectedFilters"
            (selectedFilterItem)="selectFilterItems($event)"
            (closeFilter)="isDropdownFilter = false"
          ></app-custom-filter-button>
        </div>
      </div>
    </div>

    @if (selectedFilters.length > 0) {
      <div class="flex items-center gap-2 flex-wrap">
        @for (item of selectedFilters; track item) {
          <div class="flex flex-col w-full sm:w-auto">
            <span
              class="text-sm text-gray-600"
              [class]="getSelectedValue(item) ? 'opacity-1' : 'opacity-0'"
              id="filterLabel"
              aria-live="polite"
            >
              {{ item }}
            </span>
            <div class="relative w-full sm:w-auto flex-grow">
              <div
                (click)="removeFilterItems(item)"
                (keydown.enter)="removeFilterItems(item)"
                aria-disabled="true"
                class="absolute z-30 -right-1 -top-1 cursor-pointer"
                aria-label="Remove filter"
              >
                <img
                  src="../../../../../assets/icons/cancel.svg"
                  alt="Remove"
                  class="w-4 h-4"
                />
              </div>
              <app-custom-dropdown
                [placeholder]="getSelectedValue(item) || item"
                [options]="getOptions(item)"
                (selectionChange)="selectFilteredOption($event)"
                class="h-full"
              ></app-custom-dropdown>
            </div>
          </div>
        }
      </div>
    }
  </div>
</div>

@if (isLoading) {
  <output class="flex w-full h-full justify-center items-center">
    <div class="flex flex-col h-fit w-fit mt-[10%] mb-[10%]">
      <app-refresh-loader [loadingText]="'Loading...'"></app-refresh-loader>
    </div>
  </output>
} @else if (noQuestionsFound) {
  <app-no-result-found
    [search]="searchTerm"
    [firstMessage]="'No questions found'"
    [message]="'No questions found In Domain: ' + domainName"
    [activeFilters]="getActiveFilters()"
    [domainName]="domainName || ''"
  ></app-no-result-found>
} @else {
  <div>
    <div>
      <main>
        <div class="w-full">
          @for (
            item of questions
              | paginate
                : {
                    itemsPerPage: pageSize,
                    currentPage: page,
                    totalItems: totalNumberOfQuestions,
                  };
            track $index
          ) {
            <label [for]="item.id">
              <div
                class="surface:min-w-[22rem] w-full mb-2 flex items-center gap-3"
              >
                <div
                  class="flex-shrink-0 flex items-center justify-center h-full"
                >
                  <app-custom-check-box
                    [id]="item.id"
                    [value]="item"
                    [checked]="isQuestionSelected(item)"
                    [disabled]="AllDomainQuestionCheck"
                    [customClass]="
                      AllDomainQuestionCheck
                        ? 'input-grey'
                        : 'border-[`#0c4767`]'
                    "
                    (valueChange)="questionSelected($event, page)"
                  >
                  </app-custom-check-box>
                </div>

                <div class="surface:min-w-[22rem] w-full mb-2">
                  <app-questions-card
                    [title]="item.questionText"
                    [middleContent]="item.questionType"
                    [score]="item.score"
                    [showRightSide]="false"
                    [isClickable]="false"
                    [showToolTip]="true"
                    [system]="AllDomainQuestionCheck"
                  >
                    <div
                      class="leftSideBody1 flex items-center"
                      [ngClass]="
                        AllDomainQuestionCheck
                          ? 'grayedOut leftSideBody1'
                          : 'leftSideBody1'
                      "
                    >
                      <div
                        class="leftSideBody1 flex items-center gap-x-1 md:gap-x-2 text-xs md:text-sm"
                        #leftSideBody1
                      >
                        @if (item && item.category && item.category.name) {
                          <div class="flex items-center gap-1">
                            <img
                              src="../../../../assets/icons/code.svg"
                              alt="Category"
                              class="w-3 h-3 md:w-4 md:h-4"
                            />
                            <span class="truncate max-w-[80px] md:max-w-full">{{
                              item.category.name
                            }}</span>
                          </div>
                        }
                        <div
                          class="w-[3px] h-[3px] rounded-full bg-[#0C4767]"
                        ></div>
                        <div class="flex items-center gap-1">
                          <img
                            src="../../../../assets/icons/bytesize_book.svg"
                            alt="Difficulty"
                            class="w-3 h-3 md:w-4 md:h-4"
                          />
                          <span>{{ item.difficultyLevel }}</span>
                        </div>
                      </div>

                      <div
                        class="leftSideBody2 flex items-center gap-1 text-[#474D66] text-[10px] md:text-[12px] mt-1 md:mt-2 lg:mt-0"
                        #leftSideBody2
                      >
                        <img
                          src="../../../../assets/icons/solar_medal.svg"
                          alt="Score"
                          class="w-3 h-3 md:w-4 md:h-4"
                        />
                        <span>Score: {{ item.score }}</span>
                      </div>
                    </div>
                  </app-questions-card>
                </div>
              </div>
            </label>
          }
        </div>
      </main>
      <footer>
        <div class="grid md:flex justify-between items-center">
          <div class="text-[#474D66] text-[16px] font-medium">
            {{ range }}
          </div>

          <app-custom-pagination
            [total]="totalNumberOfQuestions"
            [size]="pageSize"
            (pagechange)="onPageChange($event)"
            (sizeSelect)="onSizeChange($event)"
          ></app-custom-pagination>
        </div>
      </footer>
    </div>
  </div>
}

<div class="flex justify-end gap-x-4 mt-6">
  <app-custombutton
    (click)="previousPage()"
    (keydown.enter)="previousPage()"
    variant="secondary"
    >Previous</app-custombutton
  >
  <app-custombutton
    (click)="proceedToDistribution()"
    (keydown.enter)="proceedToDistribution()"
    variant="testSave"
    [disabled]="!canProceed()"
  >
    Proceed
  </app-custombutton>
</div>
