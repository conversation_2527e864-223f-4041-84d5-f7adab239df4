import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { CustomPaginationComponent } from '../../../../../components/custom-pagination/custom-pagination.component';
import { NgxPaginationModule } from 'ngx-pagination';
import {
  Category,
  FilterList,
  NormalizedQuestionsData,
  QuestionsData,
} from '../../../../../Interfaces/questionInterface';
import {
  distinctUntilChanged,
  filter,
  map,
  Observable,
  Subscription,
} from 'rxjs';
import { QuestionsService } from '../../../../../services/test-management-service/questions.service';
import { CommonModule } from '@angular/common';
import { CustomCheckBoxComponent } from '../../../../../components/custom-check-box/custom-check-box.component';
import { CustomFilterButtonComponent } from '../../../../../reportsManagement/components/custom-filter-button/custom-filter-button.component';
import { CustomDropdownComponent } from '../../../../../components/custom-dropdown/custom-dropdown.component';
import {
  ALL_DIFFICULTY_LEVELS,
  ALL_QUESTION_TYPES,
  FilterType,
  reverseTransformValue,
  transformValue,
} from '../../../../../utils/testManagement';

import { NoResultFoundComponent } from '../../../../../no-result-found/no-result-found.component';
import { calculateRange } from '../../../../../utils/questionsConstants';
import { ToastService } from '../../../../../services/toast-service/toast.service';
import { DomainService } from '../../../../../services/domain.service';
import { ActivatedRoute, Router } from '@angular/router';
import { RefreshLoaderComponent } from '../../../../../components/refresh-loader/refresh-loader.component';
import { CustomButtonComponent } from '../../../../../components/custombutton/custombutton.component';
import { TestStore } from '../../../../../stores/tests-store/test.store';
import { SharedService } from '../../../../../services/shared/shared.service';
import { QuestionsCardComponent } from '../../../../../components/questions-card/questions-card.component';

@Component({
  selector: 'app-test-question-assignment',
  standalone: true,
  imports: [
    CustomPaginationComponent,
    NgxPaginationModule,
    CommonModule,
    CustomCheckBoxComponent,
    CustomFilterButtonComponent,
    CustomDropdownComponent,
    NoResultFoundComponent,
    RefreshLoaderComponent,
    CustomButtonComponent,
    QuestionsCardComponent,
  ],
  templateUrl: './test-question-assignment.component.html',
})
export class TestQuestionAssignmentComponent implements OnInit, OnDestroy {
  title = '';
  questions: QuestionsData[] = [];
  selectedQuestion: QuestionsData[] = [];
  page = 1;
  pageSize = 25;
  searchTerm = '';
  domainId: string | undefined;
  domainName: string | undefined;
  AllDomainQuestionCheck: boolean = false;
  currentDomainId: string | undefined;
  totalNumberOfQuestions = 0;
  range = '';
  allQuestions$!: Observable<NormalizedQuestionsData>;
  subscriptions: Subscription[] = [];
  checkAll: boolean[] = [];
  partialCheck: boolean = false;
  queryParams: Record<string, string> = {};
  filterArray = ['All Questions', ...ALL_QUESTION_TYPES];
  icon = '../../../../../assets/reportsManagement/Filters lines.svg';
  noQuestionsFound: boolean = false;
  isLoading: boolean = true;
  @ViewChild(CustomFilterButtonComponent)
  filterButtonComponent!: CustomFilterButtonComponent;
  allCategories: Category[] = [];
  testId: string = '';
  isEditMode = false;
  constructor(
    private readonly testStore: TestStore,
    private readonly questionService: QuestionsService,
    private readonly toast: ToastService,
    private readonly domainService: DomainService,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly sharedService: SharedService
  ) {}

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  ngOnInit(): void {
    if (this.router.url.includes('edit')) {
      this.isEditMode = true;
    }
    this.subscriptions.push(
      this.route.queryParams.subscribe((params) => {
        this.domainId = params['domainId'];
        this.currentDomainId = this.domainId;
        this.domainName = params['domainName']
          ? params['domainName'].replaceAll('-', ' ')
          : '';
        this.testId = params['testId'];
        this.queryParams = params;
      })
    );

    this.subscriptions.push(
      this.domainService.domainData
        .pipe(
          map((domainData) => domainData.data),
          map((data) => (Array.isArray(data) ? data : [data])),
          map((dataArray) =>
            dataArray.find((item) => item.id === this.domainId)
          ),
          filter(
            (item): item is typeof item & { categories: unknown[] } =>
              item !== undefined
          ),
          distinctUntilChanged(
            (prev, curr) => JSON.stringify(prev) === JSON.stringify(curr)
          )
        )
        .subscribe((data) => {
          this.allCategories = data.categories;
          this.filterLists = [
            {
              display: 'All',
              key: '',
              value: ['ALL'],
            },
            {
              display: 'Question Types',
              key: FilterType.QUESTION_TYPE,
              value: ALL_QUESTION_TYPES,
            },
            {
              display: 'Categories',
              key: FilterType.CATEGORY,
              value: this.getCategories(this.allCategories),
            },
            {
              display: 'Difficulty Level',
              key: FilterType.DIFFICULTY_LEVEL,
              value: ALL_DIFFICULTY_LEVELS,
            },
          ];
          this.restoreFiltersFromQueryParams();
        })
    );

    this.subscriptions.push(
      this.testStore.getSelectedQuestionsWithIfDomainIsChecked.subscribe(
        (response) => {
          this.selectedQuestion = response.selectedQuestions;
          this.AllDomainQuestionCheck = response.isDomainSelected;
        }
      )
    );
  }

  restoreFiltersFromQueryParams() {
    if (!this.queryParams) return;

    const param = this.queryParams;

    this.selectedFilters = [];
    this.selectedValues = {};

    if (param['questionType']) {
      const reversedValue = reverseTransformValue(param['questionType']); // e.g. "Multiple Choice"
      this.selectedValues['Question Types'] = reversedValue;
    }

    if (param['categoryId']) {
      const selectedCategory = this.allCategories.find(
        (cat) => cat.id === param['categoryId']
      );
      if (selectedCategory) {
        this.selectedValues['Categories'] = selectedCategory.name;
      }
    }

    if (param['difficultyLevel']) {
      const normalizedLevel = ALL_DIFFICULTY_LEVELS.find(
        (level) =>
          level.toLowerCase() === param['difficultyLevel'].toLowerCase()
      );
      if (normalizedLevel) {
        this.selectedValues['Difficulty Level'] = normalizedLevel;
      }
    }

    // Ensure selectedFilters contains all keys present in selectedValues
    for (const key in this.selectedValues) {
      if (this.selectedValues[key] && !this.selectedFilters.includes(key)) {
        this.selectedFilters.push(key);
      }
    }

    this.getAllQuestions(param);
  }

  getAllQuestions(filters: Record<string, string> = {}) {
    this.isLoading = true;
    if (this.domainId) {
      this.subscriptions.push(
        this.questionService
          .getAllDomainQuestions(
            this.page,
            this.pageSize,
            this.domainId,
            filters
          )
          .subscribe({
            next: (data) => {
              this.questions = this.domainId ? data.data.data : [];
              this.totalNumberOfQuestions = this.domainId
                ? data.data.totalItems
                : 0;
              this.testStore.setTotalNumberOfQuestions(
                this.totalNumberOfQuestions
              );
              this.range = calculateRange(
                this.page,
                this.pageSize,
                this.totalNumberOfQuestions
              );
              this.autoCheckAll();
              this.noQuestionsFound = this.questions.length === 0;
              this.isLoading = false;
            },
            error: (error) => {
              this.toast.onShow('Error', error, true, 'error');
              this.isLoading = false;
            },
          })
      );
    }
  }

  questionSelected(item: QuestionsData, page: number) {
    if (this.AllDomainQuestionCheck) {
      return;
    }

    const index = this.selectedQuestion.findIndex((t) => t.id === item.id);
    if (index === -1) {
      this.selectedQuestion.push(item);
    } else {
      this.selectedQuestion.splice(index, 1);
    }

    const allSelected = this.questions.every((item) =>
      this.selectedQuestion.some((t) => t.id === item.id)
    );
    const noneSelected = this.questions.every(
      (item) => !this.selectedQuestion.some((t) => t.id === item.id)
    );
    this.checkAll[page] = allSelected;
    this.partialCheck = !this.checkAll[page] && !noneSelected;

    this.testStore.setSelectedQuestionsWithDomain({
      isDomainSelected: this.AllDomainQuestionCheck,
      selectedQuestions: this.selectedQuestion,
    });
  }

  isQuestionSelected(item: QuestionsData) {
    if (this.AllDomainQuestionCheck) {
      return true;
    }
    return this.selectedQuestion.some((t) => t.id === item.id);
  }

  onPageChange($event: number) {
    if (this.page === $event) return;
    this.page = $event;
    this.loadQuestions();
  }

  onSizeChange(event: number): void {
    this.pageSize = event;
    this.page = 1;
    this.loadQuestions();
  }

  autoCheckAll() {
    if (this.AllDomainQuestionCheck) {
      this.checkAll = [];
      return;
    }

    const selectedQuestions = this.selectedQuestion;
    if (selectedQuestions) {
      this.selectedQuestion = selectedQuestions;
      const isAllSelected = this.questions.every((item) =>
        this.selectedQuestion.some((t) => t.id === item.id)
      );

      this.checkAll[this.page] = this.questions.length > 0 && isAllSelected;
      this.partialCheck = !this.checkAll[this.page] && !isAllSelected;
    }
  }

  selectAllPageQuestions(page: number) {
    if (!this.checkAll[page]) {
      this.questions.forEach((item) => {
        if (!this.selectedQuestion.some((t) => t.id === item.id)) {
          this.selectedQuestion.push(item);
        }
      });
      this.checkAll[page] = true;
    } else {
      this.questions.forEach((item) => {
        const index = this.selectedQuestion.findIndex((t) => t.id === item.id);
        if (index !== -1) {
          this.selectedQuestion.splice(index, 1);
        }
      });
      this.checkAll[page] = false;
    }
    this.testStore.setSelectedQuestionsWithDomain({
      isDomainSelected: this.AllDomainQuestionCheck,
      selectedQuestions: this.selectedQuestion,
    });
    this.partialCheck = false;
  }

  selectAllDomainQuestions(page: number) {
    this.AllDomainQuestionCheck = !this.AllDomainQuestionCheck;
    this.partialCheck = false;
    this.checkAll[page] = false;

    if (this.AllDomainQuestionCheck) {
      this.fetchAllDomainQuestions();
    } else {
      this.selectedQuestion = [];
      this.testStore.setSelectedQuestionsWithDomain({
        isDomainSelected: this.AllDomainQuestionCheck,
        selectedQuestions: this.selectedQuestion,
      });
    }
  }

  fetchAllDomainQuestions() {
    if (this.domainId) {
      this.questionService
        .getAllDomainQuestions(
          1,
          this.totalNumberOfQuestions,
          this.domainId,
          {}
        )
        .subscribe({
          next: (data) => {
            this.testStore.setSelectedQuestionsWithDomain({
              isDomainSelected: this.AllDomainQuestionCheck,
              selectedQuestions: data.data.data,
            });
          },
          error: (error) => {
            this.toast.onShow('Error', error, true, 'error');
          },
        });
    }
  }

  loadQuestions() {
    // Construct query params with possible nulls (to remove keys from URL)
    const queryParamsForRouter: Record<string, string | null> = {};

    // Construct filters object without nulls for API call
    const filtersForApi: Record<string, string> = {};

    if (this.domainId) {
      queryParamsForRouter['domainId'] = this.domainId;
      filtersForApi['domainId'] = this.domainId;
    }

    if (this.selectedValues['Categories']) {
      const selectedCategory = this.allCategories.find(
        (cat) => cat.name === this.selectedValues['Categories']
      );
      if (selectedCategory) {
        queryParamsForRouter['categoryId'] = selectedCategory.id;
        filtersForApi['categoryId'] = selectedCategory.id;
      } else {
        queryParamsForRouter['categoryId'] = null;
      }
    } else {
      queryParamsForRouter['categoryId'] = null;
    }

    if (this.selectedValues['Question Types']) {
      const questionType = transformValue(
        this.selectedValues['Question Types']
      );
      queryParamsForRouter['questionType'] = questionType;
      filtersForApi['questionType'] = questionType;
    } else {
      queryParamsForRouter['questionType'] = null;
    }

    if (this.selectedValues['Difficulty Level']) {
      const difficultyLevel = transformValue(
        this.selectedValues['Difficulty Level']
      );
      queryParamsForRouter['difficultyLevel'] = difficultyLevel;
      filtersForApi['difficultyLevel'] = difficultyLevel;
    } else {
      queryParamsForRouter['difficultyLevel'] = null;
    }

    // Update the URL, removing any filters set to null
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: queryParamsForRouter,
      queryParamsHandling: 'merge',
    });

    // Pass only strings to the API call - no nulls
    this.getAllQuestions(filtersForApi);
  }

  filterQuestions(item: string) {
    this.isLoading = true;
    let questionType: string | undefined;

    if (item !== 'All Questions') {
      questionType = transformValue(item);
      this.getAllQuestions({ questionType: questionType });
    }
    this.getAllQuestions({});
  }

  handleKeydown(event: KeyboardEvent, page: number): void {
    if (event.key === 'Enter' || event.key === ' ') {
      this.selectAllPageQuestions(page);
    }
  }

  selectedFilters: string[] = [];
  filterLists: FilterList[] = [];
  isDropdownFilter = false;
  getSelectedValue(item: string): string {
    return this.selectedValues[item] || '';
  }
  selectedValues: { [key: string]: string } = {};

  selectFilteredOption(item: string) {
    this.page = 1;
    const selectedFilter = this.filterLists.find((filter) =>
      filter.value.includes(item)
    );

    if (!selectedFilter) return;

    this.selectedValues[selectedFilter.display] = item;

    // Make sure the filter type is in selectedFilters array
    if (!this.selectedFilters.includes(selectedFilter.display)) {
      this.selectedFilters.push(selectedFilter.display);
    }

    this.loadQuestions();
  }

  removeFilterItems(item: string) {
    delete this.selectedValues[item];
    this.selectedFilters = this.selectedFilters.filter(
      (filter) => filter !== item
    );
    this.filterButtonComponent.removeFilter(item);
    this.loadQuestions();
  }

  getOptions(key: string) {
    return this.filterLists.find((item) => item.display === key)?.value ?? [];
  }

  toggleDropdownFilterVisibility() {
    this.isDropdownFilter = !this.isDropdownFilter;
  }

  getDisplayNames() {
    return this.filterLists.map((filter) => filter.display);
  }

  selectFilterItems(item: string) {
    if (item === 'All') {
      if (
        this.selectedFilters.length ===
        this.getDisplayNames().filter((items) => items !== 'All').length
      ) {
        this.selectedFilters = [];
      } else {
        this.applyAllFilters();
      }
    } else {
      this.toggleFilterSelection(item);
    }
  }

  applyAllFilters() {
    this.selectedFilters = this.getDisplayNames().filter(
      (items) => items !== 'All'
    );
  }

  toggleFilterSelection(item: string) {
    const index = this.selectedFilters.indexOf(item);
    if (index === -1) {
      this.selectedFilters.push(item);
    } else {
      this.selectedFilters.splice(index, 1);
      // Also remove the value if filter is deselected
      delete this.selectedValues[item];
    }
    this.loadQuestions();
  }

  getCategories(data: Category[]) {
    return data.map((categories) => categories.name);
  }

  canProceed(): boolean {
    return this.AllDomainQuestionCheck || this.selectedQuestion.length > 0;
  }

  hasActiveFilters(): boolean {
    return this.selectedFilters.length > 0;
  }

  proceedToDistribution() {
    const baseRoute = this.isEditMode
      ? '/dashboard/test-management/tests/edit-test/regular'
      : '/dashboard/test-management/tests/create-test/regular';

    const queryParams: Record<string, string> = {};

    // Always conditionally add these if they exist
    if (this.testId) {
      queryParams['testId'] = this.testId;
    }

    if (this.domainId) {
      queryParams['domainId'] = this.domainId;
    }

    if (this.domainName) {
      // Optional: transform or sanitize domainName for URL if needed
      queryParams['domainName'] = transformValue(this.domainName);
    }

    // Map display keys with spaces to query param keys
    const filterKeyMap: Record<string, string> = {
      'Question Types': 'questionType',
      Categories: 'categoryId',
      'Difficulty Level': 'difficultyLevel',
    };

    for (const displayKey in filterKeyMap) {
      const paramKey = filterKeyMap[displayKey];

      const selectedValue = this.selectedValues[displayKey];
      if (!selectedValue) continue;

      if (displayKey === 'Categories') {
        const category = this.allCategories.find(
          (cat) => cat.name === selectedValue
        );
        if (category) {
          queryParams[paramKey] = category.id;
        }
      } else {
        queryParams[paramKey] = transformValue(selectedValue);
      }
    }

    this.sharedService.setActiveStep(2);
    this.router.navigate([`${baseRoute}/test-distribution`], { queryParams });
  }

  previousPage() {
    const baseRoute = this.isEditMode
      ? '/dashboard/test-management/tests/edit-test/regular'
      : '/dashboard/test-management/tests/create-test/regular';

    const queryParams = {
      testId: this.testId,
    };
    this.sharedService.setActiveStep(0);
    this.router.navigate([`${baseRoute}/basicInformation`], { queryParams });
  }

  getActiveFilters(): { type: string; value: string }[] {
    const activeFilters: { type: string; value: string }[] = [];

    // Check both selectedFilters and selectedValues
    // to ensure we capture all active filters
    for (const filterType in this.selectedValues) {
      const value = this.selectedValues[filterType];
      if (value) {
        activeFilters.push({
          type: filterType,
          value: value,
        });
      }
    }

    return activeFilters;
  }

  clearSelection(): void {
    this.selectedQuestion = [];
    this.AllDomainQuestionCheck = false;
    this.partialCheck = false;
    this.checkAll = [];

    this.testStore.setSelectedQuestionsWithDomain({
      isDomainSelected: false,
      selectedQuestions: [],
    });
  }
}
