import { Routes } from '@angular/router';
import { RegularTestComponent } from './regular-test.component';

export const regularTestRoutes: Routes = [
  {
    path: '',
    component: RegularTestComponent,
    children: [
      { path: '', redirectTo: 'basicInformation', pathMatch: 'full' },
      {
        path: 'basicInformation',
        loadComponent() {
          return import(
            './test-basic-information/test-basic-information.component'
          ).then((c) => c.TestBasicInformationComponent);
        },
        data: {
          breadcrumb: 'Test Setup',
          hideButtons: false,
          showRegularTestBreadcrumb: false,
        },
      },
      {
        path: 'questions-assignment',
        loadComponent() {
          return import(
            './test-question-assignment/test-question-assignment.component'
          ).then((c) => c.TestQuestionAssignmentComponent);
        },
        data: {
          breadcrumb: 'Questions Selection',
          hideButtons: false,
          index: 1,
          showRegularTestBreadcrumb: true,
        },
      },
      {
        path: 'test-distribution',
        loadComponent() {
          return import('./test-distribution/test-distribution.component').then(
            (c) => c.TestDistributionComponent
          );
        },
        data: {
          breadcrumb: 'Distribution',
          hideButtons: false,
          index: 2,
          showRegularTestBreadcrumb: true,
        },
      },
      {
        path: 'preview',
        loadComponent() {
          return import(
            './regular-test-preview/regular-test-preview.component'
          ).then((c) => c.RegularTestPreviewComponent);
        },
        data: {
          breadcrumb: 'Preview',
          hideButtons: false,
          showRegularTestBreadcrumb: true,
        },
      },
    ],
  },
];
