import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  inject,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  Renderer2,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { Subscription } from 'rxjs';
import { MatIconModule } from '@angular/material/icon';
import { SafeHtml } from '@angular/platform-browser';
import { MultiSelectComponent } from '../../question-type/multi-select/multi-select.component';
import { MultiChoiceComponent } from '../../question-type/multi-choice/multi-choice.component';
import { TrueOrFalseComponent } from '../../question-type/true-or-false/true-or-false.component';
import { EssayComponent } from '../../question-type/essay/essay.component';
import { CustomModalComponent } from '../../../components/custom-modal/custom-modal.component';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
} from '@angular/forms';
import { ToastService } from '../../../services/toast-service/toast.service';
import { TestTakingService } from '../../../services/test-taking-service/test-taking.service';
import { QuestionsService } from '../../../services/test-management-service/questions.service';
import { AnalyticsService } from '../../../services/analytics.service';
import { TestTaking } from '../../../Interfaces/Types/testTakingInterface';
import { FillInTheBlanksComponent } from '../../question-type/fill-in-the-blanks/fill-in-the-blanks.component';
import {
  MatrixSubquestion,
  MatrixTableComponent,
} from '../../../test-management/Questions/main-questions-page/create-questions/questionTypes/matrix-questions/matrix-table/matrix-table.component';
import {
  CodingAnswer,
  Question,
  SolutionResult,
  SolutionTestCases,
} from '../../../Interfaces/Types/testTakerInterface';
import { TestTakingCodeEditorComponent } from '../../question-type/test-taking-code-editor/test-taking-code-editor.component';
import { CodingTestCasesComponent } from '../../question-type/coding-test-cases/coding-test-cases.component';
import { SafeHtmlDirective } from '../../../directives/safe-html.directive';
import { ActivatedRoute } from '@angular/router';
@Component({
  selector: 'app-base-test-interface',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MultiSelectComponent,
    MultiChoiceComponent,
    TrueOrFalseComponent,
    EssayComponent,
    CustomModalComponent,
    FormsModule,
    FillInTheBlanksComponent,
    MatrixTableComponent,
    TestTakingCodeEditorComponent,
    CodingTestCasesComponent,
    SafeHtmlDirective,
  ],
  templateUrl: './base-test-interface.component.html',
  styleUrl: './base-test-interface.component.css',
})
export class BaseTestInterfaceComponent
  implements OnInit, AfterViewInit, OnChanges, OnDestroy
{
  private readonly subscription: Subscription = new Subscription();
  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
  @Input() assessmentId = '';
  @Input() sectionName = '';
  @Input() instructions = '';
  @Input() description = '';
  @Input() testId = '';
  @Input() testTakerId = '';
  @Input() questionNumber: string | number = '';
  @Input() questionType = '';
  @Input() questionText = '';
  @Input() questionOptions: string[] = [];
  @Input() fillInTheBlanksOptions: Array<{
    value: string;
    id: string;
    blank: string;
    isBlank: boolean;
  }> = [];

  @Input() columnOptions: string[] = [];
  @Input() subQuestions!: MatrixSubquestion[];
  @Input() currentQuestion: Question | undefined;
  @Input() questionId: string = '';
  @Input() isQuestionFlagged: boolean = false;
  @Output() questionFlagStatus = new EventEmitter<boolean>();
  @Output() emitSubmitPage = new EventEmitter<boolean>();
  @Output() emitAnswerChoices = new EventEmitter<
    string[] | MatrixSubquestion[] | { code: string; languageId: number }[]
  >();
  displayFlagModal = false;
  displayInstructions = false;
  sanitizedInstruction: SafeHtml | undefined;
  questionForm!: FormGroup;
  isFlaggingQuestion = false;
  isUnflaggingQuestion = false;
  flaggedQuestionArray: {
    question: string;
    checked: boolean;
  }[] = [
    {
      question: 'The question does not pertain to the stated domain.',
      checked: false,
    },
    {
      question: 'The question contains inappropriate content.',
      checked: false,
    },
    {
      question: 'Other',
      checked: false,
    },
  ];
  typedReason: string = '';
  flaggedReasonArray: string[] = [];
  error: string = '';
  flaggedReason: string = '';
  testIdIndex: number | undefined;
  userInputs: string[] = [];
  codeTypeSolutionTestCases: SolutionTestCases[] = [];
  codeSolutionResult: SolutionResult[] = [];
  private readonly analyticsService = inject(AnalyticsService);
  private readonly toast = inject(ToastService);
  private readonly activatedRoute = inject(ActivatedRoute);
  private readonly testTakingService = inject(TestTakingService);
  private readonly questionService = inject(QuestionsService);
  private readonly fb = inject(FormBuilder);
  private readonly renderer = inject(Renderer2);
  private readonly cdr = inject(ChangeDetectorRef);
  @ViewChild('container') container!: ElementRef;
  @ViewChild('leftPanel') leftPanel!: ElementRef;
  @ViewChild('rightPanel') rightPanel!: ElementRef;
  @ViewChild('divider') divider!: ElementRef;
  @ViewChild('topPanel') topPanel!: ElementRef;
  @ViewChild('bottomPanel') bottomPanel!: ElementRef;
  @ViewChild('verticalDivider') verticalDivider!: ElementRef;
  isMobile = window.innerWidth < 1024;
  topHeight = 50;
  leftWidth = 50;
  isDragging = false;
  editorFullScreen = false;
  isVerticalDragging = false;
  startX = 0;
  startLeftWidth = 0;
  startY = 0;
  // Mobile coding question toggle
  showQuestionOnMobile = true; // true = show question, false = show editor/test cases
  startTopHeight = 0;
  returnFromSubmitPage = false;

  ngOnInit(): void {
    const actRoute = this.activatedRoute.queryParams.subscribe((params) => {
      this.returnFromSubmitPage = params['fromSubmitPage'];
    });
    this.subscription.add(actRoute);
    if (this.testTakingService.inProgressReturnedData) {
      this.testIdIndex =
        this.testTakingService.inProgressReturnedData.assessment.tests.findIndex(
          (test) => test.id === this.testId
        );

      // Check if the testIdIndex is valid and the test exists
      if (this.testIdIndex !== -1) {
        const currentTest =
          this.testTakingService.inProgressReturnedData.assessment.tests[
            this.testIdIndex
          ];
        this.instructions = currentTest.instructions;
      }
    }
  }

  ngAfterViewInit(): void {
    this.questionForm = this.fb.group({});
    this.cdr.detectChanges();

    // Delay to ensure ViewChild is available
    setTimeout(() => {
      if (this.container?.nativeElement) {
        this.replaceBlanksWithInputs();
      }
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['questionText']) {
      const previousValue = changes['questionText'].previousValue;
      const currentValue = changes['questionText'].currentValue;
      if (previousValue !== currentValue) {
        this.questionForm = this.fb.group({});
        setTimeout(() => {
          if (this.container?.nativeElement) {
            this.replaceBlanksWithInputs();
          }
          this.cdr.detectChanges();
        });
      }

      this.flaggedReasonArray = [];
      this.typedReason = '';
      this.flaggedQuestionArray = this.flaggedQuestionArray.map((item) => ({
        ...item,
        checked: false,
      }));
    }
  }

  adjustInputWidth(input: HTMLInputElement): void {
    const minWidth = 60;
    const span = this.renderer.createElement('span');
    this.renderer.setStyle(span, 'visibility', 'hidden');
    this.renderer.setStyle(span, 'position', 'absolute');
    this.renderer.setStyle(span, 'white-space', 'pre');
    this.renderer.setStyle(
      span,
      'font-family',
      window.getComputedStyle(input).fontFamily
    );
    this.renderer.setStyle(
      span,
      'font-size',
      window.getComputedStyle(input).fontSize
    );
    this.renderer.setStyle(
      span,
      'font-weight',
      window.getComputedStyle(input).fontWeight
    );

    const textToMeasure = input.value || input.placeholder;
    this.renderer.appendChild(span, this.renderer.createText(textToMeasure));
    this.renderer.appendChild(document.body, span);

    const textWidth = span.offsetWidth;

    this.renderer.removeChild(document.body, span);

    const newWidth = Math.max(minWidth, textWidth + 10);
    this.renderer.setStyle(input, 'width', `${newWidth}px`);
  }

  replaceBlanksWithInputs(): void {
    const assessmentResults = JSON.parse(
      localStorage.getItem('assessmentResults') as string
    ) as TestTaking;
    let answer: string[] = [];
    if (assessmentResults && assessmentResults.testResults.length > 0) {
      const question = assessmentResults.testResults[0].questionResults.find(
        (q) => q.questionId === this.questionId
      );
      if (question) {
        answer = question.testTakerAnswers;
      }
    }
    const blanks =
      this.container.nativeElement.querySelectorAll('.blank-placeholder');

    blanks.forEach((blank: HTMLElement, index: number) => {
      const input = this.renderer.createElement('input');
      const space = this.renderer.createText(' '); // Create a space
      const controlName = `input_${index}`;
      const control = new FormControl('');
      this.questionForm.addControl(controlName, control);
      this.renderer.setAttribute(input, 'type', 'text');
      this.renderer.setAttribute(input, 'readonly', 'true'); // Make input readonly
      this.renderer.setAttribute(
        input,
        'data-uuid',
        blank.getAttribute('data-uuid') ?? ''
      );
      this.renderer.setAttribute(input, 'formControlName', controlName);
      this.renderer.addClass(input, 'input-no-focus-border');
      this.renderer.setProperty(input, 'placeholder', 'drag here...');
      this.renderer.setProperty(input, 'value', answer[index] || '');
      this.adjustInputWidth(input);

      this.renderer.listen(input, 'dragover', (event) => {
        event.preventDefault();
        this.renderer.addClass(input, 'drag-over');
      });

      this.renderer.listen(input, 'dragleave', (event) => {
        event.preventDefault();
        this.renderer.removeClass(input, 'drag-over');
      });

      this.renderer.listen(input, 'drop', (event) => {
        event.preventDefault();
        const data = event.dataTransfer?.getData('text/plain');
        input.value = data as string;
        const control = this.questionForm.get(controlName);
        if (control) {
          control.setValue(data);
        }
        this.adjustInputWidth(input);
        this.renderer.removeClass(input, 'drag-over');

        const arr = Object.values(this.questionForm.value);
        this.emitAnswerChoices.emit(arr as string[]);
      });

      const parent = blank.parentNode;
      if (parent) {
        this.renderer.insertBefore(parent, space, blank);
        this.renderer.insertBefore(parent, input, blank);
        this.renderer.removeChild(parent, blank);
      }
    });
  }

  onChange(e: Event, controlName: string, initialValues: string[]) {
    const value = (e.target as HTMLInputElement).value;
    const control = this.questionForm.get(controlName);
    if (control) {
      control.setValue(value);
      this.adjustInputWidth(e.target as HTMLInputElement);
    }
    const arr = Object.values(this.questionForm.value);
    const updatedAnswer = arr.map((a, index) => {
      return arr[index] ?? initialValues[index];
    });
    this.emitAnswerChoices.emit(updatedAnswer as string[]);
  }

  selectedOptions(value: string[] | CodingAnswer[]) {
    this.emitAnswerChoices.emit(value);
  }
  typeReason(data: { checked: boolean; question: string }, event: Event) {
    this.error = '';
    if (this.flaggedReasonArray.includes(this.typedReason)) {
      this.flaggedReasonArray.splice(
        this.flaggedReasonArray.indexOf(this.typedReason),
        1
      );
    }
    if (data.checked === true && data.question === 'Other') {
      this.typedReason = (event.target as HTMLInputElement).value;
      this.flaggedReasonArray.push(this.typedReason);
    }
  }

  selectedReason(reason: string) {
    this.error = '';
    this.flaggedReason = reason;
  }

  selectedReasons(reason: { checked: boolean; question: string }) {
    reason.checked = !reason.checked;
    if (reason.checked) {
      reason.checked = true;
      this.flaggedReasonArray.push(reason.question);
    } else {
      reason.checked = false;
      this.typedReason = '';
      this.flaggedReasonArray.splice(
        this.flaggedReasonArray.indexOf(reason.question),
        1
      );
    }
  }

  flagQuestion() {
    if (this.flaggedReasonArray.length === 0) {
      this.error = 'You need to select an option';
      return;
    }
    if (this.flaggedReasonArray.includes('Other')) {
      this.flaggedReasonArray.splice(
        this.flaggedReasonArray.indexOf('Other'),
        1
      );
      if (this.typedReason === '') {
        this.error = 'You need to type the reason';
        return;
      }
    }

    this.isFlaggingQuestion = true;
    const flagSub = this.testTakingService
      .flagQuestion({
        testId: this.testId,
        questionId: this.questionId,
        reasonOfFlagging: this.flaggedReasonArray,
        questionText: this.questionText,
      })
      .subscribe({
        next: () => {
          this.toast.onShow(
            'success',
            'Question flagged successfully',
            true,
            'success'
          );
          this.displayFlagModal = false;
          this.isFlaggingQuestion = false;
          this.questionFlagStatus.emit(true);
          this.analyticsService
            .track('Question Flagged', {
              questionId: this.questionId,
              testId: this.testId,
              assessmentId: this.assessmentId,
              reason: this.flaggedReasonArray,
            })
            .then();
        },
        error: (err) => {
          this.toast.onShow('error', err, true, 'error');
          this.isFlaggingQuestion = false;
          this.analyticsService
            .track('Question Flagging Failed', {
              testId: this.testId,
              assessmentId: this.assessmentId,
              reason: this.flaggedReasonArray,
            })
            .then();
        },
      });
    this.subscription.add(flagSub);
  }

  unflagQuestion() {
    if (this.isUnflaggingQuestion) {
      return;
    }

    this.isUnflaggingQuestion = true;
    const unflagSub = this.testTakingService
      .unflagQuestion({
        questionId: this.questionId,
        testId: this.testId,
      })
      .subscribe({
        next: () => {
          this.toast.onShow(
            'success',
            'Question unflagged successfully',
            true,
            'success'
          );
          this.isUnflaggingQuestion = false;
          this.questionFlagStatus.emit(false);
          this.analyticsService
            .track('Question Unflagged', {
              questionId: this.questionId,
              testId: this.testId,
              assessmentId: this.assessmentId,
            })
            .then();
        },
        error: () => {
          this.toast.onShow(
            'error',
            'Oops! Something went wrong',
            true,
            'error'
          );
          this.isUnflaggingQuestion = false;
          this.analyticsService
            .track('Question Unflagging Failed', {
              reason: this.flaggedReasonArray,
              questionId: this.questionId,
              testId: this.testId,
              assessmentId: this.assessmentId,
            })
            .then();
        },
      });
    this.subscription.add(unflagSub);
  }
  getQuestionTypeString(value: string) {
    return this.questionService.questionTypeString[value];
  }

  toggleDisplayInstruction() {
    this.displayInstructions = !this.displayInstructions;
  }
  openInstructionsModal() {
    this.displayInstructions = true;
  }

  handleRadioChange(event: { rowIndex: number; option: string }) {
    const { rowIndex, option } = event;
    const subquestionId = this.subQuestions[rowIndex].id;
    const subquestion = this.subQuestions.find((q) => q.id === subquestionId);

    if (subquestion) {
      subquestion.answers = option;
      const answer = this.subQuestions.map((q) => {
        return {
          subquestionId: q.id,
          answers: [q.answers],
        };
      });
      this.emitAnswerChoices.emit(answer as unknown as MatrixSubquestion[]);
    }
  }

  startDragging(e: MouseEvent) {
    this.isDragging = true;
    this.startX = e.pageX;
    this.startLeftWidth = this.leftWidth;
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';
  }

  startVerticalDragging(e: MouseEvent) {
    this.isVerticalDragging = true;
    this.startY = e.pageY;
    this.startTopHeight = this.topHeight;
    document.body.style.cursor = 'row-resize';
    document.body.style.userSelect = 'none';
  }

  getSolutionTestCases(value: SolutionTestCases[]) {
    this.codeTypeSolutionTestCases = [...value];
  }
  getCodeRunResult(value: SolutionResult[]) {
    this.codeSolutionResult = [...value];
  }

  @HostListener('window:mouseup')
  stopDragging() {
    this.isDragging = false;
    this.isVerticalDragging = false;
    document.body.style.cursor = 'default';
    document.body.style.userSelect = 'auto';
  }

  @HostListener('window:resize', ['$event'])
  onResize(): void {
    this.isMobile = window.innerWidth < 1024;
  }

  toggleMobileView(showQuestion?: boolean): void {
    if (showQuestion !== undefined) {
      // Set to the specified value when provided
      this.showQuestionOnMobile = showQuestion;
    } else {
      // Toggle when no specific value is provided
      this.showQuestionOnMobile = !this.showQuestionOnMobile;
    }

    // Store the current editor code state if we're hiding the editor view
    if (this.questionType === 'Code') {
      // Force immediate update for visibility
      this.cdr.detectChanges();

      // Give time for DOM to update
      setTimeout(() => {
        // Force another change detection cycle
        this.cdr.detectChanges();

        // Adjust layout to ensure components are visible
        if (this.isMobile && !this.showQuestionOnMobile) {
          // Make sure editor is visible and properly sized when switching to it
          document.querySelectorAll('.monaco-editor').forEach((editor) => {
            (editor as HTMLElement).style.height = '400px';
            (editor as HTMLElement).style.minHeight = '400px';
          });
        }
      }, 100);
    }
  }

  @HostListener('window:mousemove', ['$event'])
  onMouseMove(e: MouseEvent) {
    if (this.isDragging) {
      const containerWidth =
        this.leftPanel.nativeElement.parentElement.offsetWidth;
      const dx = e.pageX - this.startX;
      const newLeftWidth = this.startLeftWidth + (dx / containerWidth) * 100;
      this.leftWidth = Math.min(Math.max(newLeftWidth, 20), 80);
      return; // Exit after handling horizontal drag
    }

    if (this.isVerticalDragging) {
      const containerHeight =
        this.topPanel.nativeElement.parentElement.offsetHeight;
      const dy = e.pageY - this.startY;
      const newTopHeight = this.startTopHeight + (dy / containerHeight) * 100;
      this.topHeight = Math.min(Math.max(newTopHeight, 30), 85);
    }
  }

  isEditorFullScreen(screenMode: { scale: number; isFullScreen: boolean }) {
    // Store previous state before updating
    const wasFullScreen = this.editorFullScreen;

    // Update state
    this.editorFullScreen = screenMode.isFullScreen;

    // Update top height for layout
    this.topHeight = screenMode.isFullScreen
      ? screenMode.scale
      : wasFullScreen
        ? 50
        : this.topHeight;

    // For mobile, ensure we show the editor view when entering fullscreen
    if (this.isMobile && screenMode.isFullScreen && this.showQuestionOnMobile) {
      this.showQuestionOnMobile = false;
    }

    // Wait for layout changes to complete before forcing change detection
    setTimeout(() => {
      this.cdr.detectChanges();
    }, 50);
  }
  submitPage() {
    this.emitSubmitPage.emit();
  }
}
