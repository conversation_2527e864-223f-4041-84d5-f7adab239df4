import { ComponentFixture, TestBed } from '@angular/core/testing';
import { <PERSON><PERSON>uilder, FormControl } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Renderer2, ChangeDetectorRef } from '@angular/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { of, throwError } from 'rxjs';

import { BaseTestInterfaceComponent } from './base-test-interface.component';
import { TestTakingService } from '../../../services/test-taking-service/test-taking.service';
import { ToastService } from '../../../services/toast-service/toast.service';
import { QuestionsService } from '../../../services/test-management-service/questions.service';
import { AnalyticsService } from '../../../services/analytics.service';
import { MonacoLoaderService } from '../../../services/monaco-loader.service';
import { MatrixSubquestion } from '../../../test-management/Questions/main-questions-page/create-questions/questionTypes/matrix-questions/matrix-table/matrix-table.component';
import {
  CodingAnswer,
  SolutionTestCases,
  SolutionResult,
} from '../../../Interfaces/Types/testTakerInterface';

describe('BaseTestInterfaceComponent', () => {
  let component: BaseTestInterfaceComponent;
  let fixture: ComponentFixture<BaseTestInterfaceComponent>;
  let mockTestTakingService: Partial<TestTakingService>;
  let mockToastService: Partial<ToastService>;
  let mockQuestionsService: Partial<QuestionsService>;
  let mockAnalyticsService: Partial<AnalyticsService>;
  let mockRenderer: Partial<Renderer2>;
  let mockChangeDetectorRef: Partial<ChangeDetectorRef>;
  let mockActivatedRoute: Partial<ActivatedRoute>;
  let mockMonacoLoaderService: Partial<MonacoLoaderService>;

  beforeEach(async () => {
    // Mock services
    mockTestTakingService = {
      inProgressReturnedData: {
        assessment: {
          tests: [
            {
              id: 'test-1',
              instructions: 'Test instructions for test 1',
            },
          ],
        },
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } as any,
      flagQuestion: jest.fn(),
      unflagQuestion: jest.fn(),
      getCandidateEssayAnswers: jest.fn().mockReturnValue([]),
      getCandidateCodeAnswers: jest.fn().mockReturnValue([]),
      getCandidateAnswers: jest.fn().mockReturnValue([]),
      selectedAnswerChoices: [],
    };

    mockToastService = {
      onShow: jest.fn(),
    };

    mockQuestionsService = {
      questionTypeString: {
        'multi-choice': 'Multiple Choice',
        essay: 'Essay',
        code: 'Code',
      },
    };

    mockAnalyticsService = {
      track: jest.fn().mockResolvedValue(undefined),
    };

    mockRenderer = {
      createElement: jest.fn(),
      appendChild: jest.fn(),
      removeChild: jest.fn(),
      insertBefore: jest.fn(),
      createText: jest.fn(),
      setStyle: jest.fn(),
      addClass: jest.fn(),
      removeClass: jest.fn(),
      setProperty: jest.fn(),
      setAttribute: jest.fn(),
      removeAttribute: jest.fn(),
      listen: jest.fn(),
    };

    mockChangeDetectorRef = {
      detectChanges: jest.fn(),
    };

    mockActivatedRoute = {
      queryParams: of({ fromSubmitPage: false }),
    };

    mockMonacoLoaderService = {
      loadMonaco: jest.fn().mockResolvedValue({}),
      configureMonaco: jest.fn(),
    };

    // Mock localStorage
    const mockLocalStorage = {
      getItem: jest.fn(),
      setItem: jest.fn(),
    };
    Object.defineProperty(window, 'localStorage', {
      value: mockLocalStorage,
      writable: true,
    });

    // Mock window properties
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1200,
    });

    await TestBed.configureTestingModule({
      imports: [BaseTestInterfaceComponent, HttpClientTestingModule],
      providers: [
        FormBuilder,
        { provide: TestTakingService, useValue: mockTestTakingService },
        { provide: ToastService, useValue: mockToastService },
        { provide: QuestionsService, useValue: mockQuestionsService },
        { provide: AnalyticsService, useValue: mockAnalyticsService },
        { provide: Renderer2, useValue: mockRenderer },
        { provide: ChangeDetectorRef, useValue: mockChangeDetectorRef },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: MonacoLoaderService, useValue: mockMonacoLoaderService },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(BaseTestInterfaceComponent);
    component = fixture.componentInstance;
  });

  describe('Component Initialization', () => {
    it('should create the component', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default values', () => {
      expect(component.displayFlagModal).toBe(false);
      expect(component.displayInstructions).toBe(false);
      expect(component.isFlaggingQuestion).toBe(false);
      expect(component.isUnflaggingQuestion).toBe(false);
      expect(component.flaggedReasonArray).toEqual([]);
      expect(component.typedReason).toBe('');
      expect(component.error).toBe('');
      expect(component.userInputs).toEqual([]);
      expect(component.showQuestionOnMobile).toBe(true);
      expect(component.isMobile).toBe(false);
      expect(component.leftWidth).toBe(50);
      expect(component.topHeight).toBe(50);
    });

    it('should set up test instructions from service data on init', () => {
      component.testId = 'test-1';
      component.ngOnInit();

      expect(component.instructions).toBe('Test instructions for test 1');
    });

    it('should handle missing test data gracefully', () => {
      mockTestTakingService.inProgressReturnedData = undefined;
      component.testId = 'non-existent-test';

      expect(() => component.ngOnInit()).not.toThrow();
    });
  });

  describe('Question Flagging', () => {
    beforeEach(() => {
      component.testId = 'test-123';
      component.questionId = 'question-456';
      component.assessmentId = 'assessment-789';
      component.questionText = 'Sample question text';
    });

    describe('flagQuestion', () => {
      it('should show error when no reasons are selected', () => {
        component.flaggedReasonArray = [];

        component.flagQuestion();

        expect(component.error).toBe('You need to select an option');
        expect(mockTestTakingService.flagQuestion).not.toHaveBeenCalled();
      });

      it('should show error when "Other" is selected but no typed reason provided', () => {
        component.flaggedReasonArray = ['Other'];
        component.typedReason = '';

        component.flagQuestion();

        expect(component.error).toBe('You need to type the reason');
        expect(mockTestTakingService.flagQuestion).not.toHaveBeenCalled();
      });

      it('should successfully flag a question with valid reasons', () => {
        component.flaggedReasonArray = [
          'The question does not pertain to the stated domain.',
        ];
        (mockTestTakingService.flagQuestion as jest.Mock).mockReturnValue(
          of({ success: true, data: { message: 'Success' } })
        );

        component.flagQuestion();

        expect(mockTestTakingService.flagQuestion).toHaveBeenCalledWith({
          testId: 'test-123',
          questionId: 'question-456',
          reasonOfFlagging: [
            'The question does not pertain to the stated domain.',
          ],
          questionText: 'Sample question text',
        });
        expect(mockToastService.onShow).toHaveBeenCalledWith(
          'success',
          'Question flagged successfully',
          true,
          'success'
        );
        expect(component.displayFlagModal).toBe(false);
        expect(component.isFlaggingQuestion).toBe(false);
      });

      it('should handle "Other" reason correctly', () => {
        component.flaggedReasonArray = ['Other', 'Custom reason'];
        component.typedReason = 'Custom reason';
        (mockTestTakingService.flagQuestion as jest.Mock).mockReturnValue(
          of({ success: true, data: { message: 'Success' } })
        );

        component.flagQuestion();

        expect(mockTestTakingService.flagQuestion).toHaveBeenCalledWith({
          testId: 'test-123',
          questionId: 'question-456',
          reasonOfFlagging: ['Custom reason'],
          questionText: 'Sample question text',
        });
      });

      it('should handle service error gracefully', () => {
        component.flaggedReasonArray = ['Some reason'];
        const errorMessage = 'Service error';
        (mockTestTakingService.flagQuestion as jest.Mock).mockReturnValue(
          throwError(() => errorMessage)
        );

        component.flagQuestion();

        expect(mockToastService.onShow).toHaveBeenCalledWith(
          'error',
          errorMessage,
          true,
          'error'
        );
        expect(component.isFlaggingQuestion).toBe(false);
        expect(mockAnalyticsService.track).toHaveBeenCalledWith(
          'Question Flagging Failed',
          {
            testId: 'test-123',
            assessmentId: 'assessment-789',
            reason: ['Some reason'],
          }
        );
      });
    });

    describe('unflagQuestion', () => {
      it('should return early if already unflagging', () => {
        component.isUnflaggingQuestion = true;

        component.unflagQuestion();

        expect(mockTestTakingService.unflagQuestion).not.toHaveBeenCalled();
      });

      it('should successfully unflag a question', () => {
        (mockTestTakingService.unflagQuestion as jest.Mock).mockReturnValue(
          of({ success: true, data: { message: 'Success' } })
        );

        component.unflagQuestion();

        expect(mockTestTakingService.unflagQuestion).toHaveBeenCalledWith({
          questionId: 'question-456',
          testId: 'test-123',
        });
        expect(mockToastService.onShow).toHaveBeenCalledWith(
          'success',
          'Question unflagged successfully',
          true,
          'success'
        );
        expect(component.isUnflaggingQuestion).toBe(false);
      });

      it('should handle service error gracefully', () => {
        (mockTestTakingService.unflagQuestion as jest.Mock).mockReturnValue(
          throwError(() => 'Error')
        );

        component.unflagQuestion();

        expect(mockToastService.onShow).toHaveBeenCalledWith(
          'error',
          'Oops! Something went wrong',
          true,
          'error'
        );
        expect(component.isUnflaggingQuestion).toBe(false);
      });
    });

    describe('selectedReasons', () => {
      it('should add reason when unchecked', () => {
        const reason = { checked: false, question: 'Test reason' };

        component.selectedReasons(reason);

        expect(reason.checked).toBe(true);
        expect(component.flaggedReasonArray).toContain('Test reason');
      });

      it('should remove reason when checked', () => {
        const reason = { checked: true, question: 'Test reason' };
        component.flaggedReasonArray = ['Test reason'];

        component.selectedReasons(reason);

        expect(reason.checked).toBe(false);
        expect(component.flaggedReasonArray).not.toContain('Test reason');
        expect(component.typedReason).toBe('');
      });
    });

    describe('typeReason', () => {
      it('should handle "Other" reason typing', () => {
        const data = { checked: true, question: 'Other' };
        const event = {
          target: { value: 'Custom reason' },
        } as unknown as Event;
        component.flaggedReasonArray = ['Other'];
        component.typedReason = 'Other';

        component.typeReason(data, event);

        expect(component.typedReason).toBe('Custom reason');
        expect(component.flaggedReasonArray).toContain('Custom reason');
        expect(component.error).toBe('');
      });
    });
  });

  describe('Answer Handling', () => {
    beforeEach(() => {
      component.questionForm = new FormBuilder().group({});
    });

    describe('selectedOptions', () => {
      it('should emit selected options', () => {
        const spy = jest.spyOn(component.emitAnswerChoices, 'emit');
        const options = ['Option 1', 'Option 2'];

        component.selectedOptions(options);

        expect(spy).toHaveBeenCalledWith(options);
      });

      it('should handle coding answers', () => {
        const spy = jest.spyOn(component.emitAnswerChoices, 'emit');
        const codingAnswers: CodingAnswer[] = [
          { code: 'console.log("hello")', languageId: 1 },
        ];

        component.selectedOptions(codingAnswers);

        expect(spy).toHaveBeenCalledWith(codingAnswers);
      });
    });

    describe('onChange', () => {
      it('should update form control and emit answer choices', () => {
        const spy = jest.spyOn(component.emitAnswerChoices, 'emit');
        const adjustWidthSpy = jest
          .spyOn(component, 'adjustInputWidth')
          .mockImplementation();

        component.questionForm.addControl(
          'input_0',
          new FormControl('initial')
        );
        component.questionForm.addControl('input_1', new FormControl('value2'));

        const event = { target: { value: 'new value' } } as unknown as Event;
        const initialValues = ['initial', 'value2'];

        component.onChange(event, 'input_0', initialValues);

        expect(component.questionForm.get('input_0')?.value).toBe('new value');
        expect(adjustWidthSpy).toHaveBeenCalledWith(event.target);
        expect(spy).toHaveBeenCalledWith(['new value', 'value2']);
      });
    });

    describe('handleRadioChange', () => {
      it('should update matrix subquestion answers', () => {
        const spy = jest.spyOn(component.emitAnswerChoices, 'emit');
        component.subQuestions = [
          { id: '1', answers: '', subquestion: 'Question 1' },
          { id: '2', answers: '', subquestion: 'Question 2' },
        ] as MatrixSubquestion[];

        const event = { rowIndex: 0, option: 'Option A' };

        component.handleRadioChange(event);

        expect(component.subQuestions[0].answers).toBe('Option A');
        expect(spy).toHaveBeenCalledWith([
          { subquestionId: '1', answers: ['Option A'] },
          { subquestionId: '2', answers: [''] },
        ]);
      });
    });
  });

  describe('UI State Management', () => {
    describe('toggleDisplayInstruction', () => {
      it('should toggle display instructions', () => {
        component.displayInstructions = false;

        component.toggleDisplayInstruction();

        expect(component.displayInstructions).toBe(true);

        component.toggleDisplayInstruction();

        expect(component.displayInstructions).toBe(false);
      });
    });

    describe('openInstructionsModal', () => {
      it('should open instructions modal', () => {
        component.displayInstructions = false;

        component.openInstructionsModal();

        expect(component.displayInstructions).toBe(true);
      });
    });

    describe('submitPage', () => {
      it('should emit submit page event', () => {
        const spy = jest.spyOn(component.emitSubmitPage, 'emit');

        component.submitPage();

        expect(spy).toHaveBeenCalled();
      });
    });
  });

  describe('Mobile and Responsive Features', () => {
    describe('onResize', () => {
      it('should update mobile state on window resize', () => {
        Object.defineProperty(window, 'innerWidth', {
          value: 800,
          writable: true,
        });

        component.onResize();

        expect(component.isMobile).toBe(true);

        Object.defineProperty(window, 'innerWidth', {
          value: 1200,
          writable: true,
        });

        component.onResize();

        expect(component.isMobile).toBe(false);
      });
    });
  });

  describe('Drag and Resize Functionality', () => {
    describe('startDragging', () => {
      it('should initialize horizontal dragging', () => {
        const event = { pageX: 100 } as MouseEvent;
        component.leftWidth = 60;

        component.startDragging(event);

        expect(component.isDragging).toBe(true);
        expect(component.startX).toBe(100);
        expect(component.startLeftWidth).toBe(60);
      });
    });

    describe('startVerticalDragging', () => {
      it('should initialize vertical dragging', () => {
        const event = { pageY: 200 } as MouseEvent;
        component.topHeight = 70;

        component.startVerticalDragging(event);

        expect(component.isVerticalDragging).toBe(true);
        expect(component.startY).toBe(200);
        expect(component.startTopHeight).toBe(70);
      });
    });

    describe('stopDragging', () => {
      it('should stop all dragging operations', () => {
        component.isDragging = true;
        component.isVerticalDragging = true;

        component.stopDragging();

        expect(component.isDragging).toBe(false);
        expect(component.isVerticalDragging).toBe(false);
      });
    });

    describe('isEditorFullScreen', () => {
      it('should handle fullscreen mode changes', () => {
        const screenMode = { scale: 100, isFullScreen: true };
        component.isMobile = true;
        component.showQuestionOnMobile = true;

        component.isEditorFullScreen(screenMode);

        expect(component.editorFullScreen).toBe(true);
        expect(component.topHeight).toBe(100);
        expect(component.showQuestionOnMobile).toBe(false);
      });

      it('should handle exit fullscreen', () => {
        const screenMode = { scale: 50, isFullScreen: false };
        component.editorFullScreen = true;

        component.isEditorFullScreen(screenMode);

        expect(component.editorFullScreen).toBe(false);
        expect(component.topHeight).toBe(50);
      });
    });
  });

  describe('Code Testing Features', () => {
    describe('getSolutionTestCases', () => {
      it('should update solution test cases', () => {
        const testCases: SolutionTestCases[] = [
          { input: 'test input', output: 'expected output', test_case_id: '1' },
        ];

        component.getSolutionTestCases(testCases);

        expect(component.codeTypeSolutionTestCases).toEqual(testCases);
      });
    });

    describe('getCodeRunResult', () => {
      it('should update code run results', () => {
        const results: SolutionResult[] = [
          {
            test_case_id: '1',
            input: 'test input',
            output: 'expected output',
            actualOutput: 'test output',
            isCorrect: true,
            executionTime: '0.1',
            memory: 128,
            statusId: 3,
            statusDescription: 'Accepted',
            error: null,
            inQueue: false,
          },
        ];

        component.getCodeRunResult(results);

        expect(component.codeSolutionResult).toEqual(results);
      });
    });
  });

  describe('Utility Methods', () => {
    describe('getQuestionTypeString', () => {
      it('should return question type string', () => {
        const result = component.getQuestionTypeString('multi-choice');

        expect(result).toBe('Multiple Choice');
      });
    });

    describe('selectedReason', () => {
      it('should set flagged reason and clear error', () => {
        component.error = 'Some error';

        component.selectedReason('Test reason');

        expect(component.flaggedReason).toBe('Test reason');
        expect(component.error).toBe('');
      });
    });
  });

  describe('Component Lifecycle', () => {
    describe('ngOnChanges', () => {
      it('should handle question text changes', () => {
        const changes = {
          questionText: {
            previousValue: 'old text',
            currentValue: 'new text',
            firstChange: false,
            isFirstChange: () => false,
          },
        };

        component.ngOnChanges(changes);

        expect(component.flaggedReasonArray).toEqual([]);
        expect(component.typedReason).toBe('');
      });
    });

    describe('ngOnDestroy', () => {
      it('should unsubscribe from subscriptions', () => {
        const unsubscribeSpy = jest.spyOn(
          component['subscription'],
          'unsubscribe'
        );

        component.ngOnDestroy();

        expect(unsubscribeSpy).toHaveBeenCalled();
      });
    });
  });
});
