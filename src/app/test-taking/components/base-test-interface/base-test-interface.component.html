<div class="flex flex-col h-full">
  <div class="flex justify-end gap-3 items-center">
    @if (returnFromSubmitPage) {
      <button
        (click)="submitPage()"
        class="px-3 py-1 text-xs bg-[#0C4767] text-white rounded hover:bg-[#155e86] transition duration-150 focus:outline-none focus:ring-1 focus:ring-[#0C4767]"
      >
        Submit
      </button>
    }
    <button
      (click)="openInstructionsModal()"
      class="instruction-text font-semibold underline text-[#0C4767] hover:text-[#3D6B85] text-sm sm:text-base cursor-pointer viewInst self-end mb-2"
    >
      View Instructions
    </button>
  </div>

  <!-- Mobile Toggle for Coding Questions -->
  @if (isMobile && questionType === 'Code') {
    <div class="flex justify-center mb-4">
      <div class="bg-gray-200 rounded-lg p-1 flex shadow-md w-full max-w-xs">
        <button
          (click)="toggleMobileView(true)"
          [ngClass]="{
            'bg-white shadow-md text-[#0C4767] font-bold': showQuestionOnMobile,
            'text-gray-600': !showQuestionOnMobile,
          }"
          class="flex-1 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200"
        >
          Question
        </button>
        <button
          (click)="toggleMobileView(false)"
          [ngClass]="{
            'bg-white shadow-md text-[#0C4767] font-bold':
              !showQuestionOnMobile,
            'text-gray-600': showQuestionOnMobile,
          }"
          class="flex-1 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200"
        >
          Code Editor
        </button>
      </div>
    </div>
  }

  <!-- Desktop Split-Panel Layout for Coding Questions -->
  @if (!isMobile && questionType === 'Code') {
    <div class="flex flex-row gap-2 mt-1" style="height: calc(100vh - 13rem)">
      <!-- Left Panel: Question -->
      <div
        #leftPanel
        class="bg-white rounded-md p-5 overflow-y-auto overflow-x-hidden"
        [style.width.%]="leftWidth"
        style="word-wrap: break-word; word-break: break-word"
      >
        <section class="flex flex-row justify-between">
          <p class="text-[#0C4767] font-bold mb-3 mt-0 text-base">
            {{ sectionName }}
          </p>
        </section>
        <div
          class="flex flex-row justify-between text-[#0C4767] text-xl font-bold gap-3"
        >
          <div class="flex flex-row gap-3 items-center">
            <span class="text-xl">{{ questionNumber }}</span>
            <div class="flex">
              <button
                *ngIf="!isQuestionFlagged"
                (click)="displayFlagModal = true"
                class="text-red-600 hover:text-red-300 cursor-pointer w-fit"
              >
                <div class="flex items-center" id="flagQuestion">
                  <mat-icon class="!text-xl">flag</mat-icon>
                  <span class="text-sm ml-1">Flag this question</span>
                </div>
              </button>
              <button
                *ngIf="isQuestionFlagged"
                (click)="unflagQuestion()"
                class="text-red-600 flex hover:text-red-300 cursor-pointer w-fit"
              >
                <div class="flex items-center" id="unflagQuestion">
                  <mat-icon class="!text-xl">flag</mat-icon>
                  <span class="text-sm ml-1">Flag this question</span>
                </div>
              </button>
            </div>
          </div>
          <span class="text-xl">{{ questionType }}</span>
        </div>
        <div class="mt-4 break-words">
          <div
            [innerHTML]="questionText"
            class="break-words whitespace-normal"
          ></div>
        </div>
      </div>

      <div #divider (mousedown)="startDragging($event)" class="divider"></div>

      <!-- Right Panel: Code Editor + Test Cases -->
      <div
        #rightPanel
        class="flex flex-col h-full gap-2"
        [style.width.%]="100 - leftWidth"
      >
        <!-- Top: Code Editor -->
        <div
          #topPanel
          class="bg-white rounded-md px-5 py-2 overflow-hidden"
          [style.height.%]="editorFullScreen ? 100 : topHeight"
          style="min-height: 180px"
        >
          <app-test-taking-code-editor
            [questionId]="questionId"
            [codeTemplate]="currentQuestion?.codeTemplates"
            (emitOptions)="selectedOptions($event)"
            [solutionTestCases]="codeTypeSolutionTestCases"
            (codeRunResult)="getCodeRunResult($event)"
            [codeConstraint]="currentQuestion?.CodeConstraint"
            (fullScreenScale)="isEditorFullScreen($event)"
          ></app-test-taking-code-editor>
        </div>

        <!-- Horizontal Divider -->
        <div
          #verticalDivider
          (mousedown)="startVerticalDragging($event)"
          class="vertical-divider w-full"
          *ngIf="!editorFullScreen"
        ></div>

        <!-- Bottom: Test Cases -->
        <div
          #bottomPanel
          class="bg-white rounded-md overflow-auto"
          [style.height.%]="100 - topHeight"
          *ngIf="!editorFullScreen"
          style="min-height: 180px"
        >
          <app-coding-test-cases
            [codeConstraint]="currentQuestion?.CodeConstraint"
            (solutionTestCases)="getSolutionTestCases($event)"
            [candidateSolutionResult]="codeSolutionResult"
          ></app-coding-test-cases>
        </div>
      </div>
    </div>
  } @else {
    <!-- Mobile/Tablet Layout OR Desktop Non-Coding Questions -->
    <div
      class="flex flex-col lg:flex-row flex-grow min-h-0 mt-1 gap-2 lg:gap-0"
    >
      <!-- Question Panel -->
      <div
        #leftPanel
        class="panel left-panel"
        [style.width.%]="isMobile ? 100 : leftWidth"
        [style.display]="
          isMobile && questionType === 'Code' && !showQuestionOnMobile
            ? 'none'
            : 'block'
        "
        [ngClass]="{
          'mb-4 lg:mb-0': questionType === 'Code',
        }"
      >
        <div
          id="question "
          class="rounded-md"
          [ngClass]="{
            'bg-[#ffffff] p-3 sm:p-5': questionType !== 'Code',
            'bg-[#ffffff] px-3 sm:px-5 py-2':
              questionType === 'Code' && !isMobile,
            'bg-transparent p-3 sm:p-5': questionType === 'Code' && isMobile,
          }"
        >
          <section class="flex flex-row justify-between">
            <p
              class="text-[#0C4767] font-bold mb-2 sm:mb-3 mt-0 text-sm sm:text-base"
            >
              {{ sectionName }}
            </p>
          </section>
          <div
            class="flex flex-col sm:flex-row justify-between text-[#0C4767] text-lg sm:text-xl font-bold gap-2 sm:gap-0"
          >
            <div
              class="flex flex-col sm:flex-row gap-2 sm:gap-3 items-start sm:items-center"
            >
              <span class="text-base sm:text-xl">{{ questionNumber }}</span>
              <div class="flex">
                <button
                  *ngIf="!isQuestionFlagged"
                  (click)="displayFlagModal = true"
                  class="text-red-600 hover:text-red-300 cursor-pointer w-fit"
                >
                  <div class="flex items-center" id="flagQuestion">
                    <mat-icon class="!text-lg sm:!text-xl">flag</mat-icon>
                    <span class="text-xs sm:text-sm ml-1"
                      >Flag this question</span
                    >
                  </div>
                </button>
                <button
                  *ngIf="isQuestionFlagged"
                  (click)="unflagQuestion()"
                  class="text-red-600 flex hover:text-red-300 cursor-pointer w-fit"
                >
                  <div class="flex items-center" id="unflagQuestion">
                    <mat-icon class="!text-lg sm:!text-xl">flag</mat-icon>
                    <span class="text-xs sm:text-sm ml-1"
                      >Unflag this question</span
                    >
                  </div>
                </button>
              </div>
            </div>
            <span class="text-sm sm:text-base lg:text-xl">{{
              getQuestionTypeString(questionType)
            }}</span>
          </div>
          <section class="flex w-full pt-2 sm:pt-4 box-border mb-4">
            <div
              #container
              class="question-text max-w-full text-sm sm:text-base"
              [appSafeHtml]="questionText"
            ></div>
          </section>
        </div>
      </div>

      <!-- Divider (desktop only) -->
      <div
        #divider
        (mousedown)="startDragging($event)"
        class="mx-2 sm:mx-4 h-full hidden lg:block"
        [ngClass]="{
          '': isMobile,
          divider: !isMobile,
        }"
      ></div>

      <!-- Code Editor Panel -->
      <div
        #rightPanel
        class="panel right-panel"
        [style.width.%]="isMobile ? 100 : 100 - leftWidth"
        [style.display]="
          isMobile && questionType === 'Code' && showQuestionOnMobile
            ? 'none'
            : 'block'
        "
      >
        <div class="h-full flex flex-col rounded-md w-full">
          <div
            #topPanel
            class="rounded-md w-full"
            [style.height.%]="
              questionType === 'Code' ? (isMobile ? 'auto' : topHeight) : '100'
            "
            [ngClass]="{
              'bg-[#fff] rounded-lg p-5 sm:p-5 overflow-y-auto':
                questionType !== 'Code',
              'bg-[#ffffff] px-3 sm:px-5 py-2':
                questionType === 'Code' && !isMobile,
              'bg-[#ffffff] p-3 sm:p-5 min-h-[400px]':
                questionType === 'Code' && isMobile,
            }"
          >
            <!-- Fill in the blanks component -->
            <div
              [style.display]="questionType === 'Fill_in' ? 'block' : 'none'"
            >
              <p class="text-[#0C4767] text-sm sm:text-base mb-2">
                Kindly drag your answers into the blanks
              </p>
              <app-fill-in-the-blanks
                [fillInTheBlanksOptions]="fillInTheBlanksOptions"
              ></app-fill-in-the-blanks>
            </div>

            <!-- Multi select component -->
            <div
              [style.display]="
                questionType === 'Multi_select' ? 'block' : 'none'
              "
            >
              <app-multi-select
                [questionOptions]="questionOptions"
                (emitOptions)="selectedOptions($event)"
                [currentQuestion]="currentQuestion"
              ></app-multi-select>
            </div>

            <!-- Multiple choice component -->
            <div
              [style.display]="
                questionType === 'Multiple_choice' ? 'block' : 'none'
              "
            >
              <app-multi-choice
                [questionOptions]="questionOptions"
                [currentQuestion]="currentQuestion"
                (emitOptions)="selectedOptions($event)"
              ></app-multi-choice>
            </div>

            <!-- Essay component -->
            <div [style.display]="questionType === 'Essay' ? 'block' : 'none'">
              <app-essay
                (emitOptions)="selectedOptions($event)"
                [questionId]="questionId"
              ></app-essay>
            </div>

            <!-- True/False component -->
            <div
              [style.display]="
                questionType === 'True_or_false' ? 'block' : 'none'
              "
            >
              <app-true-or-false
                [questionOptions]="questionOptions"
                (emitOptions)="selectedOptions($event)"
                [currentQuestion]="currentQuestion"
              ></app-true-or-false>
            </div>

            <!-- Matrix component -->
            <div [style.display]="questionType === 'Matrix' ? 'block' : 'none'">
              <app-matrix-table
                [options]="columnOptions"
                [subquestions]="subQuestions"
                (radioChange)="handleRadioChange($event)"
                [isTestTaking]="true"
                [preview]="''"
              ></app-matrix-table>
            </div>

            <!-- Code editor component -->
            <div
              [style.display]="questionType === 'Code' ? 'block' : 'none'"
              class="w-full h-full"
            >
              <app-test-taking-code-editor
                [questionId]="questionId"
                [codeTemplate]="currentQuestion?.codeTemplates"
                (emitOptions)="selectedOptions($event)"
                [solutionTestCases]="codeTypeSolutionTestCases"
                (codeRunResult)="getCodeRunResult($event)"
                [codeConstraint]="currentQuestion?.CodeConstraint"
                (fullScreenScale)="isEditorFullScreen($event)"
              ></app-test-taking-code-editor>
            </div>
          </div>

          @if (questionType === 'Code') {
            <div
              #verticalDivider
              (mousedown)="startVerticalDragging($event)"
              class="my-2 sm:my-3 vertical-divider"
              [style.display]="editorFullScreen ? 'none' : 'block'"
            ></div>

            <div
              #bottomPanel
              class="panel rounded-md mt-2 lg:mt-0 overflow-auto"
              [style.height.%]="isMobile ? 'auto' : 100 - topHeight"
              [style.display]="editorFullScreen ? 'none' : 'block'"
              style="min-height: 300px"
              [ngClass]="{
                'bg-[#ffffff]': true,
                'min-h-[300px]': true,
              }"
            >
              <app-coding-test-cases
                [codeConstraint]="currentQuestion?.CodeConstraint"
                (solutionTestCases)="getSolutionTestCases($event)"
                [candidateSolutionResult]="codeSolutionResult"
              ></app-coding-test-cases>
            </div>
          }
        </div>
      </div>
    </div>
  }
</div>

<app-custom-modal
  [headerTitle]="'Flag this Question'"
  [visibleModal]="displayFlagModal"
  (visibleModalChange)="displayFlagModal = !displayFlagModal"
>
  <div class="mt-6 mx-8">
    <div>
      <p class="w-[100%]" [appSafeHtml]="questionText"></p>
    </div>
    <h5 class="text-black mt-6 font-medium text-[16px] mb-6">Reason</h5>
    <ng-container *ngFor="let item of flaggedQuestionArray">
      <div class="flex flex-col">
        <label class="checkbox-label mb-4 flex gap-x-4 items-start w-fit">
          <input
            type="checkbox"
            [(ngModel)]="item.checked"
            class="mt-1"
            (click)="selectedReasons(item)"
            (keydown.enter)="selectedReasons(item)"
          />
          <div class="label-text flex-1">
            <span class="text-[#474D66] font-normal text-[16px]">{{
              item.question
            }}</span>
          </div>
        </label>
      </div>
      <textarea
        *ngIf="item.question === 'Other'"
        [disabled]="!item.checked"
        [rows]="6"
        class="border text-[16px] py-2 px-1 w-full outline-none rounded-md border-[#B9B0B0]"
        [value]="typedReason"
        (input)="typeReason(item, $event)"
      >
      </textarea>
    </ng-container>
    <div class="flex my-8 gap-x-4 justify-end">
      <button
        (click)="flagQuestion()"
        class="md:min-w-[8rem] bg-[#0C4767] hover:bg-[#3D6B85] text-white w-[157px] rounded-lg h-11"
      >
        Submit
      </button>
    </div>
  </div>
</app-custom-modal>

<app-custom-modal
  [headerTitle]="'Instructions'"
  [visibleModal]="displayInstructions"
  (visibleModalChange)="toggleDisplayInstruction()"
>
  <div class="my-6 mx-8 max-h-[calc(100vh-20rem)] overflow-y-auto">
    <p [appSafeHtml]="instructions"></p>
  </div>
</app-custom-modal>
