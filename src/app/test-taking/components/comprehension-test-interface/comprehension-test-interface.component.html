<div class="flex flex-col lg:flex-row h-[calc(100vh-15rem)]">
  <div class="left-side w-full lg:w-1/2 p-2 sm:p-4">
    <h1 class="text-[#0C4767] text-lg sm:text-[24px] font-medium mb-4 sm:mb-8">
      Comprehension Passage
    </h1>

    <div class="overflow-y-auto mb-4">
      <p [appSafeHtml]="comprehensionPassage" class="text-sm sm:text-base"></p>
    </div>
  </div>

  <div class="dividing-line w-full lg:w-px lg:h-full h-px bg-gray-300"></div>

  <div class="right-side w-full lg:w-1/2 p-2 sm:p-4">
    <button
      (click)="openInstructionsModal()"
      class="instruction-text flex justify-end underline text-[#0C4767] text-base sm:text-xl my-2 sm:my-4 cursor-pointer viewInst"
    >
      View Instructions
    </button>
    <div
      class="flex flex-col sm:flex-row justify-between text-[#0C4767] text-lg sm:text-xl font-bold gap-2 sm:gap-0"
    >
      <span class="mb-2 sm:mb-4">{{ questionNumber }}</span>
      <span class="sm:ml-4 text-sm sm:text-base lg:text-xl">{{
        getQuestionTypeString(questionType)
      }}</span>
    </div>
    <section class="mb-4">
      <p
        #container
        class="w-full text-sm sm:text-base"
        [appSafeHtml]="questionText"
      ></p>
      <div
        class="rounded-md px-2 sm:px-4 py-4 sm:py-8 pb-16 lg:pb-8 space-y-4 sm:space-y-8 w-full sm:w-[90%] lg:w-[75%]"
      >
        @if (questionType === 'Multi_select') {
          <app-multi-select
            [questionOptions]="questionOptions"
            (emitOptions)="selectedOptions($event)"
            [currentQuestion]="currentQuestion"
          ></app-multi-select>
        }
        @if (questionType === 'Multiple_choice') {
          <app-multi-choice
            [questionOptions]="questionOptions"
            (emitOptions)="selectedOptions($event)"
            [currentQuestion]="currentQuestion"
          ></app-multi-choice>
        }
        @if (questionType === 'True_or_false') {
          <app-true-or-false
            [questionOptions]="questionOptions"
            (emitOptions)="selectedOptions($event)"
            [currentQuestion]="currentQuestion"
          ></app-true-or-false>
        }

        @if (questionType === 'Fill_in') {
          @if (questionType === 'Fill_in') {
            <app-fill-in-the-blanks
              [fillInTheBlanksOptions]="fillInTheBlanksOptions"
            ></app-fill-in-the-blanks>
          }
        }
        @if (questionType === 'Matrix') {
          <app-matrix-table
            [options]="columnOptions"
            [subquestions]="subQuestions"
            (radioChange)="handleRadioChange($event)"
            [isTestTaking]="true"
          ></app-matrix-table>
        }
      </div>
      <div
        class="flex justify-start items-start mt-6 sm:mt-[50px] mr-3 sm:mr-6"
      >
        <button
          *ngIf="!isQuestionFlagged"
          (click)="displayFlagModal = true"
          class="text-red-600 hover:text-red-300 cursor-pointer w-fit"
        >
          <div class="flex items-center" id="flagQuestion">
            <mat-icon class="!text-lg sm:!text-xl">flag</mat-icon
            ><span class="text-xs sm:text-sm pb-1 ml-1"
              >Flag this question</span
            >
          </div>
        </button>
        <button
          *ngIf="isQuestionFlagged"
          (click)="unflagQuestion()"
          class="text-red-600 flex hover:text-red-300 cursor-pointer w-fit"
        >
          <div class="flex items-center" id="unflagQuestion">
            <mat-icon class="!text-lg sm:!text-xl">flag</mat-icon
            ><span class="text-xs sm:text-sm pb-1 ml-1"
              >Unflag this question</span
            >
          </div>
        </button>
      </div>
    </section>
  </div>
</div>

<app-custom-modal
  [headerTitle]="'Flag this Question'"
  [visibleModal]="displayFlagModal"
  (visibleModalChange)="displayFlagModal = !displayFlagModal"
>
  <div class="mt-6 mx-8">
    <div>
      <p class="w-[100%]" [appSafeHtml]="questionText"></p>
    </div>
    <h5 class="text-black mt-6 font-medium text-[16px] mb-6">Reason</h5>
    <ng-container *ngFor="let item of flaggedQuestionArray">
      <div class="flex flex-col">
        <label class="checkbox-label mb-4 flex gap-x-4 items-start w-fit">
          <input
            type="checkbox"
            [(ngModel)]="item.checked"
            class="mt-1"
            (click)="selectedReasons(item)"
            (keydown.enter)="selectedReasons(item)"
          />
          <div class="label-text flex-1">
            <span class="text-[#474D66] font-normal text-[16px]">{{
              item.question
            }}</span>
          </div>
        </label>
      </div>
      <textarea
        *ngIf="item.question === 'Other'"
        [disabled]="!item.checked"
        [rows]="6"
        class="border text-[16px] py-2 px-1 w-full outline-none rounded-md border-[#B9B0B0]"
        [value]="typedReason"
        (input)="typeReason(item, $event)"
      >
      </textarea>
    </ng-container>
    <div class="flex my-8 gap-x-4 justify-end">
      <button
        (click)="flagQuestion()"
        class="md:min-w-[8rem] bg-[#0c4767] text-white w-[157px] rounded-lg h-11"
      >
        Submit
      </button>
    </div>
  </div>
</app-custom-modal>

<app-custom-modal
  [headerTitle]="'Instructions'"
  [visibleModal]="displayInstructions"
  (visibleModalChange)="toggleDisplayInstruction()"
>
  <div class="my-6 mx-8 max-h-[calc(100vh-20rem)] overflow-y-auto">
    <p [appSafeHtml]="instructions"></p>
  </div>
</app-custom-modal>
