import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  Renderer2,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MultiSelectComponent } from '../../question-type/multi-select/multi-select.component';
import { MultiChoiceComponent } from '../../question-type/multi-choice/multi-choice.component';
import { TrueOrFalseComponent } from '../../question-type/true-or-false/true-or-false.component';
import { CustomModalComponent } from '../../../components/custom-modal/custom-modal.component';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
} from '@angular/forms';
import { SafeHtml } from '@angular/platform-browser';
import { ToastService } from '../../../services/toast-service/toast.service';
import { TestTakingService } from '../../../services/test-taking-service/test-taking.service';
import {
  MatrixSubquestion,
  MatrixTableComponent,
} from '../../../test-management/Questions/main-questions-page/create-questions/questionTypes/matrix-questions/matrix-table/matrix-table.component';
import { FillInTheBlanksComponent } from '../../question-type/fill-in-the-blanks/fill-in-the-blanks.component';
import { TestTaking } from '../../../Interfaces/Types/testTakingInterface';
import { Question } from '../../../Interfaces/Types/testTakerInterface';
import { SafeHtmlDirective } from '../../../directives/safe-html.directive';

@Component({
  selector: 'app-comprehension-test-interface',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MultiSelectComponent,
    MultiChoiceComponent,
    TrueOrFalseComponent,
    CustomModalComponent,
    FormsModule,
    MatrixTableComponent,
    FillInTheBlanksComponent,
    SafeHtmlDirective,
  ],
  templateUrl: './comprehension-test-interface.component.html',
  styleUrl: './comprehension-test-interface.component.css',
})
export class ComprehensionTestInterfaceComponent
  implements OnInit, OnChanges, AfterViewInit
{
  constructor(
    private readonly toast: ToastService,
    private readonly testTakingService: TestTakingService,
    private readonly renderer: Renderer2,
    private readonly cdr: ChangeDetectorRef,
    private readonly fb: FormBuilder
  ) {}

  @Input() assessmentId = '';
  @Input() instructions = '';
  @Input() comprehensionPassage = '';
  @Input() description = '';
  @Input() testInstruction = '';
  @Input() testId: string = '';
  @Input() testTakerId = '';
  @Input() questionNumber: string | number = '';
  @Input() questionType = '';
  @Input() questionText = '';
  @Input() questionOptions: string[] = [];
  @Input() questionId = '';
  @Input() isQuestionFlagged = false;
  @Input() fillInTheBlanksOptions: Array<{
    value: string;
    id: string;
    blank: string;
    isBlank: boolean;
  }> = [];
  @Input() columnOptions: string[] = [];
  @Input() subQuestions!: MatrixSubquestion[];
  @Input() currentQuestion: Question | undefined;
  @Output() questionFlagStatus = new EventEmitter<boolean>();
  @Output() emitAnswerChoices = new EventEmitter<
    string[] | MatrixSubquestion[]
  >();
  displayFlagModal = false;
  questionForm!: FormGroup;
  displayInstructions = false;
  sanitizedInstruction: SafeHtml | undefined;
  isFlaggingQuestion = false;
  isUnflaggingQuestion = false;
  flaggedQuestionArray: {
    question: string;
    checked: boolean;
  }[] = [
    {
      question: 'The question does not pertain to the stated domain.',
      checked: false,
    },
    {
      question: 'The question contains inappropriate content.',
      checked: false,
    },
    {
      question: 'Other',
      checked: false,
    },
  ];
  typedReason: string = '';
  flaggedReasonArray: string[] = [];
  error: string = '';
  flaggedReason: string = '';
  testIdIndex: number | undefined;

  questionTypeString: { [key: string]: string } = {
    Multi_select: 'Multiple Select',
    Multiple_choice: 'Multiple Choice',
    Essay: 'False',
    True_or_false: 'True or false',
    Fill_in: 'Fill In',
    Matrix: 'Matrix',
  };
  @ViewChild('container') container!: ElementRef;

  ngAfterViewInit(): void {
    this.questionForm = this.fb.group({});
    this.replaceBlanksWithInputs();
    this.cdr.detectChanges();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['questionText']) {
      const previousValue = changes['questionText'].previousValue;
      const currentValue = changes['questionText'].currentValue;
      if (previousValue !== currentValue) {
        this.questionForm = this.fb.group({});
        setTimeout(() => {
          this.replaceBlanksWithInputs();
          this.cdr.detectChanges();
        }, 0);
      }
    }
  }

  ngOnInit(): void {
    if (this.testTakingService.inProgressReturnedData) {
      this.testIdIndex =
        this.testTakingService.inProgressReturnedData.assessment.tests.findIndex(
          (test) => test.id === this.testId
        );
      this.instructions =
        this.testTakingService.inProgressReturnedData.assessment.tests[
          this.testIdIndex
        ].instructions || 'No instructions';
    }
  }
  adjustInputWidth(input: HTMLInputElement): void {
    const minWidth = 60;
    const span = this.renderer.createElement('span');
    this.renderer.setStyle(span, 'visibility', 'hidden');
    this.renderer.setStyle(span, 'position', 'absolute');
    this.renderer.setStyle(span, 'white-space', 'pre');
    this.renderer.setStyle(
      span,
      'font-family',
      window.getComputedStyle(input).fontFamily
    );
    this.renderer.setStyle(
      span,
      'font-size',
      window.getComputedStyle(input).fontSize
    );
    this.renderer.setStyle(
      span,
      'font-weight',
      window.getComputedStyle(input).fontWeight
    );

    const textToMeasure = input.value || input.placeholder;
    this.renderer.appendChild(span, this.renderer.createText(textToMeasure));
    this.renderer.appendChild(document.body, span);

    const textWidth = span.offsetWidth;

    this.renderer.removeChild(document.body, span);

    const newWidth = Math.max(minWidth, textWidth + 10);
    this.renderer.setStyle(input, 'width', `${newWidth}px`);
  }

  replaceBlanksWithInputs(): void {
    const assessmentResults = JSON.parse(
      localStorage.getItem('assessmentResults') as string
    ) as TestTaking;
    let answer: string[] = [];
    if (assessmentResults && assessmentResults.testResults.length > 0) {
      const question = assessmentResults.testResults[0].questionResults.find(
        (q) => q.questionId === this.questionId
      );
      if (question) {
        answer = question.testTakerAnswers;
      }
    }
    const blanks =
      this.container.nativeElement.querySelectorAll('.blank-placeholder');

    blanks.forEach((blank: HTMLElement, index: number) => {
      const input = this.renderer.createElement('input');
      const space = this.renderer.createText(' ');
      const controlName = `input_${index}`;
      const control = new FormControl('');
      this.questionForm.addControl(controlName, control);
      this.renderer.setAttribute(input, 'type', 'text');
      this.renderer.setAttribute(input, 'readonly', 'true');
      this.renderer.setAttribute(
        input,
        'data-uuid',
        blank.getAttribute('data-uuid') ?? ''
      );
      this.renderer.setAttribute(input, 'formControlName', controlName);
      this.renderer.addClass(input, 'input-no-focus-border');
      this.renderer.setProperty(input, 'placeholder', 'drag here...');
      this.renderer.setProperty(input, 'value', answer[index] || '');
      this.adjustInputWidth(input);

      this.renderer.listen(input, 'dragover', (event) => {
        event.preventDefault();
        this.renderer.addClass(input, 'drag-over');
      });

      this.renderer.listen(input, 'dragleave', (event) => {
        event.preventDefault();
        this.renderer.removeClass(input, 'drag-over');
      });

      this.renderer.listen(input, 'drop', (event) => {
        event.preventDefault();
        const data = event.dataTransfer?.getData('text/plain');
        input.value = data as string;
        const control = this.questionForm.get(controlName);
        if (control) {
          control.setValue(data);
          this.adjustInputWidth(input);
        }
        this.onChange(event, controlName, answer);
        this.renderer.removeClass(input, 'drag-over');
      });

      const parent = blank.parentNode;
      if (parent) {
        this.renderer.insertBefore(parent, space, blank);
        this.renderer.insertBefore(parent, input, blank);
        this.renderer.removeChild(parent, blank);
      }
    });
  }

  onChange(e: Event, controlName: string, initialValues: string[]) {
    const value = (e.target as HTMLInputElement).value;
    const control = this.questionForm.get(controlName);
    if (control) {
      control.setValue(value);
      this.adjustInputWidth(e.target as HTMLInputElement);
    }
    const arr = Object.values(this.questionForm.value);
    const updatedAnswer = arr.map((_a, index) => {
      return arr[index] || initialValues[index];
    });
    this.emitAnswerChoices.emit(updatedAnswer as string[]);
  }

  selectedOptions(value: string[]) {
    this.emitAnswerChoices.emit(value);
  }
  typeReason(data: { checked: boolean; question: string }, event: Event) {
    this.error = '';
    if (this.flaggedReasonArray.includes(this.typedReason)) {
      this.flaggedReasonArray.splice(
        this.flaggedReasonArray.indexOf(this.typedReason),
        1
      );
    }
    if (data.checked === true && data.question === 'Other') {
      this.typedReason = (event.target as HTMLInputElement).value;
      this.flaggedReasonArray.push(this.typedReason);
    }
  }

  selectedReason(reason: string) {
    this.error = '';
    this.flaggedReason = reason;
  }

  selectedReasons(reason: { checked: boolean; question: string }) {
    reason.checked = !reason.checked;
    if (reason.checked) {
      reason.checked = true;
      this.flaggedReasonArray.push(reason.question);
    } else {
      reason.checked = false;
      this.typedReason = '';
      this.flaggedReasonArray.splice(
        this.flaggedReasonArray.indexOf(reason.question),
        1
      );
    }
  }

  flagQuestion() {
    if (this.flaggedReasonArray.length === 0) {
      this.error = 'You need to select an option';
      return;
    }
    if (this.flaggedReasonArray.includes('Other')) {
      this.flaggedReasonArray.splice(
        this.flaggedReasonArray.indexOf('Other'),
        1
      );
      if (this.typedReason === '') {
        this.error = 'You need to type the reason';
        return;
      }
    }

    this.isFlaggingQuestion = true;
    this.testTakingService
      .flagQuestion({
        testId: this.testId,
        questionId: this.questionId,
        reasonOfFlagging: this.flaggedReasonArray,
        questionText: this.questionText,
      })
      .subscribe({
        next: () => {
          this.toast.onShow(
            'success',
            'Question flagged successfully',
            true,
            'success'
          );
          this.displayFlagModal = false;
          this.isFlaggingQuestion = false;
          this.questionFlagStatus.emit(true);
        },
        error: (err) => {
          this.toast.onShow('error', err, true, 'error');
          this.isFlaggingQuestion = false;
        },
      });
  }

  unflagQuestion() {
    if (this.isUnflaggingQuestion) {
      return;
    }

    this.isUnflaggingQuestion = true;
    this.testTakingService
      .unflagQuestion({
        questionId: this.questionId,
        testId: this.testId,
      })
      .subscribe({
        next: () => {
          this.toast.onShow(
            'success',
            'Question unflagged successfully',
            true,
            'success'
          );
          this.isUnflaggingQuestion = false;
          this.questionFlagStatus.emit(false);
        },
        error: () => {
          this.toast.onShow(
            'error',
            'Oops! Something went wrong',
            true,
            'error'
          );
          this.isUnflaggingQuestion = false;
        },
      });
  }
  getQuestionTypeString(value: string) {
    return this.questionTypeString[value];
  }

  toggleDisplayInstruction() {
    this.displayInstructions = !this.displayInstructions;
  }
  openInstructionsModal() {
    this.displayInstructions = true;
  }

  handleRadioChange(event: { rowIndex: number; option: string }) {
    const { rowIndex, option } = event;
    const subquestionId = this.subQuestions[rowIndex].id;
    const subquestion = this.subQuestions.find((q) => q.id === subquestionId);

    if (subquestion) {
      subquestion.answers = option;
      const answer = this.subQuestions.map((q) => {
        return {
          subquestionId: q.id,
          answers: [q.answers],
        };
      });
      this.emitAnswerChoices.emit(answer as unknown as MatrixSubquestion[]);
    }
  }
}
