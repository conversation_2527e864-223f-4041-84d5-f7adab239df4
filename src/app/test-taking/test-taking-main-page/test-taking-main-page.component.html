<app-custom-toast></app-custom-toast>

<div class="flex flex-col bg-[#EFEFEF] h-[100svh]">
  <div class="px-3 sm:px-6 py-2 flex flex-col top-header">
    <section
      class="flex flex-col sm:flex-row justify-between items-center gap-4 sm:gap-0"
    >
      <div class="flex-shrink-0">
        <img
          src="../../../assets/icons/dodopkoIcon.svg"
          alt="logo"
          class="h-12 sm:h-[4.5rem]"
        />
      </div>
      <div class="flex items-center order-3 sm:order-2">
        <app-test-progress-bar
          [totalQuestions]="currentTestQuestions.length"
          [questionNumber]="currentQuestionIndex + 1"
          [percentage]="this.questionPercentage"
        ></app-test-progress-bar>
      </div>
      <div
        class="flex items-center gap-2 sm:gap-3 w-full sm:w-[235px] justify-center sm:justify-end order-2 sm:order-3"
      >
        <app-test-progress-ring
          [testPercentage]="testProgress"
        ></app-test-progress-ring>

        <app-timer
          [timeLimit]="this.timeToContinue || 0"
          (isTimerDone)="onAssessmentTimeExhaustion()"
        ></app-timer>
      </div>
    </section>
  </div>
  @if (!isConfirmSectionSubmission) {
    <div class="flex flex-col h-full">
      <div
        class="flex-grow px-3 sm:px-6 mt-3 bg-transparent lg:bg-[#EFEFEF] overflow-y-auto"
      >
        @if (currentTest && !currentTest.passage) {
          @if (currentQuestion && currentAssessment) {
            <app-base-test-interface
              [sectionName]="currentTest.title"
              [questionId]="currentQuestion.id"
              [currentQuestion]="currentQuestion"
              [questionNumber]="getQuestionNumber()"
              [questionType]="currentQuestion.questionType"
              [questionText]="currentQuestion.questionText"
              [questionOptions]="currentQuestionOptions"
              [fillInTheBlanksOptions]="currentQuestionFillInOptions"
              (emitAnswerChoices)="getChoices($event)"
              (emitSubmitPage)="sectionValidation()"
              [isQuestionFlagged]="
                currentQuestion ? currentQuestion.flagged : false
              "
              (questionFlagStatus)="setFlagStatus($event)"
              [testId]="currentTestId"
              [assessmentId]="currentAssessment.id"
              [testTakerId]="testTakerId"
              [columnOptions]="
                currentQuestion.matchMatrixAnswer
                  ? currentQuestion.matchMatrixAnswer.options
                  : []
              "
              [subQuestions]="
                currentQuestion.matchMatrixAnswer
                  ? currentQuestion.matchMatrixAnswer.questions
                  : []
              "
            ></app-base-test-interface>
          }
        } @else {
          @if (currentQuestion && currentAssessment) {
            <app-comprehension-test-interface
              [comprehensionPassage]="currentTest.passage"
              [currentQuestion]="currentQuestion"
              [questionId]="currentQuestion.id"
              [questionNumber]="getQuestionNumber()"
              [questionType]="currentQuestion.questionType"
              [questionText]="currentQuestion.questionText"
              [questionOptions]="currentQuestionOptions"
              [columnOptions]="
                currentQuestion.matchMatrixAnswer
                  ? currentQuestion.matchMatrixAnswer.options
                  : []
              "
              [subQuestions]="
                currentQuestion.matchMatrixAnswer
                  ? currentQuestion.matchMatrixAnswer.questions
                  : []
              "
              (emitAnswerChoices)="getChoices($event)"
              [isQuestionFlagged]="
                currentQuestion ? currentQuestion.flagged : false
              "
              (questionFlagStatus)="setFlagStatus($event)"
              [testId]="currentTestId"
              [assessmentId]="currentAssessment.id"
              [testTakerId]="testTakerId"
              [fillInTheBlanksOptions]="currentQuestionFillInOptions"
            ></app-comprehension-test-interface>
          }
        }
      </div>

      <div
        class="w-full flex lg:flex-row justify-center lg:justify-end gap-3 lg:gap-6 px-3 lg:pr-6 pt-2 pb-2"
      >
        <app-custom-button-test-taker
          (clicked)="navigate('previous')"
          [variant]="'secondary'"
          [hidden]="currentQuestionIndex === 0"
          class="w-full lg:w-auto"
        >
          Previous
        </app-custom-button-test-taker>

        <app-custom-button-test-taker
          [variant]="'smallPrimary'"
          (clicked)="navigate('next')"
          [hidden]="currentQuestionIndex === currentTestQuestions.length - 1"
        >
          Next
        </app-custom-button-test-taker>

        @if (currentQuestionIndex === currentTestQuestions.length - 1) {
          <app-custom-button-test-taker
            (clicked)="sectionValidation()"
            class="w-full lg:w-auto"
          >
            Next
          </app-custom-button-test-taker>
        }
      </div>
    </div>
  }

  @if (isConfirmSectionSubmission) {
    <div class="px-3 sm:px-6 flex-grow justify-center items-center flex">
      <div
        class="border border-[#0822301A] bg-white p-3 sm:p-5 w-full max-w-[1040px] min-h-[calc(100svh-20svh)] lg:gap-3 flex flex-col rounded-lg mx-2 sm:mx-0"
      >
        <h1 class="text-xl sm:text-[24px] font-bold text-[#0C4767]">
          Section Submission
        </h1>
        <p class="text-sm sm:text-base">
          Please review your answers before submitting. Once submitted, you
          cannot make any changes
        </p>
        <div
          class="flex flex-col sm:flex-row gap-3 overflow-x-auto scrollbar-hide"
        >
          <section
            class="h-[120px] min-w-[280px] sm:w-[320px] p-4 sm:p-5 bg-[#04952C0A] rounded-lg border border-[#0C476633] flex flex-col justify-between flex-shrink-0"
          >
            <h1 class="text-lg sm:text-xl flex gap-2 sm:gap-4 items-center">
              <span>
                <img
                  src="../../../../assets/testTakingImages/test-passed.svg"
                  alt="passed"
                  class="w-5 h-5 sm:w-6 sm:h-6"
              /></span>
              <span class="text-sm sm:text-base">Answered Questions</span>
            </h1>
            <p class="text-2xl sm:text-3xl text-[#082230E5]">
              {{ currentTestQuestions.length - unAnsweredQuestion.length }}/{{
                currentTestQuestions.length
              }}
            </p>
          </section>
          <section
            class="h-[120px] min-w-[280px] sm:w-[320px] p-4 sm:p-5 bg-[#FEFCE8] rounded-lg border border-[#F59E0B33] flex flex-col justify-between flex-shrink-0"
          >
            <h1 class="text-lg sm:text-xl flex gap-2 sm:gap-4 items-center">
              <span>
                <img
                  src="../../../../assets/testTakingImages/stash_flag.svg"
                  alt="flagged"
                  class="w-5 h-5 sm:w-6 sm:h-6"
              /></span>
              <span class="text-sm sm:text-base">Flagged Questions</span>
            </h1>
            <p class="text-2xl sm:text-3xl text-[#082230E5]">
              {{ numberOfFlaggedQuestions }}
            </p>
          </section>
          <section
            class="h-[120px] min-w-[280px] sm:w-[320px] p-4 sm:p-5 bg-[#C735320A] rounded-lg border border-[#C7353233] flex flex-col justify-between flex-shrink-0"
          >
            <h1 class="text-lg sm:text-xl flex gap-2 sm:gap-4 items-center">
              <span
                ><img
                  src="../../../../assets/testTakingImages/not-passed.svg"
                  alt="wrong"
                  class="w-5 h-5 sm:w-6 sm:h-6"
              /></span>
              <span class="text-sm sm:text-base">Unanswered Questions</span>
            </h1>
            <p class="text-2xl sm:text-3xl text-[#082230E5]">
              {{ unAnsweredQuestion.length }}
            </p>
          </section>
        </div>
        <div>
          <h1
            class="h-11 rounded-t-lg rounded-tr-lg bg-[#0822300A] text-base sm:text-lg text-[#082230E5] flex items-center pl-3 sm:pl-5"
          >
            Section Details
          </h1>
          <section
            [ngClass]="{
              'max-h-[calc(100vh-450px)] sm:max-h-[calc(100vh-550px)]':
                unAnsweredQuestion.length > 0,
              'max-h-[calc(100vh-450px)]': unAnsweredQuestion.length === 0,
            }"
            class="overflow-y-auto"
          >
            <div *ngFor="let question of currentTestQuestions; let i = index">
              <button
                (click)="goToQuestion(i)"
                (keydown.enter)="goToQuestion(i)"
                class="h-12 sm:h-16 border-b border-[#0822301A] flex px-3 sm:px-5 items-center justify-between w-full"
                [ngClass]="{
                  'bg-[#C735320A]':
                    getQuestionStatus(question) === 'unanswered',
                }"
              >
                <p class="max-w-fit max-h-fit text-sm sm:text-base">
                  Question {{ i + 1 }}
                </p>
                <div class="flex items-center">
                  @if (getQuestionStatus(question) === 'unanswered') {
                    <span class="ml-2 text-[#C73A3A] text-sm">
                      <img
                        src="../../../../assets/testTakingImages/not-passed.svg"
                        alt="not answered"
                        class="w-4 h-4 sm:w-5 sm:h-5"
                      />
                    </span>
                  } @else if (getQuestionStatus(question) === 'answered') {
                    <span class="ml-2 text-[#04952C] text-sm">
                      <img
                        src="../../../../assets/testTakingImages/test-passed.svg"
                        alt="answered"
                        class="w-4 h-4 sm:w-5 sm:h-5"
                      />
                    </span>
                  }
                </div>
              </button>
            </div>
          </section>
        </div>
        <div
          *ngIf="unAnsweredQuestion.length > 0"
          class="min-h-[3.5rem] bg-[#C735320A] text-[#C73A3A] flex items-center pl-3 sm:pl-5 rounded-lg"
        >
          <p class="flex items-center gap-2 text-sm sm:text-base">
            <span
              ><img
                src="../../../../assets/testTakingImages/caution.svg"
                alt="caution"
                class="w-4 h-4 sm:w-5 sm:h-5"
            /></span>
            You have some unanswered questions. Are you sure you want to submit?
          </p>
        </div>
        <div
          class="w-full sm:max-w-[25rem] flex flex-col sm:flex-row gap-3 sm:ml-auto mt-auto"
        >
          <app-custom-button-test-taker
            (clicked)="onCancelSectionConfirmationModal()"
            [variant]="'secondary'"
            class="w-full sm:w-auto"
            [disabled]="isLoading"
          >
            Return to Section
          </app-custom-button-test-taker>
          <app-custom-button-test-taker
            (clicked)="submitSection()"
            [spinner]="isLoading"
            class="w-full sm:w-auto"
            [disabled]="isLoading"
          >
            {{ isLoading ? '' : ' Submit Section' }}
          </app-custom-button-test-taker>
        </div>
      </div>
    </div>
  }
</div>

<app-custom-mini-modal
  [(visibleModal)]="exhaustedTimeModal"
  [bodyText]="
    'You\'ve reached the end of the allocated time for this assessment. We\'re now automatically submitting your answers. Your progress has been saved'
  "
  [textAlign]="'center'"
  [headerTitle]="'Time\'s Up - Submitting Your Assessment'"
  [width]="'582px'"
  [height]="'338px'"
  [showButtons]="false"
  [loaderBelow]="true"
  [closeButton]="false"
>
</app-custom-mini-modal>

@if (
  apiScreenAccessState === false &&
  this.proctoringProtocols.includes('Screen Capture')
) {
  <app-custom-mini-modal
    [headerTitle]="'Screen Sharing Required'"
    [textAlign]="'center'"
    [right]="'Re-share Screen'"
    [visibleModal]="apiScreenAccessState === false"
    [bodyText]="
      'Let\'s resume your test by re-sharing your screen. Please note, assessment countdown is still in progress.'
    "
    [width]="'486px'"
    [height]="'278px'"
    (rightClickEvent)="confirmShareScreen()"
    [caution]="true"
    [closeButton]="false"
    [showLeftButton]="false"
  >
  </app-custom-mini-modal>
}
