import { Component, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import {
  Assessment,
  BasicViolationData,
  CodingAnswer,
  Question,
  QuestionsResults,
  Result,
  SelectedAnswers,
  Test,
  TestTakingInformation,
  ViolationScreenShot,
} from '../../Interfaces/Types/testTakerInterface';
import { TestTakingService } from '../../services';
import { ActivatedRoute, Router } from '@angular/router';
import { CustomToastComponent } from '../../components/custom-toast/custom-toast.component';
import { TestProgressBarComponent } from '../components/test-progress-bar/test-progress-bar.component';
import { TestProgressRingComponent } from '../components/test-progress-ring/test-progress-ring.component';
import { TimerComponent } from '../components/timer/timer.component';
import { BaseTestInterfaceComponent } from '../components/base-test-interface/base-test-interface.component';
import { ComprehensionTestInterfaceComponent } from '../components/comprehension-test-interface/comprehension-test-interface.component';
import { CustomMiniModalComponent } from '../../components/custom-mini-modal/custom-mini-modal.component';
import { ToastService } from '../../services/toast-service/toast.service';
import { ProctoringService } from '../../services/proctoring.service';
import { retry, Subscription } from 'rxjs';
import { environment } from '../../../../src/environments/environment';
import { AnalyticsService } from '../../services/analytics.service';
import { CustomButtonTestTakerComponent } from '../../test-taker/components/custom-button-test-taker/custom-button-test-taker.component';
import { MatrixSubquestion } from '../../test-management/Questions/main-questions-page/create-questions/questionTypes/matrix-questions/matrix-table/matrix-table.component';
import { CommonModule } from '@angular/common';
@Component({
  selector: 'app-test-taking-main-page',
  standalone: true,
  imports: [
    CustomToastComponent,
    TestProgressBarComponent,
    TestProgressRingComponent,
    TimerComponent,
    BaseTestInterfaceComponent,
    ComprehensionTestInterfaceComponent,
    CustomMiniModalComponent,
    CustomButtonTestTakerComponent,
    CommonModule,
  ],
  templateUrl: './test-taking-main-page.component.html',
  styleUrl: './test-taking-main-page.component.css',
})
export class TestTakingMainPageComponent implements OnInit, OnDestroy {
  subscription: Subscription = new Subscription();
  selectedAnswers: SelectedAnswers = [];
  questionPercentage: string = '';
  idleTime: number = 0;

  public readonly testTakingService = inject(TestTakingService);
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  private readonly toast = inject(ToastService);
  private readonly proctoringService = inject(ProctoringService);
  private readonly analyticsService = inject(AnalyticsService);

  comprehension = false;
  currentQuestionIndex: number = 0;
  currentQuestion: Question | undefined;
  currentTestQuestions: Question[] = [];
  currentQuestionOptions: string[] = [];
  currentTest!: Test;
  currentAssessment: Assessment | undefined;
  currentTestId: string = '';
  isLoading: boolean = false;
  assessmentTests: Test[] = [];
  candidateEmail: string = '';
  totalTestQuestions: number = 0;
  currentTestIndex: number = 0;
  nextTextIndex: number = 0;
  testProgress: number = 0;
  flaggedQuestions: Array<string> = [];
  testTakerId: string = '';
  unAnsweredQuestion: Result[] = [];
  numberOfFlaggedQuestions: number = 0;
  result: Result[] = [];
  timeToContinue: number | undefined;
  isConfirmSectionSubmission: boolean = false;
  proctoringSubscription: Subscription = new Subscription();
  windowViolationInterval: NodeJS.Timeout | undefined;
  screenCaptureInterval: NodeJS.Timeout | undefined;
  candidateCaptureInterval: NodeJS.Timeout | undefined;
  windowViolationCounter: number = 0;
  violationTimeStamp: number | null = null;
  myCountNumber: number = 0;
  myCountStart: string = '';
  myCountEnd: string = '';
  proctoringProtocols: string[] = [];
  violateSnapsScreen: ViolationScreenShot[] = [];
  violateSnapsTaker: ViolationScreenShot[] = [];
  isProduction: boolean | string = environment.production;
  exhaustedTimeModal: boolean = false;
  intervalDraftSubscription: Subscription = new Subscription();
  surveyState!: boolean;
  resultState!: boolean;
  apiScreenAccessState: boolean | undefined;
  cameraAccess: boolean | undefined = true;
  trackScreenStream: Subscription | undefined;
  cameraStreamTracking: Subscription | undefined;
  currentQuestionFillInOptions!: Array<{
    id: string;
    blank: string;
    value: string;
    isBlank: boolean;
  }>;

  ngOnInit(): void {
    const queryParamsSub = this.route.queryParams.subscribe((params) => {
      this.currentTestId = params['testId'];
      this.currentQuestionIndex =
        typeof params['questionIndex'] === 'string'
          ? parseInt(params['questionIndex'], 10)
          : 0;
    });
    this.subscription.add(queryParamsSub);

    this.testTakerId =
      this.testTakingService.getTestTakerIdFromParam() as string;

    this.loadStoredData();
    this.setUpTestResultBucket();

    // 🟢 Remove: do NOT run protocols here, only after configureTestView()
    if (this.testTakingService.inProgressReturnedData) {
      this.configureTestView(this.testTakingService.inProgressReturnedData);
    }

    this.trackScreenStream = this.proctoringService.trackStream(
      1000,
      { streamGetter: () => this.proctoringService.screenStream?.active },
      (isActive) => (this.apiScreenAccessState = isActive)
    );

    const savedProgress = localStorage.getItem('testProgress');
    if (savedProgress) {
      this.testProgress = parseFloat(savedProgress);
    }
  }

  async configureTestView(data: TestTakingInformation) {
    this.testTakingService.receivedAssessmentInformation = data;
    this.currentAssessment = data.assessment;
    this.surveyState = data.conductSurvey;
    this.resultState = data.showResults;
    this.assessmentTests = this.currentAssessment.tests;
    this.candidateEmail = data.email;

    this.proctoringProtocols = data.proctorFeatures;

    if (this.proctoringProtocols.includes('Window Violation')) {
      await this.monitorWindowViolation();
    }

    for (const item of this.assessmentTests) {
      this.handleTest(item);
    }
  }

  setUpTestResultBucket(): void {
    const existingTestResult =
      this.testTakingService.assessmentData.testResults.find(
        (result) => result.testId === this.currentTestId
      );

    if (!existingTestResult) {
      this.testTakingService.assessmentData.startTime =
        new Date().toISOString();
      this.testTakingService.assessmentData.testResults.push({
        testId: this.currentTestId,
        questionResults: [],
      });
    }
  }

  handleTest(test: Test) {
    this.totalTestQuestions += test.questions.length;
    if (test.id === this.currentTestId) {
      this.handleCurrentTest(test);
    }
    this.handleTestProgressBarPercentage();
  }

  handleCurrentTest(test: Test) {
    const testIndex =
      this.findTestIndexById(this.assessmentTests, this.currentTestId) + 1;
    this.currentTestIndex = testIndex;
    this.currentTest = test;
    this.currentTestQuestions = test.questions;
    const testResult = this.currentTestQuestions[this.currentQuestionIndex];
    this.currentQuestion = testResult;

    this.setFlaggedQuestions();
    this.setQuestionOptions();
  }
  handleTestProgressBarPercentage() {
    this.questionPercentage = (
      ((this.currentQuestionIndex + 1) / this.currentTestQuestions.length) *
      100
    ).toString();
  }

  findTestIndexById(tests: Test[] | undefined, targetTestId: string) {
    for (let i = 0; i < (tests as Test[]).length; i++) {
      if ((tests as Test[])[i].id === targetTestId) {
        return i;
      }
    }

    return -1;
  }

  setFlagStatus(event: boolean) {
    (this.currentQuestion as Question).flagged = event;
    this.currentTestQuestions[this.currentQuestionIndex].flagged = event;
    this.flaggedQuestions = JSON.parse(
      localStorage.getItem('flaggedQuestions') ?? '[]'
    );

    const questionId = (this.currentQuestion as Question).id;
    if (event) {
      if (!this.flaggedQuestions.includes(questionId)) {
        this.flaggedQuestions.push(questionId);
      }
    } else {
      const index = this.flaggedQuestions.indexOf(questionId);
      if (index > -1) {
        this.flaggedQuestions.splice(index, 1);
      }
    }
    localStorage.setItem(
      'flaggedQuestions',
      JSON.stringify(this.flaggedQuestions)
    );
  }

  setFlaggedQuestions() {
    this.flaggedQuestions = JSON.parse(
      localStorage.getItem('flaggedQuestions') ?? '[]'
    );
    this.currentTestQuestions.forEach((question) => {
      question.flagged = this.flaggedQuestions.includes(question.id);
    });
  }
  setQuestionOptions() {
    this.currentQuestionOptions = [];
    this.selectedAnswers = [];
    this.testTakingService.selectedAnswerChoices = [];

    const answerOptions =
      this.currentQuestion?.multipleChoiceAnswer?.options ??
      this.currentQuestion?.trueOrFalseAnswer?.options ??
      this.currentQuestion?.multipleSelectAnswer?.options;

    const existingTestResult =
      this.testTakingService.assessmentData.testResults.find(
        (result) => result.testId === this.currentTestId
      );

    if (
      this.currentQuestion?.id ===
      existingTestResult?.questionResults[this.currentQuestionIndex]?.questionId
    ) {
      const existinTestTakerAnswers = existingTestResult?.questionResults[
        this.currentQuestionIndex
      ]?.testTakerAnswers as string[];

      this.testTakingService.testTakerAnswer = existinTestTakerAnswers;
    } else {
      this.testTakingService.testTakerAnswer = [];
    }

    if (answerOptions) {
      answerOptions.forEach((option: string) => {
        existingTestResult?.questionResults.forEach(
          (question: QuestionsResults) => {
            if (question.questionId === this.currentQuestion?.id) {
              this.selectedAnswers = question.testTakerAnswers;
              this.testTakingService.setCandidateAnswers(
                question.testTakerAnswers
              );
            }
          }
        );
        this.currentQuestionOptions.push(option);
      });
    } else if (this.currentQuestion?.questionType === 'Matrix') {
      const existingQuestionOptions = existingTestResult?.questionResults[
        this.currentQuestionIndex
      ]?.testTakerAnswers as unknown as MatrixSubquestion[];
      const currentQuestionOptions =
        this.currentQuestion.matchMatrixAnswer.questions;
      this.updateWithMatrixAnswers(
        existingQuestionOptions,
        currentQuestionOptions
      );
    } else if (this.currentQuestion?.questionType === 'Fill_in') {
      this.currentQuestionFillInOptions = [];
      const fillInAnswer = this.currentQuestion?.fillInAnswer;
      if (fillInAnswer) {
        fillInAnswer.options.forEach((option) => {
          existingTestResult?.questionResults.forEach(
            (question: QuestionsResults) => {
              if (question.questionId === this.currentQuestion?.id) {
                this.selectedAnswers = question.testTakerAnswers;
                this.testTakingService.setCandidateAnswers(
                  question.testTakerAnswers
                );
              }
            }
          );
          this.currentQuestionFillInOptions.push({
            id: option.id,
            blank: option.blank,
            value: option.value,
            isBlank: option.value === '',
          });
        });
      }
    } else {
      const existingTestResult =
        this.testTakingService.assessmentData.testResults.find(
          (result) => result.testId === this.currentTestId
        );
      this.testTakingService.setCandidateEssayAnswers(
        existingTestResult?.questionResults as QuestionsResults[]
      );
    }
  }

  getChoices(event: string[] | MatrixSubquestion[] | CodingAnswer[]) {
    this.selectedAnswers = event;
  }

  getQuestionNumber(): string {
    const questionNumber = this.currentQuestionIndex + 1;
    return `Q${questionNumber}`;
  }

  private getFinalAnswer(): string[] | MatrixSubquestion[] {
    const finalAnswer: string[] | MatrixSubquestion[] = [];
    if (this.selectedAnswers.length !== 0) {
      finalAnswer.push(
        this.processSelectedAnswers() as unknown as string & MatrixSubquestion
      );
    } else if (
      this.currentQuestion?.questionType === 'Matrix' &&
      this.selectedAnswers.length === 0
    ) {
      finalAnswer.push(
        this.processMatrixAnswer() as unknown as string & MatrixSubquestion
      );
    }
    return finalAnswer;
  }

  private processSelectedAnswers(): SelectedAnswers {
    return this.selectedAnswers.map((answer) => {
      if (typeof answer === 'object' && 'subquestionId' in answer) {
        return {
          subquestionId: answer.subquestionId,
          answers: (answer.answers as unknown as string[]).flatMap(
            (ans) => ans ?? ''
          ),
        } as unknown as string & MatrixSubquestion;
      }
      return (answer ?? '') as string & MatrixSubquestion & CodingAnswer;
    });
  }

  private processMatrixAnswer(): MatrixSubquestion[] {
    return (
      this.currentQuestion?.matchMatrixAnswer.questions
        ?.filter(
          () =>
            this.currentQuestion?.id ===
            this.currentQuestion?.matchMatrixAnswer.questionId
        )
        .map(
          (question) =>
            ({
              subquestionId: question.id,
              answers: [''],
            }) as unknown as MatrixSubquestion
        ) ?? []
    );
  }

  private createQuestionResults(finalAnswer: string[] | MatrixSubquestion[]) {
    return {
      questionId: this.currentQuestion?.id,
      testTakerAnswers: finalAnswer.flat(),
      idleTime: this.idleTime,
      questionType: this.currentQuestion?.questionType,
    };
  }

  navigate(direction: 'next' | 'previous') {
    const finalAnswer: string[] | MatrixSubquestion[] = this.getFinalAnswer();
    const questionResults = this.createQuestionResults(finalAnswer);

    this.testTakingService.addTestResult(
      questionResults as QuestionsResults,
      this.currentTestId
    );
    if (direction === 'next') {
      this.idleTime = 0;
      this.currentQuestionIndex = this.currentQuestionIndex + 1;
      this.currentQuestion =
        this.currentTestQuestions[this.currentQuestionIndex];
      this.updateProgressBar();
      this.setQuestionOptions();
      this.saveAnsweringDraft();
    } else if (direction === 'previous') {
      this.currentQuestionIndex = this.currentQuestionIndex - 1;
      this.currentQuestion =
        this.currentTestQuestions[this.currentQuestionIndex];

      this.updateProgressBar();
      this.setQuestionOptions();
    }

    this.router.navigate([], {
      queryParams: {
        questionIndex: this.currentQuestionIndex,
      },
      queryParamsHandling: 'merge', // optional: preserve existing params
    });
  }

  updateProgressBar() {
    this.questionPercentage = (
      ((this.currentQuestionIndex + 1) / this.currentTestQuestions.length) *
      100
    ).toString();
    let countsQuestions = 0;
    let number = 0;
    this.assessmentTests.forEach((test) => {
      test.questions.forEach((question: Question) => {
        if (
          question.id === this.currentQuestion?.id &&
          test.id === this.currentTestId
        ) {
          number = countsQuestions + 1;
        }
        countsQuestions += 1;
      });
    });

    this.testProgress = (number / this.totalTestQuestions) * 100;
    localStorage.setItem('testProgress', this.testProgress.toString());
  }

  sectionValidation(state = true) {
    this.isConfirmSectionSubmission = state;
    const finalAnswer = this.getFinalAnswer();
    const questionResults = this.createQuestionResults(finalAnswer);
    this.idleTime = 0;
    this.testTakingService.addTestResult(
      questionResults as QuestionsResults,
      this.currentTestId
    );
    this.result = JSON.parse(
      localStorage.getItem('assessmentResults')!
    )?.testResults[0]?.questionResults;
    this.selectedAnswers = [];

    this.unAnsweredQuestion = this.result.filter((item) => {
      if (
        item.questionType === 'Matrix' &&
        Array.isArray(item.testTakerAnswers)
      ) {
        return (item.testTakerAnswers as MatrixSubquestion[]).every(
          (sub) =>
            Array.isArray(sub.answers) &&
            sub.answers.every((ans: string) => !ans || ans.trim() === '')
        );
      }
      return !item.testTakerAnswers || item.testTakerAnswers.length === 0;
    });
    this.numberOfFlaggedQuestions = this.currentTestQuestions.filter(
      (question) => question.flagged === true
    ).length;
  }

  getQuestionStatus(question: Question): string {
    if (this.unAnsweredQuestion.length > 0) {
      const unansweredQuestionIds = this.unAnsweredQuestion.map(
        (item) => item.questionId
      );
      if (unansweredQuestionIds.includes(question.id)) {
        return 'unanswered';
      }
    }
    return 'answered';
  }

  submitSection() {
    this.isLoading = true;
    this.testTakingService.assessmentData.finishTime = new Date().toISOString();
    const currentTestIndex = this.assessmentTests.findIndex(
      (test) => test.id === this.currentTestId
    );
    this.nextTextIndex = currentTestIndex + 1;
    if (this.nextTextIndex < this.assessmentTests.length) {
      this.confirmSectionSubmission();
    } else {
      this.assessmentSubmission();
    }
  }

  onCancelSectionConfirmationModal() {
    this.isConfirmSectionSubmission = false;
    this.updateProgressBar();
    this.setQuestionOptions();
    this.router.navigate([], {
      queryParams: {
        fromSubmitPage: true,
      },
      queryParamsHandling: 'merge', // preserves other query params if needed
    });
  }

  confirmSectionSubmission() {
    const sectionSub = this.testTakingService.postSectionSubmit().subscribe({
      next: (response) => {
        this.toast.onShow(
          'Success Message',
          `${response.data.message}`,
          true,
          'success'
        );

        this.onSectionSubmitted();
        this.isLoading = false;
      },
      error: (error) => {
        this.toast.onShow('Error Message', `${error}`, true, 'error');

        if (error === 'Section already submitted') {
          this.onSectionSubmitted();
          this.analyticsService.track('Section already submitted', {
            testId: this.currentTestId,
            assessmentId: this.currentAssessment?.id,
          });
        }
        this.analyticsService.track('Section Submision Failed', {
          testId: this.currentTestId,
          assessmentId: this.currentAssessment?.id,
        });
        this.isLoading = false;
      },
    });
    this.subscription.add(sectionSub);
  }

  onSectionSubmitted() {
    this.testTakingService.assessmentData.testResults = [];
    this.testTakingService.assessmentData.candidateMonitoring = [];
    this.testTakingService.assessmentData.screenMonitoring = [];

    this.analyticsService.track('Section Submitted', {
      testId: this.currentTestId,
      assessmentId: this.currentAssessment?.id,
    });

    const nextTextId = this.assessmentTests[this.nextTextIndex].id;

    // Save data before navigating
    localStorage.setItem(
      'assessmentResults',
      JSON.stringify(this.testTakingService.assessmentData)
    );

    // ✅ Navigate and clear browser history
    this.router.navigate(['test-taking/waiting-page'], {
      queryParams: { testTakerId: this.testTakerId, testId: nextTextId },
      replaceUrl: true, // <--- this replaces current history entry
    });

    this.testTakingService.assessmentData.startTime = new Date().toISOString();

    // Save again if needed (redundant but kept from your original code)
    localStorage.setItem(
      'assessmentResults',
      JSON.stringify(this.testTakingService.assessmentData)
    );
  }

  assessmentSubmission() {
    const assessmentSub = this.testTakingService
      .postAssessmentSubmit()
      .subscribe({
        next: (response) => {
          if (response) {
            this.assessmentSubmitted();
          }
          this.isLoading = false;
          this.exhaustedTimeModal = false;
        },
        error: (error) => {
          this.isLoading = false;
          if (
            error ===
            '"testResults[0].questionResults[0].testTakerAnswers[0]" is not allowed to be empty'
          ) {
            this.toast.onShow(
              'Error Message',
              `Please answer all questions`,
              true,
              'error'
            );
          } else {
            this.toast.onShow('Error Message', `${error}`, true, 'error');
            if (error === 'Assessment already completed') {
              this.assessmentSubmitted();
              this.analyticsService
                .track('Assessment already completed', {
                  assessmentTitle: this.currentAssessment?.title,
                  assessmentId: this.currentAssessment?.id,
                  error,
                })
                .then();
            }
          }
          this.analyticsService
            .track('Assessment Submission Failed', {
              assessmentTitle: this.currentAssessment?.title,
              assessmentId: this.currentAssessment?.id,
              error,
            })
            .then();
        },
      });
    this.subscription.add(assessmentSub);
  }

  assessmentSubmitted() {
    localStorage.removeItem('assessmentResults');
    localStorage.removeItem('proctor');
    localStorage.removeItem('surveyTaken');
    localStorage.removeItem('isInitialCapture');
    localStorage.removeItem('flaggedQuestions');
    localStorage.setItem('surveyState', JSON.stringify(this.surveyState));
    localStorage.setItem('resultState', JSON.stringify(this.resultState));
    this.proctoringService.isClearedStoredTime(true);
    const organizationId =
      this.testTakingService.inProgressReturnedData?.organizationId;
    this.router.navigate(['test-taker/assessment-completed'], {
      queryParams: {
        organizationId: organizationId,
        testTakerId: this.testTakerId,
      },
    });
    this.testTakingService.inProgressReturnedData = undefined;
  }
  loadStoredData() {
    const timeLeft = Number(localStorage.getItem('assessmentTimeRemaining'));
    if (timeLeft) {
      this.timeToContinue = timeLeft;
    }
    const savedAssessmentResult = JSON.parse(
      localStorage.getItem('assessmentResults') as string
    );
    const assessmentDataDefault = {
      startTime: '',
      finishTime: '',
      testResults: [],
      screenMonitoring: [],
      candidateMonitoring: [],
      isAutoSubmission: false,
    };
    this.testTakingService.assessmentData =
      savedAssessmentResult ?? assessmentDataDefault;
    if (this.timeToContinue === 0) {
      this.onAssessmentTimeExhaustion();
    }
  }

  async monitorWindowViolation() {
    this.myCountStart = '';
    this.myCountEnd = '';
    this.violationTimeStamp = null;

    this.proctoringSubscription = this.proctoringService.tabStatus$.subscribe(
      (status: boolean) => {
        if (status) {
          if (this.myCountStart) {
            this.myCountEnd = new Date().toISOString();
            this.setScreenWindowViolationData();
            this.setTakerWindowViolationData();
            this.clearFocusCounter();
          } else {
            this.analyticsService.track('Window Violation', {
              testId: this.currentTestId,
              assessmentId: this.currentAssessment?.id,
              reason: 'User left the window before the timer started.',
            });
          }
        } else {
          // user left the window
          this.myCountStart = new Date().toISOString();
          this.violationTimeStamp = Date.now();
          this.focusCounter();
          this.myCountNumber += 1;
        }
      }
    );
  }

  clearFocusCounter() {
    clearInterval(this.windowViolationInterval as unknown as number);
    if (!this.isProduction) {
      this.toast.onShow(
        'Window Violation Warning',
        `You have violated the exam window for ${this.windowViolationCounter} seconds`,
        true,
        'warning'
      );
    }
  }

  focusCounter() {
    this.windowViolationInterval = setInterval(() => {
      this.windowViolationCounter++;
    }, 1000);
    if (!this.isProduction) {
      this.toast.onShow(
        'Tab Change Warning',
        `You changed tabs for ${this.windowViolationCounter} seconds`,
        true,
        'warning'
      );
    }
  }

  setScreenWindowViolationData() {
    this.testTakingService.assessmentData.screenMonitoring.push({
      ...this.getBasicViolationData(),
      shots: this.proctoringService.violateSnapsScreen,
    });
  }

  setTakerWindowViolationData() {
    const basicCandidateMonitoringData = this.getBasicViolationData();
    delete basicCandidateMonitoringData.isIntegrityShot;
    this.testTakingService.assessmentData.candidateMonitoring.push({
      ...basicCandidateMonitoringData,
      shots: this.proctoringService.violateSnapsTaker,
    });
  }

  getBasicViolationData() {
    return {
      violation: true,
      startTime: this.myCountStart,
      endTime: this.myCountEnd,
      violationNumber: this.myCountNumber,
      questionId: this.currentQuestion?.id,
      questNumber: this.currentQuestionIndex + 1,
      testId: this.currentTestId,
      testNumber: this.currentTestIndex,
      isIntegrityShot: false,
    } as BasicViolationData;
  }

  async onAssessmentTimeExhaustion() {
    await this.analyticsService.track('Assessment Duration Exhausted', {
      assessmentId: this.currentAssessment?.id,
    });
    this.exhaustedTimeModal = true;
    setTimeout(() => {
      this.sectionValidation(false);
      this.testTakingService.assessmentData.finishTime =
        new Date().toISOString();
      for (
        let questionLocation = this.result.length;
        questionLocation < this.currentTestQuestions.length;
        questionLocation++
      ) {
        const questionResults = {
          questionId: this.currentTestQuestions[questionLocation]?.id,
          testTakerAnswers: this.selectedAnswers,
          idleTime: this.idleTime,
          questionType:
            this.currentTestQuestions[questionLocation]?.questionType,
        };
        this.testTakingService.addTestResult(
          questionResults,
          this.currentTestId
        );
      }
      this.assessmentSubmission();
    }, 8000);
  }

  saveAnsweringDraft() {
    const draftSub = this.testTakingService
      .postSavedQuestionsWithAnswersAsDraft()
      ?.pipe(retry(2))
      ?.subscribe({
        error: (error) => {
          this.analyticsService
            .track('Save Draft Failed', {
              testId: this.currentTestId,
              assessmentId: this.currentAssessment?.id,
              reason: error,
            })
            .then();
        },
      }) as Subscription;
    this.intervalDraftSubscription = draftSub;
    this.subscription.add(draftSub);
  }

  ngOnDestroy(): void {
    this.proctoringSubscription.unsubscribe();
    this.intervalDraftSubscription.unsubscribe();
    this.subscription.unsubscribe();
    // Stop screen switching monitor
  }

  async confirmShareScreen() {
    await this.proctoringService.shareScreen();
  }

  updateWithMatrixAnswers(
    existingQuestionOptions: MatrixSubquestion[],
    currentQuestionOptions: MatrixSubquestion[]
  ): void {
    const answerMap = new Map(
      existingQuestionOptions?.map((item) => [item.subquestionId, item.answers])
    );

    currentQuestionOptions.forEach((item) => {
      item.answers = answerMap.get(item.id);
    });
  }

  goToQuestion(index: number): void {
    this.isConfirmSectionSubmission = false;

    this.currentQuestionIndex = index;
    this.currentQuestion = this.currentTestQuestions[index];

    this.updateProgressBar();
    this.setQuestionOptions();
    this.saveAnsweringDraft();

    this.router.navigate([], {
      queryParams: {
        questionIndex: index,
        fromSubmitPage: true,
      },
      queryParamsHandling: 'merge', // preserves other query params if needed
    });
  }
}
