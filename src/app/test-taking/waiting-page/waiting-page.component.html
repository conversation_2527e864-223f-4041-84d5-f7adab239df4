<app-custom-toast />
<div class="px-6 py-2">
  <div class="header">
    <div class="flex">
      <img
        src="../../assets/icons/dodopkoIcon.svg"
        alt="logo"
        class="h-[3.5rem]"
      />
    </div>
  </div>
  <hr class="mt-2 border border-[#0C4767] dark:bg-gray-700" />
  <div class="flex flex-col h-[70vh] justify-between">
    <div class="warmup-main gap-5 justify-between">
      <div>
        <h1 class="font-bold text-center text-blue-B700 text-[24px]">
          Good job, You have Completed {{ this.testNumber - 1 }} of
          {{ this.tests.length }} Section(s)
        </h1>
        <h2 class="text-center text-blue-B700 text-[20px]">
          Get Ready To Start the Next Section
        </h2>
        <h3 class="font-bold text-center text-blue-B700 text-[18px]">
          {{ this.currentTest?.title }}
        </h3>
      </div>
      <div class="stopwatch">
        <img
          src="../../../assets/generalImages/Waiting-amico 1.svg"
          alt="online test icon"
          class="object-contain"
        />
        <p
          class="absolute ml-[60vw] font-bold text-center text-blue-B700 text-[24px]"
        >
          Next Section Duration : {{ time }}
        </p>
        <div
          class="absolute ml-[60vw] mt-32 font-bold text-center text-blue-B700 text-[24px]"
        ></div>
      </div>
    </div>
    <div class="flex flex-col ml-auto gap-9">
      <div>
        <app-timer
          [timeLimit]="this.waitingTime"
          (isTimerDone)="startTest(currentTest?.id || '')"
        ></app-timer>
      </div>
      <button
        class="bg-[#0c4767] text-white w-[157px] rounded-lg h-11"
        (click)="startTest(currentTest?.id || '')"
      >
        Start Section
      </button>
    </div>
  </div>
</div>
