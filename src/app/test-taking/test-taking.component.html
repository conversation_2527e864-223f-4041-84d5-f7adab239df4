<router-outlet></router-outlet>
@if (
  cameraAccess === false &&
  this.proctoringProtocols.includes('Candidate Capture')
) {
  <app-custom-mini-modal
    [headerTitle]="'Camera Access Required'"
    [textAlign]="'center'"
    [right]="'Re-enable Camera'"
    [visibleModal]="cameraAccess === false"
    [bodyText]="
      'Let\'s resume your test by granting us permission to your camera. Select icon <img src=\'../../../assets/icons/page-info.svg\' alt=\'info icon\' class=\'h-6 w-6 inline align-text-bottom\'>/<img src=\'../../../assets/icons/privacy.svg\' alt=\'info icon\' class=\'h-6 w-6 inline align-text-bottom\'> at the left side of your address bar, then toggle on the camera permission. After that, click on &quot;Re-enable Camera&quot; to continue. Please note, assessment countdown is still in progress.</div>'
    "
    [width]="'546px'"
    [height]="'355px'"
    (rightClickEvent)="confirmOpenCamera()"
    [caution]="true"
    [closeButton]="false"
    [showLeftButton]="false"
  >
  </app-custom-mini-modal>
}
