<div *ngFor="let option of questionOptions">
  <label class="flex gap-x-6 w-full mb-3">
    <input
      class="hover:cursor-pointer"
      type="radio"
      [checked]="isSelected(option)"
      name="selectedChoice"
      [value]="option"
      (change)="onChange(option)"
    />
    <div
      [appSafeHtml]="option"
      class="bg-white w-full border-2 border-background:#708794 p-4 rounded-md hover:border-[#0C4767] hover:cursor-pointer cardIn"
    ></div>
  </label>
</div>
