import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CodingTestCasesComponent } from './coding-test-cases.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ActivatedRoute } from '@angular/router';
import { of, throwError, Observable } from 'rxjs';
import { TestTakingService } from '../../../services/test-taking-service/test-taking.service';
import { ToastService } from '../../../services/toast-service/toast.service';
import {
  CodeConstraint,
  CodeTestCases,
  SolutionResult,
} from '../../../Interfaces/Types/testTakerInterface';
import { SimpleChanges } from '@angular/core';

class MockResizeObserver {
  observe() {}
  unobserve() {}
  disconnect() {}
}

global.ResizeObserver = MockResizeObserver;

describe('CodingTestCasesComponent', () => {
  let component: CodingTestCasesComponent;
  let fixture: ComponentFixture<CodingTestCasesComponent>;
  let mockTestTakingService: Partial<jest.Mocked<TestTakingService>>;
  let mockToastService: Partial<jest.Mocked<ToastService>>;

  // Test data factories
  const createMockTestCase = (
    overrides: Partial<CodeTestCases> = {}
  ): CodeTestCases => ({
    test_case_id: 'test-case-1',
    input_data: '1 2',
    output_data: '3',
    visibility: 'public',
    question_id: 'question-1',
    validation_status: '',
    s3_location: '',
    consoleOutput: '',
    ...overrides,
  });

  const createMockSolutionResult = (
    overrides: Partial<SolutionResult> = {}
  ): SolutionResult => ({
    test_case_id: 'test-case-1',
    question_id: 'question-1',
    input: '1 2',
    output: '3',
    actualOutput: '3',
    consoleOutput: '',
    isCorrect: true,
    executionTime: '100ms',
    memory: 256,
    error: null,
    statusDescription: 'Accepted',
    inQueue: false,
    statusId: 3,
    ...overrides,
  });

  const createMockCodeConstraint = (
    overrides: Partial<CodeConstraint> = {}
  ): CodeConstraint => ({
    id: 'constraint-1',
    timeLimit: 1000,
    memoryLimit: 256,
    timeComplexity: 'O(n)',
    spaceComplexity: 'O(1)',
    questionId: 'question-1',
    ...overrides,
  });

  beforeEach(async () => {
    mockTestTakingService = {
      getQuestiontTestcases: jest.fn(),
    } as Partial<jest.Mocked<TestTakingService>>;

    mockToastService = {
      onShow: jest.fn(),
    } as Partial<jest.Mocked<ToastService>>;

    await TestBed.configureTestingModule({
      imports: [CodingTestCasesComponent, HttpClientTestingModule],
      providers: [
        { provide: TestTakingService, useValue: mockTestTakingService },
        { provide: ToastService, useValue: mockToastService },
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {},
            params: {},
            queryParams: of({ organizationId: 'test-org-id' }),
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(CodingTestCasesComponent);
    component = fixture.componentInstance;

    // Reset mocks before each test
    jest.clearAllMocks();
  });

  afterEach(() => {
    fixture.destroy();
  });

  describe('Component Initialization', () => {
    it('should create the component successfully', () => {
      expect(component).toBeTruthy();
      expect(component.testCases).toEqual([]);
      expect(component.visibleTestCases).toEqual([]);
      expect(component.startIndex).toBe(0);
      expect(component.maxVisibleItems).toBe(0);
      expect(component.isTestCasesLoading).toBe(false);
    });

    it('should initialize with default values before detectChanges', () => {
      // Before detectChanges, properties should be in their initial state
      expect(component.selectedTestCaseIndex).toBeUndefined();
      expect(component.candidateSolutionResult).toBeUndefined();
      expect(component.codeConstraint).toBeUndefined();
    });

    it('should initialize properly after detectChanges', () => {
      fixture.detectChanges();

      // After detectChanges, the component lifecycle runs and may set initial values
      expect(component).toBeTruthy();
      expect(component.testCases).toEqual([]);
      expect(component.visibleTestCases).toEqual([]);
    });
  });

  describe('ngOnChanges', () => {
    beforeEach(() => {
      // Ensure component is initialized for these tests
      fixture.detectChanges();
    });

    it('should call getTestCases when codeConstraint changes with questionId', () => {
      const spy = jest.spyOn(component, 'getTestCases');
      mockTestTakingService.getQuestiontTestcases!.mockReturnValue(of([]));

      // Set the codeConstraint BEFORE calling ngOnChanges
      component.codeConstraint = createMockCodeConstraint();

      const changes: SimpleChanges = {
        codeConstraint: {
          currentValue: createMockCodeConstraint(),
          previousValue: undefined,
          firstChange: true,
          isFirstChange: () => true,
        },
      };

      component.ngOnChanges(changes);

      expect(spy).toHaveBeenCalled();
      // After ngOnChanges completes, the component state should be reset
      expect(component.testCases).toEqual([]);
      expect(component.visibleTestCases).toEqual([]);
      expect(component.selectedTestCaseIndex).toBe(0);
    });

    it('should set loading state to true immediately when codeConstraint changes', () => {
      // Mock the service to delay the response so we can check the loading state
      let observer: {
        next: (value: CodeTestCases[]) => void;
        complete: () => void;
      } | null = null;
      const mockObservable = new Observable<CodeTestCases[]>((obs) => {
        observer = obs;
        return () => {};
      });
      mockTestTakingService.getQuestiontTestcases!.mockReturnValue(
        mockObservable
      );

      // Set the codeConstraint BEFORE calling ngOnChanges
      component.codeConstraint = createMockCodeConstraint();

      // Initially should not be loading
      expect(component.isTestCasesLoading).toBe(false);

      const changes: SimpleChanges = {
        codeConstraint: {
          currentValue: createMockCodeConstraint(),
          previousValue: undefined,
          firstChange: true,
          isFirstChange: () => true,
        },
      };

      component.ngOnChanges(changes);

      // Should be loading immediately after ngOnChanges
      expect(component.isTestCasesLoading).toBe(true);

      // Complete the observable to clean up
      observer!.next([]);
      observer!.complete();
    });

    it('should set loading state during test case fetching', () => {
      // Mock the service to return an observable that we can control
      let observer: {
        next: (value: CodeTestCases[]) => void;
        complete: () => void;
      } | null = null;
      const mockObservable = new Observable<CodeTestCases[]>((obs) => {
        observer = obs;
        return () => {};
      });
      mockTestTakingService.getQuestiontTestcases!.mockReturnValue(
        mockObservable
      );

      component.codeConstraint = createMockCodeConstraint();
      component.getTestCases();

      // Should be loading while waiting for response
      expect(component.isTestCasesLoading).toBe(true);

      // Complete the observable
      observer!.next([]);
      observer!.complete();

      // Should not be loading after completion
      expect(component.isTestCasesLoading).toBe(false);
    });

    it('should not call getTestCases when codeConstraint has no questionId', () => {
      const spy = jest.spyOn(component, 'getTestCases');

      // Set the codeConstraint BEFORE calling ngOnChanges
      component.codeConstraint = {
        ...createMockCodeConstraint(),
        questionId: undefined,
      };

      const changes: SimpleChanges = {
        codeConstraint: {
          currentValue: {
            ...createMockCodeConstraint(),
            questionId: undefined,
          },
          previousValue: undefined,
          firstChange: true,
          isFirstChange: () => true,
        },
      };

      component.ngOnChanges(changes);

      expect(spy).not.toHaveBeenCalled();
    });

    it('should reset component state when codeConstraint changes', () => {
      // Setup mock service response first
      mockTestTakingService.getQuestiontTestcases!.mockReturnValue(of([]));

      component.testCases = [createMockTestCase()];
      component.selectedTestCaseIndex = 1;

      // Set the codeConstraint BEFORE calling ngOnChanges
      component.codeConstraint = createMockCodeConstraint();

      const changes: SimpleChanges = {
        codeConstraint: {
          currentValue: createMockCodeConstraint(),
          previousValue: undefined,
          firstChange: true,
          isFirstChange: () => true,
        },
      };

      component.ngOnChanges(changes);

      expect(component.testCases).toEqual([]);
      expect(component.visibleTestCases).toEqual([]);
      expect(component.selectedTestCaseIndex).toBe(0);
    });

    it('should handle testCases changes and update visible cases', () => {
      const spy = jest.spyOn(component, 'updateVisibleTestCases');

      const changes: SimpleChanges = {
        testCases: {
          currentValue: [createMockTestCase()],
          previousValue: [],
          firstChange: false,
          isFirstChange: () => false,
        },
      };

      component.ngOnChanges(changes);

      expect(component.startIndex).toBe(0);
      expect(spy).toHaveBeenCalled();
    });

    it('should reset selectedTestCaseIndex when testCases becomes empty', () => {
      component.selectedTestCaseIndex = 2;

      const changes: SimpleChanges = {
        testCases: {
          currentValue: [],
          previousValue: [createMockTestCase()],
          firstChange: false,
          isFirstChange: () => false,
        },
      };

      component.testCases = [];
      component.ngOnChanges(changes);

      expect(component.selectedTestCaseIndex).toBe(0);
    });
  });

  describe('Test Cases Fetching', () => {
    beforeEach(() => {
      // Ensure component is initialized for these tests
      fixture.detectChanges();
    });

    it('should fetch test cases successfully', () => {
      const mockTestCases = [
        createMockTestCase({ test_case_id: '1', visibility: 'public' }),
        createMockTestCase({ test_case_id: '2', visibility: 'private' }),
      ];

      mockTestTakingService.getQuestiontTestcases!.mockReturnValue(
        of(mockTestCases)
      );

      const setSolutionTestCasesSpy = jest.spyOn(
        component,
        'setSolutionTestCases'
      );
      const updateVisibleTestCasesSpy = jest.spyOn(
        component,
        'updateVisibleTestCases'
      );
      const selectTestCaseSpy = jest.spyOn(component, 'selectTestCase');

      component.codeConstraint = createMockCodeConstraint();
      component.getTestCases();

      expect(mockTestTakingService.getQuestiontTestcases).toHaveBeenCalledWith(
        'question-1'
      );
      expect(component.testCases).toEqual(mockTestCases);
      expect(component.isTestCasesLoading).toBe(false);
      expect(setSolutionTestCasesSpy).toHaveBeenCalled();
      expect(updateVisibleTestCasesSpy).toHaveBeenCalled();
      expect(selectTestCaseSpy).toHaveBeenCalledWith(0); // First public test case
    });

    it('should select first public test case when available', () => {
      const mockTestCases = [
        createMockTestCase({ test_case_id: '1', visibility: 'private' }),
        createMockTestCase({ test_case_id: '2', visibility: 'public' }),
      ];

      mockTestTakingService.getQuestiontTestcases!.mockReturnValue(
        of(mockTestCases)
      );
      const selectTestCaseSpy = jest.spyOn(component, 'selectTestCase');

      component.codeConstraint = createMockCodeConstraint();
      component.getTestCases();

      expect(selectTestCaseSpy).toHaveBeenCalledWith(1); // Index of first public test case
    });

    it('should handle error when fetching test cases', () => {
      const errorMessage = 'Failed to fetch test cases';
      const error = { message: errorMessage };

      mockTestTakingService.getQuestiontTestcases!.mockReturnValue(
        throwError(() => error)
      );

      component.codeConstraint = createMockCodeConstraint();
      component.getTestCases();

      expect(component.isTestCasesLoading).toBe(false);
      expect(mockToastService.onShow).toHaveBeenCalledWith(
        error,
        errorMessage,
        true,
        'error'
      );
    });

    it('should emit solution test cases after fetching', () => {
      const mockTestCases = [createMockTestCase()];
      mockTestTakingService.getQuestiontTestcases!.mockReturnValue(
        of(mockTestCases)
      );

      const emitSpy = jest.spyOn(component.solutionTestCases, 'emit');

      component.codeConstraint = createMockCodeConstraint();
      component.getTestCases();

      expect(emitSpy).toHaveBeenCalledWith([
        {
          test_case_id: 'test-case-1',
          input: '1 2',
          output: '3',
        },
      ]);
    });
  });

  describe('Test Case Status', () => {
    beforeEach(() => {
      // Ensure component is initialized for these tests
      fixture.detectChanges();
    });

    it('should return "not-run" when no result exists', () => {
      const testCase = createMockTestCase();
      component.candidateSolutionResult = [];

      expect(component.getTestCaseStatus(testCase)).toBe('not-run');
    });

    it('should return "not-run" when actualOutput is empty', () => {
      const testCase = createMockTestCase();
      component.candidateSolutionResult = [
        createMockSolutionResult({ actualOutput: '' }),
      ];

      expect(component.getTestCaseStatus(testCase)).toBe('not-run');
    });

    it('should return "failed" when actualOutput is null', () => {
      const testCase = createMockTestCase();
      component.candidateSolutionResult = [
        {
          ...createMockSolutionResult(),
          actualOutput: null as unknown as string,
        },
      ];

      expect(component.getTestCaseStatus(testCase)).toBe('failed');
    });

    it('should return "passed" when test case is correct', () => {
      const testCase = createMockTestCase();
      component.candidateSolutionResult = [
        createMockSolutionResult({ isCorrect: true, actualOutput: '3' }),
      ];

      expect(component.getTestCaseStatus(testCase)).toBe('passed');
    });

    it('should return "failed" when test case is incorrect', () => {
      const testCase = createMockTestCase();
      component.candidateSolutionResult = [
        createMockSolutionResult({ isCorrect: false, actualOutput: '4' }),
      ];

      expect(component.getTestCaseStatus(testCase)).toBe('failed');
    });
  });

  describe('Navigation Controls', () => {
    beforeEach(() => {
      // Ensure component is initialized for these tests
      fixture.detectChanges();

      component.testCases = Array.from({ length: 5 }, (_, i) =>
        createMockTestCase({ test_case_id: `test-${i}` })
      );
      component.maxVisibleItems = 3;
    });

    it('should increment startIndex on showNext when possible', () => {
      component.startIndex = 0;
      component.showNext();
      expect(component.startIndex).toBe(1);
    });

    it('should not increment startIndex beyond valid range', () => {
      component.startIndex = 2; // Max possible with 5 items and 3 visible
      component.showNext();
      expect(component.startIndex).toBe(2); // Should not change
    });

    it('should decrement startIndex on showPrevious when possible', () => {
      component.startIndex = 2;
      component.showPrevious();
      expect(component.startIndex).toBe(1);
    });

    it('should not decrement startIndex below 0', () => {
      component.startIndex = 0;
      component.showPrevious();
      expect(component.startIndex).toBe(0);
    });

    it('should correctly determine if previous button is disabled', () => {
      component.startIndex = 0;
      expect(component.isPreviousDisabled).toBe(true);

      component.startIndex = 1;
      expect(component.isPreviousDisabled).toBe(false);
    });

    it('should correctly determine if next button is disabled', () => {
      component.startIndex = 0;
      expect(component.isNextDisabled).toBe(false);

      component.startIndex = 2; // At max position
      expect(component.isNextDisabled).toBe(true);
    });

    it('should handle case when all items fit in view', () => {
      component.testCases = [createMockTestCase()];
      component.maxVisibleItems = 3;
      component.startIndex = 0;

      expect(component.isNextDisabled).toBe(true);
      expect(component.isPreviousDisabled).toBe(true);
    });
  });

  describe('Test Case Selection', () => {
    beforeEach(() => {
      // Ensure component is initialized for these tests
      fixture.detectChanges();

      component.testCases = [
        createMockTestCase({ test_case_id: 'case-1' }),
        createMockTestCase({ test_case_id: 'case-2' }),
      ];
    });

    it('should select valid test case index', () => {
      component.selectTestCase(1);
      expect(component.selectedTestCaseIndex).toBe(1);
    });

    it('should not select invalid test case index', () => {
      component.selectedTestCaseIndex = 0;
      component.selectTestCase(-1);
      expect(component.selectedTestCaseIndex).toBe(0);

      component.selectTestCase(5);
      expect(component.selectedTestCaseIndex).toBe(0);
    });

    it('should return null for selectedTestCase when index is invalid', () => {
      component.selectedTestCaseIndex = -1;
      expect(component.selectedTestCase).toBeNull();

      component.selectedTestCaseIndex = 10;
      expect(component.selectedTestCase).toBeNull();
    });

    it('should return correct selectedTestCase with solution data', () => {
      const testCase = createMockTestCase({
        test_case_id: 'case-1',
        input_data: '5 10',
        output_data: '15',
      });

      const solutionResult = createMockSolutionResult({
        test_case_id: 'case-1',
        actualOutput: '15',
        isCorrect: true,
        executionTime: '50ms',
        memory: 128,
      });

      component.testCases = [testCase];
      component.candidateSolutionResult = [solutionResult];
      component.selectedTestCaseIndex = 0;

      const result = component.selectedTestCase;

      expect(result).toEqual({
        test_case_id: 'case-1',
        question_id: 'question-1',
        input: '5 10',
        output: '15',
        actualOutput: '15',
        consoleOutput: '',
        isCorrect: true,
        executionTime: '50ms',
        memory: 128,
        error: null,
        statusDescription: 'Accepted',
        inQueue: false,
        statusId: 3,
      });
    });

    it('should return selectedTestCase with default values when no solution exists', () => {
      const testCase = createMockTestCase({
        test_case_id: 'case-1',
        input_data: '5 10',
        output_data: '15',
      });

      component.testCases = [testCase];
      component.candidateSolutionResult = [];
      component.selectedTestCaseIndex = 0;

      const result = component.selectedTestCase;

      expect(result).toEqual({
        test_case_id: 'case-1',
        question_id: 'question-1',
        input: '5 10',
        output: '15',
        actualOutput: '',
        consoleOutput: '',
        isCorrect: false,
        executionTime: undefined,
        memory: undefined,
        error: undefined,
        statusDescription: undefined,
        inQueue: undefined,
        statusId: undefined,
      });
    });
  });

  describe('Visibility and Layout', () => {
    beforeEach(() => {
      // Ensure component is initialized for these tests
      fixture.detectChanges();
    });

    it('should calculate correct button width based on status', () => {
      const testCase = createMockTestCase();

      // Mock passed status
      component.candidateSolutionResult = [
        createMockSolutionResult({ isCorrect: true, actualOutput: 'result' }),
      ];
      expect(component.getTestCaseButtonWidth(testCase)).toBe(150);

      // Mock failed status
      component.candidateSolutionResult = [
        createMockSolutionResult({ isCorrect: false, actualOutput: 'result' }),
      ];
      expect(component.getTestCaseButtonWidth(testCase)).toBe(150);

      // Mock not-run status
      component.candidateSolutionResult = [
        createMockSolutionResult({ actualOutput: '' }),
      ];
      expect(component.getTestCaseButtonWidth(testCase)).toBe(140);
    });

    it('should update visible test cases correctly', () => {
      component.testCases = Array.from({ length: 5 }, (_, i) =>
        createMockTestCase({ test_case_id: `test-${i}` })
      );
      component.maxVisibleItems = 3;
      component.startIndex = 1;

      component.updateVisibleTestCases();

      expect(component.visibleTestCases).toHaveLength(4); // maxVisibleItems + 1
      expect(component.visibleTestCases[0].test_case_id).toBe('test-1');
    });

    it('should handle edge case when near end of test cases', () => {
      component.testCases = Array.from({ length: 3 }, (_, i) =>
        createMockTestCase({ test_case_id: `test-${i}` })
      );
      component.maxVisibleItems = 5;
      component.startIndex = 0;

      component.updateVisibleTestCases();

      expect(component.visibleTestCases).toHaveLength(3); // All available
    });
  });

  describe('Lifecycle Management', () => {
    beforeEach(() => {
      // Ensure component is initialized for these tests
      fixture.detectChanges();
    });

    it('should clean up ResizeObserver on destroy', () => {
      const mockObserver = {
        disconnect: jest.fn(),
        observe: jest.fn(),
        unobserve: jest.fn(),
      };

      component['resizeObserver'] = mockObserver as ResizeObserver;

      component.ngOnDestroy();

      expect(mockObserver.disconnect).toHaveBeenCalled();
      expect(component['resizeObserver']).toBeNull();
    });

    it('should handle destroy when no ResizeObserver exists', () => {
      component['resizeObserver'] = null;

      expect(() => component.ngOnDestroy()).not.toThrow();
    });

    it('should remove test cases container element on destroy', () => {
      const mockElement = {
        remove: jest.fn(),
      };

      component.testCasesContainer = {
        nativeElement: mockElement,
      } as { nativeElement: { remove: () => void } };

      component.ngOnDestroy();

      expect(mockElement.remove).toHaveBeenCalled();
    });
  });

  describe('Solution Test Cases Emission', () => {
    beforeEach(() => {
      // Ensure component is initialized for these tests
      fixture.detectChanges();
    });

    it('should emit correct solution test cases format', () => {
      const mockTestCases = [
        createMockTestCase({
          test_case_id: 'case-1',
          input_data: 'input1',
          output_data: 'output1',
        }),
        createMockTestCase({
          test_case_id: 'case-2',
          input_data: 'input2',
          output_data: 'output2',
        }),
      ];

      component.testCases = mockTestCases;
      const emitSpy = jest.spyOn(component.solutionTestCases, 'emit');

      component.setSolutionTestCases();

      expect(emitSpy).toHaveBeenCalledWith([
        {
          test_case_id: 'case-1',
          input: 'input1',
          output: 'output1',
        },
        {
          test_case_id: 'case-2',
          input: 'input2',
          output: 'output2',
        },
      ]);
    });

    it('should emit empty array when no test cases exist', () => {
      component.testCases = [];
      const emitSpy = jest.spyOn(component.solutionTestCases, 'emit');

      component.setSolutionTestCases();

      expect(emitSpy).toHaveBeenCalledWith([]);
    });
  });
});
