<div class="flex flex-col flex-grow min-h-[300px] lg:min-h-0 bg-white">
  <div
    class="flex justify-between items-center w-full flex-shrink-0 bg-[#F5F8F9] border-b"
  >
    <div class="flex-grow overflow-hidden h-full overflow-x-auto">
      <div #testCasesContainer class="flex h-full w-full">
        <button
          *ngFor="let testCase of visibleTestCases; let i = index"
          class="flex items-center whitespace-nowrap h-[45px] cursor-pointer hover:bg-[#7b9fb4] hover:text-white disabled:text-[#082230] disabled:text-opacity-[10%] disabled:bg-[#F5F8F9] disabled:cursor-not-allowed gap-2 justify-center text-[#082230E5]"
          (click)="selectTestCase(startIndex + i)"
          [ngClass]="{
            'bg-[#ffffff]': selectedTestCaseIndex === startIndex + i,
            'text-[#082230E5] bg-[#0C47660A]':
              selectedTestCaseIndex !== startIndex + i,
          }"
          [disabled]="testCase.visibility === 'private'"
          [style.width.px]="getTestCaseButtonWidth(testCase)"
        >
          Test Case {{ startIndex + i + 1 }}
          @if (getTestCaseStatus(testCase) === 'passed') {
            <img
              src="../../../../assets/testTakingImages/test-passed.svg"
              alt="passed"
            />
          } @else if (getTestCaseStatus(testCase) === 'failed') {
            <img
              src="../../../../assets/testTakingImages/not-passed.svg"
              alt="wrong"
            />
          }
        </button>
      </div>
    </div>

    <div class="flex gap-2 p-2 flex-shrink-0">
      <button
        class="h-7 w-7 flex justify-center items-center bg-[#ffffff] text-[#082230E5] rounded cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[#3D6B85] hover:text-white"
        aria-label="Previous test cases"
        (click)="showPrevious()"
        [disabled]="isPreviousDisabled"
      >
        &lt;
      </button>
      <button
        class="h-7 w-7 flex justify-center items-center bg-[#ffffff] text-[#082230E5] rounded cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[#3D6B85] hover:text-white"
        aria-label="Next test cases"
        (click)="showNext()"
        [disabled]="isNextDisabled"
      >
        &gt;
      </button>
    </div>
  </div>

  <div class="flex-grow" style="min-height: 200px">
    @if (isTestCasesLoading) {
      <div class="flex justify-center items-center h-[25vh]">
        <app-dodokpo-loader></app-dodokpo-loader>
      </div>
    } @else {
      <app-test-case-result-view
        [activeTestCase]="selectedTestCase"
      ></app-test-case-result-view>
    }
  </div>
</div>
