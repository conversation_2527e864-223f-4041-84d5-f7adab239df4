import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  inject,
  Input,
  OnChanges,
  OnDestroy,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { TestCaseResultViewComponent } from './component/test-case-result-view/test-case-result-view.component';
import {
  CodeConstraint,
  CodeTestCases,
  SolutionResult,
  SolutionTestCases,
} from '../../../Interfaces/Types/testTakerInterface';
import { TestTakingService } from '../../../services/test-taking-service/test-taking.service';
import { ToastService } from '../../../services/toast-service/toast.service';
import { DodokpoLoaderComponent } from '../../../components/dodokpo-loader/dodokpo-loader.component';

@Component({
  selector: 'app-coding-test-cases',
  standalone: true,
  imports: [CommonModule, TestCaseResultViewComponent, DodokpoLoaderComponent],
  templateUrl: './coding-test-cases.component.html',
})
export class CodingTestCasesComponent
  implements OnChanges, AfterViewInit, OnDestroy
{
  @Input() codeConstraint!: CodeConstraint | undefined;
  @Input() candidateSolutionResult!: SolutionResult[];
  @Output() solutionTestCases = new EventEmitter<SolutionTestCases[]>();
  @ViewChild('testCasesContainer', { static: false })
  testCasesContainer!: ElementRef;
  testTakingService = inject(TestTakingService);
  toastService = inject(ToastService);
  testCases: CodeTestCases[] = [];
  visibleTestCases: CodeTestCases[] = [];
  startIndex = 0;
  maxVisibleItems = 0; // Max items to show at once
  selectedTestCaseIndex!: number;
  isTestCasesLoading = false;
  private resizeObserver: ResizeObserver | null = null;

  updateMaxVisibleItems(): void {
    if (this.testCasesContainer?.nativeElement) {
      const parentWidth = this.testCasesContainer.nativeElement.offsetWidth;
      const maxWidth = this.visibleTestCases.length
        ? Math.max(
            ...this.visibleTestCases.map((tc) =>
              this.getTestCaseButtonWidth(tc)
            )
          )
        : 140;
      this.maxVisibleItems = Math.max(1, Math.floor(parentWidth / maxWidth));
      this.updateVisibleTestCases();
    }
  }

  ngAfterViewInit(): void {
    this.updateMaxVisibleItems();
    if (this.testCasesContainer?.nativeElement) {
      this.resizeObserver = new ResizeObserver(() => {
        this.updateMaxVisibleItems();
        this.startIndex = 0;
      });
      this.resizeObserver.observe(this.testCasesContainer.nativeElement);
    }

    this.updateVisibleTestCases();
    if (this.testCases.length > 0) {
      this.selectTestCase(0);
    }
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['testCases']) {
      this.startIndex = 0;
      this.updateVisibleTestCases();
      if (this.testCases.length > 0 && this.selectedTestCaseIndex === null) {
        this.selectTestCase(0);
      } else if (this.testCases.length === 0) {
        this.selectedTestCaseIndex = 0;
      }
    }

    if (changes['codeConstraint'] && this.codeConstraint?.questionId) {
      this.testCases = [];
      this.visibleTestCases = [];
      this.selectedTestCaseIndex = 0;
      this.isTestCasesLoading = true;
      this.updateVisibleTestCases();
      this.getTestCases();
    }
  }

  getTestCases() {
    this.isTestCasesLoading = true;
    this.testTakingService
      .getQuestiontTestcases(this.codeConstraint?.questionId ?? '')
      .subscribe({
        next: (res) => {
          this.testCases = [...res];
          this.isTestCasesLoading = false;
          this.startIndex = 0;
          this.updateVisibleTestCases();

          if (this.testCases.length > 0) {
            const firstUnhiddenTestCase = this.testCases.find(
              (item) => item.visibility === 'public'
            );
            const index = firstUnhiddenTestCase
              ? this.testCases.indexOf(firstUnhiddenTestCase)
              : 0;
            this.selectTestCase(index);
          } else {
            this.selectedTestCaseIndex = 0;
          }
          this.setSolutionTestCases();
        },
        error: (err) => {
          this.isTestCasesLoading = false;
          this.toastService.onShow(err, err.message, true, 'error');
        },
      });
  }

  setSolutionTestCases() {
    const testCases: SolutionTestCases[] = this.testCases.map((testCase) => ({
      test_case_id: testCase.test_case_id,
      input: testCase.input_data,
      output: testCase.output_data,
    }));
    this.solutionTestCases.emit(testCases);
  }
  updateVisibleTestCases(): void {
    const numToShow = Math.min(
      this.maxVisibleItems + 1,
      this.testCases.length - this.startIndex
    );
    const endIndex = this.startIndex + numToShow;
    this.visibleTestCases = this.testCases.slice(this.startIndex, endIndex);
  }

  showNext(): void {
    const maxStartIndex = Math.max(
      0,
      this.testCases.length - this.maxVisibleItems
    );
    if (this.startIndex < maxStartIndex) {
      this.startIndex++;
      this.updateVisibleTestCases();
    }
  }

  showPrevious(): void {
    if (this.startIndex > 0) {
      this.startIndex--;
      this.updateVisibleTestCases();
    }
  }

  get isPreviousDisabled(): boolean {
    return this.startIndex === 0;
  }

  get isNextDisabled(): boolean {
    return (
      this.testCases.length <= this.maxVisibleItems ||
      this.startIndex + 1 >= this.testCases.length - this.maxVisibleItems
    );
  }

  selectTestCase(index: number): void {
    if (index >= 0 && index < this.testCases.length) {
      this.selectedTestCaseIndex = index;
    }
  }

  get selectedTestCase() {
    if (
      this.selectedTestCaseIndex !== null &&
      this.testCases[this.selectedTestCaseIndex]
    ) {
      const testCase = this.testCases[this.selectedTestCaseIndex];
      const solution = this.candidateSolutionResult?.find(
        (item) => item.test_case_id === testCase.test_case_id
      );
      return {
        test_case_id: testCase.test_case_id,
        question_id: testCase.question_id,
        input: testCase.input_data,
        output: testCase.output_data,
        actualOutput: solution?.actualOutput ?? '',
        consoleOutput: solution?.consoleOutput ?? '',
        isCorrect: solution?.isCorrect ?? false,
        executionTime: solution?.executionTime,
        memory: solution?.memory,
        error: solution?.error,
        statusDescription: solution?.statusDescription,
        inQueue: solution?.inQueue,
        statusId: solution?.statusId,
      } as SolutionResult;
    }
    return null;
  }

  ngOnDestroy(): void {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
      this.resizeObserver = null;
    }
    if (this.testCasesContainer?.nativeElement) {
      this.testCasesContainer.nativeElement.remove();
    }
  }

  getTestCaseStatus(testCase: CodeTestCases) {
    const result = this.candidateSolutionResult?.find(
      (r) => r.test_case_id === testCase.test_case_id
    );

    if (result?.actualOutput === null) {
      return 'failed';
    }
    if (!result?.actualOutput || result.actualOutput === '') {
      return 'not-run';
    }
    return result.isCorrect ? 'passed' : 'failed';
  }

  getTestCaseButtonWidth(testCase: CodeTestCases): number {
    const status = this.getTestCaseStatus(testCase);
    return status === 'passed' || status === 'failed' ? 150 : 140;
  }
}
