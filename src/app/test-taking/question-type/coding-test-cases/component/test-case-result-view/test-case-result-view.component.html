<div class="flex flex-col gap-3 px-3 pt-5 pb-5 w-full">
  <div class="flex flex-col gap-1">
    <label for="input" class="text-sm font-medium text-gray-700">Input</label>
    <p
      class="w-full min-h-10 items-center flex bg-gray-50 border border-gray-200 rounded-md px-3 py-2 text-gray-800 break-words text-sm"
    >
      {{ activeTestCase?.input || 'No input data' }}
    </p>
  </div>
  <div class="flex flex-col gap-1">
    <label for="input" class="text-sm font-medium text-gray-700"
      >Expected Output</label
    >
    <p
      class="w-full min-h-10 items-center flex bg-gray-50 border border-gray-200 rounded-md px-3 py-2 text-gray-800 break-words text-sm"
    >
      {{ activeTestCase?.output || 'No expected output' }}
    </p>
  </div>
  <div class="flex flex-col gap-1">
    <label for="input" class="text-sm font-medium text-gray-700"
      >Console Output</label
    >
    <div
      class="w-full min-h-10 bg-gray-50 border border-gray-200 rounded-md px-3 py-2 text-gray-800 break-words text-sm overflow-auto"
    >
      <pre class="whitespace-pre-wrap font-sans">{{
        activeTestCase?.consoleOutput || 'No console output'
      }}</pre>
    </div>
  </div>
  <div class="flex flex-col gap-1 flex-grow min-h-0">
    <label for="input" class="text-sm font-medium text-gray-700"
      >Your Output</label
    >
    <div
      class="bg-gray-50 border border-gray-200 rounded-md overflow-y-auto p-3 flex-grow min-h-16"
      [ngClass]="{
        'bg-green-50 border-green-300': testCaseStatus === 'correct',
        'bg-red-50 border-red-300': testCaseStatus === 'wrong',
      }"
    >
      <p
        [ngClass]="{
          'text-green-700 font-semibold': testCaseStatus === 'correct',
          'text-red-700 font-semibold': testCaseStatus === 'wrong',
          'text-gray-600': testCaseStatus === 'none',
        }"
        class="break-words text-sm leading-relaxed whitespace-pre-wrap"
      >
        @if (activeTestCase && activeTestCase.error) {
          {{ activeTestCase.error }}
        } @else if (activeTestCase && activeTestCase.actualOutput) {
          {{ activeTestCase.actualOutput }}
        } @else {
          <span class="text-gray-500 italic"
            >No output yet - Run your code to see results</span
          >
        }
      </p>
    </div>
  </div>
</div>
