import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SolutionResult } from '../../../../../Interfaces/Types/testTakerInterface';

@Component({
  selector: 'app-test-case-result-view',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './test-case-result-view.component.html',
})
export class TestCaseResultViewComponent {
  @Input() activeTestCase!: SolutionResult | null;

  get testCaseStatus() {
    if (this.activeTestCase?.actualOutput || this.activeTestCase?.error) {
      return this.activeTestCase?.isCorrect ? 'correct' : 'wrong';
    }
    return 'none';
  }
}
