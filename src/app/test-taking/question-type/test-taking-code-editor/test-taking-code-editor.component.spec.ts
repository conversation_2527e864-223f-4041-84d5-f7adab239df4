import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import {
  SimpleChanges,
  Component,
  Input,
  Output,
  EventEmitter,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { By } from '@angular/platform-browser';
import { of, throwError } from 'rxjs';

import { TestTakingCodeEditorComponent } from './test-taking-code-editor.component';
import { TestTakingService } from '../../../services/test-taking-service/test-taking.service';
import { ToastService } from '../../../services/toast-service/toast.service';
import { CodeEditorComponent } from '../../../components/code-editor/code-editor.component';
import { CustomDropdownComponent } from '../../../components/custom-dropdown/custom-dropdown.component';
import { CustomMiniModalComponent } from '../../../components/custom-mini-modal/custom-mini-modal.component';

import {
  CodeConstraint,
  CodeTemplate,
  CodeLanguage,
  SolutionTestCases,
  RunSolutionResponse,
  CodingAnswer,
} from '../../../Interfaces/Types/testTakerInterface';

// Mock Components
@Component({
  selector: 'app-code-editor',
  template: '<div></div>',
  standalone: true,
})
class MockCodeEditorComponent {
  @Input() language: string = 'javascript';
  @Output() codeChange = new EventEmitter<string>();
  setValue = jest.fn();
  getValue = jest.fn().mockReturnValue('');
}

@Component({
  selector: 'app-custom-dropdown',
  template: '<select></select>',
  standalone: true,
})
class MockCustomDropdownComponent {
  @Input() placeholder: string = '';
  @Input() options: unknown[] = [];
  @Input() displayKey: string = '';
  @Input() formControl: FormControl = new FormControl('');
  @Output() selectionChange = new EventEmitter<unknown>();
}

@Component({
  selector: 'app-custom-mini-modal',
  template: '<div></div>',
  standalone: true,
})
class MockCustomMiniModalComponent {
  @Input() headerTitle: string = '';
  @Input() textAlign: string = '';
  @Input() right: string = '';
  @Input() left: string = '';
  @Input() visibleModal: boolean = false;
  @Input() bodyText: string = '';
  @Input() width: string = '';
  @Input() height: string = '';
  @Input() caution: boolean = false;
  @Input() closeButton: boolean = false;
  @Output() rightClickEvent = new EventEmitter<void>();
  @Output() leftClickEvent = new EventEmitter<void>();
  @Output() visibleModalChange = new EventEmitter<boolean>();
}

// Test Data Factory
class TestDataFactory {
  static createCodeTemplate(
    lang: 'JavaScript' | 'Python' = 'JavaScript'
  ): CodeTemplate[] {
    if (lang === 'Python') {
      return [
        {
          id: '2',
          codeType: 'template',
          body: 'def solution():\n    return "Hello Python"',
          questionId: 'test-question-1',
          languageId: '71',
          Language: { id: '71', name: 'Python', judgeLanguageId: 71 },
        },
      ];
    }
    return [
      {
        id: '1',
        codeType: 'template',
        body: '// Default JavaScript code\nfunction solution() {\n  return "Hello World";\n}',
        questionId: 'test-question-1',
        languageId: '63',
        Language: { id: '63', name: 'JavaScript', judgeLanguageId: 63 },
      },
    ];
  }

  static createCodeConstraint(): CodeConstraint {
    return {
      timeLimit: 2000,
      memoryLimit: 128000,
      timeComplexity: 'O(n)',
      spaceComplexity: 'O(1)',
    };
  }

  static createSolutionTestCases(): SolutionTestCases[] {
    return [
      { test_case_id: '1', input: 'test input 1', output: 'expected output 1' },
    ];
  }

  static createCodeLanguage(
    overrides: Partial<CodeLanguage> = {}
  ): CodeLanguage {
    return { id: '63', name: 'JavaScript', judgeLanguageId: 63, ...overrides };
  }

  static createRunSolutionResponse(): RunSolutionResponse {
    return {
      success: true,
      data: {
        summary: {
          totalTests: 1,
          passedTests: 1,
          failedTests: 0,
          inQueueTests: 0,
          allPassed: true,
          stillProcessing: false,
        },
        results: [
          {
            test_case_id: '1',
            input: 'test input',
            output: 'expected output',
            actualOutput: 'actual output',
            consoleOutput: 'console output',
            isCorrect: true,
            memory: 1024,
            statusDescription: 'Accepted',
            error: null,
            statusId: 3,
            executionTime: '0.05',
            inQueue: false,
          },
        ],
      },
    };
  }

  static createCodingAnswer(): CodingAnswer {
    return { code: 'function solution() { return "test"; }', languageId: 63 };
  }

  static createCandidateEssayAnswer(): {
    questionId: string;
    questionType: string;
    idleTime: number;
    testTakerAnswers: CodingAnswer[];
  }[] {
    return [
      {
        questionId: 'test-question-1',
        questionType: 'coding',
        idleTime: 0,
        testTakerAnswers: [this.createCodingAnswer()],
      },
    ];
  }
}

describe('TestTakingCodeEditorComponent', () => {
  let component: TestTakingCodeEditorComponent;
  let fixture: ComponentFixture<TestTakingCodeEditorComponent>;
  let mockTestTakingService: jest.Mocked<TestTakingService>;
  let mockToastService: jest.Mocked<ToastService>;
  let mockCodeEditor: MockCodeEditorComponent;
  let checkForSavedSolutionSpy: jest.SpyInstance;

  beforeEach(async () => {
    mockTestTakingService = {
      checkSolutionCode: jest.fn(),
      selectedAnswerChoices: [],
      getCandidateEssayAnswers: jest.fn().mockReturnValue([]),
    } as unknown as jest.Mocked<TestTakingService>;

    mockToastService = {
      onShow: jest.fn(),
    } as unknown as jest.Mocked<ToastService>;

    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn(),
        setItem: jest.fn(),
        removeItem: jest.fn(),
        clear: jest.fn(),
      },
      writable: true,
      configurable: true,
    });

    await TestBed.configureTestingModule({
      imports: [
        TestTakingCodeEditorComponent,
        ReactiveFormsModule,
        CommonModule,
      ],
      providers: [
        { provide: TestTakingService, useValue: mockTestTakingService },
        { provide: ToastService, useValue: mockToastService },
      ],
    })
      .overrideComponent(TestTakingCodeEditorComponent, {
        remove: {
          imports: [
            CodeEditorComponent,
            CustomDropdownComponent,
            CustomMiniModalComponent,
          ],
        },
        add: {
          imports: [
            MockCodeEditorComponent,
            MockCustomDropdownComponent,
            MockCustomMiniModalComponent,
          ],
        },
      })
      .compileComponents();

    fixture = TestBed.createComponent(TestTakingCodeEditorComponent);
    component = fixture.componentInstance;

    // Setup default properties before detectChanges
    component.questionId = 'test-question-1';
    component.codeTemplate = TestDataFactory.createCodeTemplate();
    component.codeConstraint = TestDataFactory.createCodeConstraint();
    component.solutionTestCases = TestDataFactory.createSolutionTestCases();

    // Mock the checkForSavedSolution method to prevent it from accessing codeEditor during setup
    checkForSavedSolutionSpy = jest
      .spyOn(component, 'checkForSavedSolution')
      .mockImplementation(() => {});

    fixture.detectChanges(); // This triggers ngOnInit, ngOnChanges, and ngAfterViewInit

    // Now get the mock component after render and set it properly
    const codeEditorDebugElement = fixture.debugElement.query(
      By.directive(MockCodeEditorComponent)
    );
    mockCodeEditor = codeEditorDebugElement.componentInstance;
    component.codeEditor = mockCodeEditor as unknown as CodeEditorComponent;
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Component Initialization', () => {
    it('should create component successfully', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default properties and setup from ngOnInit', () => {
      expect(component.isEditorFullScreen).toBe(false);
      expect(component.isCodeRunning).toBe(false);
      expect(component.resetModal).toBe(false);
      expect(component.latestEditorCode).toBe('');

      // Test state after ngOnInit is called by detectChanges()
      expect(component.editorInitialLanguageSyntax).toBe('javascript');
      expect(component.codeEditorLanguage.value).toBe('JavaScript');
    });

    it('should initialize language options from CODING_LANGUAGE_CONFIG', () => {
      expect(component.languageOptions).toBeDefined();
      expect(component.languageOptions.length).toBeGreaterThan(0);
      expect(component.languageOptions[0]).toHaveProperty('name');
      expect(component.languageOptions[0]).toHaveProperty('judgeLanguageId');
    });
  });

  describe('ngAfterViewInit', () => {
    it('should check for saved solutions after view init', () => {
      // Reset the mock to clear previous calls from setup
      checkForSavedSolutionSpy.mockClear();

      component.ngAfterViewInit();
      expect(checkForSavedSolutionSpy).toHaveBeenCalled();
    });
  });

  describe('ngOnChanges', () => {
    it('should check for saved solution when questionId changes', () => {
      // Reset the mock to clear previous calls from setup
      checkForSavedSolutionSpy.mockClear();

      const changes: SimpleChanges = {
        questionId: {
          currentValue: 'new-question',
          previousValue: 'old-question',
          firstChange: false,
          isFirstChange: () => false,
        },
      };
      component.ngOnChanges(changes);
      expect(checkForSavedSolutionSpy).toHaveBeenCalled();
    });

    it('should not check for saved solution on first change', () => {
      // Reset the mock to clear previous calls from setup
      checkForSavedSolutionSpy.mockClear();

      const changes: SimpleChanges = {
        questionId: {
          currentValue: 'new-question',
          previousValue: undefined,
          firstChange: true,
          isFirstChange: () => true,
        },
      };
      component.ngOnChanges(changes);
      expect(checkForSavedSolutionSpy).not.toHaveBeenCalled();
    });
  });

  describe('Code and Language Management', () => {
    it('should load boilerplate code and update editor', fakeAsync(() => {
      const onCodeChangedSpy = jest.spyOn(component, 'onCodeChanged');
      component.loadBoilerPlateCode();
      tick(1); // Wait for setTimeout

      const expectedCode = TestDataFactory.createCodeTemplate()[0].body;
      expect(mockCodeEditor.setValue).toHaveBeenCalledWith(expectedCode);
      expect(component.latestEditorCode).toBe(expectedCode);
      expect(onCodeChangedSpy).toHaveBeenCalledWith(expectedCode);
    }));

    it('should select a language and update editor syntax', () => {
      const pythonLang = TestDataFactory.createCodeLanguage({
        id: '71',
        name: 'Python',
        judgeLanguageId: 71,
      });
      component.selectLanguage(pythonLang);

      expect(component.selectedLanguage).toEqual(pythonLang);
      expect(component.editorInitialLanguageSyntax).toBe('python');
      expect(component.codeEditorLanguage.value).toBe('Python');
    });

    it('should update latest code and save to service and localStorage on code change', () => {
      const testCode = 'console.log("hello");';
      component.onCodeChanged(testCode);

      expect(component.latestEditorCode).toBe(testCode);
      const expectedAnswer: CodingAnswer = { code: testCode, languageId: 63 };
      expect(mockTestTakingService.selectedAnswerChoices).toEqual([
        expectedAnswer,
      ]);

      // Check that localStorage.setItem was called with the correct key
      expect(localStorage.setItem).toHaveBeenCalledWith(
        `code_backup_${component.questionId}`,
        expect.any(String)
      );

      // Verify that the stored value is valid JSON containing the expected data
      const setItemCall = (localStorage.setItem as jest.Mock).mock.calls[0];
      const storedValue = JSON.parse(setItemCall[1]);
      expect(storedValue).toEqual({
        code: testCode,
        languageId: 63,
        timestamp: expect.any(Number),
      });
    });
  });

  describe('Saved Solution Handling', () => {
    beforeEach(() => {
      // Restore the original implementation for these tests
      checkForSavedSolutionSpy.mockRestore();
    });

    it('should restore from service if available', fakeAsync(() => {
      const serviceAnswers = TestDataFactory.createCandidateEssayAnswer();
      mockTestTakingService.getCandidateEssayAnswers.mockReturnValue(
        serviceAnswers
      );
      const restoreSpy = jest.spyOn(component, 'restoreFromService');

      component.checkForSavedSolution();
      tick(1);

      expect(restoreSpy).toHaveBeenCalledWith(
        serviceAnswers[0].testTakerAnswers[0]
      );
    }));

    it('should restore from localStorage if service is empty', fakeAsync(() => {
      const backupData = {
        code: 'backup code',
        languageId: 71,
        timestamp: Date.now(),
      };
      (localStorage.getItem as jest.Mock).mockReturnValue(
        JSON.stringify(backupData)
      );
      const restoreSpy = jest.spyOn(component, 'restoreFromBackup');

      component.checkForSavedSolution();
      tick(1);

      expect(restoreSpy).toHaveBeenCalled();
      expect(mockCodeEditor.setValue).toHaveBeenCalledWith(backupData.code);
    }));
  });

  describe('Solution Execution', () => {
    beforeEach(() => {
      component.latestEditorCode = 'function solution() { return "test"; }';
      fixture.detectChanges();
    });

    it('should run solution successfully and process results', () => {
      const mockResponse = TestDataFactory.createRunSolutionResponse();
      mockTestTakingService.checkSolutionCode.mockReturnValue(of(mockResponse));
      const resultSpy = jest.spyOn(component, 'resultFromRunSolution');
      const emitSpy = jest.spyOn(component.codeRunResult, 'emit');

      component.runSolution();

      expect(component.isCodeRunning).toBe(false); // It's sync now with `of`
      expect(mockTestTakingService.checkSolutionCode).toHaveBeenCalled();
      expect(component.resultResponse).toEqual(mockResponse);
      expect(resultSpy).toHaveBeenCalled();
      expect(emitSpy).toHaveBeenCalled();
    });

    it('should handle solution execution error', () => {
      const mockError = { message: 'Execution failed' };
      mockTestTakingService.checkSolutionCode.mockReturnValue(
        throwError(() => mockError)
      );

      component.runSolution();

      expect(mockToastService.onShow).toHaveBeenCalledWith(
        'Error',
        mockError,
        true,
        'error'
      );
      expect(component.isCodeRunning).toBe(false);
    });

    it('should show toast if language is not selected', () => {
      component.selectedLanguage = { name: 'Select Language' };
      component.runSolution();
      expect(mockToastService.onShow).toHaveBeenCalledWith(
        'Error',
        'Please select a language and test cases to run the solution.',
        true,
        'error'
      );
    });
  });

  describe('UI Interactions', () => {
    it('should toggle fullscreen mode and emit scale changes', () => {
      const emitSpy = jest.spyOn(component.fullScreenScale, 'emit');

      component.toggleFullscreen(); // Enter fullscreen
      expect(component.isEditorFullScreen).toBe(true);
      expect(emitSpy).toHaveBeenCalledWith({ scale: 100, isFullScreen: true });

      component.toggleFullscreen(); // Exit fullscreen
      expect(component.isEditorFullScreen).toBe(false);
      expect(emitSpy).toHaveBeenCalledWith({ scale: 50, isFullScreen: false });
    });

    it('should show reset modal on button click', () => {
      expect(component.resetModal).toBe(false);
      component.resetButtonOnClick();
      expect(component.resetModal).toBe(true);
    });

    it('should reset editor when confirmed', () => {
      const resetSpy = jest.spyOn(component, 'loadBoilerPlateCode');
      component.resetModal = true;

      component.resetEditor();

      expect(resetSpy).toHaveBeenCalled();
      expect(component.resetModal).toBe(false);
    });
  });
});
