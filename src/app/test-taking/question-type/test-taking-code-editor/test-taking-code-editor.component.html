<div class="h-full flex flex-col relative">
  <!-- Top Section: Dropdown + Reset (Fixed Height) -->
  <div class="flex ml-auto h-fit items-center mb-1 flex-shrink-0">
    <app-custom-dropdown
      [placeholder]="'Select Language'"
      [options]="languageOptions"
      [displayKey]="'name'"
      (selectionChange)="selectLanguage($event)"
      [formControl]="codeEditorLanguage"
    ></app-custom-dropdown>
    <button
      aria-label="Reset"
      class="bg-[#EFEFEF] hover:bg-slate-300 w-fit rounded-md h-fit py-1 px-3 ml-2"
      (click)="resetButtonOnClick()"
    >
      <img src="../../../../assets/testTakingImages/reset.svg" alt="reset" />
    </button>
    <button
      class="ml-2 px-2 py-1 rounded bg-gray-200 hover:bg-gray-300"
      (click)="toggleFullscreen()"
    >
      <img src="../../../../assets/testTakingImages/expand.svg" alt="expand" />
    </button>
  </div>

  <!-- Middle Section: Code Editor (Flexible Height) -->
  <div
    class="flex-1 min-h-0 z-index-0"
    style="height: calc(100% - 60px); margin-bottom: 60px"
  >
    <app-code-editor
      #codeEditor
      [language]="editorInitialLanguageSyntax"
      (codeChange)="onCodeChanged($event)"
      style="height: 100%; min-height: 200px"
    ></app-code-editor>
  </div>

  <!-- Bottom Section: Run Test Button (Fixed Height) -->
  <div
    class="mt-2 w-full flex-shrink-0 flex justify-end z-10 absolute bottom-2 rounded-md"
  >
    <button
      class="bg-[#04952C] hover:bg-[#04952CCC] text-white w-[124px] rounded-lg py-2 px-3 flex justify-center gap-2 items-center"
      [ngClass]="isEditorFullScreen ? 'bg-background-acs' : 'bg-primary-acs'"
      (click)="runSolution()"
    >
      @if (isCodeRunning) {
        <span class="flex items-center justify-center w-5 h-5">
          <i
            class="pi pi-spinner animate-spin mr-2"
            style="font-size: 1.25rem"
          ></i>
        </span>
      } @else {
        Run Test
        <img src="../../../../assets/testTakingImages/run.svg" alt="run" />
      }
    </button>
  </div>

  <app-custom-mini-modal
    [headerTitle]="'Are you sure you want to reset your code?'"
    [textAlign]="'center'"
    [right]="'Yes, Reset'"
    [left]="'Cancel'"
    [visibleModal]="resetModal"
    [bodyText]="
      'This action will permanently remove all the changes you\'ve made in the editor and revert it to its original state. This cannot be undone. Please confirm if you want to proceed'
    "
    [width]="'556px'"
    [height]="'288px'"
    (rightClickEvent)="resetEditor()"
    [caution]="true"
    [closeButton]="true"
    (leftClickEvent)="resetModal = false"
    (visibleModalChange)="resetModal = false"
  >
  </app-custom-mini-modal>
</div>
