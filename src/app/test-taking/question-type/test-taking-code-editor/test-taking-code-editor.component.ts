import {
  AfterViewInit,
  Component,
  EventEmitter,
  HostListener,
  inject,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { CustomDropdownComponent } from '../../../components/custom-dropdown/custom-dropdown.component';
import { CodeEditorComponent } from '../../../components/code-editor/code-editor.component';
import { CommonModule } from '@angular/common';
import { FormControl } from '@angular/forms';
import {
  CodeConstraint,
  CodeLanguage,
  CodeTemplate,
  CodingAnswer,
  RunSolution,
  RunSolutionResponse,
  SolutionResult,
  SolutionTestCases,
} from '../../../Interfaces/Types/testTakerInterface';
import { TestTakingService } from '../../../services/test-taking-service/test-taking.service';
import { ToastService } from '../../../services/toast-service/toast.service';
import { CODING_LANGUAGE_CONFIG } from '../../../utils/codingTypeUtils';
import { CustomMiniModalComponent } from '../../../components/custom-mini-modal/custom-mini-modal.component';

@Component({
  selector: 'app-test-taking-code-editor',
  standalone: true,
  imports: [
    CustomDropdownComponent,
    CodeEditorComponent,
    CommonModule,
    CustomMiniModalComponent,
  ],
  templateUrl: './test-taking-code-editor.component.html',
})
export class TestTakingCodeEditorComponent
  implements AfterViewInit, OnInit, OnChanges
{
  codeTypeAnswer = new FormControl('');
  @Input() questionId: string = '';
  @Output() emitOptions = new EventEmitter<string[] | CodingAnswer[]>();
  @Input() codeTemplate!: CodeTemplate[] | undefined;
  @Input() solutionTestCases!: SolutionTestCases[];
  @ViewChild(CodeEditorComponent) codeEditor!: CodeEditorComponent;
  @Output() codeRunResult = new EventEmitter<SolutionResult[]>();
  @Input() codeConstraint!: CodeConstraint | undefined;
  @Output() fullScreenScale = new EventEmitter<{
    scale: number;
    isFullScreen: boolean;
  }>();
  isEditorFullScreen: boolean = false;
  isCodeRunning: boolean = false;
  resetModal: boolean = false;
  editorInitialLanguageSyntax: string = '';
  codeEditorLanguage = new FormControl('');
  /*   languageOptions: CodeLanguage[] = []; */
  languageOptions = CODING_LANGUAGE_CONFIG.map((lang) => ({
    id: String(lang.judge0Id),
    name: lang.languageName,
    judgeLanguageId: lang.judge0Id,
  }));

  selectedLanguage:
    | CodeLanguage
    | {
        id?: string;
        name?: string;
        judgeLanguageId?: number;
      } = {
    name: 'Select Language',
    judgeLanguageId: undefined,
    id: undefined,
  };
  testTakingService = inject(TestTakingService);
  toastService = inject(ToastService);
  latestEditorCode = '';
  resultResponse!: RunSolutionResponse;
  ngOnInit(): void {
    this.boilerPlateCodeLanguageSyntax();
  }

  ngAfterViewInit() {
    this.checkForSavedSolution();

    // Handle resize for mobile devices
    if (window.innerWidth < 1024) {
      setTimeout(() => {
        // Force Monaco editor to resize properly on mobile
        const editorElement = document.querySelector(
          '.monaco-editor'
        ) as HTMLElement;
        if (editorElement) {
          editorElement.style.minHeight = '300px';
          editorElement.style.height = '300px';

          // Trigger resize event for Monaco editor
          window.dispatchEvent(new Event('resize'));
        }
      }, 100);
    }
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['questionId'] && !changes['questionId'].firstChange) {
      this.checkForSavedSolution();
    }
  }

  loadBoilerPlateCode() {
    if (this.codeTemplate?.[0]) {
      // First clear the editor to ensure change is detected
      this.codeEditor.setValue('');

      // Force reapply boilerplate even if it's the same
      setTimeout(() => {
        this.codeEditor.setValue(this.codeTemplate![0].body);
        this.latestEditorCode = this.codeTemplate![0].body;
        this.onCodeChanged(this.latestEditorCode);
      });
    }
  }

  boilerPlateCodeLanguageSyntax() {
    // Find the language in CODING_LANGUAGE_CONFIG by id or name
    const templateLang = this.codeTemplate?.[0]?.Language;
    if (templateLang) {
      const foundLang = this.languageOptions.find(
        (lang) =>
          lang.judgeLanguageId === templateLang.judgeLanguageId ||
          lang.name === templateLang.name
      );
      if (foundLang) {
        this.editorInitialLanguageSyntax = foundLang.name.toLowerCase();
        this.codeEditorLanguage.setValue(foundLang.name);
        this.selectedLanguage = {
          id: String(foundLang.judgeLanguageId),
          name: foundLang.name,
          judgeLanguageId: foundLang.judgeLanguageId,
        };
      }
    }
  }

  selectLanguage(language: CodeLanguage) {
    this.selectedLanguage = language;
    this.editorInitialLanguageSyntax = language.name.toLowerCase();
    this.codeEditorLanguage.setValue(language.name);
  }
  onCodeChanged(code: string) {
    this.latestEditorCode = code;
    const candidateCodeAnswer = {
      code: this.latestEditorCode,
      languageId: this.selectedLanguage.judgeLanguageId as number,
    };
    this.testTakingService.selectedAnswerChoices = this.latestEditorCode
      ? [candidateCodeAnswer]
      : [];

    // Save the current answer to localStorage as a backup
    if (this.questionId && this.latestEditorCode) {
      try {
        // Get existing backup if any
        const backupKey = `code_backup_${this.questionId}`;
        localStorage.setItem(
          backupKey,
          JSON.stringify({
            code: this.latestEditorCode,
            languageId: this.selectedLanguage.judgeLanguageId,
            timestamp: new Date().getTime(),
          })
        );
      } catch (e) {
        // Silent fail if localStorage is not available
      }
    }

    this.emitOptions.emit(this.testTakingService.selectedAnswerChoices);
  }

  resetEditor() {
    this.loadBoilerPlateCode();
    this.resetModal = false;
  }

  checkForSavedSolution() {
    // First check for existing answers in the service
    const existingAnswers =
      this.testTakingService.getCandidateEssayAnswers() || [];
    const existingAnswer = existingAnswers.find(
      (question) => question.questionId === this.questionId
    );

    // Then check localStorage backup
    let localBackup: {
      code: string;
      languageId: number;
      timestamp: number;
    } | null = null;
    try {
      const backupKey = `code_backup_${this.questionId}`;
      const backupStr = localStorage.getItem(backupKey);
      if (backupStr) {
        localBackup = JSON.parse(backupStr) as {
          code: string;
          languageId: number;
          timestamp: number;
        };
      }
    } catch (e) {
      // Silent fail if localStorage is not available
    }

    if (existingAnswer?.testTakerAnswers?.length) {
      const codeAnswer = existingAnswer.testTakerAnswers[0] as CodingAnswer;

      // If we have both service data and local backup, use the most recent one
      if (localBackup && localBackup.timestamp) {
        // Use local backup if it's more recent
        this.restoreFromBackup(codeAnswer, localBackup);
      } else {
        // Use service data
        this.restoreFromService(codeAnswer);
      }
    } else if (localBackup) {
      // Use local backup if no service data exists
      const codeAnswer = {
        code: localBackup.code,
        languageId: localBackup.languageId,
      };
      this.restoreFromBackup(codeAnswer, localBackup);
    } else {
      // No saved data, use boilerplate
      this.codeEditor.setValue('');
      setTimeout(() => {
        this.loadBoilerPlateCode();
      });
    }
  }

  restoreFromService(codeAnswer: CodingAnswer) {
    this.codeEditor.setValue('');
    setTimeout(() => {
      this.codeEditor.setValue(codeAnswer.code);
      this.latestEditorCode = codeAnswer.code;
      const foundLang = this.languageOptions.find(
        (language) => language.judgeLanguageId === codeAnswer.languageId
      );
      if (foundLang) {
        this.selectedLanguage = {
          id: String(foundLang.judgeLanguageId),
          name: foundLang.name,
          judgeLanguageId: foundLang.judgeLanguageId,
        };
      }
      this.onCodeChanged(this.latestEditorCode);
    });
  }

  restoreFromBackup(
    codeAnswer: CodingAnswer,
    backup: { code: string; languageId: number; timestamp: number }
  ) {
    this.codeEditor.setValue('');
    setTimeout(() => {
      this.codeEditor.setValue(backup.code);
      this.latestEditorCode = backup.code;
      const foundLang = this.languageOptions.find(
        (language) => language.judgeLanguageId === backup.languageId
      );
      if (foundLang) {
        this.selectedLanguage = {
          id: String(foundLang.judgeLanguageId),
          name: foundLang.name,
          judgeLanguageId: foundLang.judgeLanguageId,
        };
      }
      this.onCodeChanged(this.latestEditorCode);
    });
  }

  runSolution() {
    // Save the current state to prevent data loss
    const savedCode = this.latestEditorCode;

    this.isCodeRunning = true;
    if (
      this.solutionTestCases &&
      this.selectedLanguage.judgeLanguageId !== undefined
    ) {
      const solutionResult: RunSolution = {
        code: this.latestEditorCode,
        language_id: this.selectedLanguage.judgeLanguageId,
        testCases: this.solutionTestCases,

        codeConstraint: {
          timeLimit: this.codeConstraint?.timeLimit as number,
          memoryLimit: this.codeConstraint?.memoryLimit as number,
          timeComplexity: this.codeConstraint?.timeComplexity as string,
          spaceComplexity: this.codeConstraint?.spaceComplexity as string,
        },
      };
      this.testTakingService
        .checkSolutionCode(solutionResult, this.questionId)
        .subscribe({
          next: (response: RunSolutionResponse) => {
            this.resultResponse = response;
            this.resultFromRunSolution();
            this.isCodeRunning = false;

            // Exit fullscreen mode if active, but restore the state afterward
            if (this.isEditorFullScreen) {
              this.exitFullScreen();

              // Ensure we maintain editor content after fullscreen exit
              setTimeout(() => {
                if (this.latestEditorCode !== savedCode) {
                  this.codeEditor.setValue(savedCode);
                  this.onCodeChanged(savedCode);
                }
              }, 100);
            }
          },
          error: (error) => {
            this.toastService.onShow('Error', error, true, 'error');
            this.isCodeRunning = false;

            // Restore state if needed
            if (this.latestEditorCode !== savedCode) {
              this.codeEditor.setValue(savedCode);
              this.onCodeChanged(savedCode);
            }
          },
        });
    } else {
      this.toastService.onShow(
        'Error',
        'Please select a language and test cases to run the solution.',
        true,
        'error'
      );
      this.isCodeRunning = false;
    }
  }

  exitFullScreen() {
    this.fullScreenScale.emit({
      scale: 40,
      isFullScreen: false,
    });
    this.isEditorFullScreen = false;
  }
  resultFromRunSolution() {
    const resultArray = this.resultResponse.data.results;
    const result = resultArray.map(
      (testCase) =>
        ({
          test_case_id: testCase.test_case_id,
          input: testCase.input,
          output: testCase.output,
          actualOutput: testCase.actualOutput,
          consoleOutput: testCase.consoleOutput,
          isCorrect: testCase.isCorrect,
          memory: testCase.memory,
          statusDescription: testCase.statusDescription,
          error: testCase.error,
          statusId: testCase.statusId,
          executionTime: testCase.executionTime,
          inQueue: testCase.inQueue,
        }) as SolutionResult
    );
    this.codeRunResult.emit(result);
  }

  toggleFullscreen() {
    this.isEditorFullScreen = !this.isEditorFullScreen;
    this.fullScreenScale.emit({
      scale: this.isEditorFullScreen ? 100 : 50,
      isFullScreen: this.isEditorFullScreen,
    });

    // Force resize after toggling fullscreen
    setTimeout(() => {
      window.dispatchEvent(new Event('resize'));
    }, 100);
  }

  @HostListener('window:resize')
  onResize() {
    // Make sure the editor is always properly sized
    setTimeout(() => {
      const isMobile = window.innerWidth < 1024;
      const editorElement = document.querySelector(
        '.monaco-editor'
      ) as HTMLElement;

      if (editorElement && isMobile) {
        editorElement.style.minHeight = '300px';
        editorElement.style.height = '300px';
      }
    }, 100);
  }

  resetButtonOnClick() {
    this.resetModal = true;
  }
}
