import { TimeAgoPipe } from './time-ago.pipe';

describe('TimeAgoPipe', () => {
  let pipe: TimeAgoPipe;

  beforeEach(() => {
    pipe = new TimeAgoPipe();
  });

  it('create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it('should return empty string for empty input', () => {
    expect(pipe.transform('')).toBe('');
  });

  it('should handle valid date strings', () => {
    const testDate = '2024-01-01T00:00:00Z';
    const result = pipe.transform(testDate);

    expect(result).toBeDefined();
    expect(typeof result).toBe('string');
    expect(result.length).toBeGreaterThan(0);
  });

  it('should handle invalid date strings gracefully', () => {
    const result = pipe.transform('invalid-date');
    expect(result).toBeDefined();
    expect(typeof result).toBe('string');
  });

  it('should return consistent results for same input', () => {
    const testDate = '2024-01-01T00:00:00Z';
    const result1 = pipe.transform(testDate);
    const result2 = pipe.transform(testDate);

    expect(result1).toBe(result2);
  });
});
