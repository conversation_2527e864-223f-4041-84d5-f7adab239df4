import { Pipe, PipeTransform } from '@angular/core';
import { timeAgo } from '../utils/assessment/assessment';

@Pipe({
  name: 'timeAgo',
  standalone: true,
  pure: false, // Set to false so time-based output updates as time passes
})
export class TimeAgoPipe implements PipeTransform {
  transform(dateString: string): string {
    if (!dateString) {
      return '';
    }

    try {
      return timeAgo(dateString);
    } catch (error) {
      return 'Invalid date';
    }
  }
}
