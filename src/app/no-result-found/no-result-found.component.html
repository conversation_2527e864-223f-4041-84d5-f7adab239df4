@if (search === '') {
  <main class="flex flex-col items-center justify-center h-full min-h-[50vh]">
    <div>
      <img src="../../assets/generalImages/no data image.svg" alt="No data" />
    </div>
    <p class="text-[16px] text-[#0C4767] font-semibold mb-[12px]">
      {{ contextualMessage || message || firstMessage }}
    </p>
  </main>
} @else {
  <main class="flex flex-col items-center justify-center h-full min-h-[30vh]">
    @if (search === null) {
      <div>
        <img src="../../assets/icons/folder.svg" alt="Empty folder" />
      </div>
      <p
        class="max-w-[408px] text-[13px] text-[#474d66] font-normal mb-[20px] mt-[11px] text-center"
      >
        {{ contextualMessage || message }}
      </p>
      @if (showButton) {
        <div class="max-w-[130px]">
          <app-custombutton
            (clicked)="clickToPerformAction()"
            [variant]="'secondary'"
            >{{ buttonLabel }}</app-custombutton
          >
        </div>
      }
    } @else {
      <div>
        <img src="../../assets/icons/searchIcon.svg" alt="Search" />
      </div>
      <div class="text-center">
        <p class="text-[24px] text-[#0C4767] font-semibold mb-[12px] mt-[21px]">
          {{ contextualMessage || message }}
        </p>
        <p class="text-[#OC4767] text-[16px] font-normal">
          Sorry, we can't find any results matching your search. Kindly try
          again with another term.
        </p>
      </div>
    }
  </main>
}
