// Mock for face-api.js to prevent TensorFlow.js warnings in tests

const mockDetectionChain = {
  withFaceLandmarks: jest.fn().mockReturnThis(),
  withFaceExpressions: jest.fn().mockReturnThis(),
  withAgeAndGender: jest.fn().mockReturnThis(),
};

const mockNets = {
  tinyFaceDetector: {
    loadFromUri: jest.fn().mockResolvedValue(undefined),
  },
  faceLandmark68Net: {
    loadFromUri: jest.fn().mockResolvedValue(undefined),
  },
  faceRecognitionNet: {
    loadFromUri: jest.fn().mockResolvedValue(undefined),
  },
  faceExpressionNet: {
    loadFromUri: jest.fn().mockResolvedValue(undefined),
  },
  ageGenderNet: {
    loadFromUri: jest.fn().mockResolvedValue(undefined),
  },
  ssdMobilenetv1: {
    loadFromUri: jest.fn().mockResolvedValue(undefined),
  },
};

const faceapi = {
  nets: mockNets,
  TinyFaceDetectorOptions: jest.fn().mockImplementation(() => ({})),
  detectAllFaces: jest.fn().mockReturnValue(mockDetectionChain),
};

// Support both CommonJS and ES module imports
module.exports = faceapi;
module.exports.default = faceapi;
