/* You can add global styles to this file, and also import other style files */
@import url('https://fonts.googleapis.com/css2?family=Work+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import 'highlight.js/styles/github.css';
@import 'primeicons/primeicons.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Work Sans', serif, sans-serif !important;
  margin: 0;
}

.drag-over {
  border-color: #2563eb;
  background-color: #eff6ff;
}

/* Vertical scrollbar */
::-webkit-scrollbar {
  width: 8px;
  background-color: #bdcbd3;
}

::-webkit-scrollbar-thumb {
  background-color: #4a4a4a;
  border-radius: 10px;
}

/* Horizontal scrollbar */
::-webkit-scrollbar:horizontal {
  height: 8px;
  background-color: #7a9aac66;
}

::-webkit-scrollbar-thumb:horizontal {
  background-color: #4a4a4a;
  border-radius: 10px;
}

html,
body {
  margin: 0;
  font-family: Roboto, 'Helvetica Neue', sans-serif;
}

label {
  text-transform: capitalize;
}

.card-tooltip {
  max-width: min(90vw, 600px);
  width: auto;
  min-width: 100px;
  z-index: 1000;
  word-break: break-word;
  white-space: normal;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: visible;
  transition:
    opacity 0.2s ease,
    visibility 0.2s ease;
  left: 0;
  transform: none;
  margin-left: 0;
}

.tooltip-content {
  display: block;
  max-height: 70vh;
  overflow-y: auto;
  padding: 4px;
  width: 100%;
}

.tooltip-top {
  position: absolute;
  bottom: 100%;
  margin-bottom: 8px;
}

.tooltip-bottom {
  position: absolute;
  top: 100%;
  margin-top: 8px;
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

.min-w-\[80px\] {
  min-width: 80px;
}

.button-container {
  display: flex;
  justify-content: flex-start;
}

.button-container app-custombutton {
  width: auto;
}

.editor.empty:before {
  content: attr(placeholder);
  color: #9ca3af;
  pointer-events: none;
}

.editor.empty:focus:before {
  content: attr(placeholder);
  color: #d1d5db;
}
