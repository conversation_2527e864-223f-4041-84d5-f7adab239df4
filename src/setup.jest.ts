/* eslint-disable @typescript-eslint/no-explicit-any */
import 'jest-preset-angular/setup-jest';

// Suppress TensorFlow.js and face-api.js warnings in tests
const originalWarn = console.warn;
const originalError = console.error;

beforeAll(() => {
  console.warn = jest.fn((message: string, ...args: unknown[]) => {
    // Suppress specific TensorFlow.js warnings
    if (
      typeof message === 'string' &&
      (message.includes('Platform browser has already been set') ||
        message.includes('TensorFlow.js') ||
        message.includes('face-api.js') ||
        message.includes('@tensorflow/tfjs'))
    ) {
      return;
    }
    // Allow other warnings to pass through
    originalWarn(message, ...args);
  });

  console.error = jest.fn((message: string, ...args: unknown[]) => {
    // Suppress specific face-api.js errors in test environment
    if (
      typeof message === 'string' &&
      (message.includes('Failed to load face-api.js models') ||
        message.includes(
          'fetch - missing fetch implementation for nodejs environment'
        ) ||
        message.includes('Face detection error'))
    ) {
      return;
    }
    // Allow other errors to pass through
    originalError(message, ...args);
  });

  // Create a mock for the Audio class
  class MockAudio {
    // Mock properties and methods
    src: string | undefined;
    currentTime: number = 0;
    duration: number = 0;
    paused: boolean = true;
    ended: boolean = false;
    volume: number = 1;

    play = jest.fn().mockImplementation(() => {
      this.paused = false;
      return Promise.resolve();
    });

    pause = jest.fn().mockImplementation(() => {
      this.paused = true;
    });

    // Add other necessary methods/properties as needed
    addEventListener = jest.fn();
    removeEventListener = jest.fn();
  }

  // Assign the mock to the global scope
  (global as any).Audio = MockAudio;
});

afterAll(() => {
  console.warn = originalWarn;
  console.error = originalError;
});

class Quill {
  public static readonly register = jest.fn();

  on: jest.Mock;
  getContents: jest.Mock;
  setContents: jest.Mock;

  constructor() {
    this.on = jest.fn();
    this.getContents = jest.fn();
    this.setContents = jest.fn();
  }
}

jest.mock('quill', () => {
  return {
    __esModule: true,
    default: Quill,
  };
});

jest.mock('quill/blots/embed', () => {
  return {
    __esModule: true,
    default: jest.fn(),
  };
});
// Mock EventSource
class MockEventSource {
  onmessage: (event: unknown) => void;
  constructor() {
    this.onmessage = () => {};
  }
  close() {
    /* close method on event source */
  }
}

// Add to global scope
(global as any).EventSource = MockEventSource;

window.URL.createObjectURL = jest.fn();
window.URL.revokeObjectURL = jest.fn();
HTMLAnchorElement.prototype.click = jest.fn();

jest.mock('jspdf', () => {
  return jest.fn().mockImplementation(() => {
    return {
      internal: {
        pageSize: {
          width: 100,
          height: 100,
          getWidth: jest.fn(),
        },
        getFontSize: jest.fn(),
        getFont: jest.fn(),
      },
      addImage: jest.fn(),
      save: jest.fn(),
      setFontSize: jest.fn(),
      text: jest.fn(),
      addPage: jest.fn(),
      setFillColor: jest.fn(),
      rect: jest.fn(),
      setTextColor: jest.fn(),
      getNumberOfPages: jest.fn(),
      setPage: jest.fn(),
      roundedRect: jest.fn(),
      lastAutoTable: {
        finalY: 0,
      },
      getTextWidth: jest.fn(),
    };
  });
});

jest.mock('jspdf-autotable', () => {
  return jest.fn().mockImplementation(() => {
    return {
      autoTable: jest.fn(),
    };
  });
});

// Mock history.state
Object.defineProperty(window, 'history', {
  value: {
    state: { currentPage: 1 }, // Set initial state
    pushState: jest.fn((state) => {
      Object.assign(window.history, { state });
    }),
    replaceState: jest.fn((state) => {
      Object.assign(window.history, { state });
    }),
    back: jest.fn(),
    forward: jest.fn(),
    go: jest.fn(),
  },
  writable: true, // Allows modification if needed
});

// Mock ResizeObserver
class MockResizeObserver {
  private readonly callback: ResizeObserverCallback;
  private readonly observedElements: Set<Element> = new Set();

  constructor(callback: ResizeObserverCallback) {
    this.callback = callback;
  }

  observe(element: Element): void {
    this.observedElements.add(element);
  }

  unobserve(element: Element): void {
    this.observedElements.delete(element);
  }

  disconnect(): void {
    this.observedElements.clear();
  }

  // Method to trigger the callback for testing purposes
  trigger(entries: ResizeObserverEntry[]): void {
    this.callback(entries, this);
  }
}

// Add to global scope
(global as any).ResizeObserver = MockResizeObserver;

// setup-jest.ts
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: (query: string) => ({
    matches: false, // or true, depending on your test needs
    media: query,
    onchange: null,
    addListener: jest.fn(), // Deprecated
    removeListener: jest.fn(), // Deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  }),
});
