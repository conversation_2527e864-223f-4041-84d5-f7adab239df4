/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./src/**/*.{html,ts}'],
  theme: {
    extend: {
      colors: {
        'orange-O50': '#F9E0D7',
        'orange-O100': '#F2BEAB',
        'orange-O200': '#EB9C7F',
        'orange-O300': '#E47A53',
        'orange-O400': '#DD5928',
        'orange-O500': '#B5461D',
        'orange-O600': '#893516',
        'neutral-N0': '#FFFFFF',
        'neutral-N50': '#FAFBFF',
        'neutral-N75': '#F9FAFC',
        'neutral-N100': '#F4F6FA',
        'neutral-N200': '#EDEFF5',
        'neutral-N300': '#E6E8F0',
        'neutral-N400': '#D8DAE5',
        'neutral-N500': '#C1C4D6',
        'neutral-N600': '#8F95B2',
        'neutral-N700': '#696F8C',
        'neutral-N800': '#474D66',
        'neutral-N900': '#101840',
        'red-R50': '#FDF4F4',
        'red-R100': '#F9DADA',
        'red-R200': '#F4B6B6',
        'red-R300': '#EE9191',
        'red-R400': '#D14343',
        'red-R500': '#A73636',
        'red-R600': '#7D2828',
        'red-R700': '#A41201',
        'yellow-Y50': '#FFFAF1',
        'yellow-Y100': '#FFEFD2',
        'yellow-Y200': '#FFDFA6',
        'yellow-Y300': '#FFD079',
        'yellow-Y400': '#FFB020',
        'yellow-Y500': '#996A13',
        'yellow-Y600': '#66460D',
        'green-G50': '#FFFAF1',
        'green-G100': '#FFEFD2',
        'green-G200': '#DCF2EA',
        'green-G300': '#FFD079',
        'green-G400': '#52BD94',
        'green-G500': '#429777',
        'green-G600': '#317159',
        'blue-B50': '#F3F6FF',
        'blue-B100': '#EBF0FF',
        'blue-B200': '#D6E0FF',
        'blue-B300': '#9DB5FF',
        'blue-B400': '#3366FF',
        'blue-B500': '#2952CC',
        'blue-B600': '#1F3D99',
        'blue-B700': '#0C4767',
        'blue-B800': '#474D66',
        'blue-B900': '#D0E3E3',
        'violet-V50': '#F8F7FD',
        'violet-V100': '#E7E4F9',
        'violet-V200': '#D0CAF4',
        'violet-V300': '#B8AFEE',
        'violet-V400': '#897AE3',
        'violet-V500': '#6E62B6',
        'violet-V600': '#524988',
        'teal-T50': '#F2FCFD',
        'teal-T100': '#D3F5F7',
        'teal-T200': '#A8EAEF',
        'teal-T300': '#7CE0E6',
        'teal-T400': '#25CBD6',
        'teal-T500': '#10899E',
        'teal-T600': '#0F5156',
        'pink-P50': '#FEF5FB',
        'pink-P100': '#FBDDF3',
        'pink-P200': '#F8BBE7',
        'pink-P300': '#F499DA',
        'pink-P400': '#ED55C2',
        'pink-P500': '#BE449B',
        'pink-P600': '#8E3374',
        'gray-G500': '#9BB3C1',
      },
      fontSize: {
        H900: '2.074rem',
        H800: '1.728rem',
        H700: '1.44rem',
        H600: '1.2rem',
        H500: '1.0rem',
        H400: '0.833rem',
        H300: '0.694rem',
        H200: '0.694rem',
        H100: '0.579rem',
        P300: '1.2rem',
        P250: '1rem',
        P200: '1rem',
        P100: '0.833rem',
      },
      screens: {
        '3xl': { min: '1367px' },
        'hp-screen': '1300px',
        'hp-max': { max: '1300px' },
        surface: { max: '1045px' },
        below600: { max: '600px' },
        desktop: { min: '1440px' },
        above1700: { min: '1700px' },
        above1800: { min: '1800px' },
        'short': { 'raw': '(max-height: 608px)' },
      },
      boxShadow: {
        light: '0 2px 4px rgba(0, 0, 0, 0.05)',
        custom: '0px 4px 8px 0px rgba(0, 0, 0, 0.10)',
        custom2: '0px 4px 16px 0px rgba(16, 11, 39, 0.06)',
      },
    },
  },
  plugins: [],
};
