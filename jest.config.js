module.exports = {
  preset: 'jest-preset-angular',
  setupFilesAfterEnv: ['<rootDir>/src/setup.jest.ts', 'jest-canvas-mock'],
  moduleNameMapper: {
    '\\.(css|scss|sass)$': 'identity-obj-proxy',
    '^quill/dist/quill.snow.css$': 'identity-obj-proxy',
    '^face-api\\.js$': '<rootDir>/src/__mocks__/face-api.js',
  },
  testMatch: ['**/+(*.)+(spec).+(ts)'],
  coverageThreshold: {
    global: {
      branches: 59,
      functions: 75,
      lines: 80,
      statements: 80,
    },
  },
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageReporters: ['lcov', 'text'],
};
