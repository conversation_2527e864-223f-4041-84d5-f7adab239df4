import { FillInTheBlankData } from '../src/app/Interfaces/questionInterface';

export const mockFillInTheBlankQuestion: FillInTheBlankData = {
  questionText:
    "The capital of France is <span class='blank-placeholder' data-uuid='1'>_______</span>.",
  questionType: 'Fill_in',
  difficultyLevel: 'Medium',
  score: 5,
  categoryId: 'geography',
  domainId: 'global',
  correctAnswers: ['Paris'],
  fillInOptions: [
    { blank: '1', value: 'Paris' },
    { blank: '2', value: 'Berlin' },
    { blank: '3', value: 'Madrid' },
  ],
};

export const fillInTypeDataMock = {
  options: [
    {
      id: '1',
      blank: '1',
      fillInAnswerId: '1',
      value: '',
    },
  ],
  answer: ['True'],
};
