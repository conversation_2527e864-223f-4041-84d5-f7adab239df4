import { BehaviorSubject } from 'rxjs';

export const mockTestTakingService = {
  postSavedQuestionsWithAnswersAsDraft: jest.fn(),
  postAssessmentSubmit: jest.fn(),
  flagQuestion: jest.fn(),
  getAssessmentInformation: jest.fn(),
  unflagQuestion: jest.fn(),
  testTakerApiData$: new BehaviorSubject({
    assessmentTaker: {
      organizationId: '1234',
      assessmentName: 'Sample Assessment',
      email: '<EMAIL>',
      proctorFeatures: ['feature1', 'feature2'],
    },
  }),
  getTestTakerIdFromParam: jest.fn().mockReturnValue('test-taker-id'),
  inProgressReturnedData: {
    assessment: {
      tests: [
        { id: '1', duration: 600, instructions: 'Instruction for test 1' },
        { id: '2', duration: 1200, instructions: 'Instruction for test 2' },
      ],
    },
    proctorFeatures: ['feature1', 'feature2'],
  },
  addTimeTaken: jest.fn().mockImplementation((timeTaken: number) => {
    mockTestTakingService.accumulatedTimeTaken += timeTaken;
  }),
  accumulatedTimeTaken: 0,
  getProctoringInfo: jest.fn().mockReturnValue([]),
  addTestResult: jest.fn(),
  setCandidateEssayAnswers: jest.fn(),
  // Add this property for proctoring data
  assessmentData: {
    screenMonitoring: [],
    candidateMonitoring: [],
  },
};
