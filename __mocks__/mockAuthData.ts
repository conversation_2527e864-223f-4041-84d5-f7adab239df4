import {
  CreatePassword,
  ErrorMessage,
  ForgotPassword,
  GetOrganizationDataResponse,
  ILoginData,
  ILoginForm,
  LoginResponse,
  LoginUserInfo,
  UpdateProfile,
} from '../src/app/services/authServiceInterfaces';

export const mockLoginResponse: LoginResponse = {
  success: true,
  data: {
    user: {
      id: '12345',
      email: '<EMAIL>',
      role_id: 1,
      organizationId: 'org123',
      system: false,
      activated: true,
      organization_activated: true,
      role: 'admin',
      permissions: ['read', 'write', 'delete'],
    },
  },
};

// Mock data for LoginUserInfo
export const mockLoginUserInfo: LoginUserInfo = {
  activated: true,
  Id: 'user123',
  first_name: '<PERSON>',
  last_name: '<PERSON><PERSON>',
  email: '<EMAIL>',
  password: 'securePassword123',
  role: 'Admin',
  password_reset_token: null,
  phone: '************',
  createdAt: '2023-01-01T12:00:00Z',
  updatedAt: '2023-10-01T12:00:00Z',
  organizationId: 'org123',
  permissions: ['read', 'write'],
  system: false,
  image_link: 'https://example.com/image.jpg',
};

// Mock data for CreatePassword
export const mockCreatePassword: CreatePassword = {
  token: 'token123',
  password: 'newSecurePassword123',
  confirmPassword: 'newSecurePassword123',
};

// Mock data for ForgotPassword
export const mockForgotPassword: ForgotPassword = {
  email: '<EMAIL>',
};

// Mock data for LoginResponse
export const mockNewLoginResponse: LoginResponse = {
  success: true,
  data: {
    user: {
      id: 'user123',
      email: '<EMAIL>',
      role_id: 1,
      organizationId: 'org123',
      system: false,
      activated: true,
      organization_activated: true,
      role: 'Admin',
      permissions: ['read', 'write'],
    },
  },
};

// Mock data for ILoginData
export const mockILoginData: ILoginData = {
  data: {
    user: {
      activated: true,
      id: 'user123',
      userId: 'user123',
      first_name: 'John',
      last_name: 'Doe',
      email: '<EMAIL>',
      password: 'securePassword123',
      role: 'Admin',
      password_reset_token: null,
      phone: '************',
      createdAt: '2023-01-01T12:00:00Z',
      updatedAt: '2023-10-01T12:00:00Z',
      organizationId: 'org123',
      role_id: '1',
      permissions: ['read', 'write'],
      image_link: 'https://example.com/image.jpg',
    },
  },
};

// Mock data for ILoginForm
export const mockILoginForm: ILoginForm = {
  email: '<EMAIL>',
  password: 'securePassword123',
};

// Mock data for GetOrganizationData
export const mockGetOrganizationData = {
  id: 'org123',
  organizationName: 'Example Organization',
  email: '<EMAIL>',
  address: '123 Main St, Anytown, USA',
  themeColour: '#FF5733',
  logo: null,
  activated: true,
  createdAt: '2023-01-01T12:00:00Z',
  updatedAt: '2023-10-01T12:00:00Z',
  deletedAt: null,
};

// Mock data for GetOrganizationDataResponse
export const mockGetOrganizationDataResponse: GetOrganizationDataResponse = {
  success: true,
  data: {
    organization: mockGetOrganizationData,
  },
};

// Mock data for UpdateProfile
export const mockUpdateProfile: UpdateProfile = {
  first_name: 'John',
  last_name: 'Doe',
  phone: '************',
  image: new File([''], 'profile.jpg'), // This is a mock file
};

// Mock data for ErrorMessage
export const mockErrorMessage: ErrorMessage = {
  success: false,
  message: {
    message: 'The requested resource was not found',
  },
  status: 404,
  error: {
    message: 'Not Found',
  },
  data: {
    status: 404,
    error: 'Not Found',
    message: 'The requested resource was not found',
    retryAfter: '3600',
  },
};
