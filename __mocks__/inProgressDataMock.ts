import { Assessment } from '../src/app/Interfaces/Types/testTakerInterface';

export const assessmentMock: Assessment = {
  id: 'assessment-1',
  title: 'Final Exam 2025',
  instructions: 'Please answer all questions in the given time.',
  showClock: true,
  conductSurvey: true,
  screenshotsInterval: '5',
  camerashotsInterval: '10',
  isDispatched: true,
  system: true,
  attempted: false,
  createdAt: '2024-12-01T10:00:00Z',
  updatedAt: '2024-12-01T10:00:00Z',
  proctor: '<PERSON>',
  organizationId: 'org-1',
  testOrder: ['test-1', 'test-2'],
  Survey: ['survey-1', 'survey-2'],
  tests: [],
  proctorFeatures: [],
};

export const inProgressReturnedData = {
  id: '123',
  assessmentId: 'assessment-1',
  assessmentName: 'Test Assessment',
  dispatcher: 'dispatcher-1',
  showResults: true,
  organizationId: 'org-1',
  email: '<EMAIL>',
  assessmentLink: 'http://example.com',
  assessmentWindowViolationCount: 0,
  assessmentWindowViolationDuration: 0,
  assessmentTakerShotCount: 0,
  assessmentTakerViolationShotCount: 0,
  windowShotCount: 0,
  windowViolationShotCount: 0,
  assessmentDuration: 120,
  estimatedEndTime: '2025-01-01T00:00:00Z',
  expireDate: '2025-01-01T00:00:00Z',
  commenceDate: '2025-01-01T00:00:00Z',
  startTime: '2025-01-01T00:00:00Z',
  endTime: '2025-01-01T02:00:00Z',
  proctor: 'Proctor Name',
  proctorFeatures: ['feature1', 'feature2'],
  screenshotsInterval: 10,
  camerashotsInterval: 5,
  testList: ['test1', 'test2'],
  submittedTests: [], // Assuming you don't need actual tests in this test case
  phase: 'TEST_TAKING',
  linkStatus: 'valid',
  status: 'in-progress',
  logSummary: ['log1', 'log2'],
  createdAt: '2025-01-01T00:00:00Z',
  updatedAt: '2025-01-01T01:00:00Z',
  assessment: assessmentMock,
  conductSurvey: true,
  currentDate: '2025-01-01T00:00:00Z',
};
