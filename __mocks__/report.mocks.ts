import {
  CandidateReportResponse,
  CandidateSectionDataApi,
  CandidateTestResult,
  ProctoringProtocolsResponse,
} from '../src/app/Interfaces/Types/reportServiceInterface';

export const mockProctoring: ProctoringProtocolsResponse = {
  data: {
    configuration: {
      screenshotsInterval: '1',
      conductSurvey: false,
      showClock: false,
      showResults: false,
      camerashotsInterval: '',
      proctorFeatures: [
        {
          name: 'Feature1',
        },
      ],
    },
  },
  success: true,
};

export const mockTestResult = [
  {
    name: 'test',
  },
  {
    name: 'test1',
    candidateScore: 'element.candidateScore',
    percentage: 'element.percentage',
    numberOfQuestionsFailed: 'element.numberOfQuestionsFailed',
    numberOfQuestionsPassed: 'element.numberOfQuestionsPassed',
    numberOfQuestionsAnswered: 1,
    totalNumQuestions: 2,
  },
] as unknown as CandidateTestResult[];

export const mockCandidateSectionDataApi: CandidateSectionDataApi = {
  data: {
    assessmentTitle: '',
    assessmentTime: 0,
    assessmentTimeTaken: 0,
    assessmentCandidateScore: 0,
    assessmentOverallScore: 0,
    assessmentCandidatePercentage: 0,
    assessmentWindowViolationCount: 0,
    assessmentWindowViolationDuration: 0,
    assessmentTakerShotCount: 0,
    assessmentTakerViolationShotCount: 0,
    windowShotCount: 0,
    windowViolationShotCount: 0,
    screenshotsInterval: 0,
    camerashotsInterval: 0,
    integrityScore: 0,
    status: '',
    proctor: '',
    proctorLevel: '',
    commenceDate: '',
    expireDate: '',
    assessmentEndTime: '',
    assessmentStartTime: '',
    testResults: [
      {
        name: 'test',
        questionsAnswered: 0,
        totalNumQuestions: 0,
        testTime: 0,
        candidateScore: 0,
        overallScore: 0,
        percentage: 0,
        numberOfQuestionsFailed: 0,
        numberOfQuestionsPassed: 0,
        numberOfQuestionsAnswered: 0,
        numberOfQuestionsUnanswered: 0,
        numberOfQuestions: 0,
        testWindowViolationDuration: 0,
        testWindowViolationCount: 0,
        testTakerShotCount: 0,
        testTakerViolationShotCount: 0,
        testWindowShotCount: 0,
        testWindowViolationShotCount: 0,
        status: '',
        passage: null,
        questionResults: [],
      },
    ],
  },
  success: false,
};

export const mockCandidateReportResponse: CandidateReportResponse = {
  data: {
    candidates: [
      {
        email: '<EMAIL>',
        proctorFeatures: [{ name: 'Feature1' }, { name: 'Feature2' }],
        startTime: new Date().toISOString(),
        endTime: new Date().toISOString(),
        status: 'Completed',
        commenceDate: new Date().toISOString(),
        assessmentScore: 85,
        assessmentTakerScore: '2/3',
        assessmentTakerScorePercentage: '66%',
        duration: 120,
        submissionType: 'Online',
        id: '',
        invalid: false,
        createdAt: '',
        expireDate: '',
      },
    ],
    totalItems: 1,
  },
  success: false,
};
