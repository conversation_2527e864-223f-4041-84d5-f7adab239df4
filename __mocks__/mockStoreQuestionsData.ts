import { QuestionsData } from '../src/app/Interfaces/questionInterface';

export const mockTotalQuestion = 100;

export const mockQuestionData: QuestionsData = {
  id: '1',
  questionText: 'What is the capital of France?',
  questionType: 'Multiple Choice',
  score: 10,
  system: true,
  timeLimit: '30', // seconds
  difficultyLevel: { name: 'Medium', value: '2' },
  multipleChoiceAnswer: {
    options: ['Paris', 'London', 'Berlin', 'Madrid'],
    answer: ['Paris'],
    id: '',
    questionId: '',
  },
  multipleSelectAnswer: {
    options: ['Option 1', 'Option 2', 'Option 3'],
    answer: ['Option 1', 'Option 3'],
    id: '',
    questionId: '',
  },
  trueOrFalseAnswer: {
    options: ['True', 'False'],
    answer: ['True'],
    id: '',
    questionId: '',
  },
  essayAnswer: {
    rubrics: 'Explain why Paris is the capital of France.',
  },
  fillInAnswer: {
    options: [
      { id: 'opt1', fillInAnswerId: '1', blank: '___', value: 'Paris' },
      { id: 'opt2', fillInAnswerId: '2', blank: '___', value: 'France' },
    ],
    answer: ['Paris', 'France'],
  },
  strictMark: true,
  matchMatrixAnswer: {
    id: 'mm1',
    questionId: '1',
    options: ['Paris', 'London', 'Berlin', 'Madrid'],
    questions: [
      {
        id: 'sq1',
        matchMatrixAnswerId: 'mm1',
        subquestion: 'Which city is the capital of France?',
        answer: ['Paris'],
      },
      {
        id: 'sq2',
        matchMatrixAnswerId: 'mm1',
        subquestion: 'Which city is the capital of the UK?',
        answer: ['London'],
      },
    ],
  },
  isActive: true,
  category: {
    id: 'cat1',
    name: 'Geography',
  },
  domain: {
    id: 'dom1',
    name: 'Social Studies',
  },
  answerOptions: [],
};
