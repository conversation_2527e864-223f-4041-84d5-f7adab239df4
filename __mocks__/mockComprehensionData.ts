import { ComprehensionData } from '../src/app/Interfaces/questionInterface';
import { TestCreation } from '../src/app/Interfaces/testInterface';
import { ComprehensionPreview } from '../src/app/test-management/Tests/tests/comprehension-test/comprehension-test.component';

export const comprehensionConfig = {
  description:
    'A comprehensive test on various science topics, including physics, chemistry, and biology.',
  difficultyLevel: 'High',
  domain: 'Geography',
  category: 'Geography',
  passage: `Science is the systematic pursuit of knowledge involving the observation, identification, description, experimental investigation, and theoretical explanation of phenomena. It encompasses various fields such as physics, chemistry, biology, and earth sciences.`,
  passMark: 75,
  title: 'The Ultimate Science Test',
  duration: '00:00',
};

export const sampleComprehensionData: ComprehensionData = {
  id: 'test123',
  title: 'The Wonders of the Ocean',
  description:
    'A comprehension test focused on the biodiversity of marine life and oceanography.',
  domainId: 'domain456',
  passage: `The ocean covers over 70% of the Earth's surface and is home to a vast array of life forms, ranging from the smallest plankton to the largest whales. Despite its vastness, much of the ocean remains unexplored. This test will assess your understanding of marine ecosystems and the challenges they face.`,
  duration: 30,
  organizationId: 'org789',
  difficultyLevel: 'Medium',
  passMark: 70,
  categoryId: 'cat5',
  noOfQuestions: 5,
  questions: [
    {
      questionId: 'q2',
      questionText: 'Which of the following are considered marine ecosystems?',
      questionType: 'Multiple Select',
      score: 10,
      answerOptions: [
        'Coral reefs',
        'Mangrove forests',
        'Savannas',
        'Deep sea vents',
      ],
      correctAnswers: ['Coral reefs', 'Mangrove forests', 'Deep sea vents'],
      strictMark: true,
    },
    {
      questionId: 'q5',
      questionText: 'Match the following marine species to their habitats:',
      questionType: 'Matching',
      score: 10,
      answerOptions: [
        'Great White Shark - Coastal waters',
        'Anglerfish - Deep sea',
        'Dolphin - Open ocean',
        'Sea Otter - Kelp forests',
      ],
      correctAnswers: [
        'Great White Shark - Coastal waters',
        'Anglerfish - Deep sea',
        'Dolphin - Open ocean',
        'Sea Otter - Kelp forests',
      ],
      strictMark: true,
    },
  ],
};

export const sampleTestComprehensionCreationData: TestCreation = {
  id: 'test789',
  title: 'The Ultimate Science Test',
  description:
    'A comprehensive test on various science topics, including physics, chemistry, and biology.',
  passage: `Science is the systematic pursuit of knowledge involving the observation, identification, description, experimental investigation, and theoretical explanation of phenomena. It encompasses various fields such as physics, chemistry, biology, and earth sciences.`,
  duration: 0,
  instructions:
    'Read each question carefully and choose the best answer. Manage your time effectively to complete all questions within the given duration.',
  domainId: 'domain123',
  domainName: 'Science',
  passMark: 75,
  questions: [
    {
      id: 'q123',
      questionText: 'What is the capital of France?',
      questionType: 'Multiple Choice',
      score: 10,
      system: false,
      timeLimit: '5 minutes',
      difficultyLevel: { name: 'Medium', value: '2' },
      multipleChoiceAnswer: {
        id: 'mcq1',
        questionId: 'q123',
        options: ['Paris', 'Berlin', 'Madrid', 'Rome'],
        answer: ['Paris'],
      },
      essayAnswer: {
        rubrics: 'string',
      },

      answerOptions: ['Paris', 'Berlin', 'Madrid', 'Rome'],
      fillInAnswer: {
        options: [],
        answer: [],
      },
      strictMark: { name: 'Strict', value: '1' },
      isActive: true,
      category: {
        id: 'cat1',
        name: 'Geography',
      },
      domain: {
        id: 'domain1',
        name: 'General Knowledge',
      },
    },
  ],
  difficultyLevel: 'High',
  noOfQuestions: 4,
  formState: true,
  domain: {
    id: 'domain1',
    name: 'Geography',
    description:
      "A domain focused on the study of Earth's surface, features, and systems.",
    createdAt: '2024-10-17T07:36:22Z',
    updatedAt: '2024-10-17T07:36:22Z',
    system: false,
    organizationId: 'org1',
    categories: [],
  },
  useDomainQuestions: true,
  testIndexNumber: undefined,
  categoryId: '',
  isActivated: false,
  testManagerId: '',
  updatedAt: '',
  system: false,
  organizationId: '',
  createdAt: '',
  isActive: true,
};

export const returnDomain = {
  success: false,
  data: [
    {
      id: 'domain1',
      name: 'Domain One',
      description: 'This is the first domain.',
      createdAt: '2023-01-01T08:00:00Z',
      updatedAt: '2023-01-10T08:00:00Z',
      system: false,
      organizationId: 'org1',
      categories: [
        {
          id: 'cat1',
          name: 'Category One',
          description: 'This is the first category in Domain One.',
          domainId: 'domain1',
          system: false,
          organizationId: 'org1',
          createdAt: '2023-01-02T08:00:00Z',
          updatedAt: '2023-01-11T08:00:00Z',
        },
        {
          id: 'cat2',
          name: 'Category Two',
          description: 'This is the second category in Domain One.',
          domainId: 'domain1',
          system: true,
          organizationId: 'org1',
          createdAt: '2023-01-03T08:00:00Z',
          updatedAt: '2023-01-12T08:00:00Z',
        },
      ],
    },
  ],
  totalItems: 1,
};

export const comprehensionTest = {
  id: '2085fb3b-975f-4ffc-b49f-c033cdb2b31a',
  title: 'Upa',
  description: '<p>Descriptio</p>',
  domainId: 'fc4633a9-4b1d-4d52-aec9-40cce87edbe9',
  passage: '<p>Passage</p>',
  duration: 1200,
  organizationId: '1bf5694a-8452-4811-818f-dc6293f634ad',
  difficultyLevel: 'Intermediate',
  categoryId: '3f959e5d-3b77-491b-924a-db3248c4d026',
  passMark: 4,
  questions: [
    {
      questionId: '755cf9c5-81e7-4319-b8e9-a88dc5622095',
      questionText: '<p>Question test1</p>',
      questionType: 'True_or_false',
      answerOptions: ['True', 'False'],
      correctAnswers: ['False'],
      score: 6,
      difficultyLevel: 'Intermediate',
      strictMark: false,
    },
  ],
  noOfQuestions: 1,
};
export const comprehensionPreview: ComprehensionPreview = {
  title: 'Upa',
  description: '<p>Descriptio</p>',
  passage: '<p>Passage</p>',
  domain: 'Add Question Test',
  category: 'Senior Developer',
  difficultyLevel: 'Intermediate',
  duration: '00:20',
  passMark: 4,
  questions: [
    {
      id: '755cf9c5-81e7-4319-b8e9-a88dc5622095',
      questionType: 'True_or_false',
      questionText: '<p>Question test1</p>',
      score: 6,
      answerOptions: [
        {
          check: false,
          inputValue: 'True',
        },
        {
          check: true,
          inputValue: 'False',
        },
      ],
      strictMark: '',
    },
  ],
};

export const question = {
  id: 'af30bd7f-a57f-4db7-b06c-f631aa090ccc',
  questionType: 'Multiple Choice',
  questionText:
    '<p>Questisdsasdasssdsdfggfdsdgdafsmdfsddfdfgfdggdfdfdfonhome</p>',
  score: 23,
  answerOptions: [
    {
      check: true,
      inputValue: 'Kwasi',
    },
    {
      check: false,
      inputValue: 'John',
    },
  ],
  strictMark: 'No',
};

export const sampleDomain = [
  {
    id: '1',
    name: 'example.com',
    status: 'active',
    createdAt: '2024-01-01',
    expiryDate: '2025-01-01',
  },
  {
    id: '2',
    name: 'test-domain.com',
    status: 'active',
    createdAt: '2024-02-01',
    expiryDate: '2025-02-01',
  },
  {
    id: '3',
    name: 'inactive-domain.com',
    status: 'inactive',
    createdAt: '2024-03-01',
    expiryDate: '2025-03-01',
  },
];

export const mockDomains = [
  {
    name: 'Science',
    categories: [
      {
        id: 'id',
        name: 'Physics',
        description: 'string;',
        domainId: 'string;',
        system: false,
        organizationId: 'string;',
        createdAt: 'string;',
        updatedAt: 'string;',
      },
      {
        id: 'id',
        name: 'Chemistry',
        description: 'string;',
        domainId: 'string;',
        system: false,
        organizationId: 'string;',
        createdAt: 'string;',
        updatedAt: 'string;',
      },
    ],
    id: '',
  },
  {
    name: 'Arts',
    categories: [
      {
        id: 'id',
        name: 'History',
        description: ' string;',
        domainId: ' string;',
        system: false,
        organizationId: ' string;',
        createdAt: ' string;',
        updatedAt: 'string;',
      },
      {
        id: 'id',
        name: 'Literature',
        description: ' string;',
        domainId: ' string;',
        system: false,
        organizationId: ' string;',
        createdAt: ' string;',
        updatedAt: 'string;',
      },
    ],
    id: '',
  },
];
