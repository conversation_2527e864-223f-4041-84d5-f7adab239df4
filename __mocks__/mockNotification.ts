import {
  AddOns,
  StreamNotificationResponseData,
} from '../src/app/Interfaces/notifications';

export const mockAddOns: AddOns = {
  role: 'Admin',
  email: '<EMAIL>',
  userId: 'user123',
  last_name: '<PERSON><PERSON>',
  first_name: '<PERSON>',
  organizationId: 'org456',
};

export const mockStreamNotification: StreamNotificationResponseData = {
  entry: 1,
  id: 'notif123',
  notificationContent: 'This is a test notification content.',
  notificationTitle: 'Test Notification',
  addOns: mockAddOns,
  type: 'INFO',
  createdAt: new Date().toISOString(),
  read: false,
  unreadNotificationsCount: 3,
};
