{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"amap-frontend": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"allowedCommonJsDependencies": ["moment", "eventemitter3", "lodash", "quill-delta", "quill", "html-truncate", "<PERSON><PERSON><PERSON><PERSON>", "@aws-sdk", "joi", "buffer", "fast-xml-parser", "bowser", "lottie-web", "lottie-web/build/player/lottie_svg", "rgbcolor", "core-js/modules/es.array.reverse.js", "core-js/modules/es.array.index-of.js", "core-js/modules/es.array.includes.js", "core-js/modules/es.array.iterator.js", "node-fetch", "core-js/modules/es.array.reduce.js", "core-js/modules/es.promise.js", "core-js/modules/es.string.match.js", "core-js/modules/es.string.replace.js", "core-js/modules/es.string.starts-with.js", "core-js/modules/es.string.ends-with.js", "core-js/modules/es.string.split.js", "core-js/modules/es.string.trim.js", "core-js/modules/es.string.includes.js", "core-js/modules/web.dom-collections.iterator.js", "core-js/modules/es.regexp.to-string.js", "dompurify", "html2canvas", "raf", "tesseract.js", "regenerator-runtime/runtime", "crypto-js", "jspdf-autotable", "@segment/analytics.js-video-plugins/dist/index.umd.js", "@segment/facade"], "outputPath": "dist/amap-frontend", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*", "input": "node_modules/monaco-editor/min", "output": "assets/monaco"}], "styles": ["src/custom-theme.scss", "src/styles.css", "node_modules/quill/dist/quill.snow.css"], "scripts": [], "server": "src/main.server.ts", "prerender": false, "ssr": false}, "configurations": {"production": {"aot": true, "extractLicenses": false, "namedChunks": false, "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": true}, "fonts": true}, "budgets": [{"type": "initial", "maximumWarning": "1.44mb", "maximumError": "1.4mb"}, {"type": "anyComponentStyle", "maximumWarning": "4kb", "maximumError": "7kb"}], "outputHashing": "all", "sourceMap": false}, "staging": {"optimization": true, "extractLicenses": false, "sourceMap": false, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.staging.ts"}]}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": false, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}]}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "amap-frontend:build:production"}, "development": {"buildTarget": "amap-frontend:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "amap-frontend:build"}}, "test": {"builder": "@angular-builders/jest:run", "options": {"jestConfig": ["jest.config.js"]}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"schematicCollections": ["@angular-eslint/schematics"], "analytics": "4b6dd3b1-90b9-42ee-b1c6-8334dabc2d8d"}}