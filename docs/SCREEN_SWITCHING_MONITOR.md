# Screen Switching Monitor - Proctoring Feature

## Overview

The Screen Switching Monitor is an advanced proctoring feature that detects when test takers switch screens, applications, or tabs during an online test. When a violation is detected, the system provides a 5-second countdown warning before automatically terminating the test session and closing the browser.

## Features

### 🔍 **Detection Capabilities**
- **Tab Switching**: Detects when users switch between browser tabs
- **Window Switching**: Monitors when users switch to different applications
- **App Switching**: Identifies when users use keyboard shortcuts to switch applications
- **Screen Focus Loss**: Tracks when the test window loses focus
- **Keyboard Shortcut Prevention**: Blocks common switching shortcuts (Alt+Tab, Ctrl+Tab, etc.)

### ⚡ **Real-time Monitoring**
- Continuous monitoring during test sessions
- Immediate violation detection and response
- 5-second countdown with visual warnings
- Automatic test termination for security

### 📊 **Violation Logging**
- Detailed violation records with timestamps
- Violation type classification
- Duration tracking for focus loss events
- Integration with existing proctoring data

## Implementation

### 1. **Service Integration**

The `ScreenSwitchingMonitorService` is automatically integrated into the test-taking flow:

```typescript
// Automatically starts when Screen Capture proctoring is enabled
if (this.proctoringProtocols.includes('Screen Capture')) {
  this.screenSwitchMonitor.startMonitoring();

}
```

### 2. **Component Structure**

```
src/app/services/screen-switching-monitor.service.ts     # Core monitoring service
src/app/components/screen-switch-countdown/              # Countdown overlay component
src/app/test-taker/pages/test-terminated/               # Termination page
```

### 3. **Event Detection**

The service monitors multiple browser events:

- `window.focus` / `window.blur` - Window focus changes
- `document.visibilitychange` - Tab visibility changes
- `keydown` - Keyboard shortcut prevention
- `mouseleave` - Mouse leaving document area

## Configuration

### Proctoring Protocol Setup

The screen switching monitor is automatically enabled when Screen Capture is active:

```typescript
proctorFeatures: [
  'Screen Capture',      // This automatically enables screen switching monitor
  'Candidate Capture',   // Optional: Camera monitoring
  'Window Violation'     // Optional: Additional window monitoring
]
```

**No additional configuration needed!** The screen switching monitor activates automatically when `'Screen Capture'` is included in the proctoring features.

### Customization Options

The service provides several configuration points:

```typescript
// Countdown duration (default: 5 seconds)
private countdownValue = 5;

// Violation types tracked
type ViolationType = 'tab_switch' | 'window_switch' | 'app_switch';

// Monitoring sensitivity
private focusCheckDelay = 100; // milliseconds
```

## User Experience

### 1. **Normal Operation**
- Invisible monitoring during test
- No impact on test performance
- Seamless integration with existing proctoring

### 2. **Violation Detection**
1. **Immediate Warning**: Toast notification appears
2. **Countdown Display**: Full-screen overlay with 5-second timer
3. **Grace Period**: User can return to test to cancel termination
4. **Automatic Termination**: Browser closes after countdown expires

### 3. **Termination Flow**
1. Test session terminated
2. Violation logged with timestamp
3. User redirected to termination page
4. Support contact information provided

## Visual Components

### Countdown Overlay
- **Full-screen modal** with high z-index (9999)
- **Animated countdown** with color-coded urgency
- **Progress bar** showing remaining time
- **Clear instructions** for cancellation

### Termination Page
- **Professional layout** explaining the violation
- **Violation details** with timestamp and session info
- **Next steps** for contacting support
- **Contact information** for appeals

## Security Features

### 1. **Keyboard Shortcut Prevention**
Blocks common switching shortcuts:
- `Alt + Tab` (Windows app switching)
- `Ctrl + Tab` (Browser tab switching)
- `Cmd + Tab` (Mac app switching)
- `F11` (Fullscreen toggle)
- `Ctrl + W` (Close tab)
- `Ctrl + N` (New window)
- `Ctrl + T` (New tab)

### 2. **Multiple Detection Methods**
- **Primary**: Window focus/blur events
- **Secondary**: Page visibility API
- **Tertiary**: Mouse leave detection
- **Quaternary**: Keyboard shortcut monitoring

### 3. **Violation Recording**
```typescript
interface ScreenSwitchEvent {
  timestamp: string;           // ISO timestamp
  type: 'tab_switch' | 'window_switch' | 'app_switch';
  duration?: number;           // Time away in milliseconds
}
```

## Integration Points

### 1. **Test Taking Service**
- Integrates with existing proctoring protocols
- Shares violation data with assessment results
- Coordinates with other monitoring features

### 2. **Toast Service**
- Uses existing toast system for notifications
- Consistent styling with app theme
- Multiple notification types (warning, error, info)

### 3. **Analytics Service**
- Tracks violation events for reporting
- Integrates with existing analytics pipeline
- Provides data for test integrity analysis

## Testing

### Unit Tests
```bash
npm test -- screen-switching-monitor.service.spec.ts
```

### Integration Tests
- Test countdown functionality
- Verify termination flow
- Check violation logging
- Validate event detection

### Manual Testing Scenarios
1. **Tab Switching**: Switch to another browser tab
2. **App Switching**: Use Alt+Tab to switch applications
3. **Window Focus**: Click outside browser window
4. **Keyboard Shortcuts**: Try blocked key combinations
5. **Return Behavior**: Return to test during countdown

## Troubleshooting

### Common Issues

1. **False Positives**
   - **Cause**: Browser popup dialogs, system notifications
   - **Solution**: Adjust detection sensitivity, whitelist system events

2. **Countdown Not Cancelling**
   - **Cause**: Focus event not properly detected
   - **Solution**: Check browser compatibility, verify event listeners

3. **Browser Not Closing**
   - **Cause**: Browser security restrictions
   - **Solution**: Fallback to termination page redirect

### Browser Compatibility
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

### Performance Considerations
- Minimal CPU impact (< 1%)
- No memory leaks with proper cleanup
- Efficient event handling with debouncing

## Support and Maintenance

### Monitoring
- Service health checks
- Violation rate analysis
- False positive tracking
- Performance metrics

### Updates
- Regular browser compatibility testing
- Security patch integration
- Feature enhancement based on feedback
- Documentation updates

### Support Contacts
- **Technical Issues**: <EMAIL>
- **False Positives**: <EMAIL>
- **Feature Requests**: <EMAIL>

---

## Quick Start

1. **Enable Feature**: Include `'Screen Capture'` in proctoring protocols (screen switching monitor activates automatically)
2. **Test Integration**: Run test session with screen capture enabled
3. **Verify Behavior**: Test violation detection and countdown
4. **Configure Support**: Set up termination page contact information
5. **Monitor Usage**: Track violation rates and adjust sensitivity

The Screen Switching Monitor provides robust test security while maintaining a professional user experience for legitimate test takers.
