# LaunchDarkly Integration Guide

This document provides comprehensive information about the LaunchDarkly feature flag integration in the AMAP Frontend application.

## Overview

The application uses LaunchDarkly for feature flag management, allowing for:
- Safe feature rollouts
- A/B testing capabilities
- Real-time feature toggling
- User-specific feature targeting
- Gradual feature releases

## Architecture

### Core Components

1. **FeatureFlagService** - Main service for interacting with LaunchDarkly
2. **FeatureFlagDirective** - Template directive for conditional rendering
3. **FeatureFlagGuard** - Route guard for feature-gated pages
4. **FeatureFlagAdminComponent** - Admin interface for flag management

### Service Structure

```typescript
@Injectable({ providedIn: 'root' })
export class FeatureFlagService {
  // Observables for reactive flag updates
  public readonly initialized$: Observable<boolean>;
  public readonly flags$: Observable<Partial<FeatureFlagConfig>>;
  
  // Methods for flag access
  getFlag(flagName: keyof FeatureFlagConfig): Observable<boolean>;
  getFlagValue(flagName: keyof FeatureFlagConfig): boolean;
  getAllFlags(): Observable<Partial<FeatureFlagConfig>>;
}
```

## Feature Flag Categories

### AI Features
- `enableAICodeGeneration` - AI-powered code generation
- `enableAITestCaseGeneration` - AI test case generation
- `enableAIQuestionGeneration` - AI question generation
- `enableAISolutionGeneration` - AI solution generation
- `enableAIBoilerplateGeneration` - AI boilerplate generation

### Proctoring Features
- `enableFaceDetection` - Face detection during assessments
- `enableScreenRecording` - Screen recording capability
- `enableCameraMonitoring` - Camera monitoring
- `enableAudioMonitoring` - Audio monitoring
- `enableScreenSwitchingDetection` - Screen switching detection
- `enableIdleTimeTracking` - Idle time tracking
- `enableWindowViolationTracking` - Window violation tracking

### Assessment Configuration
- `enableIdCapture` - ID capture during setup
- `enableCandidateCapture` - Candidate photo capture
- `enableScreenCapture` - Screen capture
- `enableSurveyAfterTest` - Post-test survey
- `enableHonourCode` - Honour code requirement

### UI/UX Features
- `enableDarkTheme` - Dark theme support
- `enableNewDashboard` - New dashboard design
- `enableAdvancedReporting` - Advanced reporting features
- `enableBulkOperations` - Bulk operations
- `enableRealTimeNotifications` - Real-time notifications
- `enableExperimentalFeatures` - Experimental features

### Performance Features
- `enableLazyLoading` - Lazy loading optimizations
- `enableCaching` - Enhanced caching
- `enableOptimizedBundling` - Bundle optimizations

### Security Features
- `enableAdvancedFingerprinting` - Advanced fingerprinting
- `enableEnhancedEncryption` - Enhanced encryption
- `enableSessionMonitoring` - Session monitoring

## Usage Examples

### Template Usage with Directive

```html
<!-- Simple feature flag -->
<div *appFeatureFlag="'enableAICodeGeneration'">
  <button (click)="generateCode()">Generate Code with AI</button>
</div>

<!-- With else template -->
<div *appFeatureFlag="'enableNewDashboard'; else oldDashboard">
  <app-new-dashboard></app-new-dashboard>
</div>
<ng-template #oldDashboard>
  <app-old-dashboard></app-old-dashboard>
</ng-template>
```

### Service Usage in Components

```typescript
export class MyComponent implements OnInit {
  constructor(private featureFlagService: FeatureFlagService) {}
  
  ngOnInit() {
    // Reactive approach
    this.featureFlagService.getFlag('enableAICodeGeneration')
      .subscribe(enabled => {
        this.showAIFeatures = enabled;
      });
    
    // Synchronous approach
    const isDarkThemeEnabled = this.featureFlagService.getFlagValue('enableDarkTheme');
  }
}
```

### Route Protection

```typescript
const routes: Routes = [
  {
    path: 'ai-features',
    component: AIFeaturesComponent,
    canActivate: [FeatureFlagGuard],
    data: { 
      featureFlag: 'enableAICodeGeneration',
      redirectTo: '/not-available'
    }
  }
];
```

### Service Integration

```typescript
@Injectable()
export class CodeExecutionService {
  constructor(private featureFlagService: FeatureFlagService) {}
  
  generateCode(payload: any): Observable<any> {
    return this.featureFlagService.getFlag('enableAICodeGeneration').pipe(
      switchMap(enabled => {
        if (!enabled) {
          return throwError(() => new Error('AI Code Generation is disabled'));
        }
        return this.http.post('/api/generate-code', payload);
      })
    );
  }
}
```

## Configuration

### Environment Setup

Add LaunchDarkly client ID to your environment files:

```typescript
// environment.ts
export const environment = {
  // ... other config
  launchDarklyClientId: 'your-client-side-id-here'
};
```

### User Context

Update user context for targeted feature flags:

```typescript
const user: FeatureFlagUser = {
  key: 'user123',
  email: '<EMAIL>',
  name: 'John Doe',
  organizationId: 'org456',
  role: 'admin',
  custom: {
    plan: 'premium',
    region: 'us-east'
  }
};

await this.featureFlagService.updateUser(user);
```

## Best Practices

### 1. Naming Conventions
- Use descriptive, action-oriented names
- Prefix with `enable` for boolean flags
- Use camelCase for consistency

### 2. Default Values
- Always provide sensible defaults
- Consider the impact of flags being disabled
- Test both enabled and disabled states

### 3. Error Handling
- Gracefully handle LaunchDarkly initialization failures
- Provide fallback behavior when flags are unavailable
- Log errors appropriately

### 4. Performance
- Use synchronous `getFlagValue()` for performance-critical paths
- Cache flag values when appropriate
- Minimize flag evaluations in tight loops

### 5. Testing
- Mock the FeatureFlagService in tests
- Test both enabled and disabled states
- Use feature flags to enable/disable test scenarios

## Testing

### Unit Testing

```typescript
describe('MyComponent', () => {
  let mockFeatureFlagService: jest.Mocked<FeatureFlagService>;
  
  beforeEach(() => {
    const spy = {
      getFlag: jest.fn(),
      getFlagValue: jest.fn()
    };
    
    TestBed.configureTestingModule({
      providers: [
        { provide: FeatureFlagService, useValue: spy }
      ]
    });
    
    mockFeatureFlagService = TestBed.inject(FeatureFlagService) as jest.Mocked<FeatureFlagService>;
  });
  
  it('should show AI features when flag is enabled', () => {
    mockFeatureFlagService.getFlag.mockReturnValue(of(true));
    // ... test implementation
  });
});
```

### Integration Testing

```typescript
describe('Feature Flag Integration', () => {
  it('should hide AI buttons when AI features are disabled', () => {
    // Set up test with disabled AI flags
    // Verify UI elements are hidden
  });
});
```

## Monitoring and Analytics

### Flag Usage Tracking
- Monitor flag evaluation frequency
- Track user segments affected by flags
- Analyze performance impact of flags

### Rollout Monitoring
- Monitor error rates during rollouts
- Track user feedback and support tickets
- Use gradual rollouts for risky features

## Troubleshooting

### Common Issues

1. **Flags not updating**
   - Check network connectivity
   - Verify client ID configuration
   - Check browser console for errors

2. **Default values being used**
   - Verify LaunchDarkly initialization
   - Check user context setup
   - Validate flag names match LaunchDarkly

3. **Performance issues**
   - Reduce flag evaluation frequency
   - Use caching where appropriate
   - Consider using synchronous flag access

### Debug Mode

Enable debug logging:

```typescript
// In development environment
if (!environment.production) {
  console.log('Feature flags loaded:', flags);
}
```

## Migration Guide

### From Static Configuration

1. Identify hardcoded feature toggles
2. Create corresponding LaunchDarkly flags
3. Replace static checks with service calls
4. Test thoroughly in staging environment
5. Gradually migrate production traffic

### Flag Cleanup

1. Monitor flag usage analytics
2. Remove unused flags after features are stable
3. Archive old flags in LaunchDarkly
4. Clean up code references to removed flags

## Security Considerations

- Client-side flags are visible to users
- Sensitive feature logic should be server-side
- Use targeting rules for security-sensitive features
- Regularly audit flag configurations

## Support and Resources

- [LaunchDarkly Documentation](https://docs.launchdarkly.com/)
- [JavaScript SDK Reference](https://docs.launchdarkly.com/sdk/client-side/javascript)
- Internal team Slack: #feature-flags
- Code review guidelines: Always include flag impact assessment
